


# IBLIS

Laboratory Management System (LIMS)

## Technologies

![Vue.js](https://img.shields.io/badge/vuejs-%2335495e.svg?style=for-the-badge&logo=vuedotjs&logoColor=%234FC08D)

![Nuxtjs](https://img.shields.io/badge/Nuxt-002E3B?style=for-the-badge&logo=nuxtdotjs&logoColor=#00DC82)

![TailwindCSS](https://img.shields.io/badge/tailwindcss-%2338B2AC.svg?style=for-the-badge&logo=tailwind-css&logoColor=white)

![TypeScript](https://img.shields.io/badge/typescript-%23007ACC.svg?style=for-the-badge&logo=typescript&logoColor=white)

![Yarn](https://img.shields.io/badge/yarn-%232C8EBB.svg?style=for-the-badge&logo=yarn&logoColor=white)

## Tech Stack

**Client:** Nuxt JS 3.6.5, Vue 3, TypeScript, TailwindCSS

**Server:** Ruby on Rails 7



## Prerequisites

 1. Node version 18 or greater
 2. Yarn or NPM or PNPM
 3. Git



## Installation

Clone the github repository from EGPAFMALAWI organization. Make sure you have access rights. Open the terminal and run the following commands:

```bash
git  clone  `url`  or  `ssh link`
yarn  install or npm install

```

## Deployment

Follow the following instructions for deploying to production:



First deploy the IBLIS User Manual

1. Run the command `yarn docs:build`

2. Navigate to docs/.vitepress

3. Deploy the dist folder


Second, build the IBLIS for production:

```bash

git  clone  `url`  or  `ssh link`  --tag-latest

mv  .env.example  .env

change  API_BASE_URL  to  point  to  IBLIS_API  url,  include  port

change  VITEPRESS_URL  to  point  to  the  MLAB  User  Manual  url,  include  port

yarn  install

yarn  run  build

.output  build  directory

```

## User Manul Documentation

To update the manual, follow the following steps:

1. Navigate the the **docs** folder from the root of the project.

2. Navigate to **guide** folder

3. Create a file with .md extension as a module. For example: tests.md or patients.md

## Contributions
Feel free to open an issue or for feature update, create a Pull Request. Please make sure you test your feature updates.
