import { defineStore } from "pinia";
import type {
  Location,
  Preference,
  Request,
  Response,
  User,
  UserPayloadInterface,
} from "@/types";
import AuthModule from "@/repository/modules/auth";
import HttpFactory from "@/repository/factory";
import { useRouteStore } from "./route";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import { useSessionStore } from "./session";
import { useToken } from "./token";
import { useWardStore } from "./ward";

export const useAuthStore = defineStore("auth", {
  state: () => ({
    authenticated: <boolean>false,
    loading: <boolean>false,
    user: <User>{},
    token: <string>"",
    department: <string>"",
    locations: [] as Location[],
    selectedLocation: <string>"",
    serverDatetimeDiff: <number>0,
  }),
  getters: {
    isClinician(): boolean {
      if (!this.user || !this.user.roles) return false;
      return this.user.roles.some((role) =>
        String(role.role_name).toLowerCase().includes("clinician")
      );
    },
    isNurse(): boolean {
      if (!this.user || !this.user.roles) return false;
      return this.user.roles.some((role) =>
        String(role.role_name).toLowerCase().includes("nurse")
      );
    },
    isDoctor(): boolean {
      if (!this.user || !this.user.roles) return false;
      return this.user.roles.some((role) =>
        String(role.role_name).toLowerCase().includes("doctor")
      );
    },
    isClinicalRole(): boolean {
      return this.isNurse || this.isClinician || this.isDoctor;
    },
    needsWardSelection(): boolean {
      return this.isClinician || this.isNurse;
    },
    userDisplayInfo(): string {
      if (!this.user) return "";

      let roleInfo = "";
      if (this.isClinician) {
        roleInfo = " (Clinician)";
      } else if (this.isNurse) {
        roleInfo = " (Nurse)";
      }

      const wardStore = useWardStore();
      const wardInfo = wardStore.selectedWard
        ? ` - ${wardStore.selectedWard}`
        : "";

      return `${this.user.first_name} ${this.user.last_name}${roleInfo}${wardInfo}`;
    },
  },
  actions: {
    removeUser() {
      this.user = <User>{};
    },
    async authenticateUser({
      username,
      password,
      department,
    }: UserPayloadInterface) {
      let authModule = new AuthModule(new HttpFactory());
      const tokenStore = useToken();
      const credentials = { username, password, department };
      const { $toast, $router } = useNuxtApp();
      const { data, error, pending }: Response = await authModule.login(
        credentials
      );
      this.loading = pending;
      if (error.value) {
        $toast.error(error.value.data.error);
      }
      if (data.value) {
        let expiration = new Date(data?.value?.authorization.expiry_time);
        expiration.setHours(expiration.getHours());

        const { route } = useRouteStore();
        tokenStore.setToken(data?.value?.authorization.token);

        if (tokenStore.token != null) {
          this.authenticated = true;
          this.user = data?.value?.authorization.user;
          this.department = department;
          this.locations = data?.value?.authorization.user.lab_locations;

          const currentTime = Date.now();
          this.serverDatetimeDiff =
            new Date(data?.value?.authorization.server_time).getTime() -
            currentTime;

          tokenStore.setSessionExpiresAt(
            data?.value?.authorization.expiry_time
          );
          const hasManyLocations =
            data?.value?.authorization.user.lab_locations.length > 1;
          $toast.success(`Logged in as ${username}`);
          if (hasManyLocations) {
            $router.push("/locations");
          } else {
            this.selectedLocation =
              data?.value?.authorization.user.lab_locations[0].name;
            if (route == "") {
              // Route clinical roles (nurse, clinician, doctor) to tests page
              if (this.isClinicalRole) {
                $router.push("/tests");
              } else {
                $router.push("/home");
              }
            } else {
              $router.push(route);
            }
          }
        }
      }
    },
    async logUserOut() {
      const tokenStore = useToken();
      const { start, finish } = useLoadingIndicator();
      const sessionStore = useSessionStore();
      start();
      const request: Request = {
        route: endpoints.logout,
        method: "GET",
        token: `${tokenStore.token}`,
      };
      const { data, error }: Response = await fetchRequest(request);
      if (data.value) {
        sessionStore.setIsOpen(false);
        tokenStore.clearToken();
        useNuxtApp().$toast.success("Successfully logged out");
        setTimeout(() => {
          finish();
          useNuxtApp().$router.push("/");
        }, 1000);
      }
      if (error.value) {
        console.error("error: ", error.value);
        finish();
        useNuxtApp().$toast.error("Failed to log out");
      }
    },
    async refreshToken() {
      const tokenStore = useToken();
      const { data, error, pending } = await useRefreshToken(
        `${tokenStore.token}`
      );
      if (data.value) {
        tokenStore.setToken(data.value.authorization.token);
        tokenStore.setSessionExpiresAt(data.value.authorization.expiry_time);
      }
      if (error.value) {
        console.error(error.value);
      }
      return { data, error, pending };
    },
    updateUserPreferences(preferences: Preference[]) {
      this.user.preferences = preferences;
    },
  },
  persist: {
    storage: sessionStorage,
  },
});
