import { defineStore } from 'pinia';
import { v4 as uuidv4 } from 'uuid';

interface RouteMapping {
  [pathId: string]: string | number;
}

export const useRouteIdStore = defineStore('route-id', {
  state: () => ({
    routeMappings: {} as RouteMapping,
  }),

  actions: {
    /**
     * Generate a new UUID for a given entity ID and store the mapping
     * @param id - The actual entity ID
     * @returns UUID to be used in the route
     */
    generatePathId(id: string | number): string {
      const pathId = uuidv4();
      this.routeMappings[pathId] = id;
      return pathId;
    },

    /**
     * Get the actual ID from a path ID
     * @param pathId - The UUID used in the route
     * @returns The actual entity ID or undefined if not found
     */
    getActualId(pathId: string): string | number | undefined {
      return this.routeMappings[pathId];
    },

    /**
     * Clear a specific path ID mapping
     * @param pathId - The UUID to remove from mappings
     */
    clearPathId(pathId: string): void {
      delete this.routeMappings[pathId];
    },

    /**
     * Clear all path ID mappings
     */
    clearAllPathIds(): void {
      this.routeMappings = {};
    }
  },

  persist: {
    storage: sessionStorage,
  },
});
