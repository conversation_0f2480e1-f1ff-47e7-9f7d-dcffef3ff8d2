export const useToken = defineStore("app-token", {
    state: () => ({
        token: <string>"",
        sessionExpiresAt: <string>"",
    }),
    actions: {
        setToken(token: string) {
            this.token = token;
        },
        setSessionExpiresAt(sessionExpiresAt: string) {
            this.sessionExpiresAt = sessionExpiresAt;
        },
        clearToken() {
            this.token = "";
            this.sessionExpiresAt = "";
        }
    },
    persist: {
        storage: persistedState.localStorage
    }
})