import moment from "moment";
import { defineStore } from "pinia";

export const useNetworkStore = defineStore("network", {
  state: () => ({
    ip: '0.0.0.0',
    port: 8005,
    configLoaded: false,
  }),
  actions: {
    async loadNetworkConfig() {
      if (this.configLoaded) {
        console.info("Network configuration already loaded.");
        return;
      }
      try {
        await fetch(`/api.json?cache=${moment().toLocaleString()}`, {
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Content-Type': 'application/json'
          },
          cache: 'no-store'
        }).then((response) => response.json())
          .then((data) => {
            this.ip = data.ip;
            this.port = data.port;
            this.configLoaded = true;
          });
      } catch (error) {
        console.error("Failed to load network configuration:", error);
      }
    },
    async updateNetwork(ip: string, port: number): Promise<void> {
      this.ip = ip;
      this.port = port;
    }
  },
  persist: {
    storage: persistedState.cookiesWithOptions({
      secure: false,
      sameSite: 'strict'
    })
  },
});
