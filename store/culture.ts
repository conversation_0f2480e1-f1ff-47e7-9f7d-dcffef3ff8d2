import { defineStore } from "pinia";

export const useCultureRoutesStore = defineStore("culture", {
  state: () => ({
    currentRoute: {
      index: 0,
      reportId: "",
      period: ""
    },
    routes: [] as Array<{ index: number; reportId: string, period?: string }>,
  }),
  actions: {
    setCurrentRoute(index: number, reportId: string, period?: string): void {
      this.currentRoute.index = index;
      this.currentRoute.reportId = reportId;
      this.currentRoute.period = period || "";
      this.updateRoutes(index, reportId, period);
    },
    revertRoute(index: number): void {
      this.currentRoute.index = index;
      this.currentRoute.reportId = "";
      this.updateRoutes(index, "");
    },
    updateRoutes(index: number, reportId: string, period?: string): void {
      const existingRouteIndex = this.routes.findIndex(
        (route) => route.index === index
      );
      if (existingRouteIndex !== -1) {
        if (reportId !== "") {
          this.routes[existingRouteIndex].reportId = reportId;
          this.routes[existingRouteIndex].period = period || "";
          
        } else {
          this.routes.splice(existingRouteIndex, 1);
        }
      } else if (reportId !== "") {
        this.routes.push({ index, reportId, period });
      }
      this.routes.sort((a, b) => a.index - b.index);
    },
  },
  persist: {
    storage: sessionStorage,
  },
});