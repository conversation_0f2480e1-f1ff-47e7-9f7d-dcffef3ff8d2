import { defineStore } from "pinia";
import type { Ward, Request, Response } from "@/types";
import fetchRequest from "@/services/fetch";
import { endpoints } from "@/services/endpoints";
import { useToken } from "./token";

export const useWardStore = defineStore("ward", {
  state: () => ({
    wards: [] as Ward[],
    selectedWard: "" as string,
    loading: false as boolean,
  }),
  actions: {
    async fetchWards() {
      this.loading = true;
      const tokenStore = useToken();

      const request: Request = {
        route: endpoints.sections,
        method: "GET",
        token: tokenStore.token,
      };

      try {
        const { data, error }: Response = await fetchRequest(request);
        if (data.value) {
          this.wards = data.value.data;
        }
        if (error.value) {
          console.error("Error fetching wards:", error.value);
        }
      } catch (err) {
        console.error("Failed to fetch wards:", err);
      } finally {
        this.loading = false;
      }
    },
    setSelectedWard(wardName: string): void {
      this.selectedWard = wardName;
    },
    removeSelectedWard(): void {
      this.selectedWard = '';
    }
  },
  persist: {
    storage: sessionStorage
  }
});
