export const usePageRefresh = (route: any): { refresh: any, forceRefresh: any, routerRefresh: any, replaceRefresh: any, hardRefresh: any } => {
    return {
        refresh: () => {
            useNuxtApp().$router.replace({
                path: route.path,
                query: route.query,
                hash: route.hash
            });
        },
        forceRefresh: () => {
            window.location.reload();
        },
        routerRefresh: () => {
            useNuxtApp().$router.go(0);
        },
        replaceRefresh: (preserveQuery = true) => {
            useNuxtApp().$router.replace({
                path: route.path,
                ...(preserveQuery && { query: route.query })
            });
        },
        hardRefresh: () => {
            window.location.reload();
        }
    }
}