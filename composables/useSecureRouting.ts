import { useRouteIdStore } from '@/store/route-id';
import { useRouter } from '#app';

export function useSecureRouting() {
  const routeIdStore = useRouteIdStore();
  const router = useRouter();

  /**
   * Navigate to a route with a secure path ID instead of actual ID
   * @param baseRoute - Base route path without ID (e.g., '/test-catalog/test-panels/edit')
   * @param id - Actual entity ID
   */
  const navigateSecure = (baseRoute: string, id: string | number) => {
    const pathId = routeIdStore.generatePathId(id);
    router.push(`${baseRoute}/${pathId}`);
  };

  /**
   * Get the actual ID from the current route path ID
   * @param pathIdParam - The name of the path parameter in the route (default: 'id')
   * @returns The actual entity ID or undefined if not found
   */
  const getSecureId = (pathIdParam: string = 'id') => {
    const route = useRoute();
    const pathId = route.params[pathIdParam] as string;

    if (!pathId) {
      return undefined;
    }

    return routeIdStore.getActualId(pathId);
  };

  /**
   * Clear the current route mapping and navigate to a specified route
   * @param toRoute - Route to navigate to after clearing
   * @param pathIdParam - The name of the path parameter in the route (default: 'id')
   */
  const clearAndNavigate = (toRoute: string, pathIdParam: string = 'id') => {
    const route = useRoute();
    const pathId = route.params[pathIdParam] as string;

    if (pathId) {
      routeIdStore.clearPathId(pathId);
    }

    router.push(toRoute);
  };

  return {
    navigateSecure,
    getSecureId,
    clearAndNavigate,
  };
}
