import { useAuthStore } from "@/store/auth";
import { useSessionStore } from "@/store/session";
import { useRouteStore } from "@/store/route";

export const useSessionManagement = () => {
  const isOpen = ref<boolean>(false);
  const time = ref<string>("1:00");
  const loading = ref<boolean>(false);
  const timer = ref<NodeJS.Timeout | null>(null);
  const sessionStore = useSessionStore();

  const formattedTime = computed((): string => time.value);

  const startSessionExpiryWarning = (): void => {
    sessionStore.setIsOpen(true);
  };

  const closeModal = (): void => {
    sessionStore.setIsOpen(false);
    if (timer.value) clearInterval(timer.value);
  };

  const refreshToken = async (): Promise<void> => {
    if (timer.value) clearInterval(timer.value);
    loading.value = true;
    try {
      const { refreshToken } = useAuthStore();
      const { data, error } = await refreshToken();

      if (data.value) {
        sessionStore.setIsOpen(false);
        loading.value = false;
        const { resetLastActivity } = useInactivityLogout();
        resetLastActivity();
      }

      if (error.value) {
        throw error.value;
      }
    } catch (error) {
      console.error("Token refresh failed:", error);
      useNuxtApp().$router.push("/");
    }
  };

  const logOut = async (): Promise<void> => {
    const { logUserOut } = useAuthStore();
    const { lastKnownRoute } = useRouteStore();

    const currentRoute = useRoute();
    if (currentRoute.path && currentRoute.path !== "/") {
      lastKnownRoute(currentRoute.path);
    }
    closeModal();
    await logUserOut();
  };

  return {
    isOpen,
    formattedTime,
    loading,
    startSessionExpiryWarning,
    refreshToken,
    logOut,
    closeModal,
  };
};
