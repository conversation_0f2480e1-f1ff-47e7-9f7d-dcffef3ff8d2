import type { Preference } from "@/types";
import { useAuthStore } from "@/store/auth";
import { computed } from "vue";

/**
 * A composable for accessing user preferences with default values
 *
 * @param preferenceName The name of the preference to retrieve
 * @param defaultValue The default value to use if the preference is not found
 * @returns An object with a computed value and a matches property
 */
export const usePreference = <T extends string | boolean>(
  preferenceName: string,
  defaultValue: T
): {
  value: import("vue").ComputedRef<T>,
  matches: import("vue").ComputedRef<boolean>
} => {
  const authStore = useAuthStore();

  const preferenceValue = computed(() => {
    const preference = authStore.user?.preferences?.find(
      (p: Preference) => p.name === preferenceName
    );

    if (typeof defaultValue === 'boolean') {
      return (preference?.value === 'true') as T;
    }
    return (preference?.value ?? defaultValue) as T;
  });

  const matches = computed(() => {
    const preference = authStore.user?.preferences?.find(
      (p: Preference) => p.name === preferenceName
    );
    return (preference?.value ?? String(defaultValue)) === String(defaultValue);
  });

  return {
    value: preferenceValue,
    matches
  };
};

export default usePreference;
