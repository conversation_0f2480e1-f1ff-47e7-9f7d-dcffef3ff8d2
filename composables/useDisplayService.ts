import { usePreference } from '@/composables/usePreference';
import { DisplayService } from '@/utils/displayService';

/**
 * Composable for using the Display Service with Vue's reactivity system
 * This provides the same functionality as DisplayService but works with Vue's composition API
 */
export const useDisplayService = () => {
  const { value: testDisplayPreference } = usePreference("test_name_display", "preferred_name");

  /**
   * Get the appropriate display name based on user preference
   * @param fullName - The full name
   * @param preferredName - The preferred name (optional)
   * @returns The name to display based on user preference
   */
  const getDisplayName = (fullName: string, preferredName?: string | null | undefined): string => {
    return DisplayService.getDisplayName(fullName, preferredName, testDisplayPreference.value);
  };

  /**
   * Get test type display name
   * @param testTypeName - The full test type name
   * @param testTypePreferredName - The preferred test type name (optional)
   * @returns The appropriate test type name to display
   */
  const getTestTypeDisplayName = (testTypeName: string, testTypePreferredName?: string | null | undefined): string => {
    return DisplayService.getTestTypeDisplayName(testTypeName, testTypePreferredName, testDisplayPreference.value);
  };

  /**
   * Get test panel display name
   * @param testPanelName - The full test panel name
   * @param testPanelPreferredName - The preferred test panel name (optional)
   * @returns The appropriate test panel name to display
   */
  const getTestPanelDisplayName = (testPanelName: string, testPanelPreferredName?: string | null | undefined): string => {
    return DisplayService.getTestPanelDisplayName(testPanelName, testPanelPreferredName, testDisplayPreference.value);
  };

  /**
   * Get specimen display name
   * @param specimenName - The full specimen name
   * @param specimenPreferredName - The preferred specimen name (optional)
   * @returns The appropriate specimen name to display
   */
  const getSpecimenDisplayName = (specimenName: string, specimenPreferredName?: string | null | undefined): string => {
    return DisplayService.getSpecimenDisplayName(specimenName, specimenPreferredName, testDisplayPreference.value);
  };

  /**
   * Check if full name display is enabled
   * @returns boolean indicating if full name should be displayed
   */
  const isFullNameDisplay = (): boolean => {
    return DisplayService.isFullNameDisplay(testDisplayPreference.value);
  };

  return {
    getDisplayName,
    getTestTypeDisplayName,
    getTestPanelDisplayName,
    getSpecimenDisplayName,
    isFullNameDisplay,
    testDisplayPreference
  };
};

export default useDisplayService;
