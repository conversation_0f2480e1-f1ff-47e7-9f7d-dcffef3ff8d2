import { useCookie, useState, watch } from '#imports';
import type { CookieRef } from "#app";

let expiration = new Date();

expiration.setTime(expiration.getHours() + 2);

export default function useStatefulCookie<T = string> (name: string): CookieRef<T> {

    const cookie = useCookie<T>(name, { expires: expiration });

    const state = useState<T>(name, () => cookie.value);

    watch(state, () => { cookie.value = state.value; }, { deep: true });

    return state;
}
