import { ref } from "vue";
import fetchRequest, { RequestCancellation } from "@/services/fetch";
import type { Request, Response } from "@/types";

export function useCancellableRequest(): {
  loading: Ref<boolean>;
  executeCancellableRequest: (request: Request) => Promise<Response>;
  cancelRequest: () => void;
} {
  const loading = ref<boolean>(true);
  const cancellation: RequestCancellation = new RequestCancellation();
  const executeCancellableRequest = async (
    request: Request
  ): Promise<Response> => {
    loading.value = true;
    try {
      const response: Response = await fetchRequest(request, cancellation);
      return response;
    } finally {
      loading.value = false;
    }
  };
  const cancelRequest = (): void => {
    cancellation.cancel(useNuxtApp);
    loading.value = false;
  };
  return {
    loading,
    executeCancellableRequest,
    cancelRequest,
  };
}
