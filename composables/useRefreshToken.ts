import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response } from "@/types";
import { useNuxtApp } from "nuxt/app";

export default async function useRefreshToken (token: string) : Promise<Response> {
    const request : Request = {
        route: endpoints.refreshToken,
        method: 'GET',
        token: token
    };
    const { data, error, pending } : Response = await fetchRequest(request);
    if(data.value){
        useNuxtApp().$toast.success(`Session refreshed successfully!`);
    }
    return { data, error, pending }
}
