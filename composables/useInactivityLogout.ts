import { ref, onMounted, onUnmounted } from 'vue'
import { useToken } from '@/store/token';
import { useAuthStore } from '@/store/auth';

export const useInactivityLogout = (inactivityTime = INACTIVITY_TIME) => {
    const { startSessionExpiryWarning, refreshToken } = useSessionManagement();

    const token = useToken();
    const { serverDatetimeDiff } = useAuthStore();
    const lastActivity = ref<number>(Date.now() + serverDatetimeDiff);
    let checkTimer: NodeJS.Timeout | null = null;
    let tokenRefreshed = ref<boolean>(false);

    const resetLastActivity = (): void => {
        lastActivity.value = getServerTime();

        if (token && !tokenRefreshed.value) {
            const timeUntilExpiration = new Date(token.sessionExpiresAt).getTime() + serverDatetimeDiff;
            const currentServerTime = getServerTime();

            if (timeUntilExpiration - currentServerTime < TIME_EXPIRATION_LEFT) {
                console.info("Session refresh triggered");
                refreshToken();
                tokenRefreshed.value = true;
                setTimeout(() => {
                    tokenRefreshed.value = false;
                }, 2 * 60 * 1000);
            }
        }
    }

    const getServerTime = (): number => {
        return new Date().getTime() + serverDatetimeDiff;
    }

    const checkInactivity = (): void => {
        const currentServerTime = getServerTime();
        const timeSinceLastActivity = currentServerTime - lastActivity.value;

        if (timeSinceLastActivity >= inactivityTime) {
            startSessionExpiryWarning();
        }

        // uncomment code to debug
        // console.info('Inactivity Check', {
        //     currentServerTime: new Date(currentServerTime),
        //     lastActivity: new Date(lastActivity.value),
        //     timeSinceLastActivity: timeSinceLastActivity / 1000 + ' seconds'
        // });
    }

    const setupActivityListeners = (): void => {
        const events: string[] = [
            'mousedown',
            'keydown',
            'mousemove',
            'wheel',
            'touchstart'
        ];
        events.forEach((event: string) => {
            window.addEventListener(event, resetLastActivity)
        });
        checkTimer = setInterval(checkInactivity, 10000);
    }

    const cleanupActivityListeners = (): void => {
        const events: string[] = [
            'mousedown',
            'keydown',
            'mousemove',
            'wheel',
            'touchstart'
        ];
        events.forEach((event: string) => {
            window.removeEventListener(event, resetLastActivity);
        });
        if (checkTimer) {
            clearInterval(checkTimer);
        };
    }

    onMounted((): void => {
        setupActivityListeners();
    })

    onUnmounted((): void => {
        cleanupActivityListeners();
    })

    return {
        resetLastActivity
    }
}