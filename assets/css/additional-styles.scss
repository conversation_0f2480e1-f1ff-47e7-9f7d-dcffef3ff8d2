/** Scrollbar styles **/

/* width */
::-webkit-scrollbar {
    width: 12px;
    height: 16px;
}

/* Track */
::-webkit-scrollbar-track {
border-radius: 100vh;
background: #edf2f7;
}

/* Handle */
::-webkit-scrollbar-thumb {
background: #cbd5e0;
border-radius: 100vh;
border: 3px solid #edf2f7;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
background: #a0aec0;
}

/** Global multi-select theme */

.multiselect-green {
    --ms-tag-bg: #22c55e;
    --ms-tag-color: #f0fdf4;
}

// *{
//     user-select: none;
// }
