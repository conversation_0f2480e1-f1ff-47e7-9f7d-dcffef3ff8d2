#!/bin/bash

echo "========= DEPLOYING IBLIS ========"

# Check if .output exists before removing
if [ -d .output ]; then
    sudo rm -rf .output
    echo ".output folder removed"
else
    echo ".output folder not found, skipping removal"
fi

# Check if docs/.vitepress/dist exists before removing
if [ -d docs/.vitepress/dist ]; then
    sudo rm -rf docs/.vitepress/dist
    echo "docs/.vitepress/dist folder removed"
else
    echo "docs/.vitepress/dist folder not found, skipping removal"
fi

# Check if machine-integration/.vitepress/dist exists before removing
if [ -d machine-integration/.vitepress/dist ]; then
    sudo rm -rf machine-integration/.vitepress/dist
    echo "machine-integration/.vitepress/dist folder removed"
else
    echo "machine-integration/.vitepress/dist folder not found, skipping removal"
fi


echo "=== checking out latest tag ==="

# get version of latest tag
node version.js

# build
echo "===== building IBLIS Core ======"
npm run build
echo "===== building Help & Support Docs ======"
npm run docs:build
echo "===== building Machine Integration Manual ======"
npm run mi:build

sudo rm -rf ../MLAB-Core-Release/server
sudo rm -rf ../MLAB-Core-Release/public
sudo rm -rf ../MLAB-Core-Release/vite
sudo rm -rf ../MLAB-Core-Release/machine-integration
sudo rm ../MLAB-Core-Release/nitro.json

cd ../MLAB-Core-Release
git checkout main -f
cd ../mlab_core

echo "============== copying files =================="

cp -r .output/* ../MLAB-Core-Release
cp -r docs/.vitepress/dist ../MLAB-Core-Release/vite
cp -r machine-integration/.vitepress/dist ../MLAB-Core-Release/machine-integration

echo -e "\033[1;32mComplete! Copied IBLIS Core, Documentation, Machine Integration Manual\033[0m"

echo -e "\033[1;32mCopying network.bash to home directory - MLAB-Core-Release\033[0m"
cp public/network.bash ../MLAB-Core-Release/
sudo rm -f ../MLAB-Core-Release/public/network.bash

# building  v2 IBLIS Reception
echo "============== building v2 IBLIS Reception =================="
pwd
cd ../v2-iblis-reception
echo "Pulling latest changes"
git pull -f
echo "Pulling done"
echo "Building for production"
npm run build
echo "Building complete"

echo "============== removing old files =================="

# removing old files
sudo rm -rf ../MLAB-Core-Release/iblis_reception

# recreate folder
mkdir ../MLAB-Core-Release/iblis_reception

# copying new files to iblis_reception folder
cp -r dist/* ../MLAB-Core-Release/iblis_reception

echo "============== deployment completed successfully! =================="
