import { generateClasses } from '@formkit/themes';
import { genesisIcons } from '@formkit/icons';

export default {
	icons: {
		...genesisIcons,
		searchIcon: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="text-gray-500"><path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" /></svg>',
		eyeClosed: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"><path d="M3 13c3.6-8 14.4-8 18 0"/><path fill="currentColor" d="M12 17a3 3 0 1 1 0-6a3 3 0 0 1 0 6"/></g></svg>',
		eyeOpen: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="m19.5 16l-2.475-3.396M12 17.5V14m-7.5 2l2.469-3.388M3 8c3.6 8 14.4 8 18 0"/></svg>'
	},
	config: {
		classes: generateClasses({
			color: {
				inner:
					'flex max-w-[5.5em] w-full formkit-prefix-icon:max-w-[7.5em] formkit-suffix-icon:formkit-prefix-icon:max-w-[10em]',
				input:
					'$reset appearance-none w-full cursor-pointer border-none rounded p-0 m-0 bg-transparent [&::-webkit-color-swatch-wrapper]:p-0 [&::-webkit-color-swatch]:border-none',
				suffixIcon: 'min-w-[2.5em] pr-0 pl-0 m-auto',
			},
			text: {
				label: 'font-medium formkit-invalid:text-red-500 py-2',
				input:
					'w-full text-gray-600 border px-1.5 py-1.5 rounded focus:ring-none focus:outline-none mt-2',
				message: 'text-red-500 text-sm mt-1',
				outer: 'w-full',
				help: 'text-xs text-gray-700 mt-1 justify-end flex items-end',
			},
			date: {
				label: 'font-medium formkit-invalid:text-red-500 py-2',
				input:
					'w-full text-gray-600 border px-1.5 py-1.5 rounded focus:ring-none focus:outline-none mt-2',
				message: 'text-red-500 text-sm mt-1',
				outer: 'w-full',
			},
			tel: {
				label: 'font-medium formkit-invalid:text-red-500 py-2',
				input:
					'w-full text-gray-600 border px-1.5 py-1.5 rounded focus:ring-none focus:outline-none mt-2 pl-10',
				message: 'text-red-500 text-sm mt-1',
				outer: 'w-full',
				inner: 'relative',
				prefixIcon: 'w-5 h-5',
			},
			search: {
				label: 'font-medium formkit-invalid:text-red-500 py-2',
				input:
					'w-full block text-gray-600 border px-1.5 py-1.5 rounded focus:ring-none focus:outline-none mt-2 pl-10',
				message: 'text-red-500 text-sm mt-1',
				outer: 'w-full',
				inner: 'relative',
				prefixIcon: 'w-5 h-5 absolute left-2 mt-2'
			},
			password: {
				label: 'font-medium formkit-invalid:text-red-500 mb-4',
				input:
					'w-full text-gray-600 border px-1.5 py-1.5 rounded focus:ring-none focus:outline-none mt-2',
				message: 'text-red-500 text-sm mt-1',
				outer: 'w-full',
			},
			number: {
				label: 'font-medium formkit-invalid:text-red-500 mb-4',
				input:
					'w-full text-gray-600 border px-1.5 py-1.5 rounded focus:ring-none focus:outline-none mt-2',
				message: 'text-red-500 text-sm mt-1',
				outer: 'w-full',
				help: 'text-xs text-gray-700 mt-1 justify-start flex items-start',
			},
			textarea: {
				label: 'font-medium formkit-invalid:text-red-500 mb-4',
				input:
					'w-full text-gray-600 border px-1.5 py-1.5 rounded focus:ring-none focus:outline-none mt-2',
				message: 'text-red-500 text-sm mt-1',
				outer: 'w-full',
			},
			form: {
				message: 'px-3 py-3 text-red-500 bg-red-100 m-3 rounded hidden',
			},
			radio: {
				decorator: 'rounded-full relative border border-white bg-white',
				decoratorIcon: 'rounded-full absolute bg-white border',
				legend: 'font-medium formkit-invalid:text-red-500 py-2',
				input: 'form-radio h-4',
				label: 'ml-2 text-gray-700 ',
				outer: 'inline-flex items-center',
				inner: 'h-4 flex space-x-2',
				wrapper: 'flex space-x-2',
				message: 'text-red-500 text-sm mt-1',
			},
			checkbox: {
				label: 'ml-2 text-gray-700 ',
				legend: 'font-medium formkit-invalid:text-red-500 py-2',
				decorator: 'rounded-full relative border border-white bg-white',
				decoratorIcon: 'rounded-full absolute bg-white',
				container: 'flex items-center space-x-2',
				input:
					'w-full h-auto rounded text-green-500 border-gray-300 focus:ring-green-400',
				outer: 'flex items-center space-x-2',
				inner: 'h-4 flex space-x-2',
			},
			select: {
				message: 'text-red-500 text-sm mt-1',
				label: 'font-medium formkit-invalid:text-red-500 mb-4',
				inner:
					'flex relative max-w-md items-center rounded mb-1 ring-1 ring-gray-400 focus-within:ring-blue-500 focus-within:ring-2 [&>span:first-child]:focus-within:text-blue-500 bg-white',
				input:
					' bg-white w-full pl-3 pr-8 py-2 border-none text-base text-gray-700 placeholder-gray-400 formkit-multiple:p-0 data-[placeholder="true"]:text-gray-400 formkit-multiple:data-[placeholder="true"]:text-inherit',
				selectIcon:
					'flex p-[3px] shrink-0 w-5 mr-2 -ml-[1.5em] h-full pointer-events-none',
				option: 'formkit-multiple:p-3 formkit-multiple:text-sm text-gray-700',
			},
		}),
	},
};
