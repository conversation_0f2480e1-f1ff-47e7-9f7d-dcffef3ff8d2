{"hash": "57e64aa7", "configHash": "fce19f10", "lockfileHash": "b43e2c5f", "browserHash": "9a5faf59", "optimized": {"vue": {"src": "../../../../node_modules/vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "afcb1a96", "needsInterop": false}, "vitepress > @vue/devtools-api": {"src": "../../../../node_modules/vitepress/node_modules/@vue/devtools-api/dist/index.js", "file": "vitepress___@vue_devtools-api.js", "fileHash": "3ea7dec0", "needsInterop": false}, "vitepress > @vueuse/core": {"src": "../../../../node_modules/@vueuse/core/index.mjs", "file": "vitepress___@vueuse_core.js", "fileHash": "436ae110", "needsInterop": false}, "@braintree/sanitize-url": {"src": "../../../../node_modules/@braintree/sanitize-url/dist/index.js", "file": "@braintree_sanitize-url.js", "fileHash": "2bd82a06", "needsInterop": true}, "dayjs": {"src": "../../../../node_modules/dayjs/dayjs.min.js", "file": "dayjs.js", "fileHash": "ec12edbe", "needsInterop": true}, "debug": {"src": "../../../../node_modules/debug/src/browser.js", "file": "debug.js", "fileHash": "17ea7e64", "needsInterop": true}, "cytoscape-cose-bilkent": {"src": "../../../../node_modules/cytoscape-cose-bilkent/cytoscape-cose-bilkent.js", "file": "cytoscape-cose-bilkent.js", "fileHash": "3a742647", "needsInterop": true}, "cytoscape": {"src": "../../../../node_modules/cytoscape/dist/cytoscape.esm.mjs", "file": "cytoscape.js", "fileHash": "1a801faa", "needsInterop": false}, "vitepress > @vueuse/integrations/useFocusTrap": {"src": "../../../../node_modules/@vueuse/integrations/useFocusTrap.mjs", "file": "vitepress___@vueuse_integrations_useFocusTrap.js", "fileHash": "a67d809d", "needsInterop": false}, "vitepress > mark.js/src/vanilla.js": {"src": "../../../../node_modules/mark.js/src/vanilla.js", "file": "vitepress___mark__js_src_vanilla__js.js", "fileHash": "5cab8405", "needsInterop": false}, "vitepress > minisearch": {"src": "../../../../node_modules/minisearch/dist/es/index.js", "file": "vitepress___minisearch.js", "fileHash": "94454bcf", "needsInterop": false}}, "chunks": {"chunk-YJ6QP2VR": {"file": "chunk-YJ6QP2VR.js"}, "chunk-LW4I4DCF": {"file": "chunk-LW4I4DCF.js"}, "chunk-BUSYA2B4": {"file": "chunk-BUSYA2B4.js"}}}