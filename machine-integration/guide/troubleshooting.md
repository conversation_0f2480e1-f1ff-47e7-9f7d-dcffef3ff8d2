# Troubleshooting

In many instances, drivers many stop running, crash or fail to transfer data. Here are some common issues and solutions:


## Common Issues and Solutions

| ISSUE                  | EXPLANATION             | SOLUTION                                                                                             |
|-----------------------|-------------------|---------------------------------------------------------------------------------------------------------|
| Failing to fetch results | Results are not being sent to the server| It is recommended to make sure all connections from machine to server and all workflows are working. This entails checking if the driver is running properly (try running the node server without pm2 and transmit results while checking the logs), machine is connected properly (check the ethernet cable or serial) and verify if the JSON file is being created on the server|


