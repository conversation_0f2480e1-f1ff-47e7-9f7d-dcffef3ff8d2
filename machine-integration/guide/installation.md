# Installing Drivers

Drivers are installed in either the server or host computer of a laboratory machine. These drivers are setup by the `Node pm2 instance manager` to run as daemon services. Some are setup as windows services and run automatically on startup.

Available drivers can be found [here on Github](https://github.com/EGPAFMalawiHIS/mlab_drivers.git) Each driver has it's own README.md file as the installation guide.

Next is the documentation on the installation of currently available drivers found in the above link
