# Data Processing

## Handling Data

A connection must be established between a machine and the driver in order to receive data. This can be done using the established communication methods described in the setup guide. A socket listener is implemented to receive incoming data packets over TCP/IP or serial streams.
Once data is received from the machine, it needs to be processed before sending it to the server. This involves the following processes:

## Getting Data

A machine spits data once a test has been completed or through manual transmission (selecting the specific tests or sequence via test id's) or Exporting. Most of this data comes in Binary or Buffer formats. This raw data is manipulated to extract required parameters using the mapping schema.

## Mapping Parameters

Involves mapping out parameters that are required from the raw data received. For example extracting patient id, test name, result value etc.
Most of the drivers (especially for Hematology and Biochemistry machines) use a JSON schema in a file for example `mapping.json` to define the expected format of the data.

## Extracting Data

Data is extracted from the raw data stings/buffers received using a regular expression parser to match patterns defined. Some of these `regular expressions/regex` are generic and some are written within the driver file and are not global throughout the drivers.
