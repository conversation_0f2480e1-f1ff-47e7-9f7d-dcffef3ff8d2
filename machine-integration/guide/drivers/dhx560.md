# <PERSON><PERSON> DxH 560 Heamatology Analyzer

![](../images/dxh560.png)

Files for DxH 560 **Machine-Integration (Driver)**. This machine uses ASTM (Low Level Protocol) in data transfer to a Host computer (**Read Documentation on Host Transmission for DxH 500**).
## Installation
Make sure you have the latest version of Node.js^21 `nvm install 21`
Install packages using NPM `npm install`
##  Run on Local
Use node to run it on local: `node index.js`
## Run on Server
Use Node pm2 to run an instance to start a nodemon process. Configure the pm2 to start on server reboot.
