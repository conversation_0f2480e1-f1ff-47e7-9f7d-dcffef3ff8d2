# GeneXpert Machine Driver

![](../images/genexpert.png)


This is a service that connects with geneXpert machine to allow for automatic results availability in IBLIS. The service basically parses the result uploaded by
geneXPert machine upon completing testing.

## Requirements

1. Node v8.4.0
   Check if installed by running: `which node`
   If not installed, follow instruction from here (https://github.com/nvm-sh/nvm) to install node using nvm
2. pm2 (for auto restarting the driver on server reboot)
   Check if installed by running: `which pm2`
   If not installed, install via (https://pm2.keymetrics.io/docs/usage/quick-start/)

## Setup GeneXpert Driver

1. Make sure the service is able to run without errors by running the following command:
   `node xpert.js`


    - You'll be shown `Server is listening!` on the console if sucessfully executed with no errors

2. Leave the service running: go to geneXpert machine computer and log in as admin.
3. Navigate to the setup > system configuration
4. Window will popup, navigate to Host Communication Settings and you'll be required to edit a few settings there as follows:


    - On Enable Host Communication, tick the box to enable communication
    - Tick on Automatic Result Upload to enable auto uploading of results.
    - Under communication settings:
        > Select ASTM as protocol
        > Select server on Run Host As
        > On Server IP Address put the address of the server on which genexpet driver service is running
        > On Port # put 3031 unless you changed it in xpert.js because another app is running on that port therefore put the one you have changed to.
    - Under Host Test Code:
        >
