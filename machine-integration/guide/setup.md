# Communication Setup

Laboratory Machines use three methods of communication to produce data. The following are the methods used in machine drivers at the facilities.

1. **Serial Communication:**
   This involves using serial ports to connect machines that output raw text data. The serial port is connected from the machine(s) to the computer with the driver. Most of the computers with this configuration runs on windows.

2. **TCP/IP Communication:**
   This method involves communication over a network using the TCP/IP protocol suite. A designated ip address and port is assigned in the communication configurations of the Machines. An **Ethernet** cable is required for this configuration. It connects from the machine to a local network switch/router. A machine which uses this method for example is the **DxH560, GeneXpert**

3. **File Export:**
   Machines can also export data directly to files stored locally or on a network drive. This method involves writing data to a file in a specified format, a machine driver then obtains that file and processes the data. A machine which uses this method for example is the **ELBA XL640 Biochemistry Analyzer**

