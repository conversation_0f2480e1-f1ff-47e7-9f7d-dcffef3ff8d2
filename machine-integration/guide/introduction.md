# Introduction to Machine Integration

Welcome to the LIS Machine Integration Guide! This guide provides comprehensive instructions for integrating laboratory machines with other systems, particularly EMR (Electronic Medical Records) systems. Below is the workflow for data transfer from the machine to LIS (Laboratory Management System).

````mermaid
graph LR;
  Machine --> Driver --> LIS;
````
## Machines & Drivers
Malawi Hospital Laboratories have different departments with different machines. Examples of these machines include **GeneXpert Machine**, **Mindray**, **BS120**, **Elba** etc. Most of these machines can be integrated i.e, automate the process of data (test results data) from the machine to IBLIS automatically. The departments notably; **Biochemistry**, **Hematology**, **Blood Bank**, **Molecular** and **Microbiology** have machines that can be integrated.

A machine driver is a Node service (written in Node.js) that interfaces between the machine and IBLIS, it obtains data from the machines, process it into readable formats and later sends it to IBLIS via a RESTful API.
[Read more about drivers and installation](/guide/installation)

## LIS Integration
As previously stated IBLIS is the main point of focus system. In IBLIS, data sent from the machine driver is saved in a **JSON** file named in respective to the order **accession number**. This JSON file is then parsed by IBLIS and results are saved in the patient's record upon fetching results in the IBLIS.
