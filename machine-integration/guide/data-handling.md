# LIS Data Handling

In our context, IBLIS is the LIS that receives data from various machine drivers. Upon receiving data, IBLIS performs the following functions:
1. Creates a JSON file using the accession number received (if already present it skips this)
2. Parses the params and queries of the API an dumps test(s) and their results into the JSON file
3. Appends data from step 2 if there are more than one tests available under the same accession number
4. Updates the fetch results button in the system enter test results section to indicate new results are available
5. On fetch, the results are populated and the user can save

````mermaid
graph LR;
  JSON --> Fetching --> Database;
````
