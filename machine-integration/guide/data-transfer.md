# Data Transfer

Data transfer is facilitated from the machine driver to the Laboratory Information System (LIS), specifically IBLIS, through a RESTful API. The formatted data is transmitted within the URL parameters, encompassing the accession number, test results, and machine attributes such as name and facility.

Given the potential existence of multiple values for test results, many drivers transmit the data repetitively to ensure comprehensive transmission. Additionally, the system generates output messages confirming successful data transmission to the server, ensuring reliability and completeness of the transfer process.
