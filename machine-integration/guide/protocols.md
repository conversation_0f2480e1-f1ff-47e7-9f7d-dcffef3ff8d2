# Machine Protocols

Laboratory machine protocols are essentially sets of rules or guidelines that govern how laboratory machines communicate and interact with each other or with other systems. They are as a common language or a set of instructions that machines follow to perform specific tasks or exchange information.

## Communication Guidelines
Protocols define how laboratory machines should communicate with each other or with other systems. This includes rules for transmitting data, receiving commands, and handling errors:

**ASTM (American Society for Testing and Materials)**
Also referred to as **Low Level Protocol** ASTM is an international standards organization that develops and publishes technical standards for a wide range of industries, including healthcare. In the context of laboratory machine protocols, ASTM standards often focus on the exchange of data and information between laboratory instruments and information systems.
ASTM standards define protocols for various aspects of laboratory operations, such as specimen identification, result reporting, and instrument interfaces.

These guidelines ensure consistency and interoperability between different laboratory instruments and information systems.

**HL7 (Health Level Seven International)**

HL7 is a widely used set of standards for the exchange, integration, sharing, and retrieval of electronic health information. It is specifically designed to facilitate communication between different healthcare systems and applications.

HL7 standards define a framework for the exchange of clinical and administrative data between healthcare systems, including laboratory information systems (LIS). HL7 messages are structured according to specific message types (e.g., ADT for patient admission, ORM for laboratory orders, ORU for laboratory results) and segments, allowing for seamless communication between disparate systems.

**IKE (Instrumentation, Systems, and Automation Society)**

The Instrumentation, Systems, and Automation Society (now known as the International Society of Automation or ISA) is a professional association that develops standards for automation and control systems across various industries, including laboratory instrumentation.
Communication Guidelines: IKE/ISA standards may cover aspects related to the automation and control of laboratory instruments and systems. These guidelines ensure uniformity in instrument design, communication protocols, and data exchange formats, thereby enhancing interoperability and ease of integration with other systems.

## Data Format
Protocols specify the format in which data should be structured and transmitted between machines. This ensures that machines can understand and interpret the information correctly. In most instances a machine spits out buffer, which is a raw data. This raw data needs to be parsed and structured into a standard format like XML, JSON or CSV for sending to LIS.

## Security Measures
Protocols often include security measures to protect data integrity and prevent unauthorized access. This may involve encryption techniques, authentication mechanisms, and access controls.

## Error Handling
Protocols outline procedures for handling errors and unexpected situations during communication. This ensures that machines can recover from errors gracefully and continue functioning properly.
