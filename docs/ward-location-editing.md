# Ward/Location Editing Feature

## Overview

This feature allows users to edit the ward/location of tests within a 24-hour window after test creation.

## Implementation Details

### Components Modified

- `components/tests/view-dialog/index.vue` - Main test view dialog with ward editing functionality

### Services Modified

- `services/endpoints.ts` - Added `updateWardLocation` endpoint

### Types Modified

- `types/index.d.ts` - Added `encounter_id` field to Test type

## Features

### 1. Ward Selection with Search

- Fetches available wards from API endpoint: `encounter_type_facility_section_mappings/facility_sections?encounter_type_id=2`
- Displays wards in a searchable dropdown component
- Shows current ward selection based on test data
- Real-time search filtering as user types
- Keyboard navigation support (Enter to select, Escape to close)
- Click-to-select functionality
- "No wards found" message when search yields no results

### 2. Time-based Restrictions

- Ward editing is only allowed within 24 hours of test creation
- Uses `created_date` field from test data to calculate time difference
- Shows lock icon (🔒) when editing is disabled due to time restriction
- Displays tooltip explaining the 24-hour restriction

### 2.1 Status-based Restrictions

- Ward editing is not allowed for tests with "verified" status
- Checks `status` field from test data to enforce this restriction

### 3. Ward Update Functionality

- Updates ward via API endpoint: `encounters/{encounter_id}`
- Sends facility_section_id as payload
- Provides real-time feedback with loading indicators
- Updates local state immediately upon successful update
- Shows success/error toast notifications

### 4. User Interface

- Searchable dropdown with custom styling
- Disabled state styling for non-editable wards
- Loading spinner during update operations
- Visual indicators for edit restrictions
- Responsive design with proper spacing
- Hover effects and focus states
- Dropdown arrow indicator
- Search input with placeholder text
- Scrollable ward list for large datasets

## API Endpoints

### Get Available Wards

```
GET encounter_type_facility_section_mappings/facility_sections?encounter_type_id=2
```

### Update Ward Location

```
PUT encounters/{encounter_id}
Body: {
  "encounter" : { facility_section_id: <ward_id> }
}
```

## Usage

1. Open test view dialog
2. Available wards are automatically loaded
3. Current ward is pre-selected based on test data
4. If within 24-hour window, user can click on ward dropdown to open search
5. Type in search box to filter wards by name (case-insensitive)
6. Scrollable list of wards appears below search input and scroll if you cannot see all wards or are just seing the search input
6. Click on desired ward or press Enter to select first filtered result
7. Ward is automatically updated when selection changes
8. Press Escape to close dropdown without selecting
9. Success/error feedback is provided to user

### Search Features

- **Real-time filtering**: Results update as you type
- **Case-insensitive search**: Matches regardless of letter case
- **Keyboard shortcuts**: Enter to select, Escape to close
- **Visual feedback**: Hover effects and clear selection states
- **No results handling**: Shows "No wards found" when search yields no matches

## Error Handling

- Network errors during ward fetching
- Invalid ward selection validation
- Time restriction enforcement
- API error responses
- User-friendly error messages via toast notifications

## Security & Validation

- 24-hour time window enforcement
- Server-side validation (assumed)
- Proper error handling for edge cases
- Token-based authentication for API calls

## Future Enhancements

- Audit trail for ward changes
