# Turn Around Time Report - Report ID Implementation

## Overview

The Turn Around Time Report has been updated to use the standard `report_id` pattern that is consistent with other aggregate reports in the system. This removes the custom "Back to Report" button functionality and utilizes the established drilldown pattern used by other reports.

## Changes Made

### 🔄 **Removed Custom Back Button**
- Removed the custom "Back to Report" button from the drilldown page
- Removed the `goBackToReport` function and related routing logic
- Now uses standard breadcrumb navigation like other reports

### 📊 **Implemented Standard Report ID Pattern**

#### 1. **Report Generation with Report ID**
```typescript
// Updated generateReport function to include report_id parameter
let queryParams = `from=${startDate.value}&to=${endDate.value}&department=${selectedDepartment.value.id}&unit=${unitSelected.value.name.toLowerCase()}&report_id=${route.query.report_id || ''}`;

// Added updateReportId call after successful data fetch
if (data.value) {
    statistics.value = data.value.data;
    updateReportId(data.value.report_id);  // Store report_id in URL
    // ... rest of the logic
}
```

#### 2. **Report ID Management Function**
```typescript
const updateReportId = (reportId: string) => {
    const currentRoute = router.currentRoute.value;
    const newQuery = {
        ...currentRoute.query,
        report_id: reportId,
    };
    router.replace({ query: newQuery }).catch((err: any) => {
        console.error('Failed to replace route:', err);
    });
};
```

#### 3. **Filter Change Handling**
```typescript
// Clear report_id when filters change (forces new report generation)
watch(dateRange, () => {
    // ... existing logic
    const newQuery = {
        ...currentRoute.query,
        ...(start && { from: start }),
        ...(end && { to: end }),
        report_id: '',  // Clear report_id to force regeneration
    };
    // ... rest of the logic
}, { deep: true });

// Similar updates for selectedDepartment and unitSelected watchers
```

#### 4. **Enhanced Auto-loading Validation**
```typescript
const validFields = computed((): boolean => {
    if (
        route.query.to !== undefined &&
        route.query.from !== undefined &&
        route.query.department !== undefined &&
        route.query.unit !== undefined &&
        route.query.report_id !== undefined  // Added report_id requirement
    ) {
        // Set all filters from query parameters
        // ... existing logic
        
        return (
            route.query.to !== "" &&
            route.query.from !== "" &&
            route.query.department !== "" &&
            route.query.unit !== "" &&
            route.query.report_id !== ""  // Validate report_id is not empty
        );
    }
    return false;
});
```

#### 5. **Updated Drilldown Navigation**
```typescript
const showDrilldown = (testType: string, category: string, count: number, associatedIds: string): void => {
    if (count !== 0 && associatedIds !== "") {
        router.push(
            `/reports/${associatedIds}?origin=aggregate&type=turn-around-time-report&from=${startDate.value}&to=${endDate.value}&test=${testType} - ${category}&department=${selectedDepartment.value.name}&department_id=${selectedDepartment.value.id}&unit=${unitSelected.value.name}&report_id=${route.query.report_id || ''}&count=${count}`
            // Added report_id parameter to drilldown URL
        );
    }
};
```

## How It Works

### 1. **Report Generation Flow**
1. User selects filters and generates report
2. Backend returns data with a unique `report_id`
3. `updateReportId()` stores the `report_id` in the URL query parameters
4. Report data is displayed with clickable drilldown cells

### 2. **Drilldown Flow**
1. User clicks on a drilldown cell (Total Tests or Tests Within Normal TAT)
2. Navigation includes all original parameters plus the `report_id`
3. Drilldown page displays detailed data
4. User can use breadcrumb navigation to return

### 3. **Auto-loading Flow**
1. When user returns to the turn-around-time report page with valid query parameters (including `report_id`)
2. `validFields` computed property validates all required parameters are present
3. If valid, `onMounted` automatically calls `generateReport()`
4. Report loads with the same data without requiring user input

### 4. **Filter Change Handling**
1. When any filter changes (date range, department, unit), the `report_id` is cleared
2. This forces a new report generation when the user next submits
3. Prevents stale data from being displayed with different filters

## URL Structure Examples

### Initial Report Generation
```
/reports/aggregate/turn-around-time?from=2024-01-01&to=2024-01-31&department=Laboratory&department_id=1&unit=Hours&report_id=abc123-def456-ghi789
```

### Drilldown Navigation
```
/reports/test123,test456?origin=aggregate&type=turn-around-time-report&from=2024-01-01&to=2024-01-31&test=Blood Test - Total Tests&department=Laboratory&department_id=1&unit=Hours&report_id=abc123-def456-ghi789&count=150
```

### Auto-loading Return
When user navigates back via breadcrumbs, the same URL with `report_id` triggers auto-loading:
```
/reports/aggregate/turn-around-time?from=2024-01-01&to=2024-01-31&department=Laboratory&department_id=1&unit=Hours&report_id=abc123-def456-ghi789
```

## Benefits of Report ID Pattern

### 🔄 **Consistency**
- Follows the same pattern as other aggregate reports (department, infection, malaria, etc.)
- Maintains consistency in codebase architecture
- Uses established navigation patterns

### 📊 **Data Integrity**
- `report_id` ensures the same dataset is used for drilldown
- Prevents data inconsistencies between aggregate and detail views
- Backend can optimize data retrieval using cached report results

### 🚀 **Performance**
- Backend can cache report data using `report_id`
- Faster drilldown navigation as data is already generated
- Reduced database queries for repeated access

### 🔧 **Maintainability**
- Removes custom navigation logic
- Uses standard Vue Router and breadcrumb components
- Easier to maintain and extend

## Files Modified

### 1. `pages/reports/aggregate/turn-around-time.vue`
- Added `report_id` parameter to `generateReport()` function
- Added `updateReportId()` function
- Updated all filter watchers to clear `report_id` on change
- Enhanced `validFields` to require `report_id`
- Updated `showDrilldown()` to include `report_id` parameter

### 2. `pages/reports/[associated_id].vue`
- Removed custom "Back to Report" button
- Removed `goBackToReport()` function
- Now relies on standard breadcrumb navigation

### 3. Documentation Updates
- Updated `docs/turn-around-time-drilldown.md` with new URL patterns
- Created this implementation guide
- Updated usage instructions to reflect standard navigation

## Testing Checklist

- [ ] Generate report and verify `report_id` appears in URL
- [ ] Click drilldown and verify `report_id` is passed to detail page
- [ ] Return via breadcrumb navigation and verify auto-loading works
- [ ] Change filters and verify `report_id` is cleared
- [ ] Generate new report and verify new `report_id` is created
- [ ] Test browser refresh with `report_id` in URL
- [ ] Verify export functionality still works
- [ ] Test with different filter combinations

## Migration Notes

### For Backend API
- Ensure the turn-around-time report endpoint returns a `report_id` field
- Implement caching/optimization using `report_id` if desired
- Handle empty `report_id` parameter gracefully (generate new report)

### For Frontend
- No breaking changes for existing functionality
- Auto-loading now requires `report_id` in addition to other parameters
- Standard breadcrumb navigation replaces custom back button

This implementation brings the Turn Around Time Report in line with other aggregate reports while maintaining all the drilldown and auto-loading functionality.
