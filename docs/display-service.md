# Display Service Documentation

The Display Service is a JavaScript/TypeScript equivalent of the Ruby GlobalService, providing utilities for choosing between full names and preferred names based on user preferences.

## Overview

The service provides two main ways to use it:

1. **Static Service (`DisplayService`)** - For use in non-Vue contexts or when you have the preference value available
2. **Vue Composable (`useDisplayService`)** - For use in Vue components with reactive preference handling

## Usage in Vue Components

### Basic Usage

```typescript
<script setup lang="ts">
import { useDisplayService } from '@/composables/useDisplayService';

const { getTestTypeDisplayName, getTestPanelDisplayName, isFullNameDisplay } = useDisplayService();

// Example usage
const displayName = getTestTypeDisplayName(
  report.test_type_name, 
  report.test_type_preferred_name
);
</script>
```

### Available Methods

```typescript
const {
  getDisplayName,           // Generic display name function
  getTestTypeDisplayName,   // For test types
  getTestPanelDisplayName,  // For test panels  
  getSpecimenDisplayName,   // For specimens
  isFullNameDisplay,        // Check current preference
  testDisplayPreference     // Reactive preference value
} = useDisplayService();
```

## Usage in Non-Vue Contexts

```typescript
import { DisplayService } from '@/utils/displayService';

// You need to provide the preference value
const preferenceValue = "full_name"; // or "preferred_name"

const displayName = DisplayService.getTestTypeDisplayName(
  testTypeName,
  testTypePreferredName,
  preferenceValue
);
```

## API Reference

### Core Methods

#### `getDisplayName(fullName, preferredName?, preferenceValue?)`
- **fullName**: The full name (required)
- **preferredName**: The preferred name (optional)
- **preferenceValue**: Current preference ("full_name" or "preferred_name")
- **Returns**: The appropriate name based on preference

#### `getTestTypeDisplayName(testTypeName, testTypePreferredName?, preferenceValue?)`
- Specialized method for test type names
- Same parameters as `getDisplayName`

#### `getTestPanelDisplayName(testPanelName, testPanelPreferredName?, preferenceValue?)`
- Specialized method for test panel names
- Same parameters as `getDisplayName`

#### `getSpecimenDisplayName(specimenName, specimenPreferredName?, preferenceValue?)`
- Specialized method for specimen names
- Same parameters as `getDisplayName`

#### `isFullNameDisplay(preferenceValue?)`
- **preferenceValue**: Current preference value
- **Returns**: Boolean indicating if full name should be displayed

#### `getColumnDisplayName(fullNameColumn, preferredNameColumn, preferenceValue?)`
- For database queries - returns appropriate column name or COALESCE expression
- **fullNameColumn**: Column name for full name
- **preferredNameColumn**: Column name for preferred name
- **preferenceValue**: Current preference value
- **Returns**: Column name or COALESCE SQL expression

## Examples

### In a Report Component

```typescript
<script setup lang="ts">
const { getTestTypeDisplayName } = useDisplayService();

const computedReportData = computed(() => {
  return reportData.value.map((report: any) => ({
    ...report,
    test_type_name: getTestTypeDisplayName(
      report.test_type_name, 
      report.test_type_preferred_name
    ),
  }));
});
</script>
```

### In a Data Table Header

```typescript
<script setup lang="ts">
const { testDisplayPreference } = useDisplayService();

const headers = computed(() => [
  { text: "PATIENT NO", value: "client.id", sortable: true },
  { 
    text: "TEST", 
    value: testDisplayPreference.value === "full_name" 
      ? "test_type_name" 
      : "test_type_preferred_name", 
    sortable: true 
  },
]);
</script>
```

### Handling Missing Preferred Names

The service gracefully handles cases where preferred names might be null, undefined, or empty:

```typescript
// If preferredName is null/undefined/empty, falls back to fullName
const displayName = getTestTypeDisplayName("Full Test Name", null);
// Returns: "Full Test Name"

const displayName2 = getTestTypeDisplayName("Full Test Name", "Short Name");  
// Returns: "Short Name" (if preference is "preferred_name")
// Returns: "Full Test Name" (if preference is "full_name")
```

## Migration from Direct Preference Usage

### Before
```typescript
const { value: testDisplayPreference } = usePreference("test_name_display", "preferred_name");

const displayName = testDisplayPreference.value === "full_name" 
  ? report.test_type_name 
  : report.test_type_preferred_name || report.test_type_name;
```

### After
```typescript
const { getTestTypeDisplayName } = useDisplayService();

const displayName = getTestTypeDisplayName(
  report.test_type_name, 
  report.test_type_preferred_name
);
```

## Benefits

1. **Consistent Logic**: Centralized logic for name display preferences
2. **Type Safety**: Full TypeScript support with proper type definitions
3. **Null Safety**: Handles missing preferred names gracefully
4. **Reactive**: Vue composable version automatically updates when preferences change
5. **Flexible**: Can be used in both Vue and non-Vue contexts
6. **Ruby Equivalent**: Matches the functionality of the Ruby GlobalService
