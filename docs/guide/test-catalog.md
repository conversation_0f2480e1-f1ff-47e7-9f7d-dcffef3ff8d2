# Test Catalog Management

## 1. Test Catalog Overview

The Test Catalog is the central repository that defines all laboratory tests available in the system. It contains comprehensive information about test types, specimen requirements, methodologies, reference ranges, and other test-related configurations. Proper management of the test catalog is critical for laboratory operations.

```mermaid
graph TD
    A[Test Catalog] --> B[Test Types]
    A --> C[Specimen Types]
    A --> D[Test Panels]
    A --> E[Reference Ranges]
    A --> F[Test Methods]
    A --> G[Result Interpretations]

    B --> B1[Test Parameters]
    B --> B2[Test Units]

    C --> C1[Specimen Requirements]
    C --> C2[Rejection Reasons]
    C --> C3[Specimen Lifespan]

    D --> D1[Panel Components]
    D --> D2[Testing Algorithms]

    E --> E1[Age-based Ranges]
    E --> E2[Gender-based Ranges]
    E --> E3[Condition-based Ranges]

    F --> F1[Equipment]
    F --> F2[Materials]
    F --> F3[Procedures]

    G --> G1[Organisms]
    G --> G2[Drugs]
    G --> G3[Diseases]

    style A fill:#f9f,stroke:#333,stroke-width:2px
```

## 2. Accessing the Test Catalog

To access the test catalog:

1. Log in with appropriate permissions
2. Click on **Administration** in the main navigation menu
3. Select **Test Catalog** from the dropdown menu
4. The system displays the test catalog dashboard with various configuration options

## 3. Managing Test Types

### 3.1 Viewing Test Types

The Test Types section displays all tests configured in the system:

1. Navigate to **Test Catalog** > **Test Types**
2. View the list of all tests with details such as:
   - Test name
   - Code
   - Department
   - Test method
   - Turnaround time (TAT)
   - Specimen types
3. Use filters to narrow down the list:
   - Department
   - Status (active/inactive)
   - Specimen type
   - Search by name or code

### 3.2 Creating a New Test Type

To add a new test to the catalog:

```mermaid
sequenceDiagram
    participant Admin
    participant System

    Admin->>System: Navigate to Test Types
    Admin->>System: Click "Create Test Type"
    System->>Admin: Display test creation form
    Admin->>System: Enter basic test information
    Admin->>System: Define specimen requirements
    Admin->>System: Set result parameters
    Admin->>System: Define reference ranges
    Admin->>System: Set interpretations
    Admin->>System: Submit form
    System->>System: Validate information
    System->>System: Create new test type
    System->>Admin: Display success message
```

1. Click the blue **Create Test Type** button
2. Complete the test information form:
   - **Name**: Full test name (e.g., "Complete Blood Count")
   - **Code**: Short identifier (e.g., "CBC")
   - **Department**: Laboratory department responsible
   - **Method**: Testing methodology
   - **Price**: Cost of the test (if applicable)
   - **Turnaround time**: Expected completion time
   - **Orderable**: Check if test can be ordered directly
3. Configure specimen requirements:
   - Select acceptable specimen types
   - Set volume requirements
   - Define specimen storage conditions
   - Set specimen stability/lifespan
4. Define result parameters:
   - Parameter names
   - Units of measurement
   - Result format (numeric, text, select)
   - Interpretation guidelines
5. Set reference ranges:
   - Define normal ranges
   - Add age-specific ranges
   - Add gender-specific ranges
   - Set critical values
6. Configure result interpretations (if applicable):
   - Link to organisms
   - Link to diseases
   - Link to antimicrobials
7. Click **Save** to create the test

### 3.3 Editing Test Types

To modify an existing test:

1. Find the test in the list
2. Click the **Edit** button next to the test
3. Update the necessary information
4. Click **Save** to apply changes

Note: Changes to test configurations will not affect previously completed tests.

### 3.4 Managing Test Status

Tests can be activated or deactivated without deletion:

1. Find the test in the list
2. Click **Edit**
3. Change the status to "Active" or "Inactive"
4. Click **Save**

Inactive tests cannot be ordered but remain in the system for historical data.

## 4. Specimen Management

### 4.1 Specimen Types

To manage specimen types:

1. Navigate to **Test Catalog** > **Specimen Types**
2. View the list of configured specimen types
3. To add a new specimen type:
   - Click **Create Specimen Type**
   - Enter details:
     - Name (e.g., "Whole Blood", "Serum")
     - Description
     - Collection instructions
     - Container type
     - Preservation requirements
   - Click **Save**
4. To edit, click the **Edit** button next to the specimen type

### 4.2 Specimen Rejection Reasons

To manage rejection reasons:

1. Navigate to **Test Catalog** > **Specimen Rejection Reasons**
2. View the list of defined rejection reasons
3. To add a new rejection reason:
   - Click **Create Rejection Reason**
   - Enter:
     - Reason description
     - Recommended action
   - Click **Save**

### 4.3 Specimen Lifespan

Define how long specimens remain viable:

1. Navigate to **Test Catalog** > **Specimen Lifespan**
2. For each specimen type, configure:
   - Room temperature stability (hours)
   - Refrigerated stability (days)
   - Frozen stability (months)
   - Transportation requirements
3. Click **Save** to apply changes

## 5. Test Panels

Test panels group multiple tests that are commonly ordered together.

### 5.1 Creating Test Panels

To create a new panel:

1. Navigate to **Test Catalog** > **Test Panels**
2. Click **Create Panel**
3. Enter panel information:
   - Name
   - Description
   - Department
4. Add component tests:
   - Select tests from the available tests list
   - Specify order of tests
   - Set any conditional logic (reflexive testing)
5. Click **Save** to create the panel

### 5.2 Panel Types

iBLIS supports different panel configurations:

1. **Simple Panels**: Fixed group of tests always performed together
2. **Conditional Panels**: Include reflexive testing based on initial results
3. **Profile Panels**: Customizable sets of tests by department

### 5.3 Editing and Managing Panels

To modify existing panels:

1. Find the panel in the list
2. Click **Edit**
3. Update panel information or component tests
4. Click **Save**

## 6. Result Interpretation Components

### 6.1 Organisms

For microbiology and related tests:

1. Navigate to **Test Catalog** > **Organisms**
2. To add a new organism:
   - Click **Create Organism**
   - Enter:
     - Organism name
     - Classification
     - Description
   - Click **Save**
3. To edit, click the **Edit** button next to the organism

### 6.2 Drugs and Antimicrobials

For susceptibility testing:

1. Navigate to **Test Catalog** > **Drugs**
2. To add a new drug:
   - Click **Create Drug**
   - Enter:
     - Drug name
     - Class
     - Abbreviation
     - Susceptibility test details
   - Click **Save**
3. To edit, click the **Edit** button next to the drug

### 6.3 Diseases

For surveillance and diagnosis:

1. Navigate to **Test Catalog** > **Diseases**
2. To add a new disease:
   - Click **Create Disease**
   - Enter:
     - Disease name
     - ICD-10 code
     - Description
     - Surveillance status
   - Click **Save**
3. To edit, click the **Edit** button next to the disease

## 7. Test Methods and Procedures

### 7.1 Configuring Test Methods

Define the methodologies used for testing:

1. Navigate to **Test Catalog** > **Test Methods**
2. To add a new method:
   - Click **Create Method**
   - Enter:
     - Method name
     - Description
     - Equipment used
     - Reference documentation
   - Click **Save**


## 8. Test Catalog Maintenance

### 8.1 Catalog Version Control

The system maintains version history of the test catalog:

1. Navigate to **Test Catalog** > **Version History**
2. View changes made to the catalog over time
3. See who made changes and when
4. Revert to previous versions if necessary

### 8.2 Importing Test Definitions

For bulk updates or initial setup:

1. Navigate to **Test Catalog** > **Import**
2. Download the template
3. Fill in test definitions
4. Upload the completed template
5. Review and confirm changes

### 8.3 Exporting the Test Catalog

To backup or transfer test definitions:

1. Navigate to **Test Catalog** > **Export**
2. Choose export format (Excel, CSV, JSON)
3. Select components to export
4. Click **Export** to download the file

## 9. Test Catalog Best Practices

### 9.1 Standardization

For consistent laboratory operations:

- Use standardized naming conventions
- Align with international standards (LOINC, SNOMED)
- Maintain consistent units of measurement
- Document methodologies thoroughly

### 9.2 Test Catalog Review

Regular maintenance ensures accuracy:

- Review catalog quarterly
- Update reference ranges based on population data
- Archive obsolete tests rather than deleting them
- Document changes with rationale

### 9.3 Test Mapping for Interoperability

For integration with other systems:

1. Navigate to **Test Catalog** > **External Mappings**
2. Map internal test codes to:
   - LOINC codes
   - SNOMED CT codes
   - Local health system codes
3. Maintain mappings when adding new tests
