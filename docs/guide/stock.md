# Stock Management Module

## 1. Stock Overview

The Stock Management Module allows laboratory staff to efficiently manage inventory, track stock levels, create requisitions, and ensure adequate supplies for laboratory operations.

```mermaid
flowchart TD
    A[Stock Dashboard] --> B[View Stock Items]
    A --> C[Create New Item]
    A --> D[Manage Categories]
    A --> E[Create Order/RIV]
    B --> F[Update Stock Levels]
    E --> G[Process Orders]
    G --> H[Receive Items]
    H --> F
```

## 2. Stock Items Management

### 2.1 Viewing Stock Items

To view all stock items:
1. Navigate to the **Stock** module from the main menu
2. The system displays all stock items with their current levels, unit of measurement, and status
3. Use the search box to filter items by name, category, or other attributes

### 2.2 Creating Stock Items

To create a new stock item:
1. Click on the **Create New Item** button
2. Fill in the required fields:
   - Item Name
   - Item Code
   - Category
   - Unit of Measurement
   - Minimum Stock Level
   - Maximum Stock Level
3. Click **Save** to add the new item to inventory

### 2.3 Updating Stock Levels

To update stock levels manually:
1. Find the item in the stock list
2. Click on the **Update** button next to the item
3. Enter the new quantity and reason for adjustment
4. Click **Save** to confirm the changes

## 3. Stock Categories

### 3.1 Viewing Categories

1. Click on the **Categories** tab in the Stock module
2. View the list of all existing categories
3. Categories help organize stock items for easier management

### 3.2 Adding a New Category

1. Click on the **Create Category** button
2. Enter the category name and description
3. Click **Save** to create the category

## 4. Requisition and Issue Voucher (RIV)

### 4.1 Creating a RIV

```mermaid
sequenceDiagram
    participant Lab Staff
    participant System
    participant Supervisor
    participant Store Manager

    Lab Staff->>System: Create RIV request
    Lab Staff->>System: Select items and quantities
    System->>Supervisor: Send for approval
    Supervisor->>System: Review and approve
    System->>Store Manager: Forward approved RIV
    Store Manager->>System: Process and issue items
    System->>Lab Staff: Notify items ready for collection
```

To create a new RIV:
1. Click on the **Create RIV** button
2. Select the department requesting supplies
3. Add items to the requisition by:
   - Selecting from the dropdown menu
   - Specifying the quantity required
4. Provide justification for the request
5. Click **Submit** to send the RIV for approval

### 4.2 Approving a RIV

For supervisors to approve a RIV:
1. Navigate to the **Pending Approvals** section
2. Review the requested items and quantities
3. Either:
   - Approve the RIV by clicking the **Approve** button
   - Reject or modify the request with comments
4. Once approved, the RIV is forwarded to the store manager for processing

### 4.3 Processing a RIV

For store managers to process an approved RIV:
1. Go to the **Approved RIVs** section
2. Click on the RIV to be processed
3. Review the requested items
4. Confirm availability and issue the items
5. Update the stock levels automatically upon issuance
6. Print the issue voucher for record-keeping

## 5. Stock Transfers

### 5.1 Transferring Stock Between Locations

1. Navigate to the **Stock Transfer** section
2. Select the source and destination locations
3. Choose the items to transfer and specify quantities
4. Click **Transfer** to complete the process
5. The system updates stock levels at both locations automatically

## 6. Stock Reports

### 6.1 Generating Stock Reports

1. Click on the **Reports** tab in the Stock module
2. Select the type of report:
   - Current Inventory Levels
   - Stock Movement Report
   - Expiry Report
   - Consumption Report
3. Specify the date range and other parameters
4. Click **Generate** to create the report
5. Export the report in PDF, Excel, or CSV format as needed

### 6.2 Stock Alerts

The system automatically generates alerts for:
- Items below minimum stock level
- Items approaching expiry date
- Items with no movement for extended periods

These alerts appear on the Stock Dashboard and can be configured in the system settings.
