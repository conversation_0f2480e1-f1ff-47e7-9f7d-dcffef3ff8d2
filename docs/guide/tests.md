# Orders & Tests Module

## 1. Test Workflow Overview

The Orders & Tests Module manages the complete lifecycle of laboratory tests from ordering to reporting. The diagram below illustrates the standard workflow:

```mermaid
stateDiagram-v2
    [*] --> Ordered
    Ordered --> Rejected: Specimen rejection
    Ordered --> Received: Accept specimen
    Received --> InProgress: Start test
    InProgress --> Completed: Enter results
    Completed --> Verified: Verify results
    Verified --> Reported: Generate report
    Rejected --> [*]
    Reported --> [*]
```

## 2. Managing Test Orders

### 2.1 Viewing All Test Orders

To access and manage test orders:

1. Click on **Tests** in the main navigation menu
2. You'll see a comprehensive table of all tests in the system
3. Filter the view using:
   - **Select Status** dropdown (e.g., Pending, Received, In Progress)
   - **Date Range** selectors for start and end dates
   - **Test Type** dropdown to filter by specific tests
   - **Patient ID** or name search
   - **Urgency** filter for STAT/routine tests

### 2.2 Test Status Colors

The system uses color-coding to quickly identify test status:

| Status | Color | Description |
|--------|-------|-------------|
| Pending | Yellow | Test ordered, specimen not yet received |
| Received | Blue | Specimen received, testing not started |
| In Progress | Orange | Testing has begun |
| Completed | Purple | Results entered, awaiting verification |
| Verified | Green | Results verified and authorized |
| Rejected | Red | Specimen rejected or test canceled |
| STAT | Red Border | Urgent test requiring immediate attention |

## 3. Specimen Processing

### 3.1 Receiving Specimens

```mermaid
sequenceDiagram
    participant Reception
    participant Lab Tech
    participant System

    Reception->>System: Order test in system
    Reception->>Lab Tech: Deliver physical specimen
    Lab Tech->>System: Scan specimen barcode
    Lab Tech->>System: Verify specimen details
    alt Specimen acceptable
        Lab Tech->>System: Click "Accept"
        System->>System: Update status to "Received"
    else Specimen problematic
        Lab Tech->>System: Click "Reject"
        System->>Lab Tech: Prompt for rejection reason
        Lab Tech->>System: Enter rejection details
        System->>System: Update status to "Rejected"
    end
```

To receive a specimen:

1. Locate the test in the pending list
2. Verify that the specimen:
   - Matches the test order (patient ID, test type)
   - Is properly labeled and of adequate quality
   - Was collected and transported appropriately
3. Click the green **Accept** button to confirm receipt
4. The status will change to "Received" and the button will change to orange **Start**

### 3.2 Rejecting Specimens

If a specimen is unsuitable for testing:

1. Click the button marked **>** on the far right of the test row
2. Select **Reject** from the dropdown menu
3. In the rejection dialog, provide:
   - Reason for rejection (select from dropdown)
   - Additional comments if needed
   - Name of person notified about the rejection
4. Click **Confirm Rejection**
5. The system will log the rejection and notify the ordering department

Common reasons for specimen rejection:

- Insufficient quantity
- Hemolyzed specimen
- Incorrect container
- Improper labeling
- Expired collection time
- Clotted specimen (for whole blood tests)

## 4. Test Processing

### 4.1 Starting a Test

Once a specimen is accepted:

1. Click the orange **Start** button
2. The system will:
   - Record the start time
   - Update the test status to "In Progress"
   - Change the button color to blue and label to **Results**

### 4.2 Entering Test Results

```mermaid
flowchart TD
    A[Access Test] --> B{Result Source?}
    B -->|Manual Entry| C[Click Results Button]
    B -->|Instrument| D[Click Fetch Results]

    C --> E[Enter Results in Form]
    D --> F[Confirm Imported Results]

    E --> G[Add Comments if Needed]
    F --> G

    G --> H[Check for Critical Values]
    H --> I[Click Update Test Results]
    I --> J[Status Changes to Completed]
```

To enter results for a test:

1. Find the test with "In Progress" status
2. Click the blue **Results** button
3. For manual result entry:
   - Enter the values in the provided fields
   - Add any relevant comments or observations
4. For instrument-integrated tests:
   - Click **Fetch Results** to import data from the connected analyzer
   - Verify the imported results
5. For tests with multiple parameters:
   - Complete all required fields
   - The system will flag any values outside reference ranges
6. Click **Update Test Results** to save the results
7. The test status will change to "Completed"

### 4.3 Managing Critical Results

When a test result falls outside critical value ranges:

1. The system automatically flags the result in red
2. A critical result notification appears
3. You must acknowledge the critical value by:
   - Confirming the result is accurate
   - Documenting who was notified (e.g., physician)
   - Recording the notification time
4. Critical results require immediate attention and communication

## 5. Results Verification and Authorization

### 5.1 Verifying Results

```mermaid
sequenceDiagram
    participant Lab Tech
    participant Supervisor
    participant System

    Lab Tech->>System: Enter test results
    System->>System: Mark test as "Completed"
    Supervisor->>System: Review test results

    alt Results acceptable
        Supervisor->>System: Click "Authorize"
        System->>System: Mark test as "Verified"
        System->>System: Make results available in reports
    else Results questionable
        Supervisor->>System: Request repeat testing
        System->>Lab Tech: Notification for repeat
        Lab Tech->>System: Enter new results
        Supervisor->>System: Review updated results
    end
```

To verify and authorize results:

1. Navigate to the tests with "Completed" status
2. Review the results for accuracy and plausibility
3. Check for any critical values or abnormalities
4. If needed, compare with patient's previous results
5. To authorize results:
   - Click the green **Authorize** button
   - Enter any additional comments if necessary
   - Confirm authorization

Note: Result verification is only available to users with appropriate permissions (typically supervisors or pathologists).

### 5.2 Batch Verification

For efficient workflow, you can verify multiple tests at once:

1. Go to the **Tests** page
2. Filter for tests with "Completed" status
3. Select multiple tests using the checkboxes
4. Click **Batch Actions** > **Verify Selected**
5. Review the list of tests to be verified
6. Enter any common comments
7. Click **Verify All** to complete the process

## 6. Test Reports

### 6.1 Generating Individual Test Reports

To generate a report for an individual test:

1. Find the verified test in the test list
2. Click on the **Report** button
3. The system generates a formatted report showing:
   - Patient demographics
   - Test information and collection time
   - Results with reference ranges
   - Comments and interpretations
   - Authentication information
4. Choose to:
   - **Print** the report directly
   - **Download** as PDF
   - **Email** to authorized recipient
   - **Save** to patient record

### 6.2 Batch Reporting

For multiple tests or panels:

1. Use the checkboxes to select multiple completed tests
2. Click **Batch Actions** > **Generate Reports**
3. Choose the report format and delivery method
4. The system will generate all selected reports

## 7. Additional Test Management Functions

### 7.1 Repeating Tests

If a test needs to be repeated:

1. Find the original test
2. Click the **>** button and select **Repeat Test**
3. The system will create a new test order linked to the original
4. Process the repeat test through the normal workflow
5. Both test results will be available in the patient record

### 7.2 Adding Additional Tests

To add tests to an existing specimen:

1. Locate the original order
2. Click **Add Test**
3. Select additional tests to be performed
4. Confirm that the original specimen is suitable for the additional tests
5. Click **Save** to create the additional test orders

### 7.3 Test Audit Trail

Every action in the test lifecycle is recorded:

1. To view the complete audit trail:
   - Open the test details
   - Click on the **History** tab
2. The audit trail shows:
   - Who ordered the test
   - When the specimen was received
   - Who performed the test
   - When results were entered
   - Who verified the results
   - Any modifications or comments

## 8. Special Test Procedures

### 8.1 Handling STAT (Urgent) Tests

STAT tests require expedited processing:

1. STAT tests are highlighted with red borders in the test list
2. They appear at the top of work lists by default
3. Process STAT tests immediately, following the same workflow
4. The system tracks STAT turnaround times separately
5. Critical results on STAT tests generate high-priority notifications

### 8.2 Referral Tests

For tests sent to external laboratories:

1. When ordering, check the **Referral Test** option
2. Select the referral laboratory from the dropdown
3. Print the referral form
4. When results return:
   - Click **Enter Referral Results**
   - Enter the results as reported by the referral lab
   - Attach any scanned documents from the reference lab
   - Complete verification as normal
