# Getting Started with iBLIS

## 1. System Access & Authentication

### 1.1 Accessing the System

iBLIS is a web-based application that can be accessed through any modern web browser. To access the system:

1. Open a supported web browser (Chrome, Firefox, Edge, or Safari)
2. Enter the iBLIS URL in the address bar
   - For local installations: `http://localhost:3000` or the configured address
   - For networked installations: Use the URL provided by your system administrator
3. The iBLIS login page will appear

```mermaid
sequenceDiagram
    participant User
    participant Browser
    participant iBLIS

    User->>Browser: Open browser
    User->>Browser: Enter iBLIS URL
    Browser->>iBLIS: Request login page
    iBLIS->>Browser: Return login page
    Browser->>User: Display login page
    User->>Browser: Enter credentials
    Browser->>iBLIS: Submit login request
    iBLIS->>iBLIS: Validate credentials
    alt Valid credentials
        iBLIS->>Browser: Authentication successful
        iBLIS->>Browser: Return dashboard
        Browser->>User: Display dashboard
    else Invalid credentials
        iBLIS->>Browser: Authentication failed
        Browser->>User: Display error message
    end
```

### 1.2 Login Process

To log into the system:

1. Enter your **username** in the username field
2. Input your **password** in the password field
3. Select the appropriate **Laboratory section** from the dropdown menu
   - This determines which department's data you'll work with
   - Only sections you have permission to access will appear
4. Click the **Login** button

#### Login Screen Layout:

```
+--------------------------------------+
|              iBLIS Logo              |
|                                      |
|  Username: [                    ]    |
|                                      |
|  Password: [                    ]    |
|                                      |
|  Laboratory Section: [Dropdown ▼]    |
|                                      |
|  [       Login       ] [  Help  ]    |
|                                      |
|  Forgot Password? Click here         |
+--------------------------------------+
```

### 1.3 Password Management

#### Forgot Password

If you've forgotten your password:

1. Click the "Forgot Password?" link on the login screen
2. Enter your username or email address
3. Click "Submit"
4. Check your email for password reset instructions
5. Follow the link in the email to create a new password
6. Return to the login page and log in with your new password

#### Changing Your Password

To change your password after logging in:

1. Click on your username in the top-right corner of the screen
2. Select "User Profile" from the dropdown menu
3. Click on the "Change Password" tab
4. Enter your current password
5. Enter and confirm your new password
6. Click "Save" to update your password

Password requirements:
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character

### 1.4 Session Management

iBLIS manages user sessions for security:

- **Session Timeout**: After 30 minutes of inactivity, your session will expire
- **Automatic Logout**: You'll be prompted to re-authenticate when your session expires
- **Manual Logout**: To log out manually, click your username in the top-right corner and select "Logout"
- **Concurrent Sessions**: The system may limit the number of simultaneous logins with the same account

### 1.5 Multi-Factor Authentication (if enabled)

If multi-factor authentication (MFA) is enabled:

1. After entering your username and password
2. You'll be prompted for an additional verification method:
   - One-time code sent via SMS
   - Authentication app (e.g., Google Authenticator)
   - Security key
3. Provide the requested verification
4. Upon successful verification, you'll be logged in

## 2. Navigation Basics

### 2.1 User Interface Layout

After successful login, you'll see the main interface with these components:

```
+------------------------------------------------------+
| Logo   | Main Menu Navigation              | User ▼  |
+------------------------------------------------------+
| Breadcrumbs                              | Search    |
+------------------------------------------------------+
|                                                      |
|                                                      |
|                  Main Content Area                   |
|                                                      |
|                                                      |
+------------------------------------------------------+
|                     Footer                           |
+------------------------------------------------------+
```

1. **Header Bar**:
   - iBLIS logo on the left
   - Main menu navigation in the center
   - User profile dropdown on the right

2. **Breadcrumb Navigation**:
   - Shows your current location in the system
   - Allows easy navigation back to previous screens

3. **Main Content Area**:
   - Displays the active module's interface
   - Changes based on the selected function

4. **Footer**:
   - Version information
   - Copyright notices
   - Quick links to support resources

### 2.2 Using the Dashboard

After login, the dashboard is your starting point:

1. **Key Statistics**: View essential metrics at a glance
2. **Recent Activities**: See recently accessed patients and tests
3. **Alerts & Notifications**: Important system messages
4. **Quick Access**: Links to common functions based on your role

### 2.3 Help and Support

To access help at any time:

1. Click the **Help** icon in the top menu
2. Context-sensitive help will appear for your current screen
3. Use the search function to find specific help topics
4. Access video tutorials for complex procedures

## 3. System Requirements

### 3.1 Supported Browsers

iBLIS works optimally with:
- Chrome 70+
- Firefox 60+
- Edge 80+
- Safari 12+

### 3.2 Display Settings

Recommended settings:
- Minimum screen resolution: 1280 x 768
- Scale: 100%
- Color depth: 16-bit or higher

### 3.3 Network Requirements

- Stable internet connection
- Minimum bandwidth: 1 Mbps
- Recommended: 5+ Mbps for optimal performance

## 4. Troubleshooting Login Issues

### 4.1 Common Problems and Solutions

| Problem | Possible Causes | Solutions |
|---------|----------------|-----------|
| Incorrect username/password | Typo, caps lock, forgotten credentials | Verify username, check caps lock, use password reset |
| Account locked | Too many failed login attempts | Wait for timeout or contact administrator |
| Can't access laboratory section | Permission issue | Contact administrator to verify section access |
| Session timeout | Inactivity period exceeded | Log in again |
| Browser compatibility | Outdated browser | Update browser or use supported browser |

### 4.2 Getting Support

If you encounter persistent login issues:

1. Contact your local system administrator
2. Provide specific error messages
3. Note the time when the issue occurred
4. Describe steps that led to the problem
5. Specify your browser and device

Support contact: [Your organization's support email/phone]
