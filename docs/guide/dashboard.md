# System Overview & Dashboard

## 1. System Architecture

iBLIS (Integrated Biomedical Laboratory Information System) is a comprehensive laboratory management system designed to streamline all aspects of laboratory operations. The system architecture is designed for efficiency, reliability, and ease of use.

```mermaid
graph TD
    A[User Interface Layer] --> B[Application Layer]
    B --> C[Database Layer]

    subgraph "User Interface"
    A1[Web Interface] --> A
    A2[Reports Interface] --> A
    end

    subgraph "Application Modules"
    B1[Patient Management] --> B
    B2[Test Processing] --> B
    B3[Inventory Management] --> B
    B4[Quality Control] --> B
    B5[Reporting] --> B
    B6[System Admin] --> B
    B7[Machine Integration] --> B
    end

    subgraph "Data Storage"
    C1[Patient Data] --> C
    C2[Test Results] --> C
    C3[Inventory Data] --> C
    C4[System Logs] --> C
    end

    D[External Systems] <--> B
```

## 2. Workflow Overview

The typical laboratory workflow in iBLIS follows these key stages:

```mermaid
flowchart LR
    A[Patient Registration] --> B[Test Ordering]
    B --> C[Sample Collection]
    C --> D[Sample Processing]
    D --> E[Result Entry/Import]
    E --> F[Verification]
    F --> G[Reporting]

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#bbf,stroke:#333,stroke-width:2px
```

## 3. Dashboard & Main Menu

### 3.1 Dashboard Overview

The dashboard serves as your command center, providing immediate access to key information and system functions.

**Dashboard Layout:**

```
+------------------------------------------------------+
| Laboratory: [Dropdown] ▼   User: [Name] ▼  [Logout]  |
+------------------------------------------------------+
|                                                      |
| [Navigation Menu]        [System Statistics]         |
|                                                      |
| • Patients               • Tests Pending: XX         |
| • Orders & Tests         • Tests Completed: XX       |
| • Reports                • Patients Today: XX        |
| • Stock                  • Critical Results: XX      |
| • Administration         • Stock Alerts: XX          |
| • System Config          • Quality Control: XX       |
|                                                      |
+------------------------------------------------------+
|                                                      |
| [Recent Activity]        [System Alerts]             |
|                                                      |
+------------------------------------------------------+
```

The dashboard is divided into several key sections:

1. **Top Navigation Bar**
   - Laboratory Section Selector: Switch between departments
   - User Profile: Access your profile, preferences, and logout
   - Date & Time: Current system date and time

2. **Left Navigation Panel**
   - Provides access to all main system modules
   - Icons and text labels for easy identification
   - Visual indicators for alerts or pending actions

3. **Statistics Dashboard**
   - Real-time metrics showing current laboratory status
   - Color-coded indicators for critical values or alerts
   - Filterable by date range and department

4. **Recent Activity**
   - Latest patients registered
   - Recent tests ordered or completed
   - Recent system activities related to your role

5. **System Alerts**
   - Critical results requiring attention
   - Stock items below minimum level
   - Quality control failures
   - System maintenance notifications

### 3.2 Navigating the System

**Main Menu Options:**

| Menu Item | Description | Access Level |
|-----------|-------------|------------|
| Patients | Patient registration, search, and management | All Users |
| Orders & Tests | Create orders, process specimens, enter results | Laboratory Staff |
| Reports | Generate and view various reports | All Users (filtered by role) |
| Stock | Inventory management and tracking | Stock Managers |
| Administration | User management, system settings | Supervisors, Administrators |
| System Config | Technical configuration, integration setup | System Administrators |

To navigate through the system:

1. Click on the desired module in the left navigation panel
2. Use breadcrumbs at the top of each page to track your location
3. Use the back button or navigation links to move between related areas
4. Click on dashboard statistics to jump directly to relevant sections

### 3.3 Customizing Your Dashboard

You can personalize your dashboard view:

1. Click on the **Settings** icon in the top-right corner
2. Select **Dashboard Preferences**
3. Choose which statistics and modules to display
4. Arrange the layout according to your workflow
5. Set default filters for your routine tasks
6. Click **Save** to apply your preferences

### 3.4 System Notifications

The notification system keeps you informed about important events:

1. **Alert Types**
   - Critical results requiring immediate attention (red)
   - Stock alerts for items below minimum level (yellow)
   - System maintenance notifications (blue)
   - General information messages (green)

2. **Handling Notifications**
   - Click on a notification to view details
   - Mark as read or snooze for later
   - Take action directly from the notification panel
   - Set notification preferences in your user profile

## 4. User Interface Elements

### 4.1 Common Controls

Throughout the system, you'll encounter these common interface elements:

- **Action Buttons**: Colored buttons for primary actions (e.g., Save, Cancel)
- **Data Tables**: Sortable and filterable tables for viewing records
- **Search Fields**: Type-ahead search functionality across the system
- **Dropdown Menus**: Selection lists for predefined options
- **Date Pickers**: Calendar controls for date selection
- **Tabs**: Categorized content within a single page
- **Breadcrumbs**: Navigation path showing your current location

### 4.2 Keyboard Shortcuts

For efficient navigation, the system supports these keyboard shortcuts:

| Shortcut | Action |
|----------|--------|
| Alt+P | Go to Patients module |
| Alt+T | Go to Tests module |
| Alt+R | Go to Reports module |
| Alt+S | Go to Stock module |
| Alt+H | Show Help |
| Alt+D | Return to Dashboard |
| Ctrl+F | Focus on Search field |
| Esc | Cancel current operation |
