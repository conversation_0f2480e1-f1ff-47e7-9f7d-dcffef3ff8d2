# Machine Integration

## 1. Overview

The Machine Integration module enables seamless communication between iBLIS and various laboratory instruments and equipment. This integration allows for automatic test result transfer, reducing manual entry errors and improving efficiency.

```mermaid
graph TD
    A[Laboratory Instrument] -->|Data Transfer| B[Machine Integration Module]
    B -->|Process Results| C[iBLIS Database]
    C -->|Update| D[Test Results]
    B -->|Error Handling| E[Error Logs]
    B -->|Audit Trail| F[Integration History]
    G[Manual Override] -->|Correct/Verify| B
```

## 2. Supported Instruments

iBLIS supports integration with a wide range of laboratory instruments, including:

| Instrument Type | Supported Models | Connection Method |
|----------------|------------------|-------------------|
| Hematology Analyzers | Mindray BC-5000, Sysmex XN-1000 | Serial, Network |
| Chemistry Analyzers | Cobas c311, Roche Integra 400 | Network, File Import |
| Immunoassay | Abbott i1000, Roche e411 | Network |
| CD4 Machines | BD FACSCount, Alere PIMA | Serial, USB |
| Viral Load | Abbott m2000, Roche CAP/CTM | Network, File Import |
| GeneXpert | GeneXpert IV, GeneXpert Infinity | Network |

## 3. Setting Up Machine Integration

### 3.1 Configuration Steps

To configure a new instrument connection:

1. Navigate to **Admin** > **System Configuration** > **Machine Integration**
2. Click on **Add New Machine**
3. Enter the following information:
   - Machine Name
   - Manufacturer
   - Model
   - Serial Number
   - Connection Type (Serial, Network, File Import)
   - IP Address/Port (for network connections)
   - COM Port (for serial connections)
   - File Path (for file import)
4. Configure the data mapping:
   - Match instrument test codes to iBLIS test codes
   - Define units of measurement
   - Set reference ranges
5. Click **Test Connection** to verify communication
6. Click **Save** to complete the setup

### 3.2 Connection Diagram

```mermaid
flowchart LR
    subgraph Laboratory
        A[Lab Instrument] -->|Serial/USB| B[Interface Computer]
        C[Lab Instrument] -->|Network| D[Laboratory Network]
    end

    B -->|Data Transfer| E[iBLIS Server]
    D -->|Data Transfer| E

    subgraph iBLIS System
        E -->|Process| F[Integration Engine]
        F -->|Store| G[Database]
        F -->|Alert| H[User Interface]
    end
```

## 4. Processing Results from Instruments

### 4.1 Automatic Result Import

When properly configured, results are automatically imported from connected instruments:

1. The instrument performs tests and generates results
2. Results are sent to iBLIS through the configured connection
3. The system matches the results to pending tests using:
   - Sample ID
   - Test codes
   - Patient information
4. Results are flagged for verification in the system

### 4.2 Verification Process

```mermaid
sequenceDiagram
    participant Instrument
    participant iBLIS
    participant LabTech

    Instrument->>iBLIS: Send test results
    iBLIS->>iBLIS: Match with pending tests
    iBLIS->>LabTech: Notify results pending verification
    LabTech->>iBLIS: Review results (normal/abnormal flags)
    alt Results acceptable
        LabTech->>iBLIS: Verify and approve
        iBLIS->>iBLIS: Update test status to 'Verified'
    else Results questionable
        LabTech->>Instrument: Request repeat test
        Instrument->>iBLIS: Send new results
        LabTech->>iBLIS: Compare and verify
    end
```

To verify instrument results:

1. Navigate to the **Test Results** module
2. Filter for tests with status "Pending Verification"
3. Review results, paying attention to:
   - Results outside reference ranges (flagged in red)
   - Instrument flags for analytical errors
4. Either:
   - Verify the result as correct
   - Request a repeat test
   - Override with manual result if necessary
5. Add comments if needed
6. Click **Verify** to complete the process

## 5. Troubleshooting Integration Issues

### 5.1 Common Issues and Solutions

| Issue | Possible Causes | Solutions |
|-------|----------------|-----------|
| Connection Failure | Network issues, instrument offline | Check physical connections, verify instrument is online, check IP/port settings |
| Result Mismatch | Mapping configuration incorrect | Review and update test code mappings |
| Incomplete Data | Instrument error, connection interrupted | Check instrument error logs, verify complete data transmission |
| Duplicate Results | Multiple transmissions, system timing issue | Check integration settings, verify result handling rules |

### 5.2 Integration Logs

To access integration logs for troubleshooting:

1. Navigate to **Admin** > **System Logs** > **Integration Logs**
2. Filter logs by:
   - Date range
   - Instrument
   - Status (Error, Warning, Success)
3. Review error details and take appropriate action
4. Contact system administrator for persistent issues

## 6. Maintenance and Best Practices

### 6.1 Regular Maintenance

- Perform monthly checks on all integrations
- Verify that test mappings are still correct after system updates
- Backup integration configurations before major system changes
- Test connections after instrument maintenance or calibration

### 6.2 Quality Assurance

- Periodically verify manual vs. automated results
- Maintain documentation of all integration settings
- Update reference ranges when instrument calibrations change
- Train staff on both automated and manual result entry procedures

## 7. Advanced Integration Features

### 7.1 Bi-directional Integration

For supported instruments, bi-directional integration allows:

- Sending test orders directly to instruments
- Receiving results automatically
- Querying instrument status
- Managing work lists

To configure bi-directional integration:

1. Navigate to **Admin** > **System Configuration** > **Bi-directional Setup**
2. Enable the option for supported instruments
3. Configure additional settings specific to order transmission
4. Test by sending a sample order to the instrument
