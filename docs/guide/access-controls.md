# User Management & Access Controls

## 1. User Management Overview

The Access Control module provides a comprehensive framework for managing users, roles, and permissions within iBLIS. This ensures that users have appropriate access based on their responsibilities while maintaining system security.

```mermaid
graph TD
    A[Access Control System] --> B[User Management]
    A --> C[Role Management]
    A --> D[Permission Management]

    B --> B1[Create Users]
    B --> B2[Edit Users]
    B --> B3[Disable Users]
    B --> B4[Reset Passwords]

    C --> C1[Create Roles]
    C --> C2[Edit Roles]
    C --> C3[Assign Permissions to Roles]

    D --> D1[System Permissions]
    D --> D2[Module-specific Permissions]
    D --> D3[Permission Groups]

    style A fill:#f9f,stroke:#333,stroke-width:2px
```

## 2. User Accounts Management

### 2.1 Accessing User Management

To access user management functions:

1. Click on **Administration** in the main menu
2. Select **Access Controls** from the dropdown
3. Click on **User Accounts**
4. The system displays a table with all users in the system

### 2.2 Creating New Users

To add a new user to the system:

```mermaid
sequenceDiagram
    participant Admin
    participant System

    Admin->>System: Navigate to User Accounts
    Admin->>System: Click "Add User"
    System->>Admin: Display User Creation Form
    Admin->>System: Enter User Details
    Admin->>System: Select Roles
    Admin->>System: Assign Lab Sections
    Admin->>System: Submit Form
    System->>System: Validate Information
    alt Information Valid
        System->>System: Create User Account
        System->>Admin: Display Success Message
    else Information Invalid
        System->>Admin: Display Validation Errors
        Admin->>System: Correct Information
        Admin->>System: Resubmit Form
    end
```

1. Click on the blue **Add User** button
2. Complete the user information form with:
   - **Username**: Unique identifier for login (no spaces)
   - **First name**: User's first name
   - **Middle name**: User's middle name (optional)
   - **Last name**: User's last name
   - **Password**: Strong password following security guidelines
   - **Confirm password**: Repeat the password for verification
   - **Email**: Valid email address for notifications
   - **Phone**: Contact number (optional)
   - **Sex**: Select from dropdown
   - **Roles**: Assign one or more roles from the list
   - **Lab section**: Assign laboratory departments the user can access
   - **Date of birth**: User's date of birth
3. Click **Save** to create the user account

Password Requirements:
- Minimum 8 characters
- Combination of uppercase and lowercase letters
- At least one number
- At least one special character

### 2.3 Editing User Accounts

To modify an existing user account:

1. Locate the user in the user list
2. Click on the **Edit** button next to the user
3. The user edit form appears with current information
4. Update any fields that need to be changed:
   - Personal information
   - Contact details
   - Role assignments
   - Laboratory section access
5. Click **Save** to apply the changes

Note: Username cannot be changed after account creation.

### 2.4 Disabling Users

Rather than deleting users (which would break data integrity), iBLIS uses a disable function:

1. Find the user in the user list
2. Click the **Disable** button next to the user
3. Confirm the action when prompted
4. The user account becomes inactive and cannot log in

To reactivate a disabled user:
1. Check the "Show disabled accounts" checkbox
2. Find the disabled user in the list
3. Click the **Enable** button next to the user

### 2.5 Password Management

Administrators can reset passwords for users:

1. Find the user in the user list
2. Click the **Reset Password** button
3. Either:
   - Set a temporary password that the user must change
   - Generate a password reset link sent to the user's email
4. Inform the user through appropriate channels

Users can reset their own passwords:
1. Click "Forgot Password" on the login screen
2. Enter username or email
3. Follow instructions in the reset email

## 3. Role Management

Roles group permissions for efficient user management. Instead of assigning individual permissions to each user, permissions are assigned to roles, and roles are assigned to users.

### 3.1 Accessing Role Management

To manage roles:

1. Click on **Administration** > **Access Controls** > **Roles**
2. The system displays all available roles

### 3.2 Standard System Roles

iBLIS comes with pre-configured roles:

| Role | Description | Example Permissions |
|------|-------------|---------------------|
| Administrator | Full system access | All system functions |
| Lab Manager | Oversees laboratory operations | Report access, user management, configurations |
| Lab Technician | Performs testing | Test processing, result entry |
| Receptionist | Handles patient registration | Patient registration, test ordering |
| Phlebotomist | Collects specimens | Sample collection, specimen tracking |
| Quality Officer | Manages quality control | QC review, performance monitoring |
| Reports Viewer | Access to reports only | View reports, export data |
| Inventory Manager | Manages stock | Stock management functions |

### 3.3 Creating Custom Roles

To create a new role:

1. Click the green **Create Role** button
2. Enter role details:
   - **Name**: Descriptive name for the role
   - **Description**: Explanation of the role's purpose
3. Assign permissions by checking appropriate boxes
4. Click **Save** to create the role

### 3.4 Editing Roles

To modify an existing role:

1. Find the role in the roles list
2. Click the **Edit** button next to the role
3. Update the role name or description
4. Modify permission assignments
5. Click **Save** to apply changes

Note: Changes to a role affect all users with that role assigned.

### 3.5 Deleting Roles

To remove a role:

1. Ensure no users are currently assigned the role
2. Click the **Delete** button next to the role
3. Confirm the deletion when prompted

## 4. Permission Management

Permissions define specific actions users can perform in the system.

### 4.1 Permission Categories

Permissions are organized into functional categories:

```mermaid
mindmap
  root((Permissions))
    Patient Management
      Create Patients
      Edit Patients
      View Patients
      Merge Patients
    Test Management
      Order Tests
      Process Specimens
      Enter Results
      Verify Results
    Reports
      View Reports
      Generate Reports
      Export Reports
    System Configuration
      Manage Users
      Configure Tests
      Manage Settings
    Stock Management
      Add Stock
      Update Inventory
      Approve Requisitions
```

### 4.2 Assigning Permissions to Roles

To set permissions for a role:

1. Navigate to **Administration** > **Access Controls** > **Permissions**
2. The system displays a matrix with:
   - Permissions listed in rows
   - Roles listed in columns
3. Check boxes at intersections to grant permissions:
   - When checked, users with that role can perform the action
   - When unchecked, users with that role cannot perform the action
4. Click **Save** to apply the changes

### 4.3 Permission Inheritance

Some permissions automatically include others:

1. **Full Access** permissions include all sub-permissions
2. **Edit** permissions typically include **View** permissions
3. **Verify** permissions include **Enter** permissions

### 4.4 Permission Conflicts

When a user has multiple roles:

1. Permissions are additive (union of all role permissions)
2. If any assigned role grants a permission, the user has that permission
3. To restrict access, ensure none of the user's roles include the permission

## 5. Laboratory Section Access

Users can be restricted to specific laboratory departments or sections:

### 5.1 Configuring Section Access

To manage a user's laboratory section access:

1. Edit the user account
2. In the **Lab Sections** area:
   - Check boxes for sections the user can access
   - Leave unchecked for restricted sections
3. If "All Sections" is checked, the user can access all departments
4. Click **Save** to apply changes

### 5.2 Section-Based Visibility

Section restrictions affect:

1. **Test visibility**: Only tests from accessible sections appear
2. **Patient data**: Patients with tests in accessible sections
3. **Reports**: Data filtered to accessible sections
4. **Stock items**: Inventory related to accessible sections

## 6. Audit Trail and Security

### 6.1 Access Control Audit Trail

The system logs all access control changes:

1. Navigate to **Administration** > **System Logs** > **Access Control Logs**
2. View a complete history of:
   - User account creations
   - Role modifications
   - Permission changes
   - Password resets
   - Login attempts (successful and failed)

### 6.2 Security Best Practices

For optimal security:

1. **Principle of Least Privilege**: Assign only necessary permissions
2. **Regular Review**: Audit user accounts and permissions quarterly
3. **Password Policies**:
   - Enforce strong passwords
   - Set password expiration
   - Limit failed login attempts
4. **Account Management**:
   - Disable accounts promptly when staff leave
   - Regularly review active accounts
   - Audit login patterns for suspicious activity
5. **Role Standardization**:
   - Create standard roles for common job functions
   - Limit custom roles to special cases
   - Document role purposes and permissions
