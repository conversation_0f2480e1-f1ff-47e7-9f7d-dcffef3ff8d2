# System Configuration

## 1. Configuration Overview

The System Configuration module allows administrators to customize iBLIS to meet specific laboratory requirements. This module provides tools for setting up system components, integrating with external systems, and maintaining reference data.

```mermaid
mindmap
  root((System Configuration))
    Laboratory Setup
      Facilities
      Departments
      Wards/Locations
    Test Management
      Test Catalog
      Specimen Types
      Test Methods
      Reference Ranges
    Instruments
      Equipment Setup
      Interfaces
      Drivers
    User Management
      Roles & Permissions
      User Accounts
      Access Control
    System Integration
      External Systems
      Data Exchange
      API Configuration
    Surveillance
      Disease Registry
      Notifiable Conditions
      Outbreak Monitoring
```

## 2. Accessing System Configuration

System configuration options are restricted to users with administrative privileges:

1. Log in with an administrator account
2. Click on **Administration** in the main menu
3. Select **System Configuration** from the dropdown
4. The configuration dashboard appears with categorized settings

## 3. Laboratory Setup

### 3.1 Facility Management

The Facilities section allows you to configure information about your laboratory and related healthcare facilities:

```mermaid
graph TD
    A[Main Laboratory] --> B[Department 1]
    A --> C[Department 2]
    A --> D[Department 3]
    A -.-> E[Referring Facility 1]
    A -.-> F[Referring Facility 2]

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style E fill:#ddf,stroke:#333,stroke-width:1px
    style F fill:#ddf,stroke:#333,stroke-width:1px
```

To manage facilities:

1. Navigate to **System Configuration** > **Facilities**
2. View the list of existing facilities
3. To add a new facility:
   - Click **Add Facility**
   - Enter facility details:
     - Name
     - Code
     - Type (Laboratory, Hospital, Clinic, etc.)
     - Address
     - Contact information
   - Specify relationship (Parent/Child/Referral)
   - Click **Save**
4. To edit a facility:
   - Click the **Edit** button next to the facility
   - Update the information
   - Click **Save**
5. To delete a facility:
   - Click the **Delete** button next to the facility
   - Confirm deletion
   - Note: Deletion is only possible if no records reference the facility

### 3.2 Department Configuration

Departments represent specialized sections within the laboratory:

To manage departments:

1. Go to **System Configuration** > **Departments**
2. To add a new department:
   - Click **Add Department**
   - Enter department details:
     - Name
     - Code
     - Parent facility
     - Description
   - Assign available tests
   - Click **Save**
3. To edit or delete a department:
   - Use the respective **Edit** or **Delete** buttons
   - Update information or confirm deletion

### 3.3 Wards and Locations

Wards and locations represent areas within healthcare facilities from which samples may be collected:

To manage wards:

1. Navigate to **System Configuration** > **Wards/Locations**
2. To create a new ward:
   - Click **Create Ward**
   - Enter ward details:
     - Name
     - Code
     - Facility
     - Type (Inpatient, Outpatient, Emergency, etc.)
   - Click **Save**
3. To edit or delete:
   - Use the **Edit** or **Delete** buttons next to the ward name

## 4. Test Catalog Configuration

### 4.1 Test Catalog Management

The Test Catalog is the foundation of laboratory operations, defining all available tests:

To manage the test catalog:

1. Go to **System Configuration** > **Test Catalog**
2. The system displays all configured tests
3. To add a new test:
   - Click **Add Test**
   - Enter test details:
     - Name
     - Code
     - Department
     - Category
     - Methodology
     - Turnaround time
     - Price (if applicable)
   - Click **Save**
4. To edit a test:
   - Click **Edit** next to the test name
   - Update information
   - Click **Save**

### 4.2 Specimen Types

To configure specimen types:

1. Go to **System Configuration** > **Specimen Types**
2. View the list of available specimen types
3. To add a new specimen type:
   - Click **Add Specimen Type**
   - Enter details:
     - Name
     - Description
     - Collection instructions
     - Preservation requirements
     - Minimum volume
     - Container type
   - Click **Save**

### 4.3 Reference Ranges

Reference ranges define normal test result parameters:

```mermaid
graph TD
    A[Reference Range Configuration] --> B[Select Test]
    B --> C[Define Range Type]
    C -->|Age-based| D[Enter Age Ranges]
    C -->|Gender-based| E[Enter Gender Ranges]
    C -->|Pregnancy-based| F[Enter Pregnancy Ranges]
    D --> G[Enter Values]
    E --> G
    F --> G
    G --> H[Save Reference Range]
```

To configure reference ranges:

1. Go to **System Configuration** > **Reference Ranges**
2. Select the test for which to define ranges
3. Click **Add Range**
4. Specify range parameters:
   - Age range (min-max)
   - Gender (male/female/both)
   - Pregnancy status (if applicable)
   - Lower and upper limits
   - Units
   - Critical low and high values
5. Click **Save**

## 5. Instrument Configuration

### 5.1 Instrument Management

Configure laboratory instruments and analyzers:

To manage instruments:

1. Navigate to **System Configuration** > **Instruments**
2. View the list of configured instruments
3. To add a new instrument:
   - Click **Add Instrument**
   - Enter instrument details:
     - Name
     - Manufacturer
     - Model
     - Serial number
     - Location
   - Click **Save**

### 5.2 Instrument Interface Setup

For instruments with electronic interfaces:

1. Select the instrument from the list
2. Click **Configure Interface**
3. Specify connection parameters:
   - Connection type (Serial, TCP/IP, File)
   - For Serial: COM port, baud rate, parity
   - For TCP/IP: IP address, port number
   - For File: Directory path, file pattern
4. Click **Test Connection** to verify
5. Click **Save Configuration**

### 5.3 Driver Management

To manage instrument drivers:

1. Navigate to **System Configuration** > **Instrument Drivers**
2. Click **New Driver** to add a driver
3. Upload the driver file or enter configuration details
4. Assign the driver to specific instruments
5. Click **Save**

## 6. Visit Types

Visit types categorize patient encounters:

To manage visit types:

1. Go to **System Configuration** > **Visit Types**
2. View existing visit types
3. To add a new visit type:
   - Click **New Visit Type**
   - Enter name and description
   - Click **Save**
4. To edit or delete:
   - Use the respective buttons next to the visit type

## 7. Surveillance Configuration

The Surveillance module supports public health monitoring:

### 7.1 Disease Registry

To configure the disease registry:

1. Navigate to **System Configuration** > **Surveillance** > **Diseases**
2. View the list of configured diseases
3. To add a new disease:
   - Click **New Disease**
   - Enter disease details:
     - Name
     - Code
     - Description
     - Notifiable status
   - Click **Save**

### 7.2 Surveillance Mapping

To map tests to diseases for surveillance:

1. Go to **System Configuration** > **Surveillance** > **Mapping**
2. Click **Add Surveillance**
3. Select:
   - Disease
   - Test type
   - Result interpretation criteria
   - Notification threshold
4. Click **Save**

## 8. System Integration

### 8.1 External Systems

Configure connections to external healthcare systems:

1. Navigate to **System Configuration** > **External Systems**
2. Click **Add System**
3. Enter system details:
   - Name
   - Type (EMR, HIS, PACS, etc.)
   - Connection parameters
4. Configure authentication details
5. Click **Test Connection**
6. Click **Save**

### 8.2 Data Exchange Configuration

Set up data exchange protocols:

1. Go to **System Configuration** > **Data Exchange**
2. Configure data formats:
   - HL7
   - FHIR
   - ASTM
   - Custom
3. Set up message mappings
4. Define export/import schedules
5. Click **Save**

## 9. System Backup and Maintenance

### 9.1 Backup Configuration

Configure system backup settings:

1. Navigate to **System Configuration** > **Backup**
2. Set backup parameters:
   - Schedule (daily, weekly, etc.)
   - Backup method
   - Storage location
   - Retention period
3. Click **Save Configuration**

### 9.2 System Maintenance

Schedule and configure system maintenance:

1. Go to **System Configuration** > **Maintenance**
2. Set up maintenance tasks:
   - Database optimization
   - Index rebuilding
   - Log rotation
   - Data archiving
3. Define maintenance windows
4. Click **Save**

## 10. Best Practices for System Configuration

### 10.1 Configuration Change Management

When making configuration changes:

1. Document the purpose and details of the change
2. Make changes during off-peak hours when possible
3. Test changes in a non-production environment first
4. Have a rollback plan ready
5. Communicate changes to affected users
6. Verify system functionality after changes

### 10.2 Configuration Audit Trail

The system maintains a complete audit trail of configuration changes:

1. Go to **Administration** > **Audit Logs** > **Configuration Changes**
2. View the history of all configuration changes
3. Filter by:
   - Date range
   - User
   - Configuration area
   - Change type
4. Export logs for compliance reporting

### 10.3 Configuration Export/Import

To transfer configuration between environments:

1. Navigate to **System Configuration** > **Export/Import**
2. To export configuration:
   - Select configuration categories
   - Click **Export**
   - Save the configuration file
3. To import configuration:
   - Click **Import**
   - Select the configuration file
   - Review changes
   - Click **Apply**
