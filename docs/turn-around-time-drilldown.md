# Turn Around Time Report - Drilldown Implementation

## Overview

The Turn Around Time Report has been updated to support drilldown functionality, allowing users to click on specific data points to view detailed test information. This implementation follows the same pattern used in other aggregate reports like infection and department reports.

## Changes Made

### 1. Data Structure Update

**Before:**

```typescript
interface Statistic {
  test_type: string;
  turn_around_time: string;
  average: number;
  total_tests: number; // Direct number
  tests_within_normal_tat: number; // Direct number
  percentage_within_normal_tat: number;
}
```

**After:**

```typescript
interface Statistic {
  test_type: string;
  turn_around_time: string;
  average: number;
  total_tests: { count: number; associated_ids: string }; // Object with drilldown data
  tests_within_normal_tat: { count: number; associated_ids: string }; // Object with drilldown data
  percentage_within_normal_tat: number;
}
```

### 2. Template Updates

**Clickable Columns:**

- **Total Tests**: Now clickable with hover effects
- **Tests Within Normal TAT**: Now clickable with hover effects
- **Percentage**: Remains non-clickable (calculated field)

**Visual Indicators:**

- Hover effects: `hover:font-medium cursor-pointer hover:text-sky-500 hover:underline`
- Smooth transitions: `transition duration-150`
- Pointer cursor to indicate clickability

### 3. Drilldown Functionality

**Function:** `showDrilldown(testType, category, count, associatedIds)`

**Parameters:**

- `testType`: The name of the test (e.g., "Blood Test")
- `category`: The category clicked (e.g., "Total Tests", "Tests Within Normal TAT")
- `count`: Number of tests in this category
- `associatedIds`: Comma-separated list of test IDs for drilldown

**Navigation URL Format:**

```
/reports/{associated_ids}?origin=aggregate&type=turn-around-time-report&from={startDate}&to={endDate}&test={testType} - {category}&department={departmentName}&department_id={departmentId}&unit={unitName}&report_id={reportId}&count={count}
```

**Return URL Format (Auto-loading):**

```
/reports/aggregate/turn-around-time?from={startDate}&to={endDate}&department={departmentName}&department_id={departmentId}&unit={unitName}&report_id={reportId}
```

### 4. Export Data Handling

Updated to handle the new data structure:

```typescript
"TOTAL TESTS": statistic.total_tests?.count || 0,
"TESTS WITHIN NORMAL TAT": statistic.tests_within_normal_tat?.count || 0,
```

## Features

### ✅ **Drilldown Navigation**

- Click on "Total Tests" count to view all tests for that test type
- Click on "Tests Within Normal TAT" count to view tests that met the target TAT
- Automatic navigation to detailed test listing page
- Uses standard breadcrumb navigation for returning to reports

### ✅ **Auto-loading from Drilldown**

- Automatically loads report data when returning from drilldown pages using report_id
- Preserves all filter settings (date range, department, unit)
- No need to re-enter filters or regenerate report
- Uses standard report_id pattern consistent with other aggregate reports

### ✅ **Data Validation**

- Prevents navigation when count is 0
- Prevents navigation when associated_ids is empty
- Shows user-friendly warning messages for invalid selections

### ✅ **Visual Feedback**

- Hover effects on clickable cells
- Cursor changes to pointer on hover
- Color changes and underline effects
- Smooth transitions for better UX

### ✅ **Query Parameter Management**

- Automatically updates URL with current filter settings
- Maintains state across browser refresh
- Enables bookmarking of specific report configurations
- Supports deep linking to specific report states

### ✅ **Backward Compatibility**

- Gracefully handles missing data with fallback to 0
- Safe navigation with optional chaining (`?.`)
- Export functionality works with both old and new data structures

## Usage

### For Users

1. Generate a turn-around-time report as usual
2. Look for clickable numbers in the "Total Tests" and "Tests Within Normal TAT" columns
3. Click on any non-zero count to drill down into detailed test data
4. View individual test records that make up that count
5. Use breadcrumb navigation to return to the aggregate report
6. **Auto-loading**: When returning from drilldown, the report data loads automatically using report_id - no need to re-enter filters!

### For Developers

1. Ensure API returns data in the new format with `count` and `associated_ids`
2. The drilldown page (`/reports/[associated_id].vue`) handles the detailed view
3. Query parameters provide context for filtering and display

## API Requirements

The backend API should return data in this format:

```json
{
  "data": [
    {
      "test_type": "Blood Test",
      "turn_around_time": "2",
      "average": 1.5,
      "total_tests": {
        "count": 150,
        "associated_ids": "test123,test456,test789"
      },
      "tests_within_normal_tat": {
        "count": 120,
        "associated_ids": "test123,test456"
      },
      "percentage_within_normal_tat": 80
    }
  ]
}
```

## Error Handling

### Client-Side Validation

- **Zero Count**: Shows "No data found for this selection"
- **Empty IDs**: Shows "No data found for this selection"
- **Missing Data**: Gracefully falls back to 0 count

### Network Errors

- Existing error handling for report generation remains unchanged
- Drilldown navigation errors are handled by the router and drilldown page

## Testing

### Unit Tests

- Data structure handling
- Drilldown function logic
- Export data transformation
- Edge cases and error conditions

### Integration Tests

- Navigation flow from aggregate to detail view
- Query parameter passing
- Back navigation functionality

### Manual Testing Checklist

- [ ] Generate report with new data structure
- [ ] Click on total tests count (non-zero)
- [ ] Verify navigation to drilldown page
- [ ] Check query parameters are correct
- [ ] Click on tests within normal TAT count
- [ ] Verify different test types work
- [ ] Test with zero counts (should show warning)
- [ ] Test export functionality
- [ ] Verify hover effects work correctly

## Files Modified

1. **`pages/reports/aggregate/turn-around-time.vue`**

   - Updated TypeScript interface
   - Added drilldown function
   - Modified template for clickable cells
   - Updated export data handling

2. **`tests/turn-around-time-drilldown.test.js`** (New)

   - Comprehensive test coverage
   - Edge case testing
   - Integration test scenarios

3. **`docs/turn-around-time-drilldown.md`** (New)
   - Implementation documentation
   - Usage instructions
   - API requirements

## Related Components

- **`pages/reports/[associated_id].vue`**: Handles the drilldown detail view
- **`components/core/Datatable.vue`**: Displays the detailed test data
- **Other aggregate reports**: Follow similar drilldown patterns

## Future Enhancements

1. **Additional Drilldown Points**: Could add drilldown to average TAT calculations
2. **Filtering Options**: Add filters on the drilldown page for more granular analysis
3. **Export from Drilldown**: Allow exporting the detailed test data
4. **Visual Charts**: Add charts to the drilldown view for better data visualization

## Migration Notes

### For Existing Installations

- Backend API must be updated to return the new data structure
- No database schema changes required (assuming IDs are already available)
- Frontend changes are backward compatible with graceful fallbacks

### For New Installations

- Implement the new data structure from the start
- Ensure proper indexing on test IDs for efficient drilldown queries
- Consider caching strategies for large datasets
