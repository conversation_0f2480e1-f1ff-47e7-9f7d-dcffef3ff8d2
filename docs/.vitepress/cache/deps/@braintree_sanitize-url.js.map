{"version": 3, "sources": ["../../../../node_modules/@braintree/sanitize-url/dist/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sanitizeUrl = exports.BLANK_URL = void 0;\nvar invalidProtocolRegex = /^([^\\w]*)(javascript|data|vbscript)/im;\nvar htmlEntitiesRegex = /&#(\\w+)(^\\w|;)?/g;\nvar htmlCtrlEntityRegex = /&(newline|tab);/gi;\nvar ctrlCharactersRegex = /[\\u0000-\\u001F\\u007F-\\u009F\\u2000-\\u200D\\uFEFF]/gim;\nvar urlSchemeRegex = /^.+(:|&colon;)/gim;\nvar relativeFirstCharacters = [\".\", \"/\"];\nexports.BLANK_URL = \"about:blank\";\nfunction isRelativeUrlWithoutProtocol(url) {\n    return relativeFirstCharacters.indexOf(url[0]) > -1;\n}\n// adapted from https://stackoverflow.com/a/29824550/2601552\nfunction decodeHtmlCharacters(str) {\n    var removedNullByte = str.replace(ctrlCharactersRegex, \"\");\n    return removedNullByte.replace(htmlEntitiesRegex, function (match, dec) {\n        return String.fromCharCode(dec);\n    });\n}\nfunction sanitizeUrl(url) {\n    if (!url) {\n        return exports.BLANK_URL;\n    }\n    var sanitizedUrl = decodeHtmlCharacters(url)\n        .replace(htmlCtrlEntityRegex, \"\")\n        .replace(ctrlCharactersRegex, \"\")\n        .trim();\n    if (!sanitizedUrl) {\n        return exports.BLANK_URL;\n    }\n    if (isRelativeUrlWithoutProtocol(sanitizedUrl)) {\n        return sanitizedUrl;\n    }\n    var urlSchemeParseResults = sanitizedUrl.match(urlSchemeRegex);\n    if (!urlSchemeParseResults) {\n        return sanitizedUrl;\n    }\n    var urlScheme = urlSchemeParseResults[0];\n    if (invalidProtocolRegex.test(urlScheme)) {\n        return exports.BLANK_URL;\n    }\n    return sanitizedUrl;\n}\nexports.sanitizeUrl = sanitizeUrl;\n"], "mappings": ";;;;;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,cAAc,QAAQ,YAAY;AAC1C,QAAI,uBAAuB;AAC3B,QAAI,oBAAoB;AACxB,QAAI,sBAAsB;AAC1B,QAAI,sBAAsB;AAC1B,QAAI,iBAAiB;AACrB,QAAI,0BAA0B,CAAC,KAAK,GAAG;AACvC,YAAQ,YAAY;AACpB,aAAS,6BAA6B,KAAK;AACvC,aAAO,wBAAwB,QAAQ,IAAI,CAAC,CAAC,IAAI;AAAA,IACrD;AAEA,aAAS,qBAAqB,KAAK;AAC/B,UAAI,kBAAkB,IAAI,QAAQ,qBAAqB,EAAE;AACzD,aAAO,gBAAgB,QAAQ,mBAAmB,SAAU,OAAO,KAAK;AACpE,eAAO,OAAO,aAAa,GAAG;AAAA,MAClC,CAAC;AAAA,IACL;AACA,aAAS,YAAY,KAAK;AACtB,UAAI,CAAC,KAAK;AACN,eAAO,QAAQ;AAAA,MACnB;AACA,UAAI,eAAe,qBAAqB,GAAG,EACtC,QAAQ,qBAAqB,EAAE,EAC/B,QAAQ,qBAAqB,EAAE,EAC/B,KAAK;AACV,UAAI,CAAC,cAAc;AACf,eAAO,QAAQ;AAAA,MACnB;AACA,UAAI,6BAA6B,YAAY,GAAG;AAC5C,eAAO;AAAA,MACX;AACA,UAAI,wBAAwB,aAAa,MAAM,cAAc;AAC7D,UAAI,CAAC,uBAAuB;AACxB,eAAO;AAAA,MACX;AACA,UAAI,YAAY,sBAAsB,CAAC;AACvC,UAAI,qBAAqB,KAAK,SAAS,GAAG;AACtC,eAAO,QAAQ;AAAA,MACnB;AACA,aAAO;AAAA,IACX;AACA,YAAQ,cAAc;AAAA;AAAA;", "names": []}