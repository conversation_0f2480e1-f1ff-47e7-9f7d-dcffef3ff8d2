{"version": 3, "sources": ["../../../../node_modules/mark.js/src/lib/domiterator.js", "../../../../node_modules/mark.js/src/lib/mark.js", "../../../../node_modules/mark.js/src/vanilla.js"], "sourcesContent": ["/**\n * A NodeIterator with iframes support and a method to check if an element is\n * matching a specified selector\n * @example\n * const iterator = new DOMIterator(\n *     document.querySelector(\"#context\"), true\n * );\n * iterator.forEachNode(NodeFilter.SHOW_TEXT, node => {\n *     console.log(node);\n * }, node => {\n *     if(DOMIterator.matches(node.parentNode, \".ignore\")){\n *         return NodeFilter.FILTER_REJECT;\n *     } else {\n *         return NodeFilter.FILTER_ACCEPT;\n *     }\n * }, () => {\n *     console.log(\"DONE\");\n * });\n * @todo Outsource into separate repository\n */\nexport default class DOMIterator {\n\n  /**\n   * @param {HTMLElement|HTMLElement[]|NodeList|string} ctx - The context DOM\n   * element, an array of DOM elements, a NodeList or a selector\n   * @param {boolean} [iframes=true] - A boolean indicating if iframes should\n   * be handled\n   * @param {string[]} [exclude=[]] - An array containing exclusion selectors\n   * for iframes\n   * @param {number} [iframesTimeout=5000] - A number indicating the ms to\n   * wait before an iframe should be skipped, in case the load event isn't\n   * fired. This also applies if the user is offline and the resource of the\n   * iframe is online (either by the browsers \"offline\" mode or because\n   * there's no internet connection)\n   */\n  constructor(ctx, iframes = true, exclude = [], iframesTimeout = 5000) {\n    /**\n     * The context of the instance. Either a DOM element, an array of DOM\n     * elements, a NodeList or a selector\n     * @type {HTMLElement|HTMLElement[]|NodeList|string}\n     * @access protected\n     */\n    this.ctx = ctx;\n    /**\n     * Boolean indicating if iframe support is enabled\n     * @type {boolean}\n     * @access protected\n     */\n    this.iframes = iframes;\n    /**\n     * An array containing exclusion selectors for iframes\n     * @type {string[]}\n     */\n    this.exclude = exclude;\n    /**\n     * The maximum ms to wait for a load event before skipping an iframe\n     * @type {number}\n     */\n    this.iframesTimeout = iframesTimeout;\n  }\n\n  /**\n   * Checks if the specified DOM element matches the selector\n   * @param  {HTMLElement} element - The DOM element\n   * @param  {string|string[]} selector - The selector or an array with\n   * selectors\n   * @return {boolean}\n   * @access public\n   */\n  static matches(element, selector) {\n    const selectors = typeof selector === 'string' ? [selector] : selector,\n      fn = (\n        element.matches ||\n        element.matchesSelector ||\n        element.msMatchesSelector ||\n        element.mozMatchesSelector ||\n        element.oMatchesSelector ||\n        element.webkitMatchesSelector\n      );\n    if (fn) {\n      let match = false;\n      selectors.every(sel => {\n        if (fn.call(element, sel)) {\n          match = true;\n          return false;\n        }\n        return true;\n      });\n      return match;\n    } else { // may be false e.g. when el is a textNode\n      return false;\n    }\n  }\n\n  /**\n   * Returns all contexts filtered by duplicates (even nested)\n   * @return {HTMLElement[]} - An array containing DOM contexts\n   * @access protected\n   */\n  getContexts() {\n    let ctx,\n      filteredCtx = [];\n    if (typeof this.ctx === 'undefined' || !this.ctx) { // e.g. null\n      ctx = [];\n    } else if (NodeList.prototype.isPrototypeOf(this.ctx)) {\n      ctx = Array.prototype.slice.call(this.ctx);\n    } else if (Array.isArray(this.ctx)) {\n      ctx = this.ctx;\n    } else if (typeof this.ctx === 'string') {\n      ctx = Array.prototype.slice.call(\n        document.querySelectorAll(this.ctx)\n      );\n    } else { // e.g. HTMLElement or element inside iframe\n      ctx = [this.ctx];\n    }\n    // filter duplicate text nodes\n    ctx.forEach(ctx => {\n      const isDescendant = filteredCtx.filter(contexts => {\n        return contexts.contains(ctx);\n      }).length > 0;\n      if (filteredCtx.indexOf(ctx) === -1 && !isDescendant) {\n        filteredCtx.push(ctx);\n      }\n    });\n    return filteredCtx;\n  }\n\n  /**\n   * @callback DOMIterator~getIframeContentsSuccessCallback\n   * @param {HTMLDocument} contents - The contentDocument of the iframe\n   */\n  /**\n   * Calls the success callback function with the iframe document. If it can't\n   * be accessed it calls the error callback function\n   * @param {HTMLElement} ifr - The iframe DOM element\n   * @param {DOMIterator~getIframeContentsSuccessCallback} successFn\n   * @param {function} [errorFn]\n   * @access protected\n   */\n  getIframeContents(ifr, successFn, errorFn = () => {}) {\n    let doc;\n    try {\n      const ifrWin = ifr.contentWindow;\n      doc = ifrWin.document;\n      if (!ifrWin || !doc) { // no permission = null. Undefined in Phantom\n        throw new Error('iframe inaccessible');\n      }\n    } catch (e) {\n      errorFn();\n    }\n    if (doc) {\n      successFn(doc);\n    }\n  }\n\n  /**\n   * Checks if an iframe is empty (if about:blank is the shown page)\n   * @param {HTMLElement} ifr - The iframe DOM element\n   * @return {boolean}\n   * @access protected\n   */\n  isIframeBlank(ifr) {\n    const bl = 'about:blank',\n      src = ifr.getAttribute('src').trim(),\n      href = ifr.contentWindow.location.href;\n    return href === bl && src !== bl && src;\n  }\n\n  /**\n   * Observes the onload event of an iframe and calls the success callback or\n   * the error callback if the iframe is inaccessible. If the event isn't\n   * fired within the specified {@link DOMIterator#iframesTimeout}, then it'll\n   * call the error callback too\n   * @param {HTMLElement} ifr - The iframe DOM element\n   * @param {DOMIterator~getIframeContentsSuccessCallback} successFn\n   * @param {function} errorFn\n   * @access protected\n   */\n  observeIframeLoad(ifr, successFn, errorFn) {\n    let called = false,\n      tout = null;\n    const listener = () => {\n      if (called) {\n        return;\n      }\n      called = true;\n      clearTimeout(tout);\n      try {\n        if (!this.isIframeBlank(ifr)) {\n          ifr.removeEventListener('load', listener);\n          this.getIframeContents(ifr, successFn, errorFn);\n        }\n      } catch (e) { // isIframeBlank maybe throws throws an error\n        errorFn();\n      }\n    };\n    ifr.addEventListener('load', listener);\n    tout = setTimeout(listener, this.iframesTimeout);\n  }\n\n  /**\n   * Callback when the iframe is ready\n   * @callback DOMIterator~onIframeReadySuccessCallback\n   * @param {HTMLDocument} contents - The contentDocument of the iframe\n   */\n  /**\n   * Callback if the iframe can't be accessed\n   * @callback DOMIterator~onIframeReadyErrorCallback\n   */\n  /**\n   * Calls the callback if the specified iframe is ready for DOM access\n   * @param  {HTMLElement} ifr - The iframe DOM element\n   * @param  {DOMIterator~onIframeReadySuccessCallback} successFn - Success\n   * callback\n   * @param {DOMIterator~onIframeReadyErrorCallback} errorFn - Error callback\n   * @see {@link http://stackoverflow.com/a/36155560/3894981} for\n   * background information\n   * @access protected\n   */\n  onIframeReady(ifr, successFn, errorFn) {\n    try {\n      if (ifr.contentWindow.document.readyState === 'complete') {\n        if (this.isIframeBlank(ifr)) {\n          this.observeIframeLoad(ifr, successFn, errorFn);\n        } else {\n          this.getIframeContents(ifr, successFn, errorFn);\n        }\n      } else {\n        this.observeIframeLoad(ifr, successFn, errorFn);\n      }\n    } catch (e) { // accessing document failed\n      errorFn();\n    }\n  }\n\n  /**\n   * Callback when all iframes are ready for DOM access\n   * @callback DOMIterator~waitForIframesDoneCallback\n   */\n  /**\n   * Iterates over all iframes and calls the done callback when all of them\n   * are ready for DOM access (including nested ones)\n   * @param {HTMLElement} ctx - The context DOM element\n   * @param {DOMIterator~waitForIframesDoneCallback} done - Done callback\n   */\n  waitForIframes(ctx, done) {\n    let eachCalled = 0;\n    this.forEachIframe(ctx, () => true, ifr => {\n      eachCalled++;\n      this.waitForIframes(ifr.querySelector('html'), () => {\n        if (!(--eachCalled)) {\n          done();\n        }\n      });\n    }, handled => {\n      if (!handled) {\n        done();\n      }\n    });\n  }\n\n  /**\n   * Callback allowing to filter an iframe. Must return true when the element\n   * should remain, otherwise false\n   * @callback DOMIterator~forEachIframeFilterCallback\n   * @param {HTMLElement} iframe - The iframe DOM element\n   */\n  /**\n   * Callback for each iframe content\n   * @callback DOMIterator~forEachIframeEachCallback\n   * @param {HTMLElement} content - The iframe document\n   */\n  /**\n   * Callback if all iframes inside the context were handled\n   * @callback DOMIterator~forEachIframeEndCallback\n   * @param {number} handled - The number of handled iframes (those who\n   * wheren't filtered)\n   */\n  /**\n   * Iterates over all iframes inside the specified context and calls the\n   * callbacks when they're ready. Filters iframes based on the instance\n   * exclusion selectors\n   * @param {HTMLElement} ctx - The context DOM element\n   * @param {DOMIterator~forEachIframeFilterCallback} filter - Filter callback\n   * @param {DOMIterator~forEachIframeEachCallback} each - Each callback\n   * @param {DOMIterator~forEachIframeEndCallback} [end] - End callback\n   * @access protected\n   */\n  forEachIframe(ctx, filter, each, end = () => {}) {\n    let ifr = ctx.querySelectorAll('iframe'),\n      open = ifr.length,\n      handled = 0;\n    ifr = Array.prototype.slice.call(ifr);\n    const checkEnd = () => {\n      if (--open <= 0) {\n        end(handled);\n      }\n    };\n    if (!open) {\n      checkEnd();\n    }\n    ifr.forEach(ifr => {\n      if (DOMIterator.matches(ifr, this.exclude)) {\n        checkEnd();\n      } else {\n        this.onIframeReady(ifr, con => {\n          if (filter(ifr)) {\n            handled++;\n            each(con);\n          }\n          checkEnd();\n        }, checkEnd);\n      }\n    });\n  }\n\n  /**\n   * Creates a NodeIterator on the specified context\n   * @see {@link https://developer.mozilla.org/en/docs/Web/API/NodeIterator}\n   * @param {HTMLElement} ctx - The context DOM element\n   * @param {DOMIterator~whatToShow} whatToShow\n   * @param {DOMIterator~filterCb} filter\n   * @return {NodeIterator}\n   * @access protected\n   */\n  createIterator(ctx, whatToShow, filter) {\n    return document.createNodeIterator(ctx, whatToShow, filter, false);\n  }\n\n  /**\n   * Creates an instance of DOMIterator in an iframe\n   * @param {HTMLDocument} contents - Iframe document\n   * @return {DOMIterator}\n   * @access protected\n   */\n  createInstanceOnIframe(contents) {\n    return new DOMIterator(contents.querySelector('html'), this.iframes);\n  }\n\n  /**\n   * Checks if an iframe occurs between two nodes, more specifically if an\n   * iframe occurs before the specified node and after the specified prevNode\n   * @param {HTMLElement} node - The node that should occur after the iframe\n   * @param {HTMLElement} prevNode - The node that should occur before the\n   * iframe\n   * @param {HTMLElement} ifr - The iframe to check against\n   * @return {boolean}\n   * @access protected\n   */\n  compareNodeIframe(node, prevNode, ifr) {\n    const compCurr = node.compareDocumentPosition(ifr),\n      prev = Node.DOCUMENT_POSITION_PRECEDING;\n    if (compCurr & prev) {\n      if (prevNode !== null) {\n        const compPrev = prevNode.compareDocumentPosition(ifr),\n          after = Node.DOCUMENT_POSITION_FOLLOWING;\n        if (compPrev & after) {\n          return true;\n        }\n      } else {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * @typedef {DOMIterator~getIteratorNodeReturn}\n   * @type {object.<string>}\n   * @property {HTMLElement} prevNode - The previous node or null if there is\n   * no\n   * @property {HTMLElement} node - The current node\n   */\n  /**\n   * Returns the previous and current node of the specified iterator\n   * @param {NodeIterator} itr - The iterator\n   * @return {DOMIterator~getIteratorNodeReturn}\n   * @access protected\n   */\n  getIteratorNode(itr) {\n    const prevNode = itr.previousNode();\n    let node;\n    if (prevNode === null) {\n      node = itr.nextNode();\n    } else {\n      node = itr.nextNode() && itr.nextNode();\n    }\n    return {\n      prevNode,\n      node\n    };\n  }\n\n  /**\n   * An array containing objects. The object key \"val\" contains an iframe\n   * DOM element. The object key \"handled\" contains a boolean indicating if\n   * the iframe was handled already.\n   * It wouldn't be enough to save all open or all already handled iframes.\n   * The information of open iframes is necessary because they may occur after\n   * all other text nodes (and compareNodeIframe would never be true). The\n   * information of already handled iframes is necessary as otherwise they may\n   * be handled multiple times\n   * @typedef DOMIterator~checkIframeFilterIfr\n   * @type {object[]}\n   */\n  /**\n   * Checks if an iframe wasn't handled already and if so, calls\n   * {@link DOMIterator#compareNodeIframe} to check if it should be handled.\n   * Information wheter an iframe was or wasn't handled is given within the\n   * <code>ifr</code> dictionary\n   * @param {HTMLElement} node - The node that should occur after the iframe\n   * @param {HTMLElement} prevNode - The node that should occur before the\n   * iframe\n   * @param {HTMLElement} currIfr - The iframe to check\n   * @param {DOMIterator~checkIframeFilterIfr} ifr - The iframe dictionary.\n   * Will be manipulated (by reference)\n   * @return {boolean} Returns true when it should be handled, otherwise false\n   * @access protected\n   */\n  checkIframeFilter(node, prevNode, currIfr, ifr) {\n    let key = false, // false === doesn't exist\n      handled = false;\n    ifr.forEach((ifrDict, i) => {\n      if (ifrDict.val === currIfr) {\n        key = i;\n        handled = ifrDict.handled;\n      }\n    });\n    if (this.compareNodeIframe(node, prevNode, currIfr)) {\n      if (key === false && !handled) {\n        ifr.push({\n          val: currIfr,\n          handled: true\n        });\n      } else if (key !== false && !handled) {\n        ifr[key].handled = true;\n      }\n      return true;\n    }\n    if (key === false) {\n      ifr.push({\n        val: currIfr,\n        handled: false\n      });\n    }\n    return false;\n  }\n\n  /**\n   * Creates an iterator on all open iframes in the specified array and calls\n   * the end callback when finished\n   * @param {DOMIterator~checkIframeFilterIfr} ifr\n   * @param {DOMIterator~whatToShow} whatToShow\n   * @param  {DOMIterator~forEachNodeCallback} eCb - Each callback\n   * @param {DOMIterator~filterCb} fCb\n   * @access protected\n   */\n  handleOpenIframes(ifr, whatToShow, eCb, fCb) {\n    ifr.forEach(ifrDict => {\n      if (!ifrDict.handled) {\n        this.getIframeContents(ifrDict.val, con => {\n          this.createInstanceOnIframe(con).forEachNode(\n            whatToShow, eCb, fCb\n          );\n        });\n      }\n    });\n  }\n\n  /**\n   * Iterates through all nodes in the specified context and handles iframe\n   * nodes at the correct position\n   * @param {DOMIterator~whatToShow} whatToShow\n   * @param {HTMLElement} ctx - The context\n   * @param  {DOMIterator~forEachNodeCallback} eachCb - Each callback\n   * @param {DOMIterator~filterCb} filterCb - Filter callback\n   * @param {DOMIterator~forEachNodeEndCallback} doneCb - End callback\n   * @access protected\n   */\n  iterateThroughNodes(whatToShow, ctx, eachCb, filterCb, doneCb) {\n    const itr = this.createIterator(ctx, whatToShow, filterCb);\n    let ifr = [],\n      elements = [],\n      node, prevNode, retrieveNodes = () => {\n        ({\n          prevNode,\n          node\n        } = this.getIteratorNode(itr));\n        return node;\n      };\n    while (retrieveNodes()) {\n      if (this.iframes) {\n        this.forEachIframe(ctx, currIfr => {\n          // note that ifr will be manipulated here\n          return this.checkIframeFilter(node, prevNode, currIfr, ifr);\n        }, con => {\n          this.createInstanceOnIframe(con).forEachNode(\n            whatToShow, ifrNode => elements.push(ifrNode), filterCb\n          );\n        });\n      }\n      // it's faster to call the each callback in an array loop\n      // than in this while loop\n      elements.push(node);\n    }\n    elements.forEach(node => {\n      eachCb(node);\n    });\n    if (this.iframes) {\n      this.handleOpenIframes(ifr, whatToShow, eachCb, filterCb);\n    }\n    doneCb();\n  }\n\n  /**\n   * Callback for each node\n   * @callback DOMIterator~forEachNodeCallback\n   * @param {HTMLElement} node - The DOM text node element\n   */\n  /**\n   * Callback if all contexts were handled\n   * @callback DOMIterator~forEachNodeEndCallback\n   */\n  /**\n   * Iterates over all contexts and initializes\n   * {@link DOMIterator#iterateThroughNodes iterateThroughNodes} on them\n   * @param {DOMIterator~whatToShow} whatToShow\n   * @param  {DOMIterator~forEachNodeCallback} each - Each callback\n   * @param {DOMIterator~filterCb} filter - Filter callback\n   * @param {DOMIterator~forEachNodeEndCallback} done - End callback\n   * @access public\n   */\n  forEachNode(whatToShow, each, filter, done = () => {}) {\n    const contexts = this.getContexts();\n    let open = contexts.length;\n    if (!open) {\n      done();\n    }\n    contexts.forEach(ctx => {\n      const ready = () => {\n        this.iterateThroughNodes(whatToShow, ctx, each, filter, () => {\n          if (--open <= 0) { // call end all contexts were handled\n            done();\n          }\n        });\n      };\n      // wait for iframes to avoid recursive calls, otherwise this would\n      // perhaps reach the recursive function call limit with many nodes\n      if (this.iframes) {\n        this.waitForIframes(ctx, ready);\n      } else {\n        ready();\n      }\n    });\n  }\n\n  /**\n   * Callback to filter nodes. Can return e.g. NodeFilter.FILTER_ACCEPT or\n   * NodeFilter.FILTER_REJECT\n   * @see {@link http://tinyurl.com/zdczmm2}\n   * @callback DOMIterator~filterCb\n   * @param {HTMLElement} node - The node to filter\n   */\n  /**\n   * @typedef DOMIterator~whatToShow\n   * @see {@link http://tinyurl.com/zfqqkx2}\n   * @type {number}\n   */\n}\n", "import DOMIterator from './domiterator';\n\n/**\n * Marks search terms in DOM elements\n * @example\n * new Mark(document.querySelector(\".context\")).mark(\"lorem ipsum\");\n * @example\n * new Mark(document.querySelector(\".context\")).markRegExp(/lorem/gmi);\n */\nexport default class Mark { // eslint-disable-line no-unused-vars\n\n  /**\n   * @param {HTMLElement|HTMLElement[]|NodeList|string} ctx - The context DOM\n   * element, an array of DOM elements, a NodeList or a selector\n   */\n  constructor(ctx) {\n    /**\n     * The context of the instance. Either a DOM element, an array of DOM\n     * elements, a NodeList or a selector\n     * @type {HTMLElement|HTMLElement[]|NodeList|string}\n     * @access protected\n     */\n    this.ctx = ctx;\n    /**\n     * Specifies if the current browser is a IE (necessary for the node\n     * normalization bug workaround). See {@link Mark#unwrapMatches}\n     * @type {boolean}\n     * @access protected\n     */\n    this.ie = false;\n    const ua = window.navigator.userAgent;\n    if (ua.indexOf('MSIE') > -1 || ua.indexOf('Trident') > -1) {\n      this.ie = true;\n    }\n  }\n\n  /**\n   * Options defined by the user. They will be initialized from one of the\n   * public methods. See {@link Mark#mark}, {@link Mark#markRegExp},\n   * {@link Mark#markRanges} and {@link Mark#unmark} for option properties.\n   * @type {object}\n   * @param {object} [val] - An object that will be merged with defaults\n   * @access protected\n   */\n  set opt(val) {\n    this._opt = Object.assign({}, {\n      'element': '',\n      'className': '',\n      'exclude': [],\n      'iframes': false,\n      'iframesTimeout': 5000,\n      'separateWordSearch': true,\n      'diacritics': true,\n      'synonyms': {},\n      'accuracy': 'partially',\n      'acrossElements': false,\n      'caseSensitive': false,\n      'ignoreJoiners': false,\n      'ignoreGroups': 0,\n      'ignorePunctuation': [],\n      'wildcards': 'disabled',\n      'each': () => {},\n      'noMatch': () => {},\n      'filter': () => true,\n      'done': () => {},\n      'debug': false,\n      'log': window.console\n    }, val);\n  }\n\n  get opt() {\n    return this._opt;\n  }\n\n  /**\n   * An instance of DOMIterator\n   * @type {DOMIterator}\n   * @access protected\n   */\n  get iterator() {\n    // always return new instance in case there were option changes\n    return new DOMIterator(\n      this.ctx,\n      this.opt.iframes,\n      this.opt.exclude,\n      this.opt.iframesTimeout\n    );\n  }\n\n  /**\n   * Logs a message if log is enabled\n   * @param {string} msg - The message to log\n   * @param {string} [level=\"debug\"] - The log level, e.g. <code>warn</code>\n   * <code>error</code>, <code>debug</code>\n   * @access protected\n   */\n  log(msg, level = 'debug') {\n    const log = this.opt.log;\n    if (!this.opt.debug) {\n      return;\n    }\n    if (typeof log === 'object' && typeof log[level] === 'function') {\n      log[level](`mark.js: ${msg}`);\n    }\n  }\n\n  /**\n   * Escapes a string for usage within a regular expression\n   * @param {string} str - The string to escape\n   * @return {string}\n   * @access protected\n   */\n  escapeStr(str) {\n    // eslint-disable-next-line no-useless-escape\n    return str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\n  }\n\n  /**\n   * Creates a regular expression string to match the specified search\n   * term including synonyms, diacritics and accuracy if defined\n   * @param  {string} str - The search term to be used\n   * @return {string}\n   * @access protected\n   */\n  createRegExp(str) {\n    if (this.opt.wildcards !== 'disabled') {\n      str = this.setupWildcardsRegExp(str);\n    }\n    str = this.escapeStr(str);\n    if (Object.keys(this.opt.synonyms).length) {\n      str = this.createSynonymsRegExp(str);\n    }\n    if (this.opt.ignoreJoiners || this.opt.ignorePunctuation.length) {\n      str = this.setupIgnoreJoinersRegExp(str);\n    }\n    if (this.opt.diacritics) {\n      str = this.createDiacriticsRegExp(str);\n    }\n    str = this.createMergedBlanksRegExp(str);\n    if (this.opt.ignoreJoiners || this.opt.ignorePunctuation.length) {\n      str = this.createJoinersRegExp(str);\n    }\n    if (this.opt.wildcards !== 'disabled') {\n      str = this.createWildcardsRegExp(str);\n    }\n    str = this.createAccuracyRegExp(str);\n    return str;\n  }\n\n  /**\n   * Creates a regular expression string to match the defined synonyms\n   * @param  {string} str - The search term to be used\n   * @return {string}\n   * @access protected\n   */\n  createSynonymsRegExp(str) {\n    const syn = this.opt.synonyms,\n      sens = this.opt.caseSensitive ? '' : 'i',\n      // add replacement character placeholder before and after the\n      // synonym group\n      joinerPlaceholder = this.opt.ignoreJoiners ||\n                this.opt.ignorePunctuation.length ? '\\u0000' : '';\n    for (let index in syn) {\n      if (syn.hasOwnProperty(index)) {\n        const value = syn[index],\n          k1 = this.opt.wildcards !== 'disabled' ?\n            this.setupWildcardsRegExp(index) :\n            this.escapeStr(index),\n          k2 = this.opt.wildcards !== 'disabled' ?\n            this.setupWildcardsRegExp(value) :\n            this.escapeStr(value);\n        if (k1 !== '' && k2 !== '') {\n          str = str.replace(\n            new RegExp(\n              `(${this.escapeStr(k1)}|${this.escapeStr(k2)})`,\n              `gm${sens}`\n            ),\n            joinerPlaceholder +\n            `(${this.processSynomyms(k1)}|` +\n            `${this.processSynomyms(k2)})` +\n            joinerPlaceholder\n          );\n        }\n      }\n    }\n    return str;\n  }\n\n  /**\n   * Setup synonyms to work with ignoreJoiners and or ignorePunctuation\n   * @param {string} str - synonym key or value to process\n   * @return {string} - processed synonym string\n   */\n  processSynomyms(str) {\n    if (this.opt.ignoreJoiners || this.opt.ignorePunctuation.length) {\n      str = this.setupIgnoreJoinersRegExp(str);\n    }\n    return str;\n  }\n\n  /**\n   * Sets up the regular expression string to allow later insertion of\n   * wildcard regular expression matches\n   * @param  {string} str - The search term to be used\n   * @return {string}\n   * @access protected\n   */\n  setupWildcardsRegExp(str) {\n    // replace single character wildcard with unicode 0001\n    str = str.replace(/(?:\\\\)*\\?/g, val => {\n      return val.charAt(0) === '\\\\' ? '?' : '\\u0001';\n    });\n    // replace multiple character wildcard with unicode 0002\n    return str.replace(/(?:\\\\)*\\*/g, val => {\n      return val.charAt(0) === '\\\\' ? '*' : '\\u0002';\n    });\n  }\n\n  /**\n   * Sets up the regular expression string to allow later insertion of\n   * wildcard regular expression matches\n   * @param  {string} str - The search term to be used\n   * @return {string}\n   * @access protected\n   */\n  createWildcardsRegExp(str) {\n    // default to \"enable\" (i.e. to not include spaces)\n    // \"withSpaces\" uses `[\\\\S\\\\s]` instead of `.` because the latter\n    // does not match new line characters\n    let spaces = this.opt.wildcards === 'withSpaces';\n    return str\n    // replace unicode 0001 with a RegExp class to match any single\n    // character, or any single non-whitespace character depending\n    // on the setting\n      .replace(/\\u0001/g, spaces ? '[\\\\S\\\\s]?' : '\\\\S?')\n    // replace unicode 0002 with a RegExp class to match zero or\n    // more characters, or zero or more non-whitespace characters\n    // depending on the setting\n      .replace(/\\u0002/g, spaces ? '[\\\\S\\\\s]*?' : '\\\\S*');\n  }\n\n  /**\n   * Sets up the regular expression string to allow later insertion of\n   * designated characters (soft hyphens & zero width characters)\n   * @param  {string} str - The search term to be used\n   * @return {string}\n   * @access protected\n   */\n  setupIgnoreJoinersRegExp(str) {\n    // adding a \"null\" unicode character as it will not be modified by the\n    // other \"create\" regular expression functions\n    return str.replace(/[^(|)\\\\]/g, (val, indx, original) => {\n      // don't add a null after an opening \"(\", around a \"|\" or before\n      // a closing \"(\", or between an escapement (e.g. \\+)\n      let nextChar = original.charAt(indx + 1);\n      if (/[(|)\\\\]/.test(nextChar) || nextChar === '') {\n        return val;\n      } else {\n        return val + '\\u0000';\n      }\n    });\n  }\n\n  /**\n   * Creates a regular expression string to allow ignoring of designated\n   * characters (soft hyphens, zero width characters & punctuation) based on\n   * the specified option values of <code>ignorePunctuation</code> and\n   * <code>ignoreJoiners</code>\n   * @param  {string} str - The search term to be used\n   * @return {string}\n   * @access protected\n   */\n  createJoinersRegExp(str) {\n    let joiner = [];\n    const ignorePunctuation = this.opt.ignorePunctuation;\n    if (Array.isArray(ignorePunctuation) && ignorePunctuation.length) {\n      joiner.push(this.escapeStr(ignorePunctuation.join('')));\n    }\n    if (this.opt.ignoreJoiners) {\n      // u+00ad = soft hyphen\n      // u+200b = zero-width space\n      // u+200c = zero-width non-joiner\n      // u+200d = zero-width joiner\n      joiner.push('\\\\u00ad\\\\u200b\\\\u200c\\\\u200d');\n    }\n    return joiner.length ?\n      str.split(/\\u0000+/).join(`[${joiner.join('')}]*`) :\n      str;\n  }\n\n  /**\n   * Creates a regular expression string to match diacritics\n   * @param  {string} str - The search term to be used\n   * @return {string}\n   * @access protected\n   */\n  createDiacriticsRegExp(str) {\n    const sens = this.opt.caseSensitive ? '' : 'i',\n      dct = this.opt.caseSensitive ? [\n        'aàáảãạăằắẳẵặâầấẩẫậäåāą', 'AÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ',\n        'cçćč', 'CÇĆČ', 'dđď', 'DĐĎ',\n        'eèéẻẽẹêềếểễệëěēę', 'EÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ',\n        'iìíỉĩịîïī', 'IÌÍỈĨỊÎÏĪ', 'lł', 'LŁ', 'nñňń',\n        'NÑŇŃ', 'oòóỏõọôồốổỗộơởỡớờợöøō', 'OÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ',\n        'rř', 'RŘ', 'sšśșş', 'SŠŚȘŞ',\n        'tťțţ', 'TŤȚŢ', 'uùúủũụưừứửữựûüůū', 'UÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ',\n        'yýỳỷỹỵÿ', 'YÝỲỶỸỴŸ', 'zžżź', 'ZŽŻŹ'\n      ] : [\n        'aàáảãạăằắẳẵặâầấẩẫậäåāąAÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ', 'cçćčCÇĆČ',\n        'dđďDĐĎ', 'eèéẻẽẹêềếểễệëěēęEÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ',\n        'iìíỉĩịîïīIÌÍỈĨỊÎÏĪ', 'lłLŁ', 'nñňńNÑŇŃ',\n        'oòóỏõọôồốổỗộơởỡớờợöøōOÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ', 'rřRŘ',\n        'sšśșşSŠŚȘŞ', 'tťțţTŤȚŢ',\n        'uùúủũụưừứửữựûüůūUÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ', 'yýỳỷỹỵÿYÝỲỶỸỴŸ', 'zžżźZŽŻŹ'\n      ];\n    let handled = [];\n    str.split('').forEach(ch => {\n      dct.every(dct => {\n        // Check if the character is inside a diacritics list\n        if (dct.indexOf(ch) !== -1) {\n          // Check if the related diacritics list was not\n          // handled yet\n          if (handled.indexOf(dct) > -1) {\n            return false;\n          }\n          // Make sure that the character OR any other\n          // character in the diacritics list will be matched\n          str = str.replace(\n            new RegExp(`[${dct}]`, `gm${sens}`), `[${dct}]`\n          );\n          handled.push(dct);\n        }\n        return true;\n      });\n    });\n    return str;\n  }\n\n  /**\n   * Creates a regular expression string that merges whitespace characters\n   * including subsequent ones into a single pattern, one or multiple\n   * whitespaces\n   * @param  {string} str - The search term to be used\n   * @return {string}\n   * @access protected\n   */\n  createMergedBlanksRegExp(str) {\n    return str.replace(/[\\s]+/gmi, '[\\\\s]+');\n  }\n\n  /**\n   * Creates a regular expression string to match the specified string with\n   * the defined accuracy. As in the regular expression of \"exactly\" can be\n   * a group containing a blank at the beginning, all regular expressions will\n   * be created with two groups. The first group can be ignored (may contain\n   * the said blank), the second contains the actual match\n   * @param  {string} str - The searm term to be used\n   * @return {str}\n   * @access protected\n   */\n  createAccuracyRegExp(str) {\n    const chars = '!\"#$%&\\'()*+,-./:;<=>?@[\\\\]^_`{|}~¡¿';\n    let acc = this.opt.accuracy,\n      val = typeof acc === 'string' ? acc : acc.value,\n      ls = typeof acc === 'string' ? [] : acc.limiters,\n      lsJoin = '';\n    ls.forEach(limiter => {\n      lsJoin += `|${this.escapeStr(limiter)}`;\n    });\n    switch (val) {\n    case 'partially':\n    default:\n      return `()(${str})`;\n    case 'complementary':\n      lsJoin = '\\\\s' + (lsJoin ? lsJoin : this.escapeStr(chars));\n      return `()([^${lsJoin}]*${str}[^${lsJoin}]*)`;\n    case 'exactly':\n      return `(^|\\\\s${lsJoin})(${str})(?=$|\\\\s${lsJoin})`;\n    }\n  }\n\n  /**\n   * @typedef Mark~separatedKeywords\n   * @type {object.<string>}\n   * @property {array.<string>} keywords - The list of keywords\n   * @property {number} length - The length\n   */\n  /**\n   * Returns a list of keywords dependent on whether separate word search\n   * was defined. Also it filters empty keywords\n   * @param {array} sv - The array of keywords\n   * @return {Mark~separatedKeywords}\n   * @access protected\n   */\n  getSeparatedKeywords(sv) {\n    let stack = [];\n    sv.forEach(kw => {\n      if (!this.opt.separateWordSearch) {\n        if (kw.trim() && stack.indexOf(kw) === -1) {\n          stack.push(kw);\n        }\n      } else {\n        kw.split(' ').forEach(kwSplitted => {\n          if (kwSplitted.trim() && stack.indexOf(kwSplitted) === -1) {\n            stack.push(kwSplitted);\n          }\n        });\n      }\n    });\n    return {\n      // sort because of https://git.io/v6USg\n      'keywords': stack.sort((a, b) => {\n        return b.length - a.length;\n      }),\n      'length': stack.length\n    };\n  }\n\n  /**\n   * Check if a value is a number\n   * @param {number|string} value - the value to check;\n   * numeric strings allowed\n   * @return {boolean}\n   * @access protected\n   */\n  isNumeric(value) {\n    // http://stackoverflow.com/a/16655847/145346\n    // eslint-disable-next-line eqeqeq\n    return Number(parseFloat(value)) == value;\n  }\n\n  /**\n   * @typedef Mark~rangeObject\n   * @type {object}\n   * @property {number} start - The start position within the composite value\n   * @property {number} length - The length of the string to mark within the\n   * composite value.\n   */\n  /**\n   * @typedef Mark~setOfRanges\n   * @type {object[]}\n   * @property {Mark~rangeObject}\n   */\n  /**\n   * Returns a processed list of integer offset indexes that do not overlap\n   * each other, and remove any string values or additional elements\n   * @param {Mark~setOfRanges} array - unprocessed raw array\n   * @return {Mark~setOfRanges} - processed array with any invalid entries\n   * removed\n   * @throws Will throw an error if an array of objects is not passed\n   * @access protected\n   */\n  checkRanges(array) {\n    // start and length indexes are included in an array of objects\n    // [{start: 0, length: 1}, {start: 4, length: 5}]\n    // quick validity check of the first entry only\n    if (\n      !Array.isArray(array) ||\n      Object.prototype.toString.call( array[0] ) !== '[object Object]'\n    ) {\n      this.log('markRanges() will only accept an array of objects');\n      this.opt.noMatch(array);\n      return [];\n    }\n    const stack = [];\n    let last = 0;\n    array\n    // acending sort to ensure there is no overlap in start & end\n    // offsets\n      .sort((a, b) => {\n        return a.start - b.start;\n      })\n      .forEach(item => {\n        let {start, end, valid} = this.callNoMatchOnInvalidRanges(item, last);\n        if (valid) {\n          // preserve item in case there are extra key:values within\n          item.start = start;\n          item.length = end - start;\n          stack.push(item);\n          last = end;\n        }\n      });\n    return stack;\n  }\n\n  /**\n   * @typedef Mark~validObject\n   * @type {object}\n   * @property {number} start - The start position within the composite value\n   * @property {number} end - The calculated end position within the composite\n   * value.\n   * @property {boolean} valid - boolean value indicating that the start and\n   * calculated end range is valid\n   */\n  /**\n    * Initial validation of ranges for markRanges. Preliminary checks are done\n    * to ensure the start and length values exist and are not zero or non-\n    * numeric\n    * @param {Mark~rangeObject} range - the current range object\n    * @param {number} last - last index of range\n    * @return {Mark~validObject}\n    * @access protected\n    */\n  callNoMatchOnInvalidRanges(range, last) {\n    let start, end,\n      valid = false;\n    if (range && typeof range.start !== 'undefined') {\n      start = parseInt(range.start, 10);\n      end = start + parseInt(range.length, 10);\n      // ignore overlapping values & non-numeric entries\n      if (\n        this.isNumeric(range.start) &&\n        this.isNumeric(range.length) &&\n        end - last > 0 &&\n        end - start > 0\n      ) {\n        valid = true;\n      } else {\n        this.log(\n          'Ignoring invalid or overlapping range: ' +\n                    `${JSON.stringify(range)}`\n        );\n        this.opt.noMatch(range);\n      }\n    } else {\n      this.log(`Ignoring invalid range: ${JSON.stringify(range)}`);\n      this.opt.noMatch(range);\n    }\n    return {\n      start: start,\n      end: end,\n      valid: valid\n    };\n  }\n\n  /**\n   * Check valid range for markRanges. Check ranges with access to the context\n   * string. Range values are double checked, lengths that extend the mark\n   * beyond the string length are limitied and ranges containing only\n   * whitespace are ignored\n   * @param {Mark~rangeObject} range - the current range object\n   * @param {number} originalLength - original length of the context string\n   * @param {string} string - current content string\n   * @return {Mark~validObject}\n   * @access protected\n   */\n  checkWhitespaceRanges(range, originalLength, string) {\n    let end,\n      valid = true,\n      // the max value changes after the DOM is manipulated\n      max = string.length,\n      // adjust offset to account for wrapped text node\n      offset = originalLength - max,\n      start = parseInt(range.start, 10) - offset;\n    // make sure to stop at max\n    start = start > max ? max : start;\n    end = start + parseInt(range.length, 10);\n    if (end > max) {\n      end = max;\n      this.log(`End range automatically set to the max value of ${max}`);\n    }\n    if (start < 0 || end - start < 0 || start > max || end > max) {\n      valid = false;\n      this.log(`Invalid range: ${JSON.stringify(range)}`);\n      this.opt.noMatch(range);\n    } else if (string.substring(start, end).replace(/\\s+/g, '') === '') {\n      valid = false;\n      // whitespace only; even if wrapped it is not visible\n      this.log('Skipping whitespace only range: ' +JSON.stringify(range));\n      this.opt.noMatch(range);\n    }\n    return {\n      start: start,\n      end: end,\n      valid: valid\n    };\n  }\n\n  /**\n   * @typedef Mark~getTextNodesDict\n   * @type {object.<string>}\n   * @property {string} value - The composite value of all text nodes\n   * @property {object[]} nodes - An array of objects\n   * @property {number} nodes.start - The start position within the composite\n   * value\n   * @property {number} nodes.end - The end position within the composite\n   * value\n   * @property {HTMLElement} nodes.node - The DOM text node element\n   */\n  /**\n   * Callback\n   * @callback Mark~getTextNodesCallback\n   * @param {Mark~getTextNodesDict}\n   */\n  /**\n   * Calls the callback with an object containing all text nodes (including\n   * iframe text nodes) with start and end positions and the composite value\n   * of them (string)\n   * @param {Mark~getTextNodesCallback} cb - Callback\n   * @access protected\n   */\n  getTextNodes(cb) {\n    let val = '',\n      nodes = [];\n    this.iterator.forEachNode(NodeFilter.SHOW_TEXT, node => {\n      nodes.push({\n        start: val.length,\n        end: (val += node.textContent).length,\n        node\n      });\n    }, node => {\n      if (this.matchesExclude(node.parentNode)) {\n        return NodeFilter.FILTER_REJECT;\n      } else {\n        return NodeFilter.FILTER_ACCEPT;\n      }\n    }, () => {\n      cb({\n        value: val,\n        nodes: nodes\n      });\n    });\n  }\n\n  /**\n   * Checks if an element matches any of the specified exclude selectors. Also\n   * it checks for elements in which no marks should be performed (e.g.\n   * script and style tags) and optionally already marked elements\n   * @param  {HTMLElement} el - The element to check\n   * @return {boolean}\n   * @access protected\n   */\n  matchesExclude(el) {\n    return DOMIterator.matches(el, this.opt.exclude.concat([\n      // ignores the elements itself, not their childrens (selector *)\n      'script', 'style', 'title', 'head', 'html'\n    ]));\n  }\n\n  /**\n   * Wraps the instance element and class around matches that fit the start\n   * and end positions within the node\n   * @param  {HTMLElement} node - The DOM text node\n   * @param  {number} start - The position where to start wrapping\n   * @param  {number} end - The position where to end wrapping\n   * @return {HTMLElement} Returns the splitted text node that will appear\n   * after the wrapped text node\n   * @access protected\n   */\n  wrapRangeInTextNode(node, start, end) {\n    const hEl = !this.opt.element ? 'mark' : this.opt.element,\n      startNode = node.splitText(start),\n      ret = startNode.splitText(end - start);\n    let repl = document.createElement(hEl);\n    repl.setAttribute('data-markjs', 'true');\n    if (this.opt.className) {\n      repl.setAttribute('class', this.opt.className);\n    }\n    repl.textContent = startNode.textContent;\n    startNode.parentNode.replaceChild(repl, startNode);\n    return ret;\n  }\n\n  /**\n   * @typedef Mark~wrapRangeInMappedTextNodeDict\n   * @type {object.<string>}\n   * @property {string} value - The composite value of all text nodes\n   * @property {object[]} nodes - An array of objects\n   * @property {number} nodes.start - The start position within the composite\n   * value\n   * @property {number} nodes.end - The end position within the composite\n   * value\n   * @property {HTMLElement} nodes.node - The DOM text node element\n   */\n  /**\n   * Each callback\n   * @callback Mark~wrapMatchesEachCallback\n   * @param {HTMLElement} node - The wrapped DOM element\n   * @param {number} lastIndex - The last matching position within the\n   * composite value of text nodes\n   */\n  /**\n   * Filter callback\n   * @callback Mark~wrapMatchesFilterCallback\n   * @param {HTMLElement} node - The matching text node DOM element\n   */\n  /**\n   * Determines matches by start and end positions using the text node\n   * dictionary even across text nodes and calls\n   * {@link Mark#wrapRangeInTextNode} to wrap them\n   * @param  {Mark~wrapRangeInMappedTextNodeDict} dict - The dictionary\n   * @param  {number} start - The start position of the match\n   * @param  {number} end - The end position of the match\n   * @param  {Mark~wrapMatchesFilterCallback} filterCb - Filter callback\n   * @param  {Mark~wrapMatchesEachCallback} eachCb - Each callback\n   * @access protected\n   */\n  wrapRangeInMappedTextNode(dict, start, end, filterCb, eachCb) {\n    // iterate over all text nodes to find the one matching the positions\n    dict.nodes.every((n, i) => {\n      const sibl = dict.nodes[i + 1];\n      if (typeof sibl === 'undefined' || sibl.start > start) {\n        if (!filterCb(n.node)) {\n          return false;\n        }\n        // map range from dict.value to text node\n        const s = start - n.start,\n          e = (end > n.end ? n.end : end) - n.start,\n          startStr = dict.value.substr(0, n.start),\n          endStr = dict.value.substr(e + n.start);\n        n.node = this.wrapRangeInTextNode(n.node, s, e);\n        // recalculate positions to also find subsequent matches in the\n        // same text node. Necessary as the text node in dict now only\n        // contains the splitted part after the wrapped one\n        dict.value = startStr + endStr;\n        dict.nodes.forEach((k, j) => {\n          if (j >= i) {\n            if (dict.nodes[j].start > 0 && j !== i) {\n              dict.nodes[j].start -= e;\n            }\n            dict.nodes[j].end -= e;\n          }\n        });\n        end -= e;\n        eachCb(n.node.previousSibling, n.start);\n        if (end > n.end) {\n          start = n.end;\n        } else {\n          return false;\n        }\n      }\n      return true;\n    });\n  }\n\n  /**\n   * Filter callback before each wrapping\n   * @callback Mark~wrapMatchesFilterCallback\n   * @param {string} match - The matching string\n   * @param {HTMLElement} node - The text node where the match occurs\n   */\n  /**\n   * Callback for each wrapped element\n   * @callback Mark~wrapMatchesEachCallback\n   * @param {HTMLElement} element - The marked DOM element\n   */\n  /**\n   * Callback on end\n   * @callback Mark~wrapMatchesEndCallback\n   */\n  /**\n   * Wraps the instance element and class around matches within single HTML\n   * elements in all contexts\n   * @param {RegExp} regex - The regular expression to be searched for\n   * @param {number} ignoreGroups - A number indicating the amount of RegExp\n   * matching groups to ignore\n   * @param {Mark~wrapMatchesFilterCallback} filterCb\n   * @param {Mark~wrapMatchesEachCallback} eachCb\n   * @param {Mark~wrapMatchesEndCallback} endCb\n   * @access protected\n   */\n  wrapMatches(regex, ignoreGroups, filterCb, eachCb, endCb) {\n    const matchIdx = ignoreGroups === 0 ? 0 : ignoreGroups + 1;\n    this.getTextNodes(dict => {\n      dict.nodes.forEach(node => {\n        node = node.node;\n        let match;\n        while (\n          (match = regex.exec(node.textContent)) !== null &&\n          match[matchIdx] !== ''\n        ) {\n          if (!filterCb(match[matchIdx], node)) {\n            continue;\n          }\n          let pos = match.index;\n          if (matchIdx !== 0) {\n            for (let i = 1; i < matchIdx; i++) {\n              pos += match[i].length;\n            }\n          }\n          node = this.wrapRangeInTextNode(\n            node,\n            pos,\n            pos + match[matchIdx].length\n          );\n          eachCb(node.previousSibling);\n          // reset index of last match as the node changed and the\n          // index isn't valid anymore http://tinyurl.com/htsudjd\n          regex.lastIndex = 0;\n        }\n      });\n      endCb();\n    });\n  }\n\n  /**\n   * Callback for each wrapped element\n   * @callback Mark~wrapMatchesAcrossElementsEachCallback\n   * @param {HTMLElement} element - The marked DOM element\n   */\n  /**\n   * Filter callback before each wrapping\n   * @callback Mark~wrapMatchesAcrossElementsFilterCallback\n   * @param {string} match - The matching string\n   * @param {HTMLElement} node - The text node where the match occurs\n   */\n  /**\n   * Callback on end\n   * @callback Mark~wrapMatchesAcrossElementsEndCallback\n   */\n  /**\n   * Wraps the instance element and class around matches across all HTML\n   * elements in all contexts\n   * @param {RegExp} regex - The regular expression to be searched for\n   * @param {number} ignoreGroups - A number indicating the amount of RegExp\n   * matching groups to ignore\n   * @param {Mark~wrapMatchesAcrossElementsFilterCallback} filterCb\n   * @param {Mark~wrapMatchesAcrossElementsEachCallback} eachCb\n   * @param {Mark~wrapMatchesAcrossElementsEndCallback} endCb\n   * @access protected\n   */\n  wrapMatchesAcrossElements(regex, ignoreGroups, filterCb, eachCb, endCb) {\n    const matchIdx = ignoreGroups === 0 ? 0 : ignoreGroups + 1;\n    this.getTextNodes(dict => {\n      let match;\n      while (\n        (match = regex.exec(dict.value)) !== null &&\n        match[matchIdx] !== ''\n      ) {\n        // calculate range inside dict.value\n        let start = match.index;\n        if (matchIdx !== 0) {\n          for (let i = 1; i < matchIdx; i++) {\n            start += match[i].length;\n          }\n        }\n        const end = start + match[matchIdx].length;\n        // note that dict will be updated automatically, as it'll change\n        // in the wrapping process, due to the fact that text\n        // nodes will be splitted\n        this.wrapRangeInMappedTextNode(dict, start, end, node => {\n          return filterCb(match[matchIdx], node);\n        }, (node, lastIndex) => {\n          regex.lastIndex = lastIndex;\n          eachCb(node);\n        });\n      }\n      endCb();\n    });\n  }\n\n  /**\n   * Callback for each wrapped element\n   * @callback Mark~wrapRangeFromIndexEachCallback\n   * @param {HTMLElement} element - The marked DOM element\n   * @param {Mark~rangeObject} range - the current range object; provided\n   * start and length values will be numeric integers modified from the\n   * provided original ranges.\n   */\n  /**\n   * Filter callback before each wrapping\n   * @callback Mark~wrapRangeFromIndexFilterCallback\n   * @param {HTMLElement} node - The text node which includes the range\n   * @param {Mark~rangeObject} range - the current range object\n   * @param {string} match - string extracted from the matching range\n   * @param {number} counter - A counter indicating the number of all marks\n   */\n  /**\n   * Callback on end\n   * @callback Mark~wrapRangeFromIndexEndCallback\n   */\n  /**\n   * Wraps the indicated ranges across all HTML elements in all contexts\n   * @param {Mark~setOfRanges} ranges\n   * @param {Mark~wrapRangeFromIndexFilterCallback} filterCb\n   * @param {Mark~wrapRangeFromIndexEachCallback} eachCb\n   * @param {Mark~wrapRangeFromIndexEndCallback} endCb\n   * @access protected\n   */\n  wrapRangeFromIndex(ranges, filterCb, eachCb, endCb) {\n    this.getTextNodes(dict => {\n      const originalLength = dict.value.length;\n      ranges.forEach((range, counter) => {\n        let {start, end, valid} = this.checkWhitespaceRanges(\n          range,\n          originalLength,\n          dict.value\n        );\n        if (valid) {\n          this.wrapRangeInMappedTextNode(dict, start, end, node => {\n            return filterCb(\n              node,\n              range,\n              dict.value.substring(start, end),\n              counter\n            );\n          }, node => {\n            eachCb(node, range);\n          });\n        }\n      });\n      endCb();\n    });\n  }\n\n  /**\n   * Unwraps the specified DOM node with its content (text nodes or HTML)\n   * without destroying possibly present events (using innerHTML) and\n   * normalizes the parent at the end (merge splitted text nodes)\n   * @param  {HTMLElement} node - The DOM node to unwrap\n   * @access protected\n   */\n  unwrapMatches(node) {\n    const parent = node.parentNode;\n    let docFrag = document.createDocumentFragment();\n    while (node.firstChild) {\n      docFrag.appendChild(node.removeChild(node.firstChild));\n    }\n    parent.replaceChild(docFrag, node);\n    if (!this.ie) { // use browser's normalize method\n      parent.normalize();\n    } else { // custom method (needs more time)\n      this.normalizeTextNode(parent);\n    }\n  }\n\n  /**\n   * Normalizes text nodes. It's a workaround for the native normalize method\n   * that has a bug in IE (see attached link). Should only be used in IE\n   * browsers as it's slower than the native method.\n   * @see {@link http://tinyurl.com/z5asa8c}\n   * @param {HTMLElement} node - The DOM node to normalize\n   * @access protected\n   */\n  normalizeTextNode(node) {\n    if (!node) {\n      return;\n    }\n    if (node.nodeType === 3) {\n      while (node.nextSibling && node.nextSibling.nodeType === 3) {\n        node.nodeValue += node.nextSibling.nodeValue;\n        node.parentNode.removeChild(node.nextSibling);\n      }\n    } else {\n      this.normalizeTextNode(node.firstChild);\n    }\n    this.normalizeTextNode(node.nextSibling);\n  }\n\n  /**\n   * Callback when finished\n   * @callback Mark~commonDoneCallback\n   * @param {number} totalMatches - The number of marked elements\n   */\n  /**\n   * @typedef Mark~commonOptions\n   * @type {object.<string>}\n   * @property {string} [element=\"mark\"] - HTML element tag name\n   * @property {string} [className] - An optional class name\n   * @property {string[]} [exclude] - An array with exclusion selectors.\n   * Elements matching those selectors will be ignored\n   * @property {boolean} [iframes=false] - Whether to search inside iframes\n   * @property {Mark~commonDoneCallback} [done]\n   * @property {boolean} [debug=false] - Wheter to log messages\n   * @property {object} [log=window.console] - Where to log messages (only if\n   * debug is true)\n   */\n  /**\n   * Callback for each marked element\n   * @callback Mark~markRegExpEachCallback\n   * @param {HTMLElement} element - The marked DOM element\n   */\n  /**\n   * Callback if there were no matches\n   * @callback Mark~markRegExpNoMatchCallback\n   * @param {RegExp} regexp - The regular expression\n   */\n  /**\n   * Callback to filter matches\n   * @callback Mark~markRegExpFilterCallback\n   * @param {HTMLElement} textNode - The text node which includes the match\n   * @param {string} match - The matching string for the RegExp\n   * @param {number} counter - A counter indicating the number of all marks\n   */\n  /**\n   * These options also include the common options from\n   * {@link Mark~commonOptions}\n   * @typedef Mark~markRegExpOptions\n   * @type {object.<string>}\n   * @property {Mark~markRegExpEachCallback} [each]\n   * @property {Mark~markRegExpNoMatchCallback} [noMatch]\n   * @property {Mark~markRegExpFilterCallback} [filter]\n   */\n  /**\n   * Marks a custom regular expression\n   * @param  {RegExp} regexp - The regular expression\n   * @param  {Mark~markRegExpOptions} [opt] - Optional options object\n   * @access public\n   */\n  markRegExp(regexp, opt) {\n    this.opt = opt;\n    this.log(`Searching with expression \"${regexp}\"`);\n    let totalMatches = 0,\n      fn = 'wrapMatches';\n    const eachCb = element => {\n      totalMatches++;\n      this.opt.each(element);\n    };\n    if (this.opt.acrossElements) {\n      fn = 'wrapMatchesAcrossElements';\n    }\n    this[fn](regexp, this.opt.ignoreGroups, (match, node) => {\n      return this.opt.filter(node, match, totalMatches);\n    }, eachCb, () => {\n      if (totalMatches === 0) {\n        this.opt.noMatch(regexp);\n      }\n      this.opt.done(totalMatches);\n    });\n  }\n\n  /**\n   * Callback for each marked element\n   * @callback Mark~markEachCallback\n   * @param {HTMLElement} element - The marked DOM element\n   */\n  /**\n   * Callback if there were no matches\n   * @callback Mark~markNoMatchCallback\n   * @param {RegExp} term - The search term that was not found\n   */\n  /**\n   * Callback to filter matches\n   * @callback Mark~markFilterCallback\n   * @param {HTMLElement} textNode - The text node which includes the match\n   * @param {string} match - The matching term\n   * @param {number} totalCounter - A counter indicating the number of all\n   * marks\n   * @param {number} termCounter - A counter indicating the number of marks\n   * for the specific match\n   */\n  /**\n   * @typedef Mark~markAccuracyObject\n   * @type {object.<string>}\n   * @property {string} value - A accuracy string value\n   * @property {string[]} limiters - A custom array of limiters. For example\n   * <code>[\"-\", \",\"]</code>\n   */\n  /**\n   * @typedef Mark~markAccuracySetting\n   * @type {string}\n   * @property {\"partially\"|\"complementary\"|\"exactly\"|Mark~markAccuracyObject}\n   * [accuracy=\"partially\"] - Either one of the following string values:\n   * <ul>\n   *   <li><i>partially</i>: When searching for \"lor\" only \"lor\" inside\n   *   \"lorem\" will be marked</li>\n   *   <li><i>complementary</i>: When searching for \"lor\" the whole word\n   *   \"lorem\" will be marked</li>\n   *   <li><i>exactly</i>: When searching for \"lor\" only those exact words\n   *   will be marked. In this example nothing inside \"lorem\". This value\n   *   is equivalent to the previous option <i>wordBoundary</i></li>\n   * </ul>\n   * Or an object containing two properties:\n   * <ul>\n   *   <li><i>value</i>: One of the above named string values</li>\n   *   <li><i>limiters</i>: A custom array of string limiters for accuracy\n   *   \"exactly\" or \"complementary\"</li>\n   * </ul>\n   */\n  /**\n   * @typedef Mark~markWildcardsSetting\n   * @type {string}\n   * @property {\"disabled\"|\"enabled\"|\"withSpaces\"}\n   * [wildcards=\"disabled\"] - Set to any of the following string values:\n   * <ul>\n   *   <li><i>disabled</i>: Disable wildcard usage</li>\n   *   <li><i>enabled</i>: When searching for \"lor?m\", the \"?\" will match zero\n   *   or one non-space character (e.g. \"lorm\", \"loram\", \"lor3m\", etc). When\n   *   searching for \"lor*m\", the \"*\" will match zero or more non-space\n   *   characters (e.g. \"lorm\", \"loram\", \"lor123m\", etc).</li>\n   *   <li><i>withSpaces</i>: When searching for \"lor?m\", the \"?\" will\n   *   match zero or one space or non-space character (e.g. \"lor m\", \"loram\",\n   *   etc). When searching for \"lor*m\", the \"*\" will match zero or more space\n   *   or non-space characters (e.g. \"lorm\", \"lore et dolor ipsum\", \"lor: m\",\n   *   etc).</li>\n   * </ul>\n   */\n  /**\n   * @typedef Mark~markIgnorePunctuationSetting\n   * @type {string[]}\n   * @property {string} The strings in this setting will contain punctuation\n   * marks that will be ignored:\n   * <ul>\n   *   <li>These punctuation marks can be between any characters, e.g. setting\n   *   this option to <code>[\"'\"]</code> would match \"Worlds\", \"World's\" and\n   *   \"Wo'rlds\"</li>\n   *   <li>One or more apostrophes between the letters would still produce a\n   *   match (e.g. \"W'o''r'l'd's\").</li>\n   *   <li>A typical setting for this option could be as follows:\n   *   <pre>ignorePunctuation: \":;.,-–—‒_(){}[]!'\\\"+=\".split(\"\"),</pre> This\n   *   setting includes common punctuation as well as a minus, en-dash,\n   *   em-dash and figure-dash\n   *   ({@link https://en.wikipedia.org/wiki/Dash#Figure_dash ref}), as well\n   *   as an underscore.</li>\n   * </ul>\n   */\n  /**\n   * These options also include the common options from\n   * {@link Mark~commonOptions}\n   * @typedef Mark~markOptions\n   * @type {object.<string>}\n   * @property {boolean} [separateWordSearch=true] - Whether to search for\n   * each word separated by a blank instead of the complete term\n   * @property {boolean} [diacritics=true] - If diacritic characters should be\n   * matched. ({@link https://en.wikipedia.org/wiki/Diacritic Diacritics})\n   * @property {object} [synonyms] - An object with synonyms. The key will be\n   * a synonym for the value and the value for the key\n   * @property {Mark~markAccuracySetting} [accuracy]\n   * @property {Mark~markWildcardsSetting} [wildcards]\n   * @property {boolean} [acrossElements=false] - Whether to find matches\n   * across HTML elements. By default, only matches within single HTML\n   * elements will be found\n   * @property {boolean} [ignoreJoiners=false] - Whether to ignore word\n   * joiners inside of key words. These include soft-hyphens, zero-width\n   * space, zero-width non-joiners and zero-width joiners.\n   * @property {Mark~markIgnorePunctuationSetting} [ignorePunctuation]\n   * @property {Mark~markEachCallback} [each]\n   * @property {Mark~markNoMatchCallback} [noMatch]\n   * @property {Mark~markFilterCallback} [filter]\n   */\n  /**\n   * Marks the specified search terms\n   * @param {string|string[]} [sv] - Search value, either a search string or\n   * an array containing multiple search strings\n   * @param  {Mark~markOptions} [opt] - Optional options object\n   * @access public\n   */\n  mark(sv, opt) {\n    this.opt = opt;\n    let totalMatches = 0,\n      fn = 'wrapMatches';\n\n    const {\n        keywords: kwArr,\n        length: kwArrLen\n      } = this.getSeparatedKeywords(typeof sv === 'string' ? [sv] : sv),\n      sens = this.opt.caseSensitive ? '' : 'i',\n      handler = kw => { // async function calls as iframes are async too\n        let regex = new RegExp(this.createRegExp(kw), `gm${sens}`),\n          matches = 0;\n        this.log(`Searching with expression \"${regex}\"`);\n        this[fn](regex, 1, (term, node) => {\n          return this.opt.filter(node, kw, totalMatches, matches);\n        }, element => {\n          matches++;\n          totalMatches++;\n          this.opt.each(element);\n        }, () => {\n          if (matches === 0) {\n            this.opt.noMatch(kw);\n          }\n          if (kwArr[kwArrLen - 1] === kw) {\n            this.opt.done(totalMatches);\n          } else {\n            handler(kwArr[kwArr.indexOf(kw) + 1]);\n          }\n        });\n      };\n    if (this.opt.acrossElements) {\n      fn = 'wrapMatchesAcrossElements';\n    }\n    if (kwArrLen === 0) {\n      this.opt.done(totalMatches);\n    } else {\n      handler(kwArr[0]);\n    }\n  }\n\n  /**\n   * Callback for each marked element\n   * @callback Mark~markRangesEachCallback\n   * @param {HTMLElement} element - The marked DOM element\n   * @param {array} range - array of range start and end points\n   */\n  /**\n   * Callback if a processed range is invalid, out-of-bounds, overlaps another\n   * range, or only matches whitespace\n   * @callback Mark~markRangesNoMatchCallback\n   * @param {Mark~rangeObject} range - a range object\n   */\n  /**\n   * Callback to filter matches\n   * @callback Mark~markRangesFilterCallback\n   * @param {HTMLElement} node - The text node which includes the range\n   * @param {array} range - array of range start and end points\n   * @param {string} match - string extracted from the matching range\n   * @param {number} counter - A counter indicating the number of all marks\n   */\n  /**\n   * These options also include the common options from\n   * {@link Mark~commonOptions}\n   * @typedef Mark~markRangesOptions\n   * @type {object.<string>}\n   * @property {Mark~markRangesEachCallback} [each]\n   * @property {Mark~markRangesNoMatchCallback} [noMatch]\n   * @property {Mark~markRangesFilterCallback} [filter]\n   */\n  /**\n   * Marks an array of objects containing a start with an end or length of the\n   * string to mark\n   * @param  {Mark~setOfRanges} rawRanges - The original (preprocessed)\n   * array of objects\n   * @param  {Mark~markRangesOptions} [opt] - Optional options object\n   * @access public\n   */\n  markRanges(rawRanges, opt) {\n    this.opt = opt;\n    let totalMatches = 0,\n      ranges = this.checkRanges(rawRanges);\n    if (ranges && ranges.length) {\n      this.log(\n        'Starting to mark with the following ranges: ' +\n        JSON.stringify(ranges)\n      );\n      this.wrapRangeFromIndex(\n        ranges, (node, range, match, counter) => {\n          return this.opt.filter(node, range, match, counter);\n        }, (element, range) => {\n          totalMatches++;\n          this.opt.each(element, range);\n        }, () => {\n          this.opt.done(totalMatches);\n        }\n      );\n    } else {\n      this.opt.done(totalMatches);\n    }\n  }\n\n  /**\n   * Removes all marked elements inside the context with their HTML and\n   * normalizes the parent at the end\n   * @param  {Mark~commonOptions} [opt] - Optional options object\n   * @access public\n   */\n  unmark(opt) {\n    this.opt = opt;\n    let sel = this.opt.element ? this.opt.element : '*';\n    sel += '[data-markjs]';\n    if (this.opt.className) {\n      sel += `.${this.opt.className}`;\n    }\n    this.log(`Removal selector \"${sel}\"`);\n    this.iterator.forEachNode(NodeFilter.SHOW_ELEMENT, node => {\n      this.unwrapMatches(node);\n    }, node => {\n      const matchesSel = DOMIterator.matches(node, sel),\n        matchesExclude = this.matchesExclude(node);\n      if (!matchesSel || matchesExclude) {\n        return NodeFilter.FILTER_REJECT;\n      } else {\n        return NodeFilter.FILTER_ACCEPT;\n      }\n    }, this.opt.done);\n  }\n}\n", "import MarkJS from './lib/mark';\n\nexport default function Mark(ctx) {\n  const instance = new MarkJS(ctx);\n  this.mark = (sv, opt) => {\n    instance.mark(sv, opt);\n    return this;\n  };\n  this.markRegExp = (sv, opt) => {\n    instance.markRegExp(sv, opt);\n    return this;\n  };\n  this.markRanges = (sv, opt) => {\n    instance.markRanges(sv, opt);\n    return this;\n  };\n  this.unmark = (opt) => {\n    instance.unmark(opt);\n    return this;\n  };\n  return this;\n}"], "mappings": ";;;AAoBA,IAAqB,cAArB,MAAqB,aAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAe/B,YAAY,KAAK,UAAU,MAAM,UAAU,CAAC,GAAG,iBAAiB,KAAM;AAOpE,SAAK,MAAM;AAMX,SAAK,UAAU;AAKf,SAAK,UAAU;AAKf,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,QAAQ,SAAS,UAAU;AAChC,UAAM,YAAY,OAAO,aAAa,WAAW,CAAC,QAAQ,IAAI,UAC5D,KACE,QAAQ,WACR,QAAQ,mBACR,QAAQ,qBACR,QAAQ,sBACR,QAAQ,oBACR,QAAQ;AAEZ,QAAI,IAAI;AACN,UAAI,QAAQ;AACZ,gBAAU,MAAM,SAAO;AACrB,YAAI,GAAG,KAAK,SAAS,GAAG,GAAG;AACzB,kBAAQ;AACR,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC;AACD,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,QAAI,KACF,cAAc,CAAC;AACjB,QAAI,OAAO,KAAK,QAAQ,eAAe,CAAC,KAAK,KAAK;AAChD,YAAM,CAAC;AAAA,IACT,WAAW,SAAS,UAAU,cAAc,KAAK,GAAG,GAAG;AACrD,YAAM,MAAM,UAAU,MAAM,KAAK,KAAK,GAAG;AAAA,IAC3C,WAAW,MAAM,QAAQ,KAAK,GAAG,GAAG;AAClC,YAAM,KAAK;AAAA,IACb,WAAW,OAAO,KAAK,QAAQ,UAAU;AACvC,YAAM,MAAM,UAAU,MAAM;AAAA,QAC1B,SAAS,iBAAiB,KAAK,GAAG;AAAA,MACpC;AAAA,IACF,OAAO;AACL,YAAM,CAAC,KAAK,GAAG;AAAA,IACjB;AAEA,QAAI,QAAQ,CAAAA,SAAO;AACjB,YAAM,eAAe,YAAY,OAAO,cAAY;AAClD,eAAO,SAAS,SAASA,IAAG;AAAA,MAC9B,CAAC,EAAE,SAAS;AACZ,UAAI,YAAY,QAAQA,IAAG,MAAM,MAAM,CAAC,cAAc;AACpD,oBAAY,KAAKA,IAAG;AAAA,MACtB;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,kBAAkB,KAAK,WAAW,UAAU,MAAM;AAAA,EAAC,GAAG;AACpD,QAAI;AACJ,QAAI;AACF,YAAM,SAAS,IAAI;AACnB,YAAM,OAAO;AACb,UAAI,CAAC,UAAU,CAAC,KAAK;AACnB,cAAM,IAAI,MAAM,qBAAqB;AAAA,MACvC;AAAA,IACF,SAAS,GAAG;AACV,cAAQ;AAAA,IACV;AACA,QAAI,KAAK;AACP,gBAAU,GAAG;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,KAAK;AACjB,UAAM,KAAK,eACT,MAAM,IAAI,aAAa,KAAK,EAAE,KAAK,GACnC,OAAO,IAAI,cAAc,SAAS;AACpC,WAAO,SAAS,MAAM,QAAQ,MAAM;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,kBAAkB,KAAK,WAAW,SAAS;AACzC,QAAI,SAAS,OACX,OAAO;AACT,UAAM,WAAW,MAAM;AACrB,UAAI,QAAQ;AACV;AAAA,MACF;AACA,eAAS;AACT,mBAAa,IAAI;AACjB,UAAI;AACF,YAAI,CAAC,KAAK,cAAc,GAAG,GAAG;AAC5B,cAAI,oBAAoB,QAAQ,QAAQ;AACxC,eAAK,kBAAkB,KAAK,WAAW,OAAO;AAAA,QAChD;AAAA,MACF,SAAS,GAAG;AACV,gBAAQ;AAAA,MACV;AAAA,IACF;AACA,QAAI,iBAAiB,QAAQ,QAAQ;AACrC,WAAO,WAAW,UAAU,KAAK,cAAc;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,cAAc,KAAK,WAAW,SAAS;AACrC,QAAI;AACF,UAAI,IAAI,cAAc,SAAS,eAAe,YAAY;AACxD,YAAI,KAAK,cAAc,GAAG,GAAG;AAC3B,eAAK,kBAAkB,KAAK,WAAW,OAAO;AAAA,QAChD,OAAO;AACL,eAAK,kBAAkB,KAAK,WAAW,OAAO;AAAA,QAChD;AAAA,MACF,OAAO;AACL,aAAK,kBAAkB,KAAK,WAAW,OAAO;AAAA,MAChD;AAAA,IACF,SAAS,GAAG;AACV,cAAQ;AAAA,IACV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,eAAe,KAAK,MAAM;AACxB,QAAI,aAAa;AACjB,SAAK,cAAc,KAAK,MAAM,MAAM,SAAO;AACzC;AACA,WAAK,eAAe,IAAI,cAAc,MAAM,GAAG,MAAM;AACnD,YAAI,CAAE,EAAE,YAAa;AACnB,eAAK;AAAA,QACP;AAAA,MACF,CAAC;AAAA,IACH,GAAG,aAAW;AACZ,UAAI,CAAC,SAAS;AACZ,aAAK;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BA,cAAc,KAAK,QAAQ,MAAM,MAAM,MAAM;AAAA,EAAC,GAAG;AAC/C,QAAI,MAAM,IAAI,iBAAiB,QAAQ,GACrC,OAAO,IAAI,QACX,UAAU;AACZ,UAAM,MAAM,UAAU,MAAM,KAAK,GAAG;AACpC,UAAM,WAAW,MAAM;AACrB,UAAI,EAAE,QAAQ,GAAG;AACf,YAAI,OAAO;AAAA,MACb;AAAA,IACF;AACA,QAAI,CAAC,MAAM;AACT,eAAS;AAAA,IACX;AACA,QAAI,QAAQ,CAAAC,SAAO;AACjB,UAAI,aAAY,QAAQA,MAAK,KAAK,OAAO,GAAG;AAC1C,iBAAS;AAAA,MACX,OAAO;AACL,aAAK,cAAcA,MAAK,SAAO;AAC7B,cAAI,OAAOA,IAAG,GAAG;AACf;AACA,iBAAK,GAAG;AAAA,UACV;AACA,mBAAS;AAAA,QACX,GAAG,QAAQ;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,eAAe,KAAK,YAAY,QAAQ;AACtC,WAAO,SAAS,mBAAmB,KAAK,YAAY,QAAQ,KAAK;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,uBAAuB,UAAU;AAC/B,WAAO,IAAI,aAAY,SAAS,cAAc,MAAM,GAAG,KAAK,OAAO;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,kBAAkB,MAAM,UAAU,KAAK;AACrC,UAAM,WAAW,KAAK,wBAAwB,GAAG,GAC/C,OAAO,KAAK;AACd,QAAI,WAAW,MAAM;AACnB,UAAI,aAAa,MAAM;AACrB,cAAM,WAAW,SAAS,wBAAwB,GAAG,GACnD,QAAQ,KAAK;AACf,YAAI,WAAW,OAAO;AACpB,iBAAO;AAAA,QACT;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,gBAAgB,KAAK;AACnB,UAAM,WAAW,IAAI,aAAa;AAClC,QAAI;AACJ,QAAI,aAAa,MAAM;AACrB,aAAO,IAAI,SAAS;AAAA,IACtB,OAAO;AACL,aAAO,IAAI,SAAS,KAAK,IAAI,SAAS;AAAA,IACxC;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BA,kBAAkB,MAAM,UAAU,SAAS,KAAK;AAC9C,QAAI,MAAM,OACR,UAAU;AACZ,QAAI,QAAQ,CAAC,SAAS,MAAM;AAC1B,UAAI,QAAQ,QAAQ,SAAS;AAC3B,cAAM;AACN,kBAAU,QAAQ;AAAA,MACpB;AAAA,IACF,CAAC;AACD,QAAI,KAAK,kBAAkB,MAAM,UAAU,OAAO,GAAG;AACnD,UAAI,QAAQ,SAAS,CAAC,SAAS;AAC7B,YAAI,KAAK;AAAA,UACP,KAAK;AAAA,UACL,SAAS;AAAA,QACX,CAAC;AAAA,MACH,WAAW,QAAQ,SAAS,CAAC,SAAS;AACpC,YAAI,GAAG,EAAE,UAAU;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,OAAO;AACjB,UAAI,KAAK;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,KAAK,YAAY,KAAK,KAAK;AAC3C,QAAI,QAAQ,aAAW;AACrB,UAAI,CAAC,QAAQ,SAAS;AACpB,aAAK,kBAAkB,QAAQ,KAAK,SAAO;AACzC,eAAK,uBAAuB,GAAG,EAAE;AAAA,YAC/B;AAAA,YAAY;AAAA,YAAK;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,oBAAoB,YAAY,KAAK,QAAQ,UAAU,QAAQ;AAC7D,UAAM,MAAM,KAAK,eAAe,KAAK,YAAY,QAAQ;AACzD,QAAI,MAAM,CAAC,GACT,WAAW,CAAC,GACZ,MAAM,UAAU,gBAAgB,MAAM;AACpC,OAAC;AAAA,QACC;AAAA,QACA;AAAA,MACF,IAAI,KAAK,gBAAgB,GAAG;AAC5B,aAAO;AAAA,IACT;AACF,WAAO,cAAc,GAAG;AACtB,UAAI,KAAK,SAAS;AAChB,aAAK,cAAc,KAAK,aAAW;AAEjC,iBAAO,KAAK,kBAAkB,MAAM,UAAU,SAAS,GAAG;AAAA,QAC5D,GAAG,SAAO;AACR,eAAK,uBAAuB,GAAG,EAAE;AAAA,YAC/B;AAAA,YAAY,aAAW,SAAS,KAAK,OAAO;AAAA,YAAG;AAAA,UACjD;AAAA,QACF,CAAC;AAAA,MACH;AAGA,eAAS,KAAK,IAAI;AAAA,IACpB;AACA,aAAS,QAAQ,CAAAC,UAAQ;AACvB,aAAOA,KAAI;AAAA,IACb,CAAC;AACD,QAAI,KAAK,SAAS;AAChB,WAAK,kBAAkB,KAAK,YAAY,QAAQ,QAAQ;AAAA,IAC1D;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,YAAY,YAAY,MAAM,QAAQ,OAAO,MAAM;AAAA,EAAC,GAAG;AACrD,UAAM,WAAW,KAAK,YAAY;AAClC,QAAI,OAAO,SAAS;AACpB,QAAI,CAAC,MAAM;AACT,WAAK;AAAA,IACP;AACA,aAAS,QAAQ,SAAO;AACtB,YAAM,QAAQ,MAAM;AAClB,aAAK,oBAAoB,YAAY,KAAK,MAAM,QAAQ,MAAM;AAC5D,cAAI,EAAE,QAAQ,GAAG;AACf,iBAAK;AAAA,UACP;AAAA,QACF,CAAC;AAAA,MACH;AAGA,UAAI,KAAK,SAAS;AAChB,aAAK,eAAe,KAAK,KAAK;AAAA,MAChC,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcF;;;AC/iBA,IAAqB,OAArB,MAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,YAAY,KAAK;AAOf,SAAK,MAAM;AAOX,SAAK,KAAK;AACV,UAAM,KAAK,OAAO,UAAU;AAC5B,QAAI,GAAG,QAAQ,MAAM,IAAI,MAAM,GAAG,QAAQ,SAAS,IAAI,IAAI;AACzD,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAI,IAAI,KAAK;AACX,SAAK,OAAO,OAAO,OAAO,CAAC,GAAG;AAAA,MAC5B,WAAW;AAAA,MACX,aAAa;AAAA,MACb,WAAW,CAAC;AAAA,MACZ,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,sBAAsB;AAAA,MACtB,cAAc;AAAA,MACd,YAAY,CAAC;AAAA,MACb,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,qBAAqB,CAAC;AAAA,MACtB,aAAa;AAAA,MACb,QAAQ,MAAM;AAAA,MAAC;AAAA,MACf,WAAW,MAAM;AAAA,MAAC;AAAA,MAClB,UAAU,MAAM;AAAA,MAChB,QAAQ,MAAM;AAAA,MAAC;AAAA,MACf,SAAS;AAAA,MACT,OAAO,OAAO;AAAA,IAChB,GAAG,GAAG;AAAA,EACR;AAAA,EAEA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,WAAW;AAEb,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK,IAAI;AAAA,MACT,KAAK,IAAI;AAAA,MACT,KAAK,IAAI;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,KAAK,QAAQ,SAAS;AACxB,UAAM,MAAM,KAAK,IAAI;AACrB,QAAI,CAAC,KAAK,IAAI,OAAO;AACnB;AAAA,IACF;AACA,QAAI,OAAO,QAAQ,YAAY,OAAO,IAAI,KAAK,MAAM,YAAY;AAC/D,UAAI,KAAK,EAAE,YAAY,GAAG,EAAE;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,KAAK;AAEb,WAAO,IAAI,QAAQ,uCAAuC,MAAM;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,KAAK;AAChB,QAAI,KAAK,IAAI,cAAc,YAAY;AACrC,YAAM,KAAK,qBAAqB,GAAG;AAAA,IACrC;AACA,UAAM,KAAK,UAAU,GAAG;AACxB,QAAI,OAAO,KAAK,KAAK,IAAI,QAAQ,EAAE,QAAQ;AACzC,YAAM,KAAK,qBAAqB,GAAG;AAAA,IACrC;AACA,QAAI,KAAK,IAAI,iBAAiB,KAAK,IAAI,kBAAkB,QAAQ;AAC/D,YAAM,KAAK,yBAAyB,GAAG;AAAA,IACzC;AACA,QAAI,KAAK,IAAI,YAAY;AACvB,YAAM,KAAK,uBAAuB,GAAG;AAAA,IACvC;AACA,UAAM,KAAK,yBAAyB,GAAG;AACvC,QAAI,KAAK,IAAI,iBAAiB,KAAK,IAAI,kBAAkB,QAAQ;AAC/D,YAAM,KAAK,oBAAoB,GAAG;AAAA,IACpC;AACA,QAAI,KAAK,IAAI,cAAc,YAAY;AACrC,YAAM,KAAK,sBAAsB,GAAG;AAAA,IACtC;AACA,UAAM,KAAK,qBAAqB,GAAG;AACnC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,KAAK;AACxB,UAAM,MAAM,KAAK,IAAI,UACnB,OAAO,KAAK,IAAI,gBAAgB,KAAK,KAGrC,oBAAoB,KAAK,IAAI,iBACnB,KAAK,IAAI,kBAAkB,SAAS,OAAW;AAC3D,aAAS,SAAS,KAAK;AACrB,UAAI,IAAI,eAAe,KAAK,GAAG;AAC7B,cAAM,QAAQ,IAAI,KAAK,GACrB,KAAK,KAAK,IAAI,cAAc,aAC1B,KAAK,qBAAqB,KAAK,IAC/B,KAAK,UAAU,KAAK,GACtB,KAAK,KAAK,IAAI,cAAc,aAC1B,KAAK,qBAAqB,KAAK,IAC/B,KAAK,UAAU,KAAK;AACxB,YAAI,OAAO,MAAM,OAAO,IAAI;AAC1B,gBAAM,IAAI;AAAA,YACR,IAAI;AAAA,cACF,IAAI,KAAK,UAAU,EAAE,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;AAAA,cAC5C,KAAK,IAAI;AAAA,YACX;AAAA,YACA,oBACA,IAAI,KAAK,gBAAgB,EAAE,CAAC,IACzB,KAAK,gBAAgB,EAAE,CAAC,MAC3B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,KAAK;AACnB,QAAI,KAAK,IAAI,iBAAiB,KAAK,IAAI,kBAAkB,QAAQ;AAC/D,YAAM,KAAK,yBAAyB,GAAG;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,qBAAqB,KAAK;AAExB,UAAM,IAAI,QAAQ,cAAc,SAAO;AACrC,aAAO,IAAI,OAAO,CAAC,MAAM,OAAO,MAAM;AAAA,IACxC,CAAC;AAED,WAAO,IAAI,QAAQ,cAAc,SAAO;AACtC,aAAO,IAAI,OAAO,CAAC,MAAM,OAAO,MAAM;AAAA,IACxC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB,KAAK;AAIzB,QAAI,SAAS,KAAK,IAAI,cAAc;AACpC,WAAO,IAIJ,QAAQ,WAAW,SAAS,cAAc,MAAM,EAIhD,QAAQ,WAAW,SAAS,eAAe,MAAM;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,yBAAyB,KAAK;AAG5B,WAAO,IAAI,QAAQ,aAAa,CAAC,KAAK,MAAM,aAAa;AAGvD,UAAI,WAAW,SAAS,OAAO,OAAO,CAAC;AACvC,UAAI,UAAU,KAAK,QAAQ,KAAK,aAAa,IAAI;AAC/C,eAAO;AAAA,MACT,OAAO;AACL,eAAO,MAAM;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,oBAAoB,KAAK;AACvB,QAAI,SAAS,CAAC;AACd,UAAM,oBAAoB,KAAK,IAAI;AACnC,QAAI,MAAM,QAAQ,iBAAiB,KAAK,kBAAkB,QAAQ;AAChE,aAAO,KAAK,KAAK,UAAU,kBAAkB,KAAK,EAAE,CAAC,CAAC;AAAA,IACxD;AACA,QAAI,KAAK,IAAI,eAAe;AAK1B,aAAO,KAAK,8BAA8B;AAAA,IAC5C;AACA,WAAO,OAAO,SACZ,IAAI,MAAM,SAAS,EAAE,KAAK,IAAI,OAAO,KAAK,EAAE,CAAC,IAAI,IACjD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,uBAAuB,KAAK;AAC1B,UAAM,OAAO,KAAK,IAAI,gBAAgB,KAAK,KACzC,MAAM,KAAK,IAAI,gBAAgB;AAAA,MAC7B;AAAA,MAA0B;AAAA,MAC1B;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAO;AAAA,MACvB;AAAA,MAAoB;AAAA,MACpB;AAAA,MAAa;AAAA,MAAa;AAAA,MAAM;AAAA,MAAM;AAAA,MACtC;AAAA,MAAQ;AAAA,MAAyB;AAAA,MACjC;AAAA,MAAM;AAAA,MAAM;AAAA,MAAS;AAAA,MACrB;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAoB;AAAA,MACpC;AAAA,MAAW;AAAA,MAAW;AAAA,MAAQ;AAAA,IAChC,IAAI;AAAA,MACF;AAAA,MAAgD;AAAA,MAChD;AAAA,MAAU;AAAA,MACV;AAAA,MAAsB;AAAA,MAAQ;AAAA,MAC9B;AAAA,MAA8C;AAAA,MAC9C;AAAA,MAAc;AAAA,MACd;AAAA,MAAoC;AAAA,MAAkB;AAAA,IACxD;AACF,QAAI,UAAU,CAAC;AACf,QAAI,MAAM,EAAE,EAAE,QAAQ,QAAM;AAC1B,UAAI,MAAM,CAAAC,SAAO;AAEf,YAAIA,KAAI,QAAQ,EAAE,MAAM,IAAI;AAG1B,cAAI,QAAQ,QAAQA,IAAG,IAAI,IAAI;AAC7B,mBAAO;AAAA,UACT;AAGA,gBAAM,IAAI;AAAA,YACR,IAAI,OAAO,IAAIA,IAAG,KAAK,KAAK,IAAI,EAAE;AAAA,YAAG,IAAIA,IAAG;AAAA,UAC9C;AACA,kBAAQ,KAAKA,IAAG;AAAA,QAClB;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,yBAAyB,KAAK;AAC5B,WAAO,IAAI,QAAQ,YAAY,QAAQ;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,qBAAqB,KAAK;AACxB,UAAM,QAAQ;AACd,QAAI,MAAM,KAAK,IAAI,UACjB,MAAM,OAAO,QAAQ,WAAW,MAAM,IAAI,OAC1C,KAAK,OAAO,QAAQ,WAAW,CAAC,IAAI,IAAI,UACxC,SAAS;AACX,OAAG,QAAQ,aAAW;AACpB,gBAAU,IAAI,KAAK,UAAU,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,YAAQ,KAAK;AAAA,MACb,KAAK;AAAA,MACL;AACE,eAAO,MAAM,GAAG;AAAA,MAClB,KAAK;AACH,iBAAS,SAAS,SAAS,SAAS,KAAK,UAAU,KAAK;AACxD,eAAO,QAAQ,MAAM,KAAK,GAAG,KAAK,MAAM;AAAA,MAC1C,KAAK;AACH,eAAO,SAAS,MAAM,KAAK,GAAG,YAAY,MAAM;AAAA,IAClD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,qBAAqB,IAAI;AACvB,QAAI,QAAQ,CAAC;AACb,OAAG,QAAQ,QAAM;AACf,UAAI,CAAC,KAAK,IAAI,oBAAoB;AAChC,YAAI,GAAG,KAAK,KAAK,MAAM,QAAQ,EAAE,MAAM,IAAI;AACzC,gBAAM,KAAK,EAAE;AAAA,QACf;AAAA,MACF,OAAO;AACL,WAAG,MAAM,GAAG,EAAE,QAAQ,gBAAc;AAClC,cAAI,WAAW,KAAK,KAAK,MAAM,QAAQ,UAAU,MAAM,IAAI;AACzD,kBAAM,KAAK,UAAU;AAAA,UACvB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO;AAAA;AAAA,MAEL,YAAY,MAAM,KAAK,CAAC,GAAG,MAAM;AAC/B,eAAO,EAAE,SAAS,EAAE;AAAA,MACtB,CAAC;AAAA,MACD,UAAU,MAAM;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,OAAO;AAGf,WAAO,OAAO,WAAW,KAAK,CAAC,KAAK;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,YAAY,OAAO;AAIjB,QACE,CAAC,MAAM,QAAQ,KAAK,KACpB,OAAO,UAAU,SAAS,KAAM,MAAM,CAAC,CAAE,MAAM,mBAC/C;AACA,WAAK,IAAI,mDAAmD;AAC5D,WAAK,IAAI,QAAQ,KAAK;AACtB,aAAO,CAAC;AAAA,IACV;AACA,UAAM,QAAQ,CAAC;AACf,QAAI,OAAO;AACX,UAGG,KAAK,CAAC,GAAG,MAAM;AACd,aAAO,EAAE,QAAQ,EAAE;AAAA,IACrB,CAAC,EACA,QAAQ,UAAQ;AACf,UAAI,EAAC,OAAO,KAAK,MAAK,IAAI,KAAK,2BAA2B,MAAM,IAAI;AACpE,UAAI,OAAO;AAET,aAAK,QAAQ;AACb,aAAK,SAAS,MAAM;AACpB,cAAM,KAAK,IAAI;AACf,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACH,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,2BAA2B,OAAO,MAAM;AACtC,QAAI,OAAO,KACT,QAAQ;AACV,QAAI,SAAS,OAAO,MAAM,UAAU,aAAa;AAC/C,cAAQ,SAAS,MAAM,OAAO,EAAE;AAChC,YAAM,QAAQ,SAAS,MAAM,QAAQ,EAAE;AAEvC,UACE,KAAK,UAAU,MAAM,KAAK,KAC1B,KAAK,UAAU,MAAM,MAAM,KAC3B,MAAM,OAAO,KACb,MAAM,QAAQ,GACd;AACA,gBAAQ;AAAA,MACV,OAAO;AACL,aAAK;AAAA,UACH,0CACa,KAAK,UAAU,KAAK,CAAC;AAAA,QACpC;AACA,aAAK,IAAI,QAAQ,KAAK;AAAA,MACxB;AAAA,IACF,OAAO;AACL,WAAK,IAAI,2BAA2B,KAAK,UAAU,KAAK,CAAC,EAAE;AAC3D,WAAK,IAAI,QAAQ,KAAK;AAAA,IACxB;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,sBAAsB,OAAO,gBAAgB,QAAQ;AACnD,QAAI,KACF,QAAQ,MAER,MAAM,OAAO,QAEb,SAAS,iBAAiB,KAC1B,QAAQ,SAAS,MAAM,OAAO,EAAE,IAAI;AAEtC,YAAQ,QAAQ,MAAM,MAAM;AAC5B,UAAM,QAAQ,SAAS,MAAM,QAAQ,EAAE;AACvC,QAAI,MAAM,KAAK;AACb,YAAM;AACN,WAAK,IAAI,mDAAmD,GAAG,EAAE;AAAA,IACnE;AACA,QAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,QAAQ,OAAO,MAAM,KAAK;AAC5D,cAAQ;AACR,WAAK,IAAI,kBAAkB,KAAK,UAAU,KAAK,CAAC,EAAE;AAClD,WAAK,IAAI,QAAQ,KAAK;AAAA,IACxB,WAAW,OAAO,UAAU,OAAO,GAAG,EAAE,QAAQ,QAAQ,EAAE,MAAM,IAAI;AAClE,cAAQ;AAER,WAAK,IAAI,qCAAoC,KAAK,UAAU,KAAK,CAAC;AAClE,WAAK,IAAI,QAAQ,KAAK;AAAA,IACxB;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBA,aAAa,IAAI;AACf,QAAI,MAAM,IACR,QAAQ,CAAC;AACX,SAAK,SAAS,YAAY,WAAW,WAAW,UAAQ;AACtD,YAAM,KAAK;AAAA,QACT,OAAO,IAAI;AAAA,QACX,MAAM,OAAO,KAAK,aAAa;AAAA,QAC/B;AAAA,MACF,CAAC;AAAA,IACH,GAAG,UAAQ;AACT,UAAI,KAAK,eAAe,KAAK,UAAU,GAAG;AACxC,eAAO,WAAW;AAAA,MACpB,OAAO;AACL,eAAO,WAAW;AAAA,MACpB;AAAA,IACF,GAAG,MAAM;AACP,SAAG;AAAA,QACD,OAAO;AAAA,QACP;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,IAAI;AACjB,WAAO,YAAY,QAAQ,IAAI,KAAK,IAAI,QAAQ,OAAO;AAAA;AAAA,MAErD;AAAA,MAAU;AAAA,MAAS;AAAA,MAAS;AAAA,MAAQ;AAAA,IACtC,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,oBAAoB,MAAM,OAAO,KAAK;AACpC,UAAM,MAAM,CAAC,KAAK,IAAI,UAAU,SAAS,KAAK,IAAI,SAChD,YAAY,KAAK,UAAU,KAAK,GAChC,MAAM,UAAU,UAAU,MAAM,KAAK;AACvC,QAAI,OAAO,SAAS,cAAc,GAAG;AACrC,SAAK,aAAa,eAAe,MAAM;AACvC,QAAI,KAAK,IAAI,WAAW;AACtB,WAAK,aAAa,SAAS,KAAK,IAAI,SAAS;AAAA,IAC/C;AACA,SAAK,cAAc,UAAU;AAC7B,cAAU,WAAW,aAAa,MAAM,SAAS;AACjD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoCA,0BAA0B,MAAM,OAAO,KAAK,UAAU,QAAQ;AAE5D,SAAK,MAAM,MAAM,CAAC,GAAG,MAAM;AACzB,YAAM,OAAO,KAAK,MAAM,IAAI,CAAC;AAC7B,UAAI,OAAO,SAAS,eAAe,KAAK,QAAQ,OAAO;AACrD,YAAI,CAAC,SAAS,EAAE,IAAI,GAAG;AACrB,iBAAO;AAAA,QACT;AAEA,cAAM,IAAI,QAAQ,EAAE,OAClB,KAAK,MAAM,EAAE,MAAM,EAAE,MAAM,OAAO,EAAE,OACpC,WAAW,KAAK,MAAM,OAAO,GAAG,EAAE,KAAK,GACvC,SAAS,KAAK,MAAM,OAAO,IAAI,EAAE,KAAK;AACxC,UAAE,OAAO,KAAK,oBAAoB,EAAE,MAAM,GAAG,CAAC;AAI9C,aAAK,QAAQ,WAAW;AACxB,aAAK,MAAM,QAAQ,CAAC,GAAG,MAAM;AAC3B,cAAI,KAAK,GAAG;AACV,gBAAI,KAAK,MAAM,CAAC,EAAE,QAAQ,KAAK,MAAM,GAAG;AACtC,mBAAK,MAAM,CAAC,EAAE,SAAS;AAAA,YACzB;AACA,iBAAK,MAAM,CAAC,EAAE,OAAO;AAAA,UACvB;AAAA,QACF,CAAC;AACD,eAAO;AACP,eAAO,EAAE,KAAK,iBAAiB,EAAE,KAAK;AACtC,YAAI,MAAM,EAAE,KAAK;AACf,kBAAQ,EAAE;AAAA,QACZ,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BA,YAAY,OAAO,cAAc,UAAU,QAAQ,OAAO;AACxD,UAAM,WAAW,iBAAiB,IAAI,IAAI,eAAe;AACzD,SAAK,aAAa,UAAQ;AACxB,WAAK,MAAM,QAAQ,UAAQ;AACzB,eAAO,KAAK;AACZ,YAAI;AACJ,gBACG,QAAQ,MAAM,KAAK,KAAK,WAAW,OAAO,QAC3C,MAAM,QAAQ,MAAM,IACpB;AACA,cAAI,CAAC,SAAS,MAAM,QAAQ,GAAG,IAAI,GAAG;AACpC;AAAA,UACF;AACA,cAAI,MAAM,MAAM;AAChB,cAAI,aAAa,GAAG;AAClB,qBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,qBAAO,MAAM,CAAC,EAAE;AAAA,YAClB;AAAA,UACF;AACA,iBAAO,KAAK;AAAA,YACV;AAAA,YACA;AAAA,YACA,MAAM,MAAM,QAAQ,EAAE;AAAA,UACxB;AACA,iBAAO,KAAK,eAAe;AAG3B,gBAAM,YAAY;AAAA,QACpB;AAAA,MACF,CAAC;AACD,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BA,0BAA0B,OAAO,cAAc,UAAU,QAAQ,OAAO;AACtE,UAAM,WAAW,iBAAiB,IAAI,IAAI,eAAe;AACzD,SAAK,aAAa,UAAQ;AACxB,UAAI;AACJ,cACG,QAAQ,MAAM,KAAK,KAAK,KAAK,OAAO,QACrC,MAAM,QAAQ,MAAM,IACpB;AAEA,YAAI,QAAQ,MAAM;AAClB,YAAI,aAAa,GAAG;AAClB,mBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,qBAAS,MAAM,CAAC,EAAE;AAAA,UACpB;AAAA,QACF;AACA,cAAM,MAAM,QAAQ,MAAM,QAAQ,EAAE;AAIpC,aAAK,0BAA0B,MAAM,OAAO,KAAK,UAAQ;AACvD,iBAAO,SAAS,MAAM,QAAQ,GAAG,IAAI;AAAA,QACvC,GAAG,CAAC,MAAM,cAAc;AACtB,gBAAM,YAAY;AAClB,iBAAO,IAAI;AAAA,QACb,CAAC;AAAA,MACH;AACA,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BA,mBAAmB,QAAQ,UAAU,QAAQ,OAAO;AAClD,SAAK,aAAa,UAAQ;AACxB,YAAM,iBAAiB,KAAK,MAAM;AAClC,aAAO,QAAQ,CAAC,OAAO,YAAY;AACjC,YAAI,EAAC,OAAO,KAAK,MAAK,IAAI,KAAK;AAAA,UAC7B;AAAA,UACA;AAAA,UACA,KAAK;AAAA,QACP;AACA,YAAI,OAAO;AACT,eAAK,0BAA0B,MAAM,OAAO,KAAK,UAAQ;AACvD,mBAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA,KAAK,MAAM,UAAU,OAAO,GAAG;AAAA,cAC/B;AAAA,YACF;AAAA,UACF,GAAG,UAAQ;AACT,mBAAO,MAAM,KAAK;AAAA,UACpB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,MAAM;AAClB,UAAM,SAAS,KAAK;AACpB,QAAI,UAAU,SAAS,uBAAuB;AAC9C,WAAO,KAAK,YAAY;AACtB,cAAQ,YAAY,KAAK,YAAY,KAAK,UAAU,CAAC;AAAA,IACvD;AACA,WAAO,aAAa,SAAS,IAAI;AACjC,QAAI,CAAC,KAAK,IAAI;AACZ,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,WAAK,kBAAkB,MAAM;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,kBAAkB,MAAM;AACtB,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,QAAI,KAAK,aAAa,GAAG;AACvB,aAAO,KAAK,eAAe,KAAK,YAAY,aAAa,GAAG;AAC1D,aAAK,aAAa,KAAK,YAAY;AACnC,aAAK,WAAW,YAAY,KAAK,WAAW;AAAA,MAC9C;AAAA,IACF,OAAO;AACL,WAAK,kBAAkB,KAAK,UAAU;AAAA,IACxC;AACA,SAAK,kBAAkB,KAAK,WAAW;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoDA,WAAW,QAAQ,KAAK;AACtB,SAAK,MAAM;AACX,SAAK,IAAI,8BAA8B,MAAM,GAAG;AAChD,QAAI,eAAe,GACjB,KAAK;AACP,UAAM,SAAS,aAAW;AACxB;AACA,WAAK,IAAI,KAAK,OAAO;AAAA,IACvB;AACA,QAAI,KAAK,IAAI,gBAAgB;AAC3B,WAAK;AAAA,IACP;AACA,SAAK,EAAE,EAAE,QAAQ,KAAK,IAAI,cAAc,CAAC,OAAO,SAAS;AACvD,aAAO,KAAK,IAAI,OAAO,MAAM,OAAO,YAAY;AAAA,IAClD,GAAG,QAAQ,MAAM;AACf,UAAI,iBAAiB,GAAG;AACtB,aAAK,IAAI,QAAQ,MAAM;AAAA,MACzB;AACA,WAAK,IAAI,KAAK,YAAY;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsHA,KAAK,IAAI,KAAK;AACZ,SAAK,MAAM;AACX,QAAI,eAAe,GACjB,KAAK;AAEP,UAAM;AAAA,MACF,UAAU;AAAA,MACV,QAAQ;AAAA,IACV,IAAI,KAAK,qBAAqB,OAAO,OAAO,WAAW,CAAC,EAAE,IAAI,EAAE,GAChE,OAAO,KAAK,IAAI,gBAAgB,KAAK,KACrC,UAAU,QAAM;AACd,UAAI,QAAQ,IAAI,OAAO,KAAK,aAAa,EAAE,GAAG,KAAK,IAAI,EAAE,GACvD,UAAU;AACZ,WAAK,IAAI,8BAA8B,KAAK,GAAG;AAC/C,WAAK,EAAE,EAAE,OAAO,GAAG,CAAC,MAAM,SAAS;AACjC,eAAO,KAAK,IAAI,OAAO,MAAM,IAAI,cAAc,OAAO;AAAA,MACxD,GAAG,aAAW;AACZ;AACA;AACA,aAAK,IAAI,KAAK,OAAO;AAAA,MACvB,GAAG,MAAM;AACP,YAAI,YAAY,GAAG;AACjB,eAAK,IAAI,QAAQ,EAAE;AAAA,QACrB;AACA,YAAI,MAAM,WAAW,CAAC,MAAM,IAAI;AAC9B,eAAK,IAAI,KAAK,YAAY;AAAA,QAC5B,OAAO;AACL,kBAAQ,MAAM,MAAM,QAAQ,EAAE,IAAI,CAAC,CAAC;AAAA,QACtC;AAAA,MACF,CAAC;AAAA,IACH;AACF,QAAI,KAAK,IAAI,gBAAgB;AAC3B,WAAK;AAAA,IACP;AACA,QAAI,aAAa,GAAG;AAClB,WAAK,IAAI,KAAK,YAAY;AAAA,IAC5B,OAAO;AACL,cAAQ,MAAM,CAAC,CAAC;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuCA,WAAW,WAAW,KAAK;AACzB,SAAK,MAAM;AACX,QAAI,eAAe,GACjB,SAAS,KAAK,YAAY,SAAS;AACrC,QAAI,UAAU,OAAO,QAAQ;AAC3B,WAAK;AAAA,QACH,iDACA,KAAK,UAAU,MAAM;AAAA,MACvB;AACA,WAAK;AAAA,QACH;AAAA,QAAQ,CAAC,MAAM,OAAO,OAAO,YAAY;AACvC,iBAAO,KAAK,IAAI,OAAO,MAAM,OAAO,OAAO,OAAO;AAAA,QACpD;AAAA,QAAG,CAAC,SAAS,UAAU;AACrB;AACA,eAAK,IAAI,KAAK,SAAS,KAAK;AAAA,QAC9B;AAAA,QAAG,MAAM;AACP,eAAK,IAAI,KAAK,YAAY;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,IAAI,KAAK,YAAY;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KAAK;AACV,SAAK,MAAM;AACX,QAAI,MAAM,KAAK,IAAI,UAAU,KAAK,IAAI,UAAU;AAChD,WAAO;AACP,QAAI,KAAK,IAAI,WAAW;AACtB,aAAO,IAAI,KAAK,IAAI,SAAS;AAAA,IAC/B;AACA,SAAK,IAAI,qBAAqB,GAAG,GAAG;AACpC,SAAK,SAAS,YAAY,WAAW,cAAc,UAAQ;AACzD,WAAK,cAAc,IAAI;AAAA,IACzB,GAAG,UAAQ;AACT,YAAM,aAAa,YAAY,QAAQ,MAAM,GAAG,GAC9C,iBAAiB,KAAK,eAAe,IAAI;AAC3C,UAAI,CAAC,cAAc,gBAAgB;AACjC,eAAO,WAAW;AAAA,MACpB,OAAO;AACL,eAAO,WAAW;AAAA,MACpB;AAAA,IACF,GAAG,KAAK,IAAI,IAAI;AAAA,EAClB;AACF;;;AC/uCe,SAARC,MAAsB,KAAK;AAChC,QAAM,WAAW,IAAI,KAAO,GAAG;AAC/B,OAAK,OAAO,CAAC,IAAI,QAAQ;AACvB,aAAS,KAAK,IAAI,GAAG;AACrB,WAAO;AAAA,EACT;AACA,OAAK,aAAa,CAAC,IAAI,QAAQ;AAC7B,aAAS,WAAW,IAAI,GAAG;AAC3B,WAAO;AAAA,EACT;AACA,OAAK,aAAa,CAAC,IAAI,QAAQ;AAC7B,aAAS,WAAW,IAAI,GAAG;AAC3B,WAAO;AAAA,EACT;AACA,OAAK,SAAS,CAAC,QAAQ;AACrB,aAAS,OAAO,GAAG;AACnB,WAAO;AAAA,EACT;AACA,SAAO;AACT;", "names": ["ctx", "ifr", "node", "dct", "<PERSON>"]}