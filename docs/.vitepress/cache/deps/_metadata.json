{"hash": "90568fdf", "configHash": "b99bab2f", "lockfileHash": "71ec11f0", "browserHash": "8d987871", "optimized": {"vue": {"src": "../../../../node_modules/vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "9e5e0303", "needsInterop": false}, "vitepress > @vue/devtools-api": {"src": "../../../../node_modules/vitepress/node_modules/@vue/devtools-api/dist/index.js", "file": "vitepress___@vue_devtools-api.js", "fileHash": "3418b915", "needsInterop": false}, "vitepress > @vueuse/core": {"src": "../../../../node_modules/@vueuse/core/index.mjs", "file": "vitepress___@vueuse_core.js", "fileHash": "a373ff2c", "needsInterop": false}, "@braintree/sanitize-url": {"src": "../../../../node_modules/@braintree/sanitize-url/dist/index.js", "file": "@braintree_sanitize-url.js", "fileHash": "cb210b54", "needsInterop": true}, "dayjs": {"src": "../../../../node_modules/dayjs/dayjs.min.js", "file": "dayjs.js", "fileHash": "f89eda84", "needsInterop": true}, "debug": {"src": "../../../../node_modules/debug/src/browser.js", "file": "debug.js", "fileHash": "40006a79", "needsInterop": true}, "cytoscape-cose-bilkent": {"src": "../../../../node_modules/cytoscape-cose-bilkent/cytoscape-cose-bilkent.js", "file": "cytoscape-cose-bilkent.js", "fileHash": "275b2fef", "needsInterop": true}, "cytoscape": {"src": "../../../../node_modules/cytoscape/dist/cytoscape.esm.mjs", "file": "cytoscape.js", "fileHash": "dd8706af", "needsInterop": false}, "vitepress > @vueuse/integrations/useFocusTrap": {"src": "../../../../node_modules/@vueuse/integrations/useFocusTrap.mjs", "file": "vitepress___@vueuse_integrations_useFocusTrap.js", "fileHash": "6ff2c57e", "needsInterop": false}, "vitepress > mark.js/src/vanilla.js": {"src": "../../../../node_modules/mark.js/src/vanilla.js", "file": "vitepress___mark__js_src_vanilla__js.js", "fileHash": "ad58d730", "needsInterop": false}, "vitepress > minisearch": {"src": "../../../../node_modules/minisearch/dist/es/index.js", "file": "vitepress___minisearch.js", "fileHash": "3b080c6b", "needsInterop": false}}, "chunks": {"chunk-YJ6QP2VR": {"file": "chunk-YJ6QP2VR.js"}, "chunk-LW4I4DCF": {"file": "chunk-LW4I4DCF.js"}, "chunk-BUSYA2B4": {"file": "chunk-BUSYA2B4.js"}}}