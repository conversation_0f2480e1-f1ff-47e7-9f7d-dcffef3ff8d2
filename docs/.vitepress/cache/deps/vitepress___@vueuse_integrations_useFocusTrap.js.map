{"version": 3, "sources": ["../../../../node_modules/tabbable/src/index.js", "../../../../node_modules/focus-trap/index.js", "../../../../node_modules/@vueuse/integrations/useFocusTrap.mjs"], "sourcesContent": ["// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nconst candidateSelectors = [\n  'input:not([inert])',\n  'select:not([inert])',\n  'textarea:not([inert])',\n  'a[href]:not([inert])',\n  'button:not([inert])',\n  '[tabindex]:not(slot):not([inert])',\n  'audio[controls]:not([inert])',\n  'video[controls]:not([inert])',\n  '[contenteditable]:not([contenteditable=\"false\"]):not([inert])',\n  'details>summary:first-of-type:not([inert])',\n  'details:not([inert])',\n];\nconst candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');\n\nconst NoElement = typeof Element === 'undefined';\n\nconst matches = NoElement\n  ? function () {}\n  : Element.prototype.matches ||\n    Element.prototype.msMatchesSelector ||\n    Element.prototype.webkitMatchesSelector;\n\nconst getRootNode =\n  !NoElement && Element.prototype.getRootNode\n    ? (element) => element?.getRootNode?.()\n    : (element) => element?.ownerDocument;\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nconst isInert = function (node, lookUp = true) {\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  const inertAtt = node?.getAttribute?.('inert');\n  const inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  const result = inert || (lookUp && node && isInert(node.parentNode)); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nconst isContentEditable = function (node) {\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  const attValue = node?.getAttribute?.('contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nconst getCandidates = function (el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n\n  let candidates = Array.prototype.slice.apply(\n    el.querySelectorAll(candidateSelector)\n  );\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nconst getCandidatesIteratively = function (\n  elements,\n  includeContainer,\n  options\n) {\n  const candidates = [];\n  const elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    const element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      const assigned = element.assignedElements();\n      const content = assigned.length ? assigned : element.children;\n      const nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push(...nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates,\n        });\n      }\n    } else {\n      // check candidate element\n      const validCandidate = matches.call(element, candidateSelector);\n      if (\n        validCandidate &&\n        options.filter(element) &&\n        (includeContainer || !elements.includes(element))\n      ) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      const shadowRoot =\n        element.shadowRoot ||\n        // check for an undisclosed shadow\n        (typeof options.getShadowRoot === 'function' &&\n          options.getShadowRoot(element));\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      const validShadowRoot =\n        !isInert(shadowRoot, false) &&\n        (!options.shadowRootFilter || options.shadowRootFilter(element));\n\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        const nestedCandidates = getCandidatesIteratively(\n          shadowRoot === true ? element.children : shadowRoot.children,\n          true,\n          options\n        );\n\n        if (options.flatten) {\n          candidates.push(...nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: nestedCandidates,\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift(...element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nconst hasTabIndex = function (node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nconst getTabIndex = function (node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if (\n      (/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) ||\n        isContentEditable(node)) &&\n      !hasTabIndex(node)\n    ) {\n      return 0;\n    }\n  }\n\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nconst getSortOrderTabIndex = function (node, isScope) {\n  const tabIndex = getTabIndex(node);\n\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n\n  return tabIndex;\n};\n\nconst sortOrderedTabbables = function (a, b) {\n  return a.tabIndex === b.tabIndex\n    ? a.documentOrder - b.documentOrder\n    : a.tabIndex - b.tabIndex;\n};\n\nconst isInput = function (node) {\n  return node.tagName === 'INPUT';\n};\n\nconst isHiddenInput = function (node) {\n  return isInput(node) && node.type === 'hidden';\n};\n\nconst isDetailsWithSummary = function (node) {\n  const r =\n    node.tagName === 'DETAILS' &&\n    Array.prototype.slice\n      .apply(node.children)\n      .some((child) => child.tagName === 'SUMMARY');\n  return r;\n};\n\nconst getCheckedRadio = function (nodes, form) {\n  for (let i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\n\nconst isTabbableRadio = function (node) {\n  if (!node.name) {\n    return true;\n  }\n  const radioScope = node.form || getRootNode(node);\n  const queryRadios = function (name) {\n    return radioScope.querySelectorAll(\n      'input[type=\"radio\"][name=\"' + name + '\"]'\n    );\n  };\n\n  let radioSet;\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.CSS !== 'undefined' &&\n    typeof window.CSS.escape === 'function'\n  ) {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error(\n        'Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s',\n        err.message\n      );\n      return false;\n    }\n  }\n\n  const checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\n\nconst isRadio = function (node) {\n  return isInput(node) && node.type === 'radio';\n};\n\nconst isNonTabbableRadio = function (node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nconst isNodeAttached = function (node) {\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  let nodeRoot = node && getRootNode(node);\n  let nodeRootHost = nodeRoot?.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  let attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    attached = !!(\n      nodeRootHost?.ownerDocument?.contains(nodeRootHost) ||\n      node?.ownerDocument?.contains(node)\n    );\n\n    while (!attached && nodeRootHost) {\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = nodeRoot?.host;\n      attached = !!nodeRootHost?.ownerDocument?.contains(nodeRootHost);\n    }\n  }\n\n  return attached;\n};\n\nconst isZeroArea = function (node) {\n  const { width, height } = node.getBoundingClientRect();\n  return width === 0 && height === 0;\n};\nconst isHidden = function (node, { displayCheck, getShadowRoot }) {\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n\n  const isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  const nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n\n  if (\n    !displayCheck ||\n    displayCheck === 'full' ||\n    displayCheck === 'legacy-full'\n  ) {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      const originalNode = node;\n      while (node) {\n        const parentElement = node.parentElement;\n        const rootNode = getRootNode(node);\n        if (\n          parentElement &&\n          !parentElement.shadowRoot &&\n          getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nconst isDisabledFromFieldset = function (node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    let parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (let i = 0; i < parentNode.children.length; i++) {\n          const child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *')\n              ? true\n              : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\n\nconst isNodeMatchingSelectorFocusable = function (options, node) {\n  if (\n    node.disabled ||\n    // we must do an inert look up to filter out any elements inside an inert ancestor\n    //  because we're limited in the type of selectors we can use in JSDom (see related\n    //  note related to `candidateSelectors`)\n    isInert(node) ||\n    isHiddenInput(node) ||\n    isHidden(node, options) ||\n    // For a details element with a summary, the summary element gets the focus\n    isDetailsWithSummary(node) ||\n    isDisabledFromFieldset(node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isNodeMatchingSelectorTabbable = function (options, node) {\n  if (\n    isNonTabbableRadio(node) ||\n    getTabIndex(node) < 0 ||\n    !isNodeMatchingSelectorFocusable(options, node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isValidShadowRootTabbable = function (shadowHostNode) {\n  const tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nconst sortByOrder = function (candidates) {\n  const regularTabbables = [];\n  const orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    const isScope = !!item.scopeParent;\n    const element = isScope ? item.scopeParent : item;\n    const candidateTabindex = getSortOrderTabIndex(element, isScope);\n    const elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope\n        ? regularTabbables.push(...elements)\n        : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements,\n      });\n    }\n  });\n\n  return orderedTabbables\n    .sort(sortOrderedTabbables)\n    .reduce((acc, sortable) => {\n      sortable.isScope\n        ? acc.push(...sortable.content)\n        : acc.push(sortable.content);\n      return acc;\n    }, [])\n    .concat(regularTabbables);\n};\n\nconst tabbable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorTabbable.bind(null, options),\n        flatten: false,\n        getShadowRoot: options.getShadowRoot,\n        shadowRootFilter: isValidShadowRootTabbable,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorTabbable.bind(null, options)\n    );\n  }\n  return sortByOrder(candidates);\n};\n\nconst focusable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorFocusable.bind(null, options),\n        flatten: true,\n        getShadowRoot: options.getShadowRoot,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorFocusable.bind(null, options)\n    );\n  }\n\n  return candidates;\n};\n\nconst isTabbable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\n\nconst focusableCandidateSelector = /* #__PURE__ */ candidateSelectors\n  .concat('iframe')\n  .join(',');\n\nconst isFocusable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { tabbable, focusable, isTabbable, isFocusable, getTabIndex };\n", "import {\n  tabbable,\n  focusable,\n  isFocusable,\n  isTabbable,\n  getTabIndex,\n} from 'tabbable';\n\nconst activeFocusTraps = {\n  activateTrap(trapStack, trap) {\n    if (trapStack.length > 0) {\n      const activeTrap = trapStack[trapStack.length - 1];\n      if (activeTrap !== trap) {\n        activeTrap.pause();\n      }\n    }\n\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex === -1) {\n      trapStack.push(trap);\n    } else {\n      // move this existing trap to the front of the queue\n      trapStack.splice(trapIndex, 1);\n      trapStack.push(trap);\n    }\n  },\n\n  deactivateTrap(trapStack, trap) {\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex !== -1) {\n      trapStack.splice(trapIndex, 1);\n    }\n\n    if (trapStack.length > 0) {\n      trapStack[trapStack.length - 1].unpause();\n    }\n  },\n};\n\nconst isSelectableInput = function (node) {\n  return (\n    node.tagName &&\n    node.tagName.toLowerCase() === 'input' &&\n    typeof node.select === 'function'\n  );\n};\n\nconst isEscapeEvent = function (e) {\n  return e?.key === 'Escape' || e?.key === 'Esc' || e?.keyCode === 27;\n};\n\nconst isTabEvent = function (e) {\n  return e?.key === 'Tab' || e?.keyCode === 9;\n};\n\n// checks for TAB by default\nconst isKeyForward = function (e) {\n  return isTabEvent(e) && !e.shiftKey;\n};\n\n// checks for SHIFT+TAB by default\nconst isKeyBackward = function (e) {\n  return isTabEvent(e) && e.shiftKey;\n};\n\nconst delay = function (fn) {\n  return setTimeout(fn, 0);\n};\n\n/**\n * Get an option's value when it could be a plain value, or a handler that provides\n *  the value.\n * @param {*} value Option's value to check.\n * @param {...*} [params] Any parameters to pass to the handler, if `value` is a function.\n * @returns {*} The `value`, or the handler's returned value.\n */\nconst valueOrHandler = function (value, ...params) {\n  return typeof value === 'function' ? value(...params) : value;\n};\n\nconst getActualTarget = function (event) {\n  // NOTE: If the trap is _inside_ a shadow DOM, event.target will always be the\n  //  shadow host. However, event.target.composedPath() will be an array of\n  //  nodes \"clicked\" from inner-most (the actual element inside the shadow) to\n  //  outer-most (the host HTML document). If we have access to composedPath(),\n  //  then use its first element; otherwise, fall back to event.target (and\n  //  this only works for an _open_ shadow DOM; otherwise,\n  //  composedPath()[0] === event.target always).\n  return event.target.shadowRoot && typeof event.composedPath === 'function'\n    ? event.composedPath()[0]\n    : event.target;\n};\n\n// NOTE: this must be _outside_ `createFocusTrap()` to make sure all traps in this\n//  current instance use the same stack if `userOptions.trapStack` isn't specified\nconst internalTrapStack = [];\n\nconst createFocusTrap = function (elements, userOptions) {\n  // SSR: a live trap shouldn't be created in this type of environment so this\n  //  should be safe code to execute if the `document` option isn't specified\n  const doc = userOptions?.document || document;\n\n  const trapStack = userOptions?.trapStack || internalTrapStack;\n\n  const config = {\n    returnFocusOnDeactivate: true,\n    escapeDeactivates: true,\n    delayInitialFocus: true,\n    isKeyForward,\n    isKeyBackward,\n    ...userOptions,\n  };\n\n  const state = {\n    // containers given to createFocusTrap()\n    // @type {Array<HTMLElement>}\n    containers: [],\n\n    // list of objects identifying tabbable nodes in `containers` in the trap\n    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap\n    //  is active, but the trap should never get to a state where there isn't at least one group\n    //  with at least one tabbable node in it (that would lead to an error condition that would\n    //  result in an error being thrown)\n    // @type {Array<{\n    //   container: HTMLElement,\n    //   tabbableNodes: Array<HTMLElement>, // empty if none\n    //   focusableNodes: Array<HTMLElement>, // empty if none\n    //   posTabIndexesFound: boolean,\n    //   firstTabbableNode: HTMLElement|undefined,\n    //   lastTabbableNode: HTMLElement|undefined,\n    //   firstDomTabbableNode: HTMLElement|undefined,\n    //   lastDomTabbableNode: HTMLElement|undefined,\n    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined\n    // }>}\n    containerGroups: [], // same order/length as `containers` list\n\n    // references to objects in `containerGroups`, but only those that actually have\n    //  tabbable nodes in them\n    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__\n    //  the same length\n    tabbableGroups: [],\n\n    nodeFocusedBeforeActivation: null,\n    mostRecentlyFocusedNode: null,\n    active: false,\n    paused: false,\n\n    // timer ID for when delayInitialFocus is true and initial focus in this trap\n    //  has been delayed during activation\n    delayInitialFocusTimer: undefined,\n\n    // the most recent KeyboardEvent for the configured nav key (typically [SHIFT+]TAB), if any\n    recentNavEvent: undefined,\n  };\n\n  let trap; // eslint-disable-line prefer-const -- some private functions reference it, and its methods reference private functions, so we must declare here and define later\n\n  /**\n   * Gets a configuration option value.\n   * @param {Object|undefined} configOverrideOptions If true, and option is defined in this set,\n   *  value will be taken from this object. Otherwise, value will be taken from base configuration.\n   * @param {string} optionName Name of the option whose value is sought.\n   * @param {string|undefined} [configOptionName] Name of option to use __instead of__ `optionName`\n   *  IIF `configOverrideOptions` is not defined. Otherwise, `optionName` is used.\n   */\n  const getOption = (configOverrideOptions, optionName, configOptionName) => {\n    return configOverrideOptions &&\n      configOverrideOptions[optionName] !== undefined\n      ? configOverrideOptions[optionName]\n      : config[configOptionName || optionName];\n  };\n\n  /**\n   * Finds the index of the container that contains the element.\n   * @param {HTMLElement} element\n   * @param {Event} [event] If available, and `element` isn't directly found in any container,\n   *  the event's composed path is used to see if includes any known trap containers in the\n   *  case where the element is inside a Shadow DOM.\n   * @returns {number} Index of the container in either `state.containers` or\n   *  `state.containerGroups` (the order/length of these lists are the same); -1\n   *  if the element isn't found.\n   */\n  const findContainerIndex = function (element, event) {\n    const composedPath =\n      typeof event?.composedPath === 'function'\n        ? event.composedPath()\n        : undefined;\n    // NOTE: search `containerGroups` because it's possible a group contains no tabbable\n    //  nodes, but still contains focusable nodes (e.g. if they all have `tabindex=-1`)\n    //  and we still need to find the element in there\n    return state.containerGroups.findIndex(\n      ({ container, tabbableNodes }) =>\n        container.contains(element) ||\n        // fall back to explicit tabbable search which will take into consideration any\n        //  web components if the `tabbableOptions.getShadowRoot` option was used for\n        //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't\n        //  look inside web components even if open)\n        composedPath?.includes(container) ||\n        tabbableNodes.find((node) => node === element)\n    );\n  };\n\n  /**\n   * Gets the node for the given option, which is expected to be an option that\n   *  can be either a DOM node, a string that is a selector to get a node, `false`\n   *  (if a node is explicitly NOT given), or a function that returns any of these\n   *  values.\n   * @param {string} optionName\n   * @param {Object} options\n   * @param {boolean} [options.hasFallback] True if the option could be a selector string\n   *  and the option allows for a fallback scenario in the case where the selector is\n   *  valid but does not match a node (i.e. the queried node doesn't exist in the DOM).\n   * @param {Array} [options.params] Params to pass to the option if it's a function.\n   * @returns {undefined | null | false | HTMLElement | SVGElement} Returns\n   *  `undefined` if the option is not specified; `null` if the option didn't resolve\n   *  to a node but `options.hasFallback=true`, `false` if the option resolved to `false`\n   *  (node explicitly not given); otherwise, the resolved DOM node.\n   * @throws {Error} If the option is set, not `false`, and is not, or does not\n   *  resolve to a node, unless the option is a selector string and `options.hasFallback=true`.\n   */\n  const getNodeForOption = function (\n    optionName,\n    { hasFallback = false, params = [] } = {}\n  ) {\n    let optionValue = config[optionName];\n\n    if (typeof optionValue === 'function') {\n      optionValue = optionValue(...params);\n    }\n\n    if (optionValue === true) {\n      optionValue = undefined; // use default value\n    }\n\n    if (!optionValue) {\n      if (optionValue === undefined || optionValue === false) {\n        return optionValue;\n      }\n      // else, empty string (invalid), null (invalid), 0 (invalid)\n\n      throw new Error(\n        `\\`${optionName}\\` was specified but was not a node, or did not return a node`\n      );\n    }\n\n    let node = optionValue; // could be HTMLElement, SVGElement, or non-empty string at this point\n\n    if (typeof optionValue === 'string') {\n      try {\n        node = doc.querySelector(optionValue); // resolve to node, or null if fails\n      } catch (err) {\n        throw new Error(\n          `\\`${optionName}\\` appears to be an invalid selector; error=\"${err.message}\"`\n        );\n      }\n\n      if (!node) {\n        if (!hasFallback) {\n          throw new Error(\n            `\\`${optionName}\\` as selector refers to no known node`\n          );\n        }\n        // else, `node` MUST be `null` because that's what `Document.querySelector()` returns\n        //  if the selector is valid but doesn't match anything\n      }\n    }\n\n    return node;\n  };\n\n  const getInitialFocusNode = function () {\n    let node = getNodeForOption('initialFocus', { hasFallback: true });\n\n    // false explicitly indicates we want no initialFocus at all\n    if (node === false) {\n      return false;\n    }\n\n    if (\n      node === undefined ||\n      (node && !isFocusable(node, config.tabbableOptions))\n    ) {\n      // option not specified nor focusable: use fallback options\n      if (findContainerIndex(doc.activeElement) >= 0) {\n        node = doc.activeElement;\n      } else {\n        const firstTabbableGroup = state.tabbableGroups[0];\n        const firstTabbableNode =\n          firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n\n        // NOTE: `fallbackFocus` option function cannot return `false` (not supported)\n        node = firstTabbableNode || getNodeForOption('fallbackFocus');\n      }\n    } else if (node === null) {\n      // option is a VALID selector string that doesn't yield a node: use the `fallbackFocus`\n      //  option instead of the default behavior when the option isn't specified at all\n      node = getNodeForOption('fallbackFocus');\n    }\n\n    if (!node) {\n      throw new Error(\n        'Your focus-trap needs to have at least one focusable element'\n      );\n    }\n\n    return node;\n  };\n\n  const updateTabbableNodes = function () {\n    state.containerGroups = state.containers.map((container) => {\n      const tabbableNodes = tabbable(container, config.tabbableOptions);\n\n      // NOTE: if we have tabbable nodes, we must have focusable nodes; focusable nodes\n      //  are a superset of tabbable nodes since nodes with negative `tabindex` attributes\n      //  are focusable but not tabbable\n      const focusableNodes = focusable(container, config.tabbableOptions);\n\n      const firstTabbableNode =\n        tabbableNodes.length > 0 ? tabbableNodes[0] : undefined;\n      const lastTabbableNode =\n        tabbableNodes.length > 0\n          ? tabbableNodes[tabbableNodes.length - 1]\n          : undefined;\n\n      const firstDomTabbableNode = focusableNodes.find((node) =>\n        isTabbable(node)\n      );\n      const lastDomTabbableNode = focusableNodes\n        .slice()\n        .reverse()\n        .find((node) => isTabbable(node));\n\n      const posTabIndexesFound = !!tabbableNodes.find(\n        (node) => getTabIndex(node) > 0\n      );\n\n      return {\n        container,\n        tabbableNodes,\n        focusableNodes,\n\n        /** True if at least one node with positive `tabindex` was found in this container. */\n        posTabIndexesFound,\n\n        /** First tabbable node in container, __tabindex__ order; `undefined` if none. */\n        firstTabbableNode,\n        /** Last tabbable node in container, __tabindex__ order; `undefined` if none. */\n        lastTabbableNode,\n\n        // NOTE: DOM order is NOT NECESSARILY \"document position\" order, but figuring that out\n        //  would require more than just https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n        //  because that API doesn't work with Shadow DOM as well as it should (@see\n        //  https://github.com/whatwg/dom/issues/320) and since this first/last is only needed, so far,\n        //  to address an edge case related to positive tabindex support, this seems like a much easier,\n        //  \"close enough most of the time\" alternative for positive tabindexes which should generally\n        //  be avoided anyway...\n        /** First tabbable node in container, __DOM__ order; `undefined` if none. */\n        firstDomTabbableNode,\n        /** Last tabbable node in container, __DOM__ order; `undefined` if none. */\n        lastDomTabbableNode,\n\n        /**\n         * Finds the __tabbable__ node that follows the given node in the specified direction,\n         *  in this container, if any.\n         * @param {HTMLElement} node\n         * @param {boolean} [forward] True if going in forward tab order; false if going\n         *  in reverse.\n         * @returns {HTMLElement|undefined} The next tabbable node, if any.\n         */\n        nextTabbableNode(node, forward = true) {\n          const nodeIdx = tabbableNodes.indexOf(node);\n          if (nodeIdx < 0) {\n            // either not tabbable nor focusable, or was focused but not tabbable (negative tabindex):\n            //  since `node` should at least have been focusable, we assume that's the case and mimic\n            //  what browsers do, which is set focus to the next node in __document position order__,\n            //  regardless of positive tabindexes, if any -- and for reasons explained in the NOTE\n            //  above related to `firstDomTabbable` and `lastDomTabbable` properties, we fall back to\n            //  basic DOM order\n            if (forward) {\n              return focusableNodes\n                .slice(focusableNodes.indexOf(node) + 1)\n                .find((el) => isTabbable(el));\n            }\n\n            return focusableNodes\n              .slice(0, focusableNodes.indexOf(node))\n              .reverse()\n              .find((el) => isTabbable(el));\n          }\n\n          return tabbableNodes[nodeIdx + (forward ? 1 : -1)];\n        },\n      };\n    });\n\n    state.tabbableGroups = state.containerGroups.filter(\n      (group) => group.tabbableNodes.length > 0\n    );\n\n    // throw if no groups have tabbable nodes and we don't have a fallback focus node either\n    if (\n      state.tabbableGroups.length <= 0 &&\n      !getNodeForOption('fallbackFocus') // returning false not supported for this option\n    ) {\n      throw new Error(\n        'Your focus-trap must have at least one container with at least one tabbable node in it at all times'\n      );\n    }\n\n    // NOTE: Positive tabindexes are only properly supported in single-container traps because\n    //  doing it across multiple containers where tabindexes could be all over the place\n    //  would require Tabbable to support multiple containers, would require additional\n    //  specialized Shadow DOM support, and would require Tabbable's multi-container support\n    //  to look at those containers in document position order rather than user-provided\n    //  order (as they are treated in Focus-trap, for legacy reasons). See discussion on\n    //  https://github.com/focus-trap/focus-trap/issues/375 for more details.\n    if (\n      state.containerGroups.find((g) => g.posTabIndexesFound) &&\n      state.containerGroups.length > 1\n    ) {\n      throw new Error(\n        \"At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.\"\n      );\n    }\n  };\n\n  /**\n   * Gets the current activeElement. If it's a web-component and has open shadow-root\n   * it will recursively search inside shadow roots for the \"true\" activeElement.\n   *\n   * @param {Document | ShadowRoot} el\n   *\n   * @returns {HTMLElement} The element that currently has the focus\n   **/\n  const getActiveElement = function (el) {\n    const activeElement = el.activeElement;\n\n    if (!activeElement) {\n      return;\n    }\n\n    if (\n      activeElement.shadowRoot &&\n      activeElement.shadowRoot.activeElement !== null\n    ) {\n      return getActiveElement(activeElement.shadowRoot);\n    }\n\n    return activeElement;\n  };\n\n  const tryFocus = function (node) {\n    if (node === false) {\n      return;\n    }\n\n    if (node === getActiveElement(document)) {\n      return;\n    }\n\n    if (!node || !node.focus) {\n      tryFocus(getInitialFocusNode());\n      return;\n    }\n\n    node.focus({ preventScroll: !!config.preventScroll });\n    // NOTE: focus() API does not trigger focusIn event so set MRU node manually\n    state.mostRecentlyFocusedNode = node;\n\n    if (isSelectableInput(node)) {\n      node.select();\n    }\n  };\n\n  const getReturnFocusNode = function (previousActiveElement) {\n    const node = getNodeForOption('setReturnFocus', {\n      params: [previousActiveElement],\n    });\n    return node ? node : node === false ? false : previousActiveElement;\n  };\n\n  /**\n   * Finds the next node (in either direction) where focus should move according to a\n   *  keyboard focus-in event.\n   * @param {Object} params\n   * @param {Node} [params.target] Known target __from which__ to navigate, if any.\n   * @param {KeyboardEvent|FocusEvent} [params.event] Event to use if `target` isn't known (event\n   *  will be used to determine the `target`). Ignored if `target` is specified.\n   * @param {boolean} [params.isBackward] True if focus should move backward.\n   * @returns {Node|undefined} The next node, or `undefined` if a next node couldn't be\n   *  determined given the current state of the trap.\n   */\n  const findNextNavNode = function ({ target, event, isBackward = false }) {\n    target = target || getActualTarget(event);\n    updateTabbableNodes();\n\n    let destinationNode = null;\n\n    if (state.tabbableGroups.length > 0) {\n      // make sure the target is actually contained in a group\n      // NOTE: the target may also be the container itself if it's focusable\n      //  with tabIndex='-1' and was given initial focus\n      const containerIndex = findContainerIndex(target, event);\n      const containerGroup =\n        containerIndex >= 0 ? state.containerGroups[containerIndex] : undefined;\n\n      if (containerIndex < 0) {\n        // target not found in any group: quite possible focus has escaped the trap,\n        //  so bring it back into...\n        if (isBackward) {\n          // ...the last node in the last group\n          destinationNode =\n            state.tabbableGroups[state.tabbableGroups.length - 1]\n              .lastTabbableNode;\n        } else {\n          // ...the first node in the first group\n          destinationNode = state.tabbableGroups[0].firstTabbableNode;\n        }\n      } else if (isBackward) {\n        // REVERSE\n\n        // is the target the first tabbable node in a group?\n        let startOfGroupIndex = state.tabbableGroups.findIndex(\n          ({ firstTabbableNode }) => target === firstTabbableNode\n        );\n\n        if (\n          startOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target, false)))\n        ) {\n          // an exception case where the target is either the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle shift+tab as if focus were on the container's\n          //  first tabbable node, and go to the last tabbable node of the LAST group\n          startOfGroupIndex = containerIndex;\n        }\n\n        if (startOfGroupIndex >= 0) {\n          // YES: then shift+tab should go to the last tabbable node in the\n          //  previous group (and wrap around to the last tabbable node of\n          //  the LAST group if it's the first tabbable node of the FIRST group)\n          const destinationGroupIndex =\n            startOfGroupIndex === 0\n              ? state.tabbableGroups.length - 1\n              : startOfGroupIndex - 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n\n          destinationNode =\n            getTabIndex(target) >= 0\n              ? destinationGroup.lastTabbableNode\n              : destinationGroup.lastDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target, false);\n        }\n      } else {\n        // FORWARD\n\n        // is the target the last tabbable node in a group?\n        let lastOfGroupIndex = state.tabbableGroups.findIndex(\n          ({ lastTabbableNode }) => target === lastTabbableNode\n        );\n\n        if (\n          lastOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target)))\n        ) {\n          // an exception case where the target is the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle tab as if focus were on the container's\n          //  last tabbable node, and go to the first tabbable node of the FIRST group\n          lastOfGroupIndex = containerIndex;\n        }\n\n        if (lastOfGroupIndex >= 0) {\n          // YES: then tab should go to the first tabbable node in the next\n          //  group (and wrap around to the first tabbable node of the FIRST\n          //  group if it's the last tabbable node of the LAST group)\n          const destinationGroupIndex =\n            lastOfGroupIndex === state.tabbableGroups.length - 1\n              ? 0\n              : lastOfGroupIndex + 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n\n          destinationNode =\n            getTabIndex(target) >= 0\n              ? destinationGroup.firstTabbableNode\n              : destinationGroup.firstDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target);\n        }\n      }\n    } else {\n      // no groups available\n      // NOTE: the fallbackFocus option does not support returning false to opt-out\n      destinationNode = getNodeForOption('fallbackFocus');\n    }\n\n    return destinationNode;\n  };\n\n  // This needs to be done on mousedown and touchstart instead of click\n  // so that it precedes the focus event.\n  const checkPointerDown = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target, e) >= 0) {\n      // allow the click since it ocurred inside the trap\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      // immediately deactivate the trap\n      trap.deactivate({\n        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,\n        //  which will result in the outside click setting focus to the node\n        //  that was clicked (and if not focusable, to \"nothing\"); by setting\n        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused\n        //  on activation (or the configured `setReturnFocus` node), whether the\n        //  outside click was on a focusable node or not\n        returnFocus: config.returnFocusOnDeactivate,\n      });\n      return;\n    }\n\n    // This is needed for mobile devices.\n    // (If we'll only let `click` events through,\n    // then on mobile they will be blocked anyways if `touchstart` is blocked.)\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      // allow the click outside the trap to take place\n      return;\n    }\n\n    // otherwise, prevent the click\n    e.preventDefault();\n  };\n\n  // In case focus escapes the trap for some strange reason, pull it back in.\n  // NOTE: the focusIn event is NOT cancelable, so if focus escapes, it may cause unexpected\n  //  scrolling if the node that got focused was out of view; there's nothing we can do to\n  //  prevent that from happening by the time we discover that focus escaped\n  const checkFocusIn = function (event) {\n    const target = getActualTarget(event);\n    const targetContained = findContainerIndex(target, event) >= 0;\n\n    // In Firefox when you Tab out of an iframe the Document is briefly focused.\n    if (targetContained || target instanceof Document) {\n      if (targetContained) {\n        state.mostRecentlyFocusedNode = target;\n      }\n    } else {\n      // escaped! pull it back in to where it just left\n      event.stopImmediatePropagation();\n\n      // focus will escape if the MRU node had a positive tab index and user tried to nav forward;\n      //  it will also escape if the MRU node had a 0 tab index and user tried to nav backward\n      //  toward a node with a positive tab index\n      let nextNode; // next node to focus, if we find one\n      let navAcrossContainers = true;\n      if (state.mostRecentlyFocusedNode) {\n        if (getTabIndex(state.mostRecentlyFocusedNode) > 0) {\n          // MRU container index must be >=0 otherwise we wouldn't have it as an MRU node...\n          const mruContainerIdx = findContainerIndex(\n            state.mostRecentlyFocusedNode\n          );\n          // there MAY not be any tabbable nodes in the container if there are at least 2 containers\n          //  and the MRU node is focusable but not tabbable (focus-trap requires at least 1 container\n          //  with at least one tabbable node in order to function, so this could be the other container\n          //  with nothing tabbable in it)\n          const { tabbableNodes } = state.containerGroups[mruContainerIdx];\n          if (tabbableNodes.length > 0) {\n            // MRU tab index MAY not be found if the MRU node is focusable but not tabbable\n            const mruTabIdx = tabbableNodes.findIndex(\n              (node) => node === state.mostRecentlyFocusedNode\n            );\n            if (mruTabIdx >= 0) {\n              if (config.isKeyForward(state.recentNavEvent)) {\n                if (mruTabIdx + 1 < tabbableNodes.length) {\n                  nextNode = tabbableNodes[mruTabIdx + 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              } else {\n                if (mruTabIdx - 1 >= 0) {\n                  nextNode = tabbableNodes[mruTabIdx - 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              }\n              // else, don't find in container order without considering direction too\n            }\n          }\n          // else, no tabbable nodes in that container (which means we must have at least one other\n          //  container with at least one tabbable node in it, otherwise focus-trap would've thrown\n          //  an error the last time updateTabbableNodes() was run): find next node among all known\n          //  containers\n        } else {\n          // check to see if there's at least one tabbable node with a positive tab index inside\n          //  the trap because focus seems to escape when navigating backward from a tabbable node\n          //  with tabindex=0 when this is the case (instead of wrapping to the tabbable node with\n          //  the greatest positive tab index like it should)\n          if (\n            !state.containerGroups.some((g) =>\n              g.tabbableNodes.some((n) => getTabIndex(n) > 0)\n            )\n          ) {\n            // no containers with tabbable nodes with positive tab indexes which means the focus\n            //  escaped for some other reason and we should just execute the fallback to the\n            //  MRU node or initial focus node, if any\n            navAcrossContainers = false;\n          }\n        }\n      } else {\n        // no MRU node means we're likely in some initial condition when the trap has just\n        //  been activated and initial focus hasn't been given yet, in which case we should\n        //  fall through to trying to focus the initial focus node, which is what should\n        //  happen below at this point in the logic\n        navAcrossContainers = false;\n      }\n\n      if (navAcrossContainers) {\n        nextNode = findNextNavNode({\n          // move FROM the MRU node, not event-related node (which will be the node that is\n          //  outside the trap causing the focus escape we're trying to fix)\n          target: state.mostRecentlyFocusedNode,\n          isBackward: config.isKeyBackward(state.recentNavEvent),\n        });\n      }\n\n      if (nextNode) {\n        tryFocus(nextNode);\n      } else {\n        tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n      }\n    }\n\n    state.recentNavEvent = undefined; // clear\n  };\n\n  // Hijack key nav events on the first and last focusable nodes of the trap,\n  // in order to prevent focus from escaping. If it escapes for even a\n  // moment it can end up scrolling the page and causing confusion so we\n  // kind of need to capture the action at the keydown phase.\n  const checkKeyNav = function (event, isBackward = false) {\n    state.recentNavEvent = event;\n\n    const destinationNode = findNextNavNode({ event, isBackward });\n    if (destinationNode) {\n      if (isTabEvent(event)) {\n        // since tab natively moves focus, we wouldn't have a destination node unless we\n        //  were on the edge of a container and had to move to the next/previous edge, in\n        //  which case we want to prevent default to keep the browser from moving focus\n        //  to where it normally would\n        event.preventDefault();\n      }\n      tryFocus(destinationNode);\n    }\n    // else, let the browser take care of [shift+]tab and move the focus\n  };\n\n  const checkTabKey = function (event) {\n    if (config.isKeyForward(event) || config.isKeyBackward(event)) {\n      checkKeyNav(event, config.isKeyBackward(event));\n    }\n  };\n\n  // we use a different event phase for the Escape key to allow canceling the event and checking for this in escapeDeactivates\n  const checkEscapeKey = function (event) {\n    if (\n      isEscapeEvent(event) &&\n      valueOrHandler(config.escapeDeactivates, event) !== false\n    ) {\n      event.preventDefault();\n      trap.deactivate();\n    }\n  };\n\n  const checkClick = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target, e) >= 0) {\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      return;\n    }\n\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n\n    e.preventDefault();\n    e.stopImmediatePropagation();\n  };\n\n  //\n  // EVENT LISTENERS\n  //\n\n  const addListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    // There can be only one listening focus trap at a time\n    activeFocusTraps.activateTrap(trapStack, trap);\n\n    // Delay ensures that the focused element doesn't capture the event\n    // that caused the focus trap activation.\n    state.delayInitialFocusTimer = config.delayInitialFocus\n      ? delay(function () {\n          tryFocus(getInitialFocusNode());\n        })\n      : tryFocus(getInitialFocusNode());\n\n    doc.addEventListener('focusin', checkFocusIn, true);\n    doc.addEventListener('mousedown', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('touchstart', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('click', checkClick, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('keydown', checkTabKey, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('keydown', checkEscapeKey);\n\n    return trap;\n  };\n\n  const removeListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    doc.removeEventListener('focusin', checkFocusIn, true);\n    doc.removeEventListener('mousedown', checkPointerDown, true);\n    doc.removeEventListener('touchstart', checkPointerDown, true);\n    doc.removeEventListener('click', checkClick, true);\n    doc.removeEventListener('keydown', checkTabKey, true);\n    doc.removeEventListener('keydown', checkEscapeKey);\n\n    return trap;\n  };\n\n  //\n  // MUTATION OBSERVER\n  //\n\n  const checkDomRemoval = function (mutations) {\n    const isFocusedNodeRemoved = mutations.some(function (mutation) {\n      const removedNodes = Array.from(mutation.removedNodes);\n      return removedNodes.some(function (node) {\n        return node === state.mostRecentlyFocusedNode;\n      });\n    });\n\n    // If the currently focused is removed then browsers will move focus to the\n    // <body> element. If this happens, try to move focus back into the trap.\n    if (isFocusedNodeRemoved) {\n      tryFocus(getInitialFocusNode());\n    }\n  };\n\n  // Use MutationObserver - if supported - to detect if focused node is removed\n  // from the DOM.\n  const mutationObserver =\n    typeof window !== 'undefined' && 'MutationObserver' in window\n      ? new MutationObserver(checkDomRemoval)\n      : undefined;\n\n  const updateObservedNodes = function () {\n    if (!mutationObserver) {\n      return;\n    }\n\n    mutationObserver.disconnect();\n    if (state.active && !state.paused) {\n      state.containers.map(function (container) {\n        mutationObserver.observe(container, {\n          subtree: true,\n          childList: true,\n        });\n      });\n    }\n  };\n\n  //\n  // TRAP DEFINITION\n  //\n\n  trap = {\n    get active() {\n      return state.active;\n    },\n\n    get paused() {\n      return state.paused;\n    },\n\n    activate(activateOptions) {\n      if (state.active) {\n        return this;\n      }\n\n      const onActivate = getOption(activateOptions, 'onActivate');\n      const onPostActivate = getOption(activateOptions, 'onPostActivate');\n      const checkCanFocusTrap = getOption(activateOptions, 'checkCanFocusTrap');\n\n      if (!checkCanFocusTrap) {\n        updateTabbableNodes();\n      }\n\n      state.active = true;\n      state.paused = false;\n      state.nodeFocusedBeforeActivation = doc.activeElement;\n\n      onActivate?.();\n\n      const finishActivation = () => {\n        if (checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        addListeners();\n        updateObservedNodes();\n        onPostActivate?.();\n      };\n\n      if (checkCanFocusTrap) {\n        checkCanFocusTrap(state.containers.concat()).then(\n          finishActivation,\n          finishActivation\n        );\n        return this;\n      }\n\n      finishActivation();\n      return this;\n    },\n\n    deactivate(deactivateOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      const options = {\n        onDeactivate: config.onDeactivate,\n        onPostDeactivate: config.onPostDeactivate,\n        checkCanReturnFocus: config.checkCanReturnFocus,\n        ...deactivateOptions,\n      };\n\n      clearTimeout(state.delayInitialFocusTimer); // noop if undefined\n      state.delayInitialFocusTimer = undefined;\n\n      removeListeners();\n      state.active = false;\n      state.paused = false;\n      updateObservedNodes();\n\n      activeFocusTraps.deactivateTrap(trapStack, trap);\n\n      const onDeactivate = getOption(options, 'onDeactivate');\n      const onPostDeactivate = getOption(options, 'onPostDeactivate');\n      const checkCanReturnFocus = getOption(options, 'checkCanReturnFocus');\n      const returnFocus = getOption(\n        options,\n        'returnFocus',\n        'returnFocusOnDeactivate'\n      );\n\n      onDeactivate?.();\n\n      const finishDeactivation = () => {\n        delay(() => {\n          if (returnFocus) {\n            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n          }\n          onPostDeactivate?.();\n        });\n      };\n\n      if (returnFocus && checkCanReturnFocus) {\n        checkCanReturnFocus(\n          getReturnFocusNode(state.nodeFocusedBeforeActivation)\n        ).then(finishDeactivation, finishDeactivation);\n        return this;\n      }\n\n      finishDeactivation();\n      return this;\n    },\n\n    pause(pauseOptions) {\n      if (state.paused || !state.active) {\n        return this;\n      }\n\n      const onPause = getOption(pauseOptions, 'onPause');\n      const onPostPause = getOption(pauseOptions, 'onPostPause');\n\n      state.paused = true;\n      onPause?.();\n\n      removeListeners();\n      updateObservedNodes();\n\n      onPostPause?.();\n      return this;\n    },\n\n    unpause(unpauseOptions) {\n      if (!state.paused || !state.active) {\n        return this;\n      }\n\n      const onUnpause = getOption(unpauseOptions, 'onUnpause');\n      const onPostUnpause = getOption(unpauseOptions, 'onPostUnpause');\n\n      state.paused = false;\n      onUnpause?.();\n\n      updateTabbableNodes();\n      addListeners();\n      updateObservedNodes();\n\n      onPostUnpause?.();\n      return this;\n    },\n\n    updateContainerElements(containerElements) {\n      const elementsAsArray = [].concat(containerElements).filter(Boolean);\n\n      state.containers = elementsAsArray.map((element) =>\n        typeof element === 'string' ? doc.querySelector(element) : element\n      );\n\n      if (state.active) {\n        updateTabbableNodes();\n      }\n\n      updateObservedNodes();\n\n      return this;\n    },\n  };\n\n  // initialize container elements\n  trap.updateContainerElements(elements);\n\n  return trap;\n};\n\nexport { createFocusTrap };\n", "import { unrefElement, tryOnScopeDispose } from '@vueuse/core';\nimport { toValue, notNullish } from '@vueuse/shared';\nimport { createFocusTrap } from 'focus-trap';\nimport { ref, computed, watch } from 'vue-demi';\n\nfunction useFocusTrap(target, options = {}) {\n  let trap;\n  const { immediate, ...focusTrapOptions } = options;\n  const hasFocus = ref(false);\n  const isPaused = ref(false);\n  const activate = (opts) => trap && trap.activate(opts);\n  const deactivate = (opts) => trap && trap.deactivate(opts);\n  const pause = () => {\n    if (trap) {\n      trap.pause();\n      isPaused.value = true;\n    }\n  };\n  const unpause = () => {\n    if (trap) {\n      trap.unpause();\n      isPaused.value = false;\n    }\n  };\n  const targets = computed(() => {\n    const _targets = toValue(target);\n    return (Array.isArray(_targets) ? _targets : [_targets]).map((el) => {\n      const _el = toValue(el);\n      return typeof _el === \"string\" ? _el : unrefElement(_el);\n    }).filter(notNullish);\n  });\n  watch(\n    targets,\n    (els) => {\n      if (!els.length)\n        return;\n      trap = createFocusTrap(els, {\n        ...focusTrapOptions,\n        onActivate() {\n          hasFocus.value = true;\n          if (options.onActivate)\n            options.onActivate();\n        },\n        onDeactivate() {\n          hasFocus.value = false;\n          if (options.onDeactivate)\n            options.onDeactivate();\n        }\n      });\n      if (immediate)\n        activate();\n    },\n    { flush: \"post\" }\n  );\n  tryOnScopeDispose(() => deactivate());\n  return {\n    hasFocus,\n    isPaused,\n    activate,\n    deactivate,\n    pause,\n    unpause\n  };\n}\n\nexport { useFocusTrap };\n"], "mappings": ";;;;;;;;;;;;;;AAKA,IAAMA,qBAAqB,CACzB,sBACA,uBACA,yBACA,wBACA,uBACA,qCACA,gCACA,gCACA,iEACA,8CACA,sBAAsB;AAExB,IAAMC,oBAAoCD,mBAAmBE,KAAK,GAAG;AAErE,IAAMC,YAAY,OAAOC,YAAY;AAErC,IAAMC,UAAUF,YACZ,WAAY;AAAA,IACZC,QAAQE,UAAUD,WAClBD,QAAQE,UAAUC,qBAClBH,QAAQE,UAAUE;AAEtB,IAAMC,cACJ,CAACN,aAAaC,QAAQE,UAAUG,cAC5B,SAACC,SAAO;AAAA,MAAAC;AAAA,SAAKD,YAAAA,QAAAA,YAAOC,SAAAA,UAAAA,uBAAPD,QAASD,iBAAW,QAAAE,yBAApBA,SAAAA,SAAAA,qBAAAC,KAAAF,OAAuB;AAAC,IACrC,SAACA,SAAO;AAAA,SAAKA,YAAAA,QAAAA,YAAAA,SAAAA,SAAAA,QAASG;AAAa;AAUzC,IAAMC,UAAU,SAAVA,SAAoBC,MAAMC,QAAe;AAAA,MAAAC;AAAA,MAAfD,WAAM,QAAA;AAANA,aAAS;EAAI;AAI3C,MAAME,WAAWH,SAAI,QAAJA,SAAIE,SAAAA,UAAAA,qBAAJF,KAAMI,kBAAYF,QAAAA,uBAAA,SAAA,SAAlBA,mBAAAL,KAAAG,MAAqB,OAAO;AAC7C,MAAMK,QAAQF,aAAa,MAAMA,aAAa;AAO9C,MAAMG,SAASD,SAAUJ,UAAUD,QAAQD,SAAQC,KAAKO,UAAU;AAElE,SAAOD;AACT;AAOA,IAAME,oBAAoB,SAApBA,mBAA8BR,MAAM;AAAA,MAAAS;AAIxC,MAAMC,WAAWV,SAAI,QAAJA,SAAIS,SAAAA,UAAAA,sBAAJT,KAAMI,kBAAYK,QAAAA,wBAAA,SAAA,SAAlBA,oBAAAZ,KAAAG,MAAqB,iBAAiB;AACvD,SAAOU,aAAa,MAAMA,aAAa;AACzC;AAQA,IAAMC,gBAAgB,SAAhBA,eAA0BC,IAAIC,kBAAkBC,QAAQ;AAG5D,MAAIf,QAAQa,EAAE,GAAG;AACf,WAAO,CAAA;EACT;AAEA,MAAIG,aAAaC,MAAMzB,UAAU0B,MAAMC,MACrCN,GAAGO,iBAAiBjC,iBAAiB,CACvC;AACA,MAAI2B,oBAAoBvB,QAAQO,KAAKe,IAAI1B,iBAAiB,GAAG;AAC3D6B,eAAWK,QAAQR,EAAE;EACvB;AACAG,eAAaA,WAAWD,OAAOA,MAAM;AACrC,SAAOC;AACT;AAoCA,IAAMM,2BAA2B,SAA3BA,0BACJC,UACAT,kBACAU,SACA;AACA,MAAMR,aAAa,CAAA;AACnB,MAAMS,kBAAkBR,MAAMS,KAAKH,QAAQ;AAC3C,SAAOE,gBAAgBE,QAAQ;AAC7B,QAAM/B,UAAU6B,gBAAgBG,MAAK;AACrC,QAAI5B,QAAQJ,SAAS,KAAK,GAAG;AAG3B;IACF;AAEA,QAAIA,QAAQiC,YAAY,QAAQ;AAE9B,UAAMC,WAAWlC,QAAQmC,iBAAgB;AACzC,UAAMC,UAAUF,SAASH,SAASG,WAAWlC,QAAQqC;AACrD,UAAMC,mBAAmBZ,0BAAyBU,SAAS,MAAMR,OAAO;AACxE,UAAIA,QAAQW,SAAS;AACnBnB,mBAAWoB,KAAIjB,MAAfH,YAAmBkB,gBAAgB;MACrC,OAAO;AACLlB,mBAAWoB,KAAK;UACdC,aAAazC;UACboB,YAAYkB;QACd,CAAC;MACH;IACF,OAAO;AAEL,UAAMI,iBAAiB/C,QAAQO,KAAKF,SAAST,iBAAiB;AAC9D,UACEmD,kBACAd,QAAQT,OAAOnB,OAAO,MACrBkB,oBAAoB,CAACS,SAASgB,SAAS3C,OAAO,IAC/C;AACAoB,mBAAWoB,KAAKxC,OAAO;MACzB;AAGA,UAAM4C,aACJ5C,QAAQ4C;MAEP,OAAOhB,QAAQiB,kBAAkB,cAChCjB,QAAQiB,cAAc7C,OAAO;AAKjC,UAAM8C,kBACJ,CAAC1C,QAAQwC,YAAY,KAAK,MACzB,CAAChB,QAAQmB,oBAAoBnB,QAAQmB,iBAAiB/C,OAAO;AAEhE,UAAI4C,cAAcE,iBAAiB;AAOjC,YAAMR,oBAAmBZ,0BACvBkB,eAAe,OAAO5C,QAAQqC,WAAWO,WAAWP,UACpD,MACAT,OACF;AAEA,YAAIA,QAAQW,SAAS;AACnBnB,qBAAWoB,KAAIjB,MAAfH,YAAmBkB,iBAAgB;QACrC,OAAO;AACLlB,qBAAWoB,KAAK;YACdC,aAAazC;YACboB,YAAYkB;UACd,CAAC;QACH;MACF,OAAO;AAGLT,wBAAgBJ,QAAOF,MAAvBM,iBAA2B7B,QAAQqC,QAAQ;MAC7C;IACF;EACF;AACA,SAAOjB;AACT;AAQA,IAAM4B,cAAc,SAAdA,aAAwB3C,MAAM;AAClC,SAAO,CAAC4C,MAAMC,SAAS7C,KAAKI,aAAa,UAAU,GAAG,EAAE,CAAC;AAC3D;AAQA,IAAM0C,cAAc,SAAdA,aAAwB9C,MAAM;AAClC,MAAI,CAACA,MAAM;AACT,UAAM,IAAI+C,MAAM,kBAAkB;EACpC;AAEA,MAAI/C,KAAKgD,WAAW,GAAG;AAQrB,SACG,0BAA0BC,KAAKjD,KAAK4B,OAAO,KAC1CpB,kBAAkBR,IAAI,MACxB,CAAC2C,YAAY3C,IAAI,GACjB;AACA,aAAO;IACT;EACF;AAEA,SAAOA,KAAKgD;AACd;AAUA,IAAME,uBAAuB,SAAvBA,sBAAiClD,MAAMmD,SAAS;AACpD,MAAMH,WAAWF,YAAY9C,IAAI;AAEjC,MAAIgD,WAAW,KAAKG,WAAW,CAACR,YAAY3C,IAAI,GAAG;AACjD,WAAO;EACT;AAEA,SAAOgD;AACT;AAEA,IAAMI,uBAAuB,SAAvBA,sBAAiCC,GAAGC,GAAG;AAC3C,SAAOD,EAAEL,aAAaM,EAAEN,WACpBK,EAAEE,gBAAgBD,EAAEC,gBACpBF,EAAEL,WAAWM,EAAEN;AACrB;AAEA,IAAMQ,UAAU,SAAVA,SAAoBxD,MAAM;AAC9B,SAAOA,KAAK4B,YAAY;AAC1B;AAEA,IAAM6B,gBAAgB,SAAhBA,eAA0BzD,MAAM;AACpC,SAAOwD,QAAQxD,IAAI,KAAKA,KAAK0D,SAAS;AACxC;AAEA,IAAMC,uBAAuB,SAAvBA,sBAAiC3D,MAAM;AAC3C,MAAM4D,IACJ5D,KAAK4B,YAAY,aACjBZ,MAAMzB,UAAU0B,MACbC,MAAMlB,KAAKgC,QAAQ,EACnB6B,KAAK,SAACC,OAAK;AAAA,WAAKA,MAAMlC,YAAY;GAAU;AACjD,SAAOgC;AACT;AAEA,IAAMG,kBAAkB,SAAlBA,iBAA4BC,OAAOC,MAAM;AAC7C,WAASC,IAAI,GAAGA,IAAIF,MAAMtC,QAAQwC,KAAK;AACrC,QAAIF,MAAME,CAAC,EAAEC,WAAWH,MAAME,CAAC,EAAED,SAASA,MAAM;AAC9C,aAAOD,MAAME,CAAC;IAChB;EACF;AACF;AAEA,IAAME,kBAAkB,SAAlBA,iBAA4BpE,MAAM;AACtC,MAAI,CAACA,KAAKqE,MAAM;AACd,WAAO;EACT;AACA,MAAMC,aAAatE,KAAKiE,QAAQvE,YAAYM,IAAI;AAChD,MAAMuE,cAAc,SAAdA,aAAwBF,MAAM;AAClC,WAAOC,WAAWnD,iBAChB,+BAA+BkD,OAAO,IACxC;;AAGF,MAAIG;AACJ,MACE,OAAOC,WAAW,eAClB,OAAOA,OAAOC,QAAQ,eACtB,OAAOD,OAAOC,IAAIC,WAAW,YAC7B;AACAH,eAAWD,YAAYE,OAAOC,IAAIC,OAAO3E,KAAKqE,IAAI,CAAC;EACrD,OAAO;AACL,QAAI;AACFG,iBAAWD,YAAYvE,KAAKqE,IAAI;aACzBO,KAAK;AAEZC,cAAQC,MACN,4IACAF,IAAIG,OACN;AACA,aAAO;IACT;EACF;AAEA,MAAMZ,UAAUJ,gBAAgBS,UAAUxE,KAAKiE,IAAI;AACnD,SAAO,CAACE,WAAWA,YAAYnE;AACjC;AAEA,IAAMgF,UAAU,SAAVA,SAAoBhF,MAAM;AAC9B,SAAOwD,QAAQxD,IAAI,KAAKA,KAAK0D,SAAS;AACxC;AAEA,IAAMuB,qBAAqB,SAArBA,oBAA+BjF,MAAM;AACzC,SAAOgF,QAAQhF,IAAI,KAAK,CAACoE,gBAAgBpE,IAAI;AAC/C;AAGA,IAAMkF,iBAAiB,SAAjBA,gBAA2BlF,MAAM;AAAA,MAAAmF;AAwBrC,MAAIC,WAAWpF,QAAQN,YAAYM,IAAI;AACvC,MAAIqF,gBAAYF,YAAGC,cAAQ,QAAAD,cAAA,SAAA,SAARA,UAAUG;AAI7B,MAAIC,WAAW;AACf,MAAIH,YAAYA,aAAapF,MAAM;AAAA,QAAAwF,eAAAC,uBAAAC;AACjCH,eAAW,CAAC,GACVC,gBAAAH,kBAAYG,QAAAA,kBAAA,WAAAC,wBAAZD,cAAc1F,mBAAa,QAAA2F,0BAAA,UAA3BA,sBAA6BE,SAASN,YAAY,KAClDrF,SAAI,QAAJA,SAAI0F,WAAAA,sBAAJ1F,KAAMF,mBAAa4F,QAAAA,wBAAA,UAAnBA,oBAAqBC,SAAS3F,IAAI;AAGpC,WAAO,CAACuF,YAAYF,cAAc;AAAA,UAAAO,YAAAC,gBAAAC;AAIhCV,iBAAW1F,YAAY2F,YAAY;AACnCA,sBAAYO,aAAGR,cAAQ,QAAAQ,eAAA,SAAA,SAARA,WAAUN;AACzBC,iBAAW,CAAC,GAAAM,iBAACR,kBAAY,QAAAQ,mBAAA,WAAAC,wBAAZD,eAAc/F,mBAAa,QAAAgG,0BAAA,UAA3BA,sBAA6BH,SAASN,YAAY;IACjE;EACF;AAEA,SAAOE;AACT;AAEA,IAAMQ,aAAa,SAAbA,YAAuB/F,MAAM;AACjC,MAAAgG,wBAA0BhG,KAAKiG,sBAAqB,GAA5CC,QAAKF,sBAALE,OAAOC,SAAMH,sBAANG;AACf,SAAOD,UAAU,KAAKC,WAAW;AACnC;AACA,IAAMC,WAAW,SAAXA,UAAqBpG,MAAIqG,MAAmC;AAAA,MAA/BC,eAAYD,KAAZC,cAAc9D,gBAAa6D,KAAb7D;AAM/C,MAAI+D,iBAAiBvG,IAAI,EAAEwG,eAAe,UAAU;AAClD,WAAO;EACT;AAEA,MAAMC,kBAAkBnH,QAAQO,KAAKG,MAAM,+BAA+B;AAC1E,MAAM0G,mBAAmBD,kBAAkBzG,KAAK2G,gBAAgB3G;AAChE,MAAIV,QAAQO,KAAK6G,kBAAkB,uBAAuB,GAAG;AAC3D,WAAO;EACT;AAEA,MACE,CAACJ,gBACDA,iBAAiB,UACjBA,iBAAiB,eACjB;AACA,QAAI,OAAO9D,kBAAkB,YAAY;AAGvC,UAAMoE,eAAe5G;AACrB,aAAOA,MAAM;AACX,YAAM2G,gBAAgB3G,KAAK2G;AAC3B,YAAME,WAAWnH,YAAYM,IAAI;AACjC,YACE2G,iBACA,CAACA,cAAcpE,cACfC,cAAcmE,aAAa,MAAM,MACjC;AAGA,iBAAOZ,WAAW/F,IAAI;QACxB,WAAWA,KAAK8G,cAAc;AAE5B9G,iBAAOA,KAAK8G;mBACH,CAACH,iBAAiBE,aAAa7G,KAAKF,eAAe;AAE5DE,iBAAO6G,SAASvB;QAClB,OAAO;AAELtF,iBAAO2G;QACT;MACF;AAEA3G,aAAO4G;IACT;AAWA,QAAI1B,eAAelF,IAAI,GAAG;AAKxB,aAAO,CAACA,KAAK+G,eAAc,EAAGrF;IAChC;AAkBA,QAAI4E,iBAAiB,eAAe;AAClC,aAAO;IACT;EAEF,WAAWA,iBAAiB,iBAAiB;AAM3C,WAAOP,WAAW/F,IAAI;EACxB;AAIA,SAAO;AACT;AAKA,IAAMgH,yBAAyB,SAAzBA,wBAAmChH,MAAM;AAC7C,MAAI,mCAAmCiD,KAAKjD,KAAK4B,OAAO,GAAG;AACzD,QAAIrB,aAAaP,KAAK2G;AAEtB,WAAOpG,YAAY;AACjB,UAAIA,WAAWqB,YAAY,cAAcrB,WAAW0G,UAAU;AAE5D,iBAAS/C,IAAI,GAAGA,IAAI3D,WAAWyB,SAASN,QAAQwC,KAAK;AACnD,cAAMJ,QAAQvD,WAAWyB,SAASkF,KAAKhD,CAAC;AAExC,cAAIJ,MAAMlC,YAAY,UAAU;AAG9B,mBAAOtC,QAAQO,KAAKU,YAAY,sBAAsB,IAClD,OACA,CAACuD,MAAM6B,SAAS3F,IAAI;UAC1B;QACF;AAEA,eAAO;MACT;AACAO,mBAAaA,WAAWoG;IAC1B;EACF;AAIA,SAAO;AACT;AAEA,IAAMQ,kCAAkC,SAAlCA,iCAA4C5F,SAASvB,MAAM;AAC/D,MACEA,KAAKiH;;;EAILlH,QAAQC,IAAI,KACZyD,cAAczD,IAAI,KAClBoG,SAASpG,MAAMuB,OAAO;EAEtBoC,qBAAqB3D,IAAI,KACzBgH,uBAAuBhH,IAAI,GAC3B;AACA,WAAO;EACT;AACA,SAAO;AACT;AAEA,IAAMoH,iCAAiC,SAAjCA,gCAA2C7F,SAASvB,MAAM;AAC9D,MACEiF,mBAAmBjF,IAAI,KACvB8C,YAAY9C,IAAI,IAAI,KACpB,CAACmH,gCAAgC5F,SAASvB,IAAI,GAC9C;AACA,WAAO;EACT;AACA,SAAO;AACT;AAEA,IAAMqH,4BAA4B,SAA5BA,2BAAsCC,gBAAgB;AAC1D,MAAMtE,WAAWH,SAASyE,eAAelH,aAAa,UAAU,GAAG,EAAE;AACrE,MAAIwC,MAAMI,QAAQ,KAAKA,YAAY,GAAG;AACpC,WAAO;EACT;AAGA,SAAO;AACT;AAMA,IAAMuE,cAAc,SAAdA,aAAwBxG,YAAY;AACxC,MAAMyG,mBAAmB,CAAA;AACzB,MAAMC,mBAAmB,CAAA;AACzB1G,aAAW2G,QAAQ,SAAUR,MAAMhD,GAAG;AACpC,QAAMf,UAAU,CAAC,CAAC+D,KAAK9E;AACvB,QAAMzC,UAAUwD,UAAU+D,KAAK9E,cAAc8E;AAC7C,QAAMS,oBAAoBzE,qBAAqBvD,SAASwD,OAAO;AAC/D,QAAM7B,WAAW6B,UAAUoE,aAAYL,KAAKnG,UAAU,IAAIpB;AAC1D,QAAIgI,sBAAsB,GAAG;AAC3BxE,gBACIqE,iBAAiBrF,KAAIjB,MAArBsG,kBAAyBlG,QAAQ,IACjCkG,iBAAiBrF,KAAKxC,OAAO;IACnC,OAAO;AACL8H,uBAAiBtF,KAAK;QACpBoB,eAAeW;QACflB,UAAU2E;QACVT;QACA/D;QACApB,SAAST;MACX,CAAC;IACH;EACF,CAAC;AAED,SAAOmG,iBACJG,KAAKxE,oBAAoB,EACzByE,OAAO,SAACC,KAAKC,UAAa;AACzBA,aAAS5E,UACL2E,IAAI3F,KAAIjB,MAAR4G,KAAYC,SAAShG,OAAO,IAC5B+F,IAAI3F,KAAK4F,SAAShG,OAAO;AAC7B,WAAO+F;EACT,GAAG,CAAA,CAAE,EACJE,OAAOR,gBAAgB;AAC5B;AAEMS,IAAAA,WAAW,SAAXA,UAAqBC,WAAW3G,SAAS;AAC7CA,YAAUA,WAAW,CAAA;AAErB,MAAIR;AACJ,MAAIQ,QAAQiB,eAAe;AACzBzB,iBAAaM,yBACX,CAAC6G,SAAS,GACV3G,QAAQV,kBACR;MACEC,QAAQsG,+BAA+Be,KAAK,MAAM5G,OAAO;MACzDW,SAAS;MACTM,eAAejB,QAAQiB;MACvBE,kBAAkB2E;IACpB,CACF;EACF,OAAO;AACLtG,iBAAaJ,cACXuH,WACA3G,QAAQV,kBACRuG,+BAA+Be,KAAK,MAAM5G,OAAO,CACnD;EACF;AACA,SAAOgG,YAAYxG,UAAU;AAC/B;AAEMqH,IAAAA,YAAY,SAAZA,WAAsBF,WAAW3G,SAAS;AAC9CA,YAAUA,WAAW,CAAA;AAErB,MAAIR;AACJ,MAAIQ,QAAQiB,eAAe;AACzBzB,iBAAaM,yBACX,CAAC6G,SAAS,GACV3G,QAAQV,kBACR;MACEC,QAAQqG,gCAAgCgB,KAAK,MAAM5G,OAAO;MAC1DW,SAAS;MACTM,eAAejB,QAAQiB;IACzB,CACF;EACF,OAAO;AACLzB,iBAAaJ,cACXuH,WACA3G,QAAQV,kBACRsG,gCAAgCgB,KAAK,MAAM5G,OAAO,CACpD;EACF;AAEA,SAAOR;AACT;AAEMsH,IAAAA,aAAa,SAAbA,YAAuBrI,MAAMuB,SAAS;AAC1CA,YAAUA,WAAW,CAAA;AACrB,MAAI,CAACvB,MAAM;AACT,UAAM,IAAI+C,MAAM,kBAAkB;EACpC;AACA,MAAIzD,QAAQO,KAAKG,MAAMd,iBAAiB,MAAM,OAAO;AACnD,WAAO;EACT;AACA,SAAOkI,+BAA+B7F,SAASvB,IAAI;AACrD;AAEA,IAAMsI,6BAA6CrJ,mBAChD+I,OAAO,QAAQ,EACf7I,KAAK,GAAG;AAELoJ,IAAAA,cAAc,SAAdA,aAAwBvI,MAAMuB,SAAS;AAC3CA,YAAUA,WAAW,CAAA;AACrB,MAAI,CAACvB,MAAM;AACT,UAAM,IAAI+C,MAAM,kBAAkB;EACpC;AACA,MAAIzD,QAAQO,KAAKG,MAAMsI,0BAA0B,MAAM,OAAO;AAC5D,WAAO;EACT;AACA,SAAOnB,gCAAgC5F,SAASvB,IAAI;AACtD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrqBA,IAAMwI,mBAAmB;EACvBC,cAAY,SAAZA,aAAaC,WAAWC,MAAM;AAC5B,QAAID,UAAUE,SAAS,GAAG;AACxB,UAAMC,aAAaH,UAAUA,UAAUE,SAAS,CAAC;AACjD,UAAIC,eAAeF,MAAM;AACvBE,mBAAWC,MAAK;MAClB;IACF;AAEA,QAAMC,YAAYL,UAAUM,QAAQL,IAAI;AACxC,QAAII,cAAc,IAAI;AACpBL,gBAAUO,KAAKN,IAAI;IACrB,OAAO;AAELD,gBAAUQ,OAAOH,WAAW,CAAC;AAC7BL,gBAAUO,KAAKN,IAAI;IACrB;;EAGFQ,gBAAc,SAAdA,eAAeT,WAAWC,MAAM;AAC9B,QAAMI,YAAYL,UAAUM,QAAQL,IAAI;AACxC,QAAII,cAAc,IAAI;AACpBL,gBAAUQ,OAAOH,WAAW,CAAC;IAC/B;AAEA,QAAIL,UAAUE,SAAS,GAAG;AACxBF,gBAAUA,UAAUE,SAAS,CAAC,EAAEQ,QAAO;IACzC;EACF;AACF;AAEA,IAAMC,oBAAoB,SAApBA,mBAA8BC,MAAM;AACxC,SACEA,KAAKC,WACLD,KAAKC,QAAQC,YAAW,MAAO,WAC/B,OAAOF,KAAKG,WAAW;AAE3B;AAEA,IAAMC,gBAAgB,SAAhBA,eAA0BC,GAAG;AACjC,UAAOA,MAAAA,QAAAA,MAAAA,SAAAA,SAAAA,EAAGC,SAAQ,aAAYD,MAAAA,QAAAA,MAAAA,SAAAA,SAAAA,EAAGC,SAAQ,UAASD,MAAAA,QAAAA,MAAAA,SAAAA,SAAAA,EAAGE,aAAY;AACnE;AAEA,IAAMC,aAAa,SAAbA,YAAuBH,GAAG;AAC9B,UAAOA,MAAAA,QAAAA,MAAC,SAAA,SAADA,EAAGC,SAAQ,UAASD,MAAC,QAADA,MAAC,SAAA,SAADA,EAAGE,aAAY;AAC5C;AAGA,IAAME,eAAe,SAAfA,cAAyBJ,GAAG;AAChC,SAAOG,WAAWH,CAAC,KAAK,CAACA,EAAEK;AAC7B;AAGA,IAAMC,gBAAgB,SAAhBA,eAA0BN,GAAG;AACjC,SAAOG,WAAWH,CAAC,KAAKA,EAAEK;AAC5B;AAEA,IAAME,QAAQ,SAARA,OAAkBC,IAAI;AAC1B,SAAOC,WAAWD,IAAI,CAAC;AACzB;AASA,IAAME,iBAAiB,SAAjBA,gBAA2BC,OAAkB;AAAA,WAAAC,OAAAC,UAAA5B,QAAR6B,SAAM,IAAAC,MAAAH,OAAAA,IAAAA,OAAA,IAAA,CAAA,GAAAI,OAAA,GAAAA,OAAAJ,MAAAI,QAAA;AAANF,WAAME,OAAAH,CAAAA,IAAAA,UAAAG,IAAA;EAAA;AAC/C,SAAO,OAAOL,UAAU,aAAaA,MAAKM,MAAIH,QAAAA,MAAM,IAAIH;AAC1D;AAEA,IAAMO,kBAAkB,SAAlBA,iBAA4BC,OAAO;AAQvC,SAAOA,MAAMC,OAAOC,cAAc,OAAOF,MAAMG,iBAAiB,aAC5DH,MAAMG,aAAY,EAAG,CAAC,IACtBH,MAAMC;AACZ;AAIA,IAAMG,oBAAoB,CAAA;AAEpBC,IAAAA,kBAAkB,SAAlBA,iBAA4BC,UAAUC,aAAa;AAGvD,MAAMC,OAAMD,gBAAW,QAAXA,gBAAW,SAAA,SAAXA,YAAaE,aAAYA;AAErC,MAAM7C,aAAY2C,gBAAW,QAAXA,gBAAW,SAAA,SAAXA,YAAa3C,cAAawC;AAE5C,MAAMM,SAAMC,eAAA;IACVC,yBAAyB;IACzBC,mBAAmB;IACnBC,mBAAmB;IACnB7B;IACAE;EAAa,GACVoB,WAAW;AAGhB,MAAMQ,QAAQ;;;IAGZC,YAAY,CAAA;;;;;;;;;;;;;;;;;IAkBZC,iBAAiB,CAAA;;;;;;IAMjBC,gBAAgB,CAAA;IAEhBC,6BAA6B;IAC7BC,yBAAyB;IACzBC,QAAQ;IACRC,QAAQ;;;IAIRC,wBAAwBC;;IAGxBC,gBAAgBD;;AAGlB,MAAI3D;AAUJ,MAAM6D,YAAY,SAAZA,WAAaC,uBAAuBC,YAAYC,kBAAqB;AACzE,WAAOF,yBACLA,sBAAsBC,UAAU,MAAMJ,SACpCG,sBAAsBC,UAAU,IAChClB,OAAOmB,oBAAoBD,UAAU;;AAa3C,MAAME,qBAAqB,SAArBA,oBAA+BC,SAAS/B,OAAO;AACnD,QAAMG,eACJ,QAAOH,UAAAA,QAAAA,UAAK,SAAA,SAALA,MAAOG,kBAAiB,aAC3BH,MAAMG,aAAY,IAClBqB;AAIN,WAAOT,MAAME,gBAAgBe,UAC3B,SAAAC,MAAA;AAAA,UAAGC,YAASD,KAATC,WAAWC,gBAAaF,KAAbE;AAAa,aACzBD,UAAUE,SAASL,OAAO;;;;OAK1B5B,iBAAAA,QAAAA,iBAAAA,SAAAA,SAAAA,aAAckC,SAASH,SAAS,MAChCC,cAAcG,KAAK,SAAC9D,MAAI;AAAA,eAAKA,SAASuD;OAAQ;IAAA,CAClD;;AAqBF,MAAMQ,mBAAmB,SAAnBA,kBACJX,YAEA;AAAA,QAAAY,QAAA9C,UAAA5B,SAAA,KAAA4B,UAAA,CAAA,MAAA8B,SAAA9B,UAAA,CAAA,IADuC,CAAA,GAAE+C,oBAAAD,MAAvCE,aAAAA,cAAWD,sBAAG,SAAA,QAAKA,mBAAAE,eAAAH,MAAE7C,QAAAA,SAAMgD,iBAAG,SAAA,CAAA,IAAEA;AAElC,QAAIC,cAAclC,OAAOkB,UAAU;AAEnC,QAAI,OAAOgB,gBAAgB,YAAY;AACrCA,oBAAcA,YAAW9C,MAAA,QAAA+C,mBAAIlD,MAAM,CAAC;IACtC;AAEA,QAAIiD,gBAAgB,MAAM;AACxBA,oBAAcpB;IAChB;AAEA,QAAI,CAACoB,aAAa;AAChB,UAAIA,gBAAgBpB,UAAaoB,gBAAgB,OAAO;AACtD,eAAOA;MACT;AAGA,YAAM,IAAIE,MAAK,IAAAC,OACRnB,YAAU,8DAAA,CACjB;IACF;AAEA,QAAIpD,OAAOoE;AAEX,QAAI,OAAOA,gBAAgB,UAAU;AACnC,UAAI;AACFpE,eAAOgC,IAAIwC,cAAcJ,WAAW;eAC7BK,KAAK;AACZ,cAAM,IAAIH,MAAKC,IAAAA,OACRnB,YAAUmB,8CAAAA,EAAAA,OAAgDE,IAAIC,SAAO,GAAA,CAC5E;MACF;AAEA,UAAI,CAAC1E,MAAM;AACT,YAAI,CAACkE,aAAa;AAChB,gBAAM,IAAII,MAAK,IAAAC,OACRnB,YAAU,uCAAA,CACjB;QACF;MAGF;IACF;AAEA,WAAOpD;;AAGT,MAAM2E,sBAAsB,SAAtBA,uBAAkC;AACtC,QAAI3E,OAAO+D,iBAAiB,gBAAgB;MAAEG,aAAa;IAAK,CAAC;AAGjE,QAAIlE,SAAS,OAAO;AAClB,aAAO;IACT;AAEA,QACEA,SAASgD,UACRhD,QAAQ,CAAC4E,YAAY5E,MAAMkC,OAAO2C,eAAe,GAClD;AAEA,UAAIvB,mBAAmBtB,IAAI8C,aAAa,KAAK,GAAG;AAC9C9E,eAAOgC,IAAI8C;MACb,OAAO;AACL,YAAMC,qBAAqBxC,MAAMG,eAAe,CAAC;AACjD,YAAMsC,oBACJD,sBAAsBA,mBAAmBC;AAG3ChF,eAAOgF,qBAAqBjB,iBAAiB,eAAe;MAC9D;IACF,WAAW/D,SAAS,MAAM;AAGxBA,aAAO+D,iBAAiB,eAAe;IACzC;AAEA,QAAI,CAAC/D,MAAM;AACT,YAAM,IAAIsE,MACR,8DACF;IACF;AAEA,WAAOtE;;AAGT,MAAMiF,sBAAsB,SAAtBA,uBAAkC;AACtC1C,UAAME,kBAAkBF,MAAMC,WAAW0C,IAAI,SAACxB,WAAc;AAC1D,UAAMC,gBAAgBwB,SAASzB,WAAWxB,OAAO2C,eAAe;AAKhE,UAAMO,iBAAiBC,UAAU3B,WAAWxB,OAAO2C,eAAe;AAElE,UAAMG,oBACJrB,cAAcrE,SAAS,IAAIqE,cAAc,CAAC,IAAIX;AAChD,UAAMsC,mBACJ3B,cAAcrE,SAAS,IACnBqE,cAAcA,cAAcrE,SAAS,CAAC,IACtC0D;AAEN,UAAMuC,uBAAuBH,eAAetB,KAAK,SAAC9D,MAAI;AAAA,eACpDwF,WAAWxF,IAAI;MAAC,CAClB;AACA,UAAMyF,sBAAsBL,eACzBM,MAAK,EACLC,QAAO,EACP7B,KAAK,SAAC9D,MAAI;AAAA,eAAKwF,WAAWxF,IAAI;OAAE;AAEnC,UAAM4F,qBAAqB,CAAC,CAACjC,cAAcG,KACzC,SAAC9D,MAAI;AAAA,eAAK6F,YAAY7F,IAAI,IAAI;MAAC,CACjC;AAEA,aAAO;QACL0D;QACAC;QACAyB;;QAGAQ;;QAGAZ;;QAEAM;;;;;;;;;QAUAC;;QAEAE;;;;;;;;;QAUAK,kBAAAA,SAAAA,iBAAiB9F,MAAsB;AAAA,cAAhB+F,UAAO7E,UAAA5B,SAAA,KAAA4B,UAAA,CAAA,MAAA8B,SAAA9B,UAAA,CAAA,IAAG;AAC/B,cAAM8E,UAAUrC,cAAcjE,QAAQM,IAAI;AAC1C,cAAIgG,UAAU,GAAG;AAOf,gBAAID,SAAS;AACX,qBAAOX,eACJM,MAAMN,eAAe1F,QAAQM,IAAI,IAAI,CAAC,EACtC8D,KAAK,SAACmC,IAAE;AAAA,uBAAKT,WAAWS,EAAE;eAAE;YACjC;AAEA,mBAAOb,eACJM,MAAM,GAAGN,eAAe1F,QAAQM,IAAI,CAAC,EACrC2F,QAAO,EACP7B,KAAK,SAACmC,IAAE;AAAA,qBAAKT,WAAWS,EAAE;aAAE;UACjC;AAEA,iBAAOtC,cAAcqC,WAAWD,UAAU,IAAI,GAAG;QACnD;;IAEJ,CAAC;AAEDxD,UAAMG,iBAAiBH,MAAME,gBAAgByD,OAC3C,SAACC,OAAK;AAAA,aAAKA,MAAMxC,cAAcrE,SAAS;IAAC,CAC3C;AAGA,QACEiD,MAAMG,eAAepD,UAAU,KAC/B,CAACyE,iBAAiB,eAAe,GACjC;AACA,YAAM,IAAIO,MACR,qGACF;IACF;AASA,QACE/B,MAAME,gBAAgBqB,KAAK,SAACsC,GAAC;AAAA,aAAKA,EAAER;KAAmB,KACvDrD,MAAME,gBAAgBnD,SAAS,GAC/B;AACA,YAAM,IAAIgF,MACR,+KACF;IACF;;AAWF,MAAM+B,oBAAmB,SAAnBA,iBAA6BJ,IAAI;AACrC,QAAMnB,gBAAgBmB,GAAGnB;AAEzB,QAAI,CAACA,eAAe;AAClB;IACF;AAEA,QACEA,cAAcpD,cACdoD,cAAcpD,WAAWoD,kBAAkB,MAC3C;AACA,aAAOuB,kBAAiBvB,cAAcpD,UAAU;IAClD;AAEA,WAAOoD;;AAGT,MAAMwB,YAAW,SAAXA,SAAqBtG,MAAM;AAC/B,QAAIA,SAAS,OAAO;AAClB;IACF;AAEA,QAAIA,SAASqG,kBAAiBpE,QAAQ,GAAG;AACvC;IACF;AAEA,QAAI,CAACjC,QAAQ,CAACA,KAAKuG,OAAO;AACxBD,gBAAS3B,oBAAmB,CAAE;AAC9B;IACF;AAEA3E,SAAKuG,MAAM;MAAEC,eAAe,CAAC,CAACtE,OAAOsE;IAAc,CAAC;AAEpDjE,UAAMK,0BAA0B5C;AAEhC,QAAID,kBAAkBC,IAAI,GAAG;AAC3BA,WAAKG,OAAM;IACb;;AAGF,MAAMsG,qBAAqB,SAArBA,oBAA+BC,uBAAuB;AAC1D,QAAM1G,OAAO+D,iBAAiB,kBAAkB;MAC9C5C,QAAQ,CAACuF,qBAAqB;IAChC,CAAC;AACD,WAAO1G,OAAOA,OAAOA,SAAS,QAAQ,QAAQ0G;;AAchD,MAAMC,kBAAkB,SAAlBA,iBAAeC,OAAoD;AAAA,QAArCnF,SAAMmF,MAANnF,QAAQD,QAAKoF,MAALpF,OAAKqF,mBAAAD,MAAEE,YAAAA,aAAUD,qBAAG,SAAA,QAAKA;AACnEpF,aAASA,UAAUF,gBAAgBC,KAAK;AACxCyD,wBAAmB;AAEnB,QAAI8B,kBAAkB;AAEtB,QAAIxE,MAAMG,eAAepD,SAAS,GAAG;AAInC,UAAM0H,iBAAiB1D,mBAAmB7B,QAAQD,KAAK;AACvD,UAAMyF,iBACJD,kBAAkB,IAAIzE,MAAME,gBAAgBuE,cAAc,IAAIhE;AAEhE,UAAIgE,iBAAiB,GAAG;AAGtB,YAAIF,YAAY;AAEdC,4BACExE,MAAMG,eAAeH,MAAMG,eAAepD,SAAS,CAAC,EACjDgG;QACP,OAAO;AAELyB,4BAAkBxE,MAAMG,eAAe,CAAC,EAAEsC;QAC5C;iBACS8B,YAAY;AAIrB,YAAII,oBAAoB3E,MAAMG,eAAec,UAC3C,SAAA2D,OAAA;AAAA,cAAGnC,oBAAiBmC,MAAjBnC;AAAiB,iBAAOvD,WAAWuD;QAAiB,CACzD;AAEA,YACEkC,oBAAoB,MACnBD,eAAevD,cAAcjC,UAC3BmD,YAAYnD,QAAQS,OAAO2C,eAAe,KACzC,CAACW,WAAW/D,QAAQS,OAAO2C,eAAe,KAC1C,CAACoC,eAAenB,iBAAiBrE,QAAQ,KAAK,IAClD;AAOAyF,8BAAoBF;QACtB;AAEA,YAAIE,qBAAqB,GAAG;AAI1B,cAAME,wBACJF,sBAAsB,IAClB3E,MAAMG,eAAepD,SAAS,IAC9B4H,oBAAoB;AAE1B,cAAMG,mBAAmB9E,MAAMG,eAAe0E,qBAAqB;AAEnEL,4BACElB,YAAYpE,MAAM,KAAK,IACnB4F,iBAAiB/B,mBACjB+B,iBAAiB5B;QACzB,WAAW,CAACjF,WAAWgB,KAAK,GAAG;AAG7BuF,4BAAkBE,eAAenB,iBAAiBrE,QAAQ,KAAK;QACjE;MACF,OAAO;AAIL,YAAI6F,mBAAmB/E,MAAMG,eAAec,UAC1C,SAAA+D,OAAA;AAAA,cAAGjC,mBAAgBiC,MAAhBjC;AAAgB,iBAAO7D,WAAW6D;QAAgB,CACvD;AAEA,YACEgC,mBAAmB,MAClBL,eAAevD,cAAcjC,UAC3BmD,YAAYnD,QAAQS,OAAO2C,eAAe,KACzC,CAACW,WAAW/D,QAAQS,OAAO2C,eAAe,KAC1C,CAACoC,eAAenB,iBAAiBrE,MAAM,IAC3C;AAOA6F,6BAAmBN;QACrB;AAEA,YAAIM,oBAAoB,GAAG;AAIzB,cAAMF,yBACJE,qBAAqB/E,MAAMG,eAAepD,SAAS,IAC/C,IACAgI,mBAAmB;AAEzB,cAAMD,oBAAmB9E,MAAMG,eAAe0E,sBAAqB;AAEnEL,4BACElB,YAAYpE,MAAM,KAAK,IACnB4F,kBAAiBrC,oBACjBqC,kBAAiB9B;QACzB,WAAW,CAAC/E,WAAWgB,KAAK,GAAG;AAG7BuF,4BAAkBE,eAAenB,iBAAiBrE,MAAM;QAC1D;MACF;IACF,OAAO;AAGLsF,wBAAkBhD,iBAAiB,eAAe;IACpD;AAEA,WAAOgD;;AAKT,MAAMS,mBAAmB,SAAnBA,kBAA6BnH,GAAG;AACpC,QAAMoB,SAASF,gBAAgBlB,CAAC;AAEhC,QAAIiD,mBAAmB7B,QAAQpB,CAAC,KAAK,GAAG;AAEtC;IACF;AAEA,QAAIU,eAAemB,OAAOuF,yBAAyBpH,CAAC,GAAG;AAErDhB,WAAKqI,WAAW;;;;;;;QAOdC,aAAazF,OAAOE;MACtB,CAAC;AACD;IACF;AAKA,QAAIrB,eAAemB,OAAO0F,mBAAmBvH,CAAC,GAAG;AAE/C;IACF;AAGAA,MAAEwH,eAAc;;AAOlB,MAAMC,eAAe,SAAfA,cAAyBtG,OAAO;AACpC,QAAMC,SAASF,gBAAgBC,KAAK;AACpC,QAAMuG,kBAAkBzE,mBAAmB7B,QAAQD,KAAK,KAAK;AAG7D,QAAIuG,mBAAmBtG,kBAAkBuG,UAAU;AACjD,UAAID,iBAAiB;AACnBxF,cAAMK,0BAA0BnB;MAClC;IACF,OAAO;AAELD,YAAMyG,yBAAwB;AAK9B,UAAIC;AACJ,UAAIC,sBAAsB;AAC1B,UAAI5F,MAAMK,yBAAyB;AACjC,YAAIiD,YAAYtD,MAAMK,uBAAuB,IAAI,GAAG;AAElD,cAAMwF,kBAAkB9E,mBACtBf,MAAMK,uBACR;AAKA,cAAQe,gBAAkBpB,MAAME,gBAAgB2F,eAAe,EAAvDzE;AACR,cAAIA,cAAcrE,SAAS,GAAG;AAE5B,gBAAM+I,YAAY1E,cAAcH,UAC9B,SAACxD,MAAI;AAAA,qBAAKA,SAASuC,MAAMK;YAAuB,CAClD;AACA,gBAAIyF,aAAa,GAAG;AAClB,kBAAInG,OAAOzB,aAAa8B,MAAMU,cAAc,GAAG;AAC7C,oBAAIoF,YAAY,IAAI1E,cAAcrE,QAAQ;AACxC4I,6BAAWvE,cAAc0E,YAAY,CAAC;AACtCF,wCAAsB;gBACxB;cAGF,OAAO;AACL,oBAAIE,YAAY,KAAK,GAAG;AACtBH,6BAAWvE,cAAc0E,YAAY,CAAC;AACtCF,wCAAsB;gBACxB;cAGF;YAEF;UACF;QAKF,OAAO;AAKL,cACE,CAAC5F,MAAME,gBAAgB6F,KAAK,SAAClC,GAAC;AAAA,mBAC5BA,EAAEzC,cAAc2E,KAAK,SAACC,GAAC;AAAA,qBAAK1C,YAAY0C,CAAC,IAAI;aAAE;UAAA,CACjD,GACA;AAIAJ,kCAAsB;UACxB;QACF;MACF,OAAO;AAKLA,8BAAsB;MACxB;AAEA,UAAIA,qBAAqB;AACvBD,mBAAWvB,gBAAgB;;;UAGzBlF,QAAQc,MAAMK;UACdkE,YAAY5E,OAAOvB,cAAc4B,MAAMU,cAAc;QACvD,CAAC;MACH;AAEA,UAAIiF,UAAU;AACZ5B,kBAAS4B,QAAQ;MACnB,OAAO;AACL5B,kBAAS/D,MAAMK,2BAA2B+B,oBAAmB,CAAE;MACjE;IACF;AAEApC,UAAMU,iBAAiBD;;AAOzB,MAAMwF,cAAc,SAAdA,aAAwBhH,OAA2B;AAAA,QAApBsF,aAAU5F,UAAA5B,SAAA,KAAA4B,UAAA,CAAA,MAAA8B,SAAA9B,UAAA,CAAA,IAAG;AAChDqB,UAAMU,iBAAiBzB;AAEvB,QAAMuF,kBAAkBJ,gBAAgB;MAAEnF;MAAOsF;IAAW,CAAC;AAC7D,QAAIC,iBAAiB;AACnB,UAAIvG,WAAWgB,KAAK,GAAG;AAKrBA,cAAMqG,eAAc;MACtB;AACAvB,gBAASS,eAAe;IAC1B;;AAIF,MAAM0B,cAAc,SAAdA,aAAwBjH,OAAO;AACnC,QAAIU,OAAOzB,aAAae,KAAK,KAAKU,OAAOvB,cAAca,KAAK,GAAG;AAC7DgH,kBAAYhH,OAAOU,OAAOvB,cAAca,KAAK,CAAC;IAChD;;AAIF,MAAMkH,iBAAiB,SAAjBA,gBAA2BlH,OAAO;AACtC,QACEpB,cAAcoB,KAAK,KACnBT,eAAemB,OAAOG,mBAAmBb,KAAK,MAAM,OACpD;AACAA,YAAMqG,eAAc;AACpBxI,WAAKqI,WAAU;IACjB;;AAGF,MAAMiB,aAAa,SAAbA,YAAuBtI,GAAG;AAC9B,QAAMoB,SAASF,gBAAgBlB,CAAC;AAEhC,QAAIiD,mBAAmB7B,QAAQpB,CAAC,KAAK,GAAG;AACtC;IACF;AAEA,QAAIU,eAAemB,OAAOuF,yBAAyBpH,CAAC,GAAG;AACrD;IACF;AAEA,QAAIU,eAAemB,OAAO0F,mBAAmBvH,CAAC,GAAG;AAC/C;IACF;AAEAA,MAAEwH,eAAc;AAChBxH,MAAE4H,yBAAwB;;AAO5B,MAAMW,eAAe,SAAfA,gBAA2B;AAC/B,QAAI,CAACrG,MAAMM,QAAQ;AACjB;IACF;AAGA3D,qBAAiBC,aAAaC,WAAWC,IAAI;AAI7CkD,UAAMQ,yBAAyBb,OAAOI,oBAClC1B,MAAM,WAAY;AAChB0F,gBAAS3B,oBAAmB,CAAE;IAChC,CAAC,IACD2B,UAAS3B,oBAAmB,CAAE;AAElC3C,QAAI6G,iBAAiB,WAAWf,cAAc,IAAI;AAClD9F,QAAI6G,iBAAiB,aAAarB,kBAAkB;MAClDsB,SAAS;MACTC,SAAS;IACX,CAAC;AACD/G,QAAI6G,iBAAiB,cAAcrB,kBAAkB;MACnDsB,SAAS;MACTC,SAAS;IACX,CAAC;AACD/G,QAAI6G,iBAAiB,SAASF,YAAY;MACxCG,SAAS;MACTC,SAAS;IACX,CAAC;AACD/G,QAAI6G,iBAAiB,WAAWJ,aAAa;MAC3CK,SAAS;MACTC,SAAS;IACX,CAAC;AACD/G,QAAI6G,iBAAiB,WAAWH,cAAc;AAE9C,WAAOrJ;;AAGT,MAAM2J,kBAAkB,SAAlBA,mBAA8B;AAClC,QAAI,CAACzG,MAAMM,QAAQ;AACjB;IACF;AAEAb,QAAIiH,oBAAoB,WAAWnB,cAAc,IAAI;AACrD9F,QAAIiH,oBAAoB,aAAazB,kBAAkB,IAAI;AAC3DxF,QAAIiH,oBAAoB,cAAczB,kBAAkB,IAAI;AAC5DxF,QAAIiH,oBAAoB,SAASN,YAAY,IAAI;AACjD3G,QAAIiH,oBAAoB,WAAWR,aAAa,IAAI;AACpDzG,QAAIiH,oBAAoB,WAAWP,cAAc;AAEjD,WAAOrJ;;AAOT,MAAM6J,kBAAkB,SAAlBA,iBAA4BC,WAAW;AAC3C,QAAMC,uBAAuBD,UAAUb,KAAK,SAAUe,UAAU;AAC9D,UAAMC,eAAelI,MAAMmI,KAAKF,SAASC,YAAY;AACrD,aAAOA,aAAahB,KAAK,SAAUtI,MAAM;AACvC,eAAOA,SAASuC,MAAMK;MACxB,CAAC;IACH,CAAC;AAID,QAAIwG,sBAAsB;AACxB9C,gBAAS3B,oBAAmB,CAAE;IAChC;;AAKF,MAAM6E,mBACJ,OAAOC,WAAW,eAAe,sBAAsBA,SACnD,IAAIC,iBAAiBR,eAAe,IACpClG;AAEN,MAAM2G,sBAAsB,SAAtBA,uBAAkC;AACtC,QAAI,CAACH,kBAAkB;AACrB;IACF;AAEAA,qBAAiBI,WAAU;AAC3B,QAAIrH,MAAMM,UAAU,CAACN,MAAMO,QAAQ;AACjCP,YAAMC,WAAW0C,IAAI,SAAUxB,WAAW;AACxC8F,yBAAiBK,QAAQnG,WAAW;UAClCoG,SAAS;UACTC,WAAW;QACb,CAAC;MACH,CAAC;IACH;;AAOF1K,SAAO;IACL,IAAIwD,SAAS;AACX,aAAON,MAAMM;;IAGf,IAAIC,SAAS;AACX,aAAOP,MAAMO;;IAGfkH,UAAAA,SAAAA,SAASC,iBAAiB;AACxB,UAAI1H,MAAMM,QAAQ;AAChB,eAAO;MACT;AAEA,UAAMqH,aAAahH,UAAU+G,iBAAiB,YAAY;AAC1D,UAAME,iBAAiBjH,UAAU+G,iBAAiB,gBAAgB;AAClE,UAAMG,oBAAoBlH,UAAU+G,iBAAiB,mBAAmB;AAExE,UAAI,CAACG,mBAAmB;AACtBnF,4BAAmB;MACrB;AAEA1C,YAAMM,SAAS;AACfN,YAAMO,SAAS;AACfP,YAAMI,8BAA8BX,IAAI8C;AAExCoF,qBAAAA,QAAAA,eAAAA,UAAAA,WAAU;AAEV,UAAMG,mBAAmB,SAAnBA,oBAAyB;AAC7B,YAAID,mBAAmB;AACrBnF,8BAAmB;QACrB;AACA2D,qBAAY;AACZe,4BAAmB;AACnBQ,2BAAAA,QAAAA,mBAAAA,UAAAA,eAAc;;AAGhB,UAAIC,mBAAmB;AACrBA,0BAAkB7H,MAAMC,WAAW+B,OAAM,CAAE,EAAE+F,KAC3CD,kBACAA,gBACF;AACA,eAAO;MACT;AAEAA,uBAAgB;AAChB,aAAO;;IAGT3C,YAAAA,SAAAA,WAAW6C,mBAAmB;AAC5B,UAAI,CAAChI,MAAMM,QAAQ;AACjB,eAAO;MACT;AAEA,UAAM2H,UAAOrI,eAAA;QACXsI,cAAcvI,OAAOuI;QACrBC,kBAAkBxI,OAAOwI;QACzBC,qBAAqBzI,OAAOyI;MAAmB,GAC5CJ,iBAAiB;AAGtBK,mBAAarI,MAAMQ,sBAAsB;AACzCR,YAAMQ,yBAAyBC;AAE/BgG,sBAAe;AACfzG,YAAMM,SAAS;AACfN,YAAMO,SAAS;AACf6G,0BAAmB;AAEnBzK,uBAAiBW,eAAeT,WAAWC,IAAI;AAE/C,UAAMoL,eAAevH,UAAUsH,SAAS,cAAc;AACtD,UAAME,mBAAmBxH,UAAUsH,SAAS,kBAAkB;AAC9D,UAAMG,sBAAsBzH,UAAUsH,SAAS,qBAAqB;AACpE,UAAM7C,cAAczE,UAClBsH,SACA,eACA,yBACF;AAEAC,uBAAAA,QAAAA,iBAAAA,UAAAA,aAAY;AAEZ,UAAMI,qBAAqB,SAArBA,sBAA2B;AAC/BjK,cAAM,WAAM;AACV,cAAI+G,aAAa;AACfrB,sBAASG,mBAAmBlE,MAAMI,2BAA2B,CAAC;UAChE;AACA+H,+BAAAA,QAAAA,qBAAAA,UAAAA,iBAAgB;QAClB,CAAC;;AAGH,UAAI/C,eAAegD,qBAAqB;AACtCA,4BACElE,mBAAmBlE,MAAMI,2BAA2B,CACtD,EAAE2H,KAAKO,oBAAoBA,kBAAkB;AAC7C,eAAO;MACT;AAEAA,yBAAkB;AAClB,aAAO;;IAGTrL,OAAAA,SAAAA,MAAMsL,cAAc;AAClB,UAAIvI,MAAMO,UAAU,CAACP,MAAMM,QAAQ;AACjC,eAAO;MACT;AAEA,UAAMkI,UAAU7H,UAAU4H,cAAc,SAAS;AACjD,UAAME,cAAc9H,UAAU4H,cAAc,aAAa;AAEzDvI,YAAMO,SAAS;AACfiI,kBAAAA,QAAAA,YAAAA,UAAAA,QAAO;AAEP/B,sBAAe;AACfW,0BAAmB;AAEnBqB,sBAAAA,QAAAA,gBAAAA,UAAAA,YAAW;AACX,aAAO;;IAGTlL,SAAAA,SAAAA,QAAQmL,gBAAgB;AACtB,UAAI,CAAC1I,MAAMO,UAAU,CAACP,MAAMM,QAAQ;AAClC,eAAO;MACT;AAEA,UAAMqI,YAAYhI,UAAU+H,gBAAgB,WAAW;AACvD,UAAME,gBAAgBjI,UAAU+H,gBAAgB,eAAe;AAE/D1I,YAAMO,SAAS;AACfoI,oBAAAA,QAAAA,cAAAA,UAAAA,UAAS;AAETjG,0BAAmB;AACnB2D,mBAAY;AACZe,0BAAmB;AAEnBwB,wBAAAA,QAAAA,kBAAAA,UAAAA,cAAa;AACb,aAAO;;IAGTC,yBAAAA,SAAAA,wBAAwBC,mBAAmB;AACzC,UAAMC,kBAAkB,CAAA,EAAG/G,OAAO8G,iBAAiB,EAAEnF,OAAOqF,OAAO;AAEnEhJ,YAAMC,aAAa8I,gBAAgBpG,IAAI,SAAC3B,SAAO;AAAA,eAC7C,OAAOA,YAAY,WAAWvB,IAAIwC,cAAcjB,OAAO,IAAIA;MAAO,CACpE;AAEA,UAAIhB,MAAMM,QAAQ;AAChBoC,4BAAmB;MACrB;AAEA0E,0BAAmB;AAEnB,aAAO;IACT;;AAIFtK,OAAK+L,wBAAwBtJ,QAAQ;AAErC,SAAOzC;AACT;;;AC/iCA,SAAS,aAAa,QAAQ,UAAU,CAAC,GAAG;AAC1C,MAAI;AACJ,QAAM,EAAE,WAAW,GAAG,iBAAiB,IAAI;AAC3C,QAAM,WAAW,IAAI,KAAK;AAC1B,QAAM,WAAW,IAAI,KAAK;AAC1B,QAAM,WAAW,CAAC,SAAS,QAAQ,KAAK,SAAS,IAAI;AACrD,QAAM,aAAa,CAAC,SAAS,QAAQ,KAAK,WAAW,IAAI;AACzD,QAAM,QAAQ,MAAM;AAClB,QAAI,MAAM;AACR,WAAK,MAAM;AACX,eAAS,QAAQ;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,QAAI,MAAM;AACR,WAAK,QAAQ;AACb,eAAS,QAAQ;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,SAAS,MAAM;AAC7B,UAAM,WAAW,QAAQ,MAAM;AAC/B,YAAQ,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO;AACnE,YAAM,MAAM,QAAQ,EAAE;AACtB,aAAO,OAAO,QAAQ,WAAW,MAAM,aAAa,GAAG;AAAA,IACzD,CAAC,EAAE,OAAO,UAAU;AAAA,EACtB,CAAC;AACD;AAAA,IACE;AAAA,IACA,CAAC,QAAQ;AACP,UAAI,CAAC,IAAI;AACP;AACF,aAAO,gBAAgB,KAAK;AAAA,QAC1B,GAAG;AAAA,QACH,aAAa;AACX,mBAAS,QAAQ;AACjB,cAAI,QAAQ;AACV,oBAAQ,WAAW;AAAA,QACvB;AAAA,QACA,eAAe;AACb,mBAAS,QAAQ;AACjB,cAAI,QAAQ;AACV,oBAAQ,aAAa;AAAA,QACzB;AAAA,MACF,CAAC;AACD,UAAI;AACF,iBAAS;AAAA,IACb;AAAA,IACA,EAAE,OAAO,OAAO;AAAA,EAClB;AACA,oBAAkB,MAAM,WAAW,CAAC;AACpC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": ["candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "NoElement", "Element", "matches", "prototype", "msMatchesSelector", "webkitMatchesSelector", "getRootNode", "element", "_element$getRootNode", "call", "ownerDocument", "isInert", "node", "lookUp", "_node$getAttribute", "inertAtt", "getAttribute", "inert", "result", "parentNode", "isContentEditable", "_node$getAttribute2", "attValue", "getCandidates", "el", "<PERSON><PERSON><PERSON><PERSON>", "filter", "candidates", "Array", "slice", "apply", "querySelectorAll", "unshift", "getCandidatesIteratively", "elements", "options", "elementsToCheck", "from", "length", "shift", "tagName", "assigned", "assignedElements", "content", "children", "nestedCandidates", "flatten", "push", "scopeParent", "validCandidate", "includes", "shadowRoot", "getShadowRoot", "validShadowRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasTabIndex", "isNaN", "parseInt", "getTabIndex", "Error", "tabIndex", "test", "getSortOrderTabIndex", "isScope", "sortOrderedTabbables", "a", "b", "documentOrder", "isInput", "isHiddenInput", "type", "isDetailsWithSummary", "r", "some", "child", "getCheckedRadio", "nodes", "form", "i", "checked", "isTabbableRadio", "name", "radioScope", "queryRadios", "radioSet", "window", "CSS", "escape", "err", "console", "error", "message", "isRadio", "isNonTabbableRadio", "isNodeAttached", "_nodeRoot", "nodeRoot", "nodeRootHost", "host", "attached", "_nodeRootHost", "_nodeRootHost$ownerDo", "_node$ownerDocument", "contains", "_nodeRoot2", "_nodeRootHost2", "_nodeRootHost2$ownerD", "isZeroArea", "_node$getBoundingClie", "getBoundingClientRect", "width", "height", "isHidden", "_ref", "displayCheck", "getComputedStyle", "visibility", "isDirectSummary", "nodeUnderDetails", "parentElement", "originalNode", "rootNode", "assignedSlot", "getClientRects", "isDisabledFromFieldset", "disabled", "item", "isNodeMatchingSelectorFocusable", "isNodeMatchingSelectorTabbable", "isValidShadowRootTabbable", "shadowHostNode", "sortByOrder", "regularTabbables", "orderedTabbables", "for<PERSON>ach", "candidateTabindex", "sort", "reduce", "acc", "sortable", "concat", "tabbable", "container", "bind", "focusable", "isTabbable", "focusableCandidateSelector", "isFocusable", "activeFocusTraps", "activateTrap", "trapStack", "trap", "length", "activeTrap", "pause", "trapIndex", "indexOf", "push", "splice", "deactivateTrap", "unpause", "isSelectableInput", "node", "tagName", "toLowerCase", "select", "isEscapeEvent", "e", "key", "keyCode", "isTabEvent", "isKeyForward", "shift<PERSON>ey", "isKeyBackward", "delay", "fn", "setTimeout", "valueOrHandler", "value", "_len", "arguments", "params", "Array", "_key", "apply", "getActualTarget", "event", "target", "shadowRoot", "<PERSON><PERSON><PERSON>", "internalTrapStack", "createFocusTrap", "elements", "userOptions", "doc", "document", "config", "_objectSpread", "returnFocusOnDeactivate", "escapeDeactivates", "delayInitialFocus", "state", "containers", "containerGroups", "tabbableGroups", "nodeFocusedBeforeActivation", "mostRecentlyFocusedNode", "active", "paused", "delayInitialFocusTimer", "undefined", "recentNavEvent", "getOption", "configOverrideOptions", "optionName", "configOptionName", "findContainerIndex", "element", "findIndex", "_ref", "container", "tabbableNodes", "contains", "includes", "find", "getNodeForOption", "_ref2", "_ref2$hasFallback", "<PERSON><PERSON><PERSON><PERSON>", "_ref2$params", "optionValue", "_toConsumableArray", "Error", "concat", "querySelector", "err", "message", "getInitialFocusNode", "isFocusable", "tabbableOptions", "activeElement", "firstTabbableGroup", "firstTabbableNode", "updateTabbableNodes", "map", "tabbable", "focusableNodes", "focusable", "lastTabbableNode", "firstDomTabbableNode", "isTabbable", "lastDomTabbableNode", "slice", "reverse", "posTabIndexesFound", "getTabIndex", "nextTabbableNode", "forward", "nodeIdx", "el", "filter", "group", "g", "getActiveElement", "tryFocus", "focus", "preventScroll", "getReturnFocusNode", "previousActiveElement", "findNextNavNode", "_ref3", "_ref3$isBackward", "isBackward", "destinationNode", "containerIndex", "containerGroup", "startOfGroupIndex", "_ref4", "destinationGroupIndex", "destinationGroup", "lastOfGroupIndex", "_ref5", "checkPointerDown", "clickOutsideDeactivates", "deactivate", "returnFocus", "allowOutsideClick", "preventDefault", "checkFocusIn", "targetContained", "Document", "stopImmediatePropagation", "nextNode", "navAcrossContainers", "mruContainerIdx", "mruTabIdx", "some", "n", "checkKeyNav", "checkTabKey", "checkEscapeKey", "checkClick", "addListeners", "addEventListener", "capture", "passive", "removeListeners", "removeEventListener", "checkDomRemoval", "mutations", "isFocusedNodeRemoved", "mutation", "removedNodes", "from", "mutationObserver", "window", "MutationObserver", "updateObservedNodes", "disconnect", "observe", "subtree", "childList", "activate", "activateOptions", "onActivate", "onPostActivate", "checkCanFocusTrap", "finishActivation", "then", "deactivateOptions", "options", "onDeactivate", "onPostDeactivate", "checkCanReturnFocus", "clearTimeout", "finishDeactivation", "pauseOptions", "onPause", "onPostPause", "unpauseOptions", "onUnpause", "onPostUnpause", "updateContainerElements", "containerElements", "elementsAsArray", "Boolean"]}