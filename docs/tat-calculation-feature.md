# TAT (Turn Around Time) Calculation Feature

## Overview

The drilldown reports now include a "TAT IN HOURS" column that automatically calculates the turnaround time between when a test was registered and when the result was completed. This provides valuable insights into laboratory efficiency and helps identify bottlenecks in the testing process.

## Implementation Details

### 🕒 **TAT Calculation Logic**

The TAT is calculated using the following formula:

```
TAT (hours) = Result Date - Test Registration Date
```

**Data Sources:**

- **Registration Date**: `report.created_date` (when the test was first registered)
- **Result Date**: `report.result_date` (when the test result was completed)

**Calculation Method:**

```typescript
const createdDate = moment(report.created_date);
const resultDate = report.result_date ? moment(report.result_date) : null;
const tatHours =
  resultDate && createdDate.isValid() && resultDate.isValid()
    ? Math.round(resultDate.diff(createdDate, "hours", true) * 100) / 100 // Round to 2 decimal places
    : null;
```

### 📊 **Display Format**

- **Completed Tests**: Shows precise hours with up to 2 decimal places (e.g., `6.25`, `24.5`, `72.0`)
- **Incomplete Tests**: Shows `N/A` when no result date is available
- **Invalid Dates**: Shows `N/A` when date parsing fails

### 🔧 **Technical Implementation**

#### 1. **Header Configuration**

```typescript
const HEADERS = ref<Header>([
  // ... existing headers
  { text: "RESULT DATE", value: "result_date", sortable: true },
  { text: "TAT IN HOURS", value: "tat_hours", sortable: true },
]);
```

#### 2. **Data Processing**

```typescript
// In computedReportData computed property
const createdDate = moment(report.created_date);
const resultDate = resultObject.result_date
  ? moment(resultObject.result_date)
  : null;
const tatHours =
  resultDate && createdDate.isValid() && resultDate.isValid()
    ? Math.round(resultDate.diff(createdDate, "hours", true) * 100) / 100
    : null;

return {
  // ... other fields
  result_date: report.result_date
    ? moment(report.result_date).format(DATE_FORMAT)
    : "N/A",
  tat_hours: tatHours !== null ? tatHours : "N/A",
};
```

#### 3. **Type Definition Update**

```typescript
export type DrillDownReportItem = {
  // ... existing fields
  tat_hours: number | string;
};
```

#### 4. **Export Integration**

```typescript
const exportData = computed(() => {
  return computedReportData.value.map((item: DrillDownReportItem) => {
    let baseObject: Record<any, any> = {
      // ... existing fields
      "TAT IN HOURS": item.tat_hours,
    };
    // ... rest of export logic
  });
});
```

## Usage Examples

### 📈 **Typical TAT Values**

| Test Type              | Expected TAT | Example Values |
| ---------------------- | ------------ | -------------- |
| **Routine Blood Work** | 2-6 hours    | `4.5`, `6.25`  |
| **Urgent Tests**       | 1-2 hours    | `1.0`, `1.75`  |
| **Culture Tests**      | 24-72 hours  | `48.0`, `72.5` |
| **Complex Analysis**   | 4-12 hours   | `8.25`, `11.5` |

### 🔍 **Data Interpretation**

**Good Performance Indicators:**

- TAT values within expected ranges for test types
- Consistent TAT across similar tests
- Lower TAT for urgent/STAT orders

**Performance Issues:**

- TAT significantly higher than expected
- High variability in TAT for similar tests
- Tests showing `N/A` (incomplete results)

## Benefits

### 📊 **For Laboratory Management**

1. **Performance Monitoring**: Track lab efficiency over time
2. **Bottleneck Identification**: Identify slow processes or equipment
3. **Staff Performance**: Monitor individual or shift performance
4. **Quality Improvement**: Data-driven process optimization

### 🏥 **For Clinical Staff**

1. **Result Expectations**: Know when to expect test results
2. **Patient Communication**: Provide accurate timelines to patients
3. **Workflow Planning**: Plan patient care around result availability
4. **Urgent Case Management**: Identify delayed urgent tests

### 📈 **For Reporting**

1. **Compliance Monitoring**: Meet regulatory TAT requirements
2. **Trend Analysis**: Identify patterns and seasonal variations
3. **Benchmarking**: Compare performance across departments
4. **Export Capability**: Include TAT data in Excel reports

## Edge Cases Handled

### ⚠️ **Data Quality Issues**

1. **Missing Result Dates**

   - **Scenario**: Test registered but no result recorded
   - **Display**: `N/A`
   - **Export**: `N/A`

2. **Invalid Date Formats**

   - **Scenario**: Corrupted or malformed date strings
   - **Display**: `N/A`
   - **Export**: `N/A`

3. **Future Result Dates**

   - **Scenario**: Result date before registration date (data error)
   - **Display**: Negative value (indicates data issue)
   - **Export**: Actual calculated value for investigation

4. **Same Date/Time**
   - **Scenario**: Result completed immediately upon registration
   - **Display**: `0` or `0.0`
   - **Export**: `0`

### 🔧 **Technical Edge Cases**

1. **Timezone Differences**

   - Uses moment.js for consistent date parsing
   - Handles various date formats from API

2. **Precision Handling**

   - Rounds to 2 decimal places for readability
   - Maintains precision for calculations

3. **Performance Optimization**
   - Calculated in computed property for reactivity
   - Cached results prevent recalculation

## Testing

### 🧪 **Test Coverage**

The implementation includes comprehensive tests covering:

1. **Basic Calculations**

   - Standard TAT calculations
   - Zero-hour TAT (immediate results)
   - Multi-day TAT calculations

2. **Edge Cases**

   - Missing result dates
   - Invalid date formats
   - Precision rounding

3. **Integration**

   - Header configuration
   - Export data inclusion
   - Type safety

4. **Real-world Scenarios**
   - Different date formats
   - Timezone handling
   - Data quality issues

### 🚀 **Performance Testing**

- Tested with large datasets (1000+ records)
- Moment.js operations optimized for performance
- Computed properties ensure efficient reactivity

## Future Enhancements

### 📊 **Potential Improvements**

1. **TAT Targets**

   - Color-coding based on target TAT ranges
   - Red/yellow/green indicators for performance

2. **Statistical Analysis**

   - Average TAT per test type
   - TAT distribution charts
   - Trend analysis over time

3. **Filtering Options**

   - Filter by TAT ranges
   - Show only delayed tests
   - Performance threshold alerts

4. **Advanced Calculations**
   - Business hours TAT (excluding weekends/holidays)
   - Department-specific TAT calculations
   - Priority-based TAT expectations

## Migration Notes

### 🔄 **Backward Compatibility**

- New columns are additive - existing functionality unchanged
- Export format includes new TAT column
- Type definitions extended, not modified

### 📋 **Deployment Checklist**

- [ ] Verify date formats in API responses
- [ ] Test with various test types and TAT ranges
- [ ] Validate export functionality includes TAT data
- [ ] Confirm sorting works on TAT column
- [ ] Test edge cases with missing/invalid dates

## Related Documentation

- [Turn Around Time Report - Drilldown Implementation](./turn-around-time-drilldown.md)
- [Turn Around Time Report - Report ID Implementation](./turn-around-time-report-id-implementation.md)
- [Test Management Documentation](./test-management.md)

This TAT calculation feature provides valuable insights into laboratory performance and helps identify opportunities for process improvement while maintaining the existing functionality and user experience.
