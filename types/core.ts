interface GroupButtonItem {
  label?: string;
  icon?: Component;
  position?: "left" | "right" | "top" | "bottom";
  color?: "primary" | "secondary" | "success" | "danger" | "warning" | "info";
  size?: "small" | "medium" | "large";
  disabled?: boolean;
  outline?: boolean;
  block?: boolean;
  click?: (item: GroupButtonItem, index: number, event: Event) => void;
}

export type { GroupButtonItem };
