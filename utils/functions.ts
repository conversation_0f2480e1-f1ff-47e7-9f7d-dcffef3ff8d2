import moment, { type MomentInput } from "moment";
import TestsViewDialog from "../components/tests/nlims-dialog/index.vue";
import type { Statuses } from "@/types";
import { createApp } from 'vue';

export function showTestDetails(details: any): Promise<any> {
  return new Promise((resolve) => {
    let testsContainer = document.getElementById("tests-container")!;
    const app = createApp(TestsViewDialog, {
      data: details,
      open: true,
      callback: ((value: string) => resolve(value))
    });
    app.mount(testsContainer, false);
  });
}

/**
 * @method interceptor
 * @param statusCode
 * @returns boolean
 */
export function interceptor(statusCode: number): boolean {
  if (statusCode === 401) {
    return true;
  } else {
    return false;
  }
}

/***
 * @method calculateAge estimates date of birth/age
 * @returns age @typedef Number
 * @param value date string
 */

export function calculateAge(date: string): Number {
  return moment().diff(date, "years");
}

/**
 * @method calculateAgeFullFormat estimates date of birth/age in years, months, weeks, days, hours
 * @param date 
 * @returns date string
 */
export function calculateAgeFullFormat(date: MomentInput): string {
  const now = moment();
  const start = moment(date);
  const years = now.diff(start, "years");
  start.add(years, "years");
  const months = now.diff(start, "months");
  start.add(months, "months");
  const weeks = now.diff(start, "weeks");
  start.add(weeks, "weeks");
  const days = now.diff(start, "days");
  start.add(days, "days");
  const hrs = now.diff(start, "hours");
  return `${years} yrs ${months} months ${weeks} wks ${days} days ${hrs} hrs`;
}

/**
 * Checks if a value is "empty" or has Falsy value.
 *
 * @param value - The value to check for emptiness.
 * @returns true if the value is null or undefined, an empty string, an empty array, or an empty object.
 * Returns false otherwise.
 */
export function isEmpty(value: any): boolean {
  if (value === null || value === undefined) return true;
  if (
    typeof value === "string" ||
    Array.isArray(value) ||
    typeof value.splice === "function"
  )
    return !value.length;
  if (typeof value === "object") return !Object.keys(value).length;
  return false;
}

/**
 *
 * @param raw original array of specimen objects from api
 * @param items filtered array of specimen names from raw
 * @returns filtered array of specimens id for api @POST
 */
export function filterArrays(
  raw: Array<any>,
  items: Array<String>
): Array<Number> {
  let filteredArray = new Array<Number>();

  items.map((name) => {
    const obj = raw.filter((obj) => obj.name === name);
    filteredArray.push(obj[0].id);
  });

  return filteredArray;
}

/**
 * @param array array of any T items
 * @param name string name to be filtered
 */
export function getIndexByName(array: Array<any>, name: string): number {
  let itemId = 0;

  array.forEach((item, index) => {
    if (item.name === name) {
      itemId = index + 1;
    }
  });

  return itemId;
}

/**
 * @method reverseFilterArrays
 * @param raw
 * @returns Array of @type any
 */
export function reverseFilterArrays(raw: Array<any>): Array<any> {
  let model = raw.map((item: { id: number; name: string }) => item.name);

  return model;
}

export const getParameterizedUrl = (
  url: string,
  params: Record<string, any> = {}
): string => {
  const hasParams = Object.keys(params).length > 0;
  if (!hasParams) {
    return url;
  }
  const queryParams = Object.entries(params)
    .filter(([, value]) => Boolean(value))
    .map(([key, value]) => `${key}=${value}`)
    .join("&");

  const delimiter = url.includes("?") ? "&" : "?";
  return `${url}${delimiter}${queryParams}`;
};

/**
 * @method createSlug creates a slug using text and todays date for exports
 * @param text
 * @returns string
 */

function createSlug(text: string): string {
  const date = new Date();
  const dateString = date.toISOString().slice(0, 10);
  const slug = text
    .trim()
    .toLowerCase()
    .replace(/[\s\W-]+/g, "-")
    .replace(/^-+|-+$/g, "");

  return `${slug}-${dateString}`;
}

/**
 * @method csvExport
 * @param values
 * @param name
 * @returns
 */
export function htmlToCsv(
  table: HTMLTableElement,
  name: string,
  period: any
): HTMLAnchorElement {
  let csvContent = "data:text/csv;charset=utf-8,";

  const headerStyle =
    "background-color: #00b0f0; color: #ffffff; font-weight: bold;";
  const dataCellStyle = "border: 1px solid #cccccc; padding: 5px;";

  csvContent += `"${name}"\n\n`;
  csvContent += `"For Period: ${period}"\n\n`;

  const headers = Array.from(table.querySelectorAll("thead th"))
    .map((header: any) => `"${header.textContent.trim()}"`)
    .join(",");
  csvContent += `${headers}\n`;

  const headerRow = `<tr style="${headerStyle}">${headers}</tr>`;

  const rows = Array.from(table.querySelectorAll("tbody tr"));
  const dataRows = rows.map((row) => {
    return Array.from(row.querySelectorAll("td"))
      .map((cell: any) => `"${cell.textContent.trim()}"`)
      .join(",");
  });

  csvContent += `${dataRows.join("\n")}\n`;

  const data = encodeURI(csvContent);

  const link = document.createElement("a");

  link.setAttribute("href", data);
  link.setAttribute("download", `${createSlug(name)}.csv`);

  return link;
}

/**
 * Delays the execution of code by the specified number of milliseconds.
 *
 * @param ms The number of milliseconds to delay execution by.
 * @returns A Promise that resolves after the specified delay time has elapsed.
 */
export function delay(ms: number) {
  return new Promise<void>((resolve) => {
    setTimeout(() => {
      resolve();
    }, ms);
  });
}

export function getStandardDate(date?: string) {
  return moment(date).format("YYYY-MM-DD");
}

export function getStandardDatetime(datetime?: string) {
  return moment(datetime).format("YYYY-MM-DD HH:mm:ss");
}

export function printDocument(): void {
  window.print();
}

export function capitalize(name: string): string {
  const nameParts = name.toLowerCase().split(" ");

  const firstName = nameParts[0];

  const lastName = nameParts[nameParts.length - 1];

  let middleName = "";

  if (nameParts.length > 2) {
    middleName = nameParts.slice(1, -1).join(" ");
  }

  return `${firstName.charAt(0).toUpperCase()}${firstName.slice(1)} ${middleName.length > 0
      ? middleName.charAt(0).toUpperCase() + middleName.slice(1) + " "
      : ""
    }${lastName.charAt(0).toUpperCase()}${lastName.slice(1)}`;
}

export function capitalizeStr(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export function ExportToWord(element: string, filename: string = "") {
  var preHtml =
    "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'><head><meta charset='utf-8'><title>Export HTML To Doc</title></head><body>";

  var postHtml = "</body></html>";

  var html = preHtml + document.getElementById(element)?.innerHTML + postHtml;

  var url =
    "data:application/vnd.ms-word;charset=utf-8," + encodeURIComponent(html);

  filename = filename ? filename + ".doc" : "document.doc";

  var downloadLink = document.createElement("a");

  document.body.appendChild(downloadLink);

  downloadLink.href = url;

  downloadLink.download = filename;

  downloadLink.click();

  document.body.removeChild(downloadLink);
}

export function getStatusColor(status: Statuses): string {
  const statusColorMap = {
    "not-received": "#ec4899",
    pending: "#f59e0b",
    started: "#0ea5e9",
    completed: "#22c55e",
    verified: "#10b981",
    voided: "#6b7280",
    "not-done": "#6b7280",
    "test-rejected": "#ef4444",
    rejected: "#ef4444",
    "specimen-not-collected": "#6b7280",
    "specimen-accepted": "green",
    "specimen-rejected": "#ef4444",
  };
  return statusColorMap[status];
}


export function getIdByName(array: Array<{ id: number; name: string }>, name: string): number {
  const item = array.find(item => item.name === name);
  return item ? item.id : 0;
}
