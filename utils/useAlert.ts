import ConfirmationDialog from '@/components/core/ConfirmationDialog.vue';
import PrintingStatus from '@/components/core/PrintingStatus.vue';
import { ref, h, createApp, defineComponent, type Component } from "vue";

export interface AlertConfirmationOptions {
  message?: string;
  title?: string;
  color?: string;
  useYesNoBtns?: boolean
}

type VoidFunction = () => void

interface ComponentProps extends AlertConfirmationOptions {
  onConfirm?: VoidFunction;
  onCancel?: VoidFunction;
}

export function useAlert() {
  const show = ref(false)
  const mountComponent = (component: Component, rootContainer: HTMLDivElement) => createApp(component).mount(rootContainer);
  const destroyModal = (modal: HTMLDivElement) => document.body.removeChild(modal);
  const createModal = (): HTMLDivElement => {
    const modal = document.createElement("div");
    modal.id = "modal-wrapper-div";
    document.body.appendChild(modal);
    return modal
  }

  const resolveComponent = (component: any, props?: ComponentProps) => ({
    render: () => h(component, {
      ...props,
      show: show.value
    })
  })

  const alertConfirmation = async (opts: AlertConfirmationOptions) => {
    const modal = createModal()
    const confirmed = await new Promise(resolve => {
      const component = defineComponent({
        extends: resolveComponent(ConfirmationDialog, {
          onConfirm: () => resolve(true),
          onCancel:  () => resolve(false),
          ...opts
        })
      })
      mountComponent(component, modal);
      show.value = true
    })
    show.value = false
    destroyModal(modal);
    return confirmed
  }

  const showPrinterStatus = async () => {
    const statusBox = createModal();
    const component = defineComponent({ extends: resolveComponent(PrintingStatus) });
    mountComponent(component, statusBox);
    show.value = true
    return statusBox;
  }

  const hidePrinterStatus = (statusBox: HTMLDivElement) => {
    show.value = false;
    document.body.removeChild(statusBox);
  }

  return {
    alertConfirmation,
    showPrinterStatus,
    hidePrinterStatus,
  }
}
