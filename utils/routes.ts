import { FolderIcon, BuildingOffice2Icon, DocumentTextIcon, CalculatorIcon, ClipboardDocumentListIcon, ClipboardIcon, DocumentDuplicateIcon, HomeModernIcon, SquaresPlusIcon, TicketIcon, TruckIcon, CpuChipIcon, LifebuoyIcon } from "@heroicons/vue/20/solid/index.js";
import type { Route } from "@/types";

export const routes = {

    labConfigurations: <Route>[
        {
            name: 'Instruments',
            href: '/lab-configuration/instruments'
        },
        {
            name: 'Facilities',
            href: '/lab-configuration/facilities'
        },
        {
            name: 'Wards',
            href: '/lab-configuration/facility-wards'
        },
        {
            name: 'Visit Types',
            href: '/lab-configuration/visit-types'
        },
        {
            name: 'Surveillance',
            href: '/lab-configuration/surveillance'
        },
    ],
    sampleEntries: <Route>[
        {
            name: 'Viral Load',
            href: '/sample-entry/viral-load'
        },
        {
            name: 'EID',
            href: '/sample-entry/eid'
        },
    ],
    testCatalogues: <Route>[
        {
            name: 'Lab Sections',
            href: '/test-catalog/lab-sections'
        },
        {
            name: 'Specimen Types',
            href: '/test-catalog/specimen-types'
        },
        {
            name: 'Specimen Rejection',
            href: '/test-catalog/specimen-rejection'
        },
        {
            name: 'Test Types',
            href: '/test-catalog/test-types'
        },
        {
            name: 'Specimen Lifespan',
            href: '/test-catalog/specimen-lifespan'
        },
        {
            name: 'Test Panels',
            href: '/test-catalog/test-panels'
        },
        {
            name: 'Drugs',
            href: '/test-catalog/drugs'
        },
        {
            name: 'Organisms',
            href: '/test-catalog/organisms'
        },
        {
            name: 'Diseases',
            href: '/test-catalog/diseases'
        }
    ],
    reports: [
        {
            type: 'Daily',
            items: [
                {
                    name: 'Patient',
                    href: '/reports/daily/patient-report'
                },
                {
                    name: 'Daily Log',
                    href: '/reports/daily/daily-log'
                },
                {
                    name: 'WHONET',
                    href: '/reports/daily/whonet'
                }
            ]
        },
        {
            type: 'Aggregate',
            items: <Route>[
                {
                    name: 'Lab Statistics',
                    href: '/reports/aggregate/lab-statistics'
                },
                {
                    name: 'Department',
                    href: '/reports/aggregate/department'
                },
                {
                    name: 'TB Tests',
                    href: '/reports/aggregate/tb-tests'
                },
                {
                    name: 'Rejected Samples',
                    href: '/reports/aggregate/rejected-samples'
                },
                {
                    name: 'Turn Around Time',
                    href: '/reports/aggregate/turn-around-time'
                },
                {
                    name: 'Infection',
                    href: '/reports/aggregate/infection'
                },
                {
                    name: 'User Statistics',
                    href: '/reports/aggregate/user-statistics'
                },
                {
                    name: 'Culture & Sensitivity',
                    href: '/reports/aggregate/culture-sensitivity'
                },
                {
                    name: 'Malaria',
                    href: '/reports/aggregate/malaria'
                },
            ]
        },
        {
            type: 'MoH Diagnostic',
            items: [
                {
                    name: 'Biochemistry',
                    href: '/reports/moh/biochemistry'
                },
                {
                    name: 'Haematology',
                    href: '/reports/moh/haematology'
                },
                {
                    name: 'Blood Bank',
                    href: '/reports/moh/blood-bank'
                },
                {
                    name: 'Parasitology',
                    href: '/reports/moh/parasitology'
                },
                {
                    name: 'Microbiology',
                    href: '/reports/moh/microbiology'
                },
                {
                    name: 'Serology',
                    href: '/reports/moh/serology'
                }
            ]
        },
    ],
    stock: <Route>[
        {
            name: 'Stock',
            href: '/stock-management/stock',
            icon: ClipboardDocumentListIcon
        },
        {
            name: 'Stock Items',
            href: '/stock-management/stock-items',
            icon: ClipboardIcon
        },
        {
            name: 'Categories',
            href: '/stock-management/categories',
            icon: DocumentDuplicateIcon
        },
        {
            name: 'Orders',
            href: '/stock-management/orders',
            icon: TicketIcon
        },
        {
            name: 'Issue',
            href: '/stock-management/issue',
            icon: FolderIcon
        },
        {
            name: 'Locations',
            href: '/stock-management/locations',
            icon: HomeModernIcon
        },
        {
            name: 'Suppliers',
            href: '/stock-management/suppliers',
            icon: BuildingOffice2Icon
        },
        {
            name: 'Metrics',
            href: '/stock-management/metrics',
            icon: CalculatorIcon
        },
        {
            name: 'Transactions',
            href: '/stock-management/transactions',
            icon: TruckIcon
        },
        {
            name: 'Adjustments',
            href: '/stock-management/adjustments',
            icon: SquaresPlusIcon
        },
        {
            name: 'Reports',
            href: '/stock-management/reports',
            icon: DocumentTextIcon
        }
    ],
    accessControls: [
        {
            name: 'User Accounts',
            href: '/access-controls/user-accounts'
        },
        {
            name: 'Permissions',
            href: '/access-controls/permissions'
        },
        {
            name: 'Roles',
            href: '/access-controls/roles'
        }
    ],
    helpSupport: <Route>[
        {
            name: 'Usage Manual',
            href: '/help-support/usage-manual',
        },
        {
            name: 'Machine Integration',
            href: '/help-support/machine-integration',
        }
    ]
}
