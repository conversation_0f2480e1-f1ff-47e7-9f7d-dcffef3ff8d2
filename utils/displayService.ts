/**
 * Global Display Service - JavaScript/TypeScript equivalent of Ruby GlobalService
 * Provides utilities for choosing between full names and preferred names based on user preferences
 */
export class DisplayService {
  /**
   * Check if full name display is enabled based on user preference
   * @param preferenceValue - The current preference value
   * @returns boolean indicating if full name should be displayed
   */
  static isFullNameDisplay(
    preferenceValue: string = "preferred_name"
  ): boolean {
    return preferenceValue === "full_name";
  }

  /**
   * Get the appropriate display name based on user preference
   * @param fullName - The full name
   * @param preferredName - The preferred name (optional)
   * @param preferenceValue - The current preference value
   * @returns The name to display based on user preference
   */
  static getDisplayName(
    fullName: string,
    preferredName?: string | null | undefined,
    preferenceValue: string = "preferred_name"
  ): string {
    // Always return fullName if it's not provided
    if (!fullName) {
      return preferredName || "";
    }

    if (this.isFullNameDisplay(preferenceValue)) {
      return fullName;
    } else if (preferredName && preferredName.trim() !== "") {
      return preferredName;
    } else {
      return fullName;
    }
  }

  /**
   * Get test type display name
   * @param testTypeName - The full test type name
   * @param testTypePreferredName - The preferred test type name
   * @param preferenceValue - The current preference value
   * @returns The appropriate test type name to display
   */
  static getTestTypeDisplayName(
    testTypeName: string,
    testTypePreferredName?: string | null | undefined,
    preferenceValue: string = "preferred_name"
  ): string {
    return this.getDisplayName(
      testTypeName,
      testTypePreferredName,
      preferenceValue
    );
  }

  /**
   * Get test panel display name
   * @param testPanelName - The full test panel name
   * @param testPanelPreferredName - The preferred test panel name
   * @param preferenceValue - The current preference value
   * @returns The appropriate test panel name to display
   */
  static getTestPanelDisplayName(
    testPanelName: string,
    testPanelPreferredName?: string | null | undefined,
    preferenceValue: string = "preferred_name"
  ): string {
    return this.getDisplayName(
      testPanelName,
      testPanelPreferredName,
      preferenceValue
    );
  }

  /**
   * Get specimen display name
   * @param specimenName - The full specimen name
   * @param specimenPreferredName - The preferred specimen name
   * @param preferenceValue - The current preference value
   * @returns The appropriate specimen name to display
   */
  static getSpecimenDisplayName(
    specimenName: string,
    specimenPreferredName?: string | null | undefined,
    preferenceValue: string = "preferred_name"
  ): string {
    return this.getDisplayName(
      specimenName,
      specimenPreferredName,
      preferenceValue
    );
  }

  /**
   * Get the appropriate column name for database queries based on preference
   * @param fullNameColumn - The full name column name
   * @param preferredNameColumn - The preferred name column name
   * @param preferenceValue - The current preference value
   * @returns The column name to use in queries
   */
  static getColumnDisplayName(
    fullNameColumn: string,
    preferredNameColumn: string,
    preferenceValue: string = "preferred_name"
  ): string {
    if (this.isFullNameDisplay(preferenceValue)) {
      return fullNameColumn;
    } else {
      return `COALESCE(${preferredNameColumn}, ${fullNameColumn})`;
    }
  }
}

/**
 * Convenience function for getting test type display name
 * @param testTypeName - The full test type name
 * @param testTypePreferredName - The preferred test type name
 * @returns The appropriate test type name to display
 */
export const getTestTypeDisplayName = (
  testTypeName: string,
  testTypePreferredName?: string | null | undefined
): string => {
  return DisplayService.getTestTypeDisplayName(
    testTypeName,
    testTypePreferredName
  );
};

/**
 * Convenience function for getting test panel display name
 * @param testPanelName - The full test panel name
 * @param testPanelPreferredName - The preferred test panel name
 * @returns The appropriate test panel name to display
 */
export const getTestPanelDisplayName = (
  testPanelName: string,
  testPanelPreferredName?: string | null
): string => {
  return DisplayService.getTestPanelDisplayName(
    testPanelName,
    testPanelPreferredName
  );
};

/**
 * Convenience function for getting specimen display name
 * @param specimenName - The full specimen name
 * @param specimenPreferredName - The preferred specimen name
 * @returns The appropriate specimen name to display
 */
export const getSpecimenDisplayName = (
  specimenName: string,
  specimenPreferredName?: string | null
): string => {
  return DisplayService.getSpecimenDisplayName(
    specimenName,
    specimenPreferredName
  );
};

/**
 * Convenience function for getting any display name
 * @param fullName - The full name
 * @param preferredName - The preferred name
 * @returns The appropriate name to display
 */
export const getDisplayName = (
  fullName: string,
  preferredName?: string | null
): string => {
  return DisplayService.getDisplayName(fullName, preferredName);
};

export default DisplayService;
