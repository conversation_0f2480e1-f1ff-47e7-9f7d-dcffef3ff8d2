import type { Gender, TimeDuration } from "@/types";

const DATE_FORMAT: string = "DD/MM/YYYY";
const DATE_FORMAT_TIME: string = "DD/MM/YYYY HH:mm:ss";
const DATE_TIME_FORMAT: string = "DD/MM/YYYY HH:mm:ss";
const DATE_PICKER_FORMAT: string = "MM-DD-yyyy";
const DATE_FORMAT_NO_TIME: string = "DD/MM/YYYY";
const MONTH_STRING_DATE_FORMAT: string  = "MMMM-yyyy";
const sex: { name: string; label: string; value: string }[] = [
  { name: "Male", label: "Male", value: "M" },
  { name: "Female", label: "Female", value: "F" },
];
const ERROR_MESSAGE: string = "An error occurred, please try again!";
const SESSION_EXPIRY_MESSAGE: string = "Session expired! Please login again";
const INTERPRETATIONS = [
  {
    name: "--select result--",
  },
  {
    name: "I - Intermediate",
  },
  {
    name: "S - Sensitive",
  },
  {
    name: "R - Resistant",
  },
];

const SELECT_MEASURE_TYPE: string = "-- select type --";
const SELECT_VALUE_TYPE: string = "-- select value --";
const MEASURE_TYPES: string[] = ["autocomplete", "numeric", "alphanumeric"];
const GENDERS_LOWERCASE: string[] = ["male", "female", "both"];
const REPORT_INDICATORS_LOAD_FAILURE = "Failed to load report indicators, please reload the page";
const SELECT_DEPARTMENT: string = "select department";
const REPORTS_DOWNLOAD_SUCCESS: string = "Reports downloaded successfully";
const REPORTS_DOWNLOAD_ERROR: string = "An error occurred downloading reports";
const TEST_TYPES_MESSAGES: Record<string, string> = {
  DURATION_REQUIRED: "Please select duration for the turn around time!",
  MEASURE_TYPE_REQUIRED: "Please select measure type!",
  GENDER_REQUIRED: "Please select a gender for the numeric measure indicator range!",
  SELECT_GENDER: "-- select sex --",
};
const GENDER: Gender[] = [
  { name: "Male", label: "Male" },
  { name: "Female", label: "Female" },
  { name: "Both", label: "Both" },
];
const TEST_TYPE_PAGE_META = [
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Test Catalog",
    link: "#",
  },
  {
    name: "Test Types",
    link: "/test-catalog/test-types",
  },
];
const TIME_DURATION: TimeDuration[] = [
  { name: "Month" },
  { name: "Weeks" },
  { name: "Days" },
  { name: "Hours" },
  { name: "Minutes" },
];

const INACTIVITY_TIME: number = 1000 * 60 * 30;
const TIME_EXPIRATION_LEFT: number = 40 * 60 * 1000;

export {
  INACTIVITY_TIME,
  TIME_EXPIRATION_LEFT,
  DATE_FORMAT,
  DATE_FORMAT_TIME,
  DATE_PICKER_FORMAT,
  sex,
  ERROR_MESSAGE,
  SESSION_EXPIRY_MESSAGE,
  INTERPRETATIONS,
  SELECT_MEASURE_TYPE,
  SELECT_VALUE_TYPE,
  MEASURE_TYPES,
  GENDERS_LOWERCASE,
  SELECT_DEPARTMENT,
  TEST_TYPES_MESSAGES,
  GENDER,
  TEST_TYPE_PAGE_META,
  TIME_DURATION,
  REPORT_INDICATORS_LOAD_FAILURE,
  DATE_FORMAT_NO_TIME,
  REPORTS_DOWNLOAD_SUCCESS,
  REPORTS_DOWNLOAD_ERROR,
  MONTH_STRING_DATE_FORMAT,
  DATE_TIME_FORMAT
};
