import { nlimsSync } from "@/services/nlims-sync";
import { endpoints } from "@/services/endpoints";
import type { Request, Response } from "@/types";

/**
 * TestCatalogModule - Handles test catalog data syncing from NLIMS controller
 * Uses NLIMS sync service instead of regular HttpFactory for data synchronization
 */
class TestCatalogModule {

    private ORGANISMS = endpoints.organisms;
    private SPECIMENS = endpoints.specimens;
    private DEPARTMENTS = endpoints.departments;
    private TEST_TYPE_INDICATORS = `${endpoints.testTypes}/test_indicator_types`

    /**
     * Sync organisms data from NLIMS controller
     * @param token Authentication token
     * @returns Promise<Response>
     */
    async getOrganisms(token: string) : Promise<Response> {
        const request : Request = {
            route: this.ORGANISMS,
            method: 'GET',
            token: `${token}`
        }
        return nlimsSync.syncData(request)
    }

    /**
     * Sync specimens data from NLIMS controller
     * @param token Authentication token
     * @returns Promise<Response>
     */
    async getSpecimens(token: string) : Promise<Response> {
        const request : Request = {
            route: this.SPECIMENS,
            method: 'GET',
            token: `${token}`
        }
        return nlimsSync.syncData(request)
    }

    /**
     * Sync departments data from NLIMS controller
     * @param token Authentication token
     * @returns Promise<Response>
     */
    async getDepartments(token: string) : Promise<Response> {
        const request : Request = {
            route: this.DEPARTMENTS,
            method: 'GET',
            token: `${token}`
        }
        return nlimsSync.syncData(request)
    }

    /**
     * Sync test types indicators data from NLIMS controller
     * @param token Authentication token
     * @returns Promise<Response>
     */
    async getTestTypesIndicators(token: string) : Promise<Response> {
        const request : Request = {
            route: this.TEST_TYPE_INDICATORS,
            method: 'GET',
            token: `${token}`
        }
        return nlimsSync.syncData(request)
    }

}

export default TestCatalogModule;
