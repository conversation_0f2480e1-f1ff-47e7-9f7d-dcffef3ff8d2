import HttpFactory from "@/repository/factory";
import type { Request, Response, StockOrder } from "@/types";

class StockModule extends HttpFactory{

    private STOCK_CATEGORY = 'stock_categories';
    private STOCK_SUPPLIER = 'stock_suppliers';
    private STOCK_ITEM = 'stock_items';
    private STOCK_UNIT = 'stock_units';
    private STOCK_LOCATION = 'stock_locations';
    private STOCK_ORDER = 'stock_orders';
    private STOCK_ORDER_STATUS = 'stock_order_statuses';
    private STOCK = 'stocks';
    private PHARMACY = 'stock_pharmacy_approver_and_issuers';
    private STOCK_MOVEMENTS = 'stock_movements';
    private STOCK_TRANSACTION_TYPES = 'stock_transaction_types';
    private STOCK_ADJUSTMENT_REASONS = 'stock_adjustment_reasons';
    private STOCK_REPORTS = 'stock_reports';

    async getStockCategory(token: string) : Promise<Response> {
        const request : Request = {
            route: this.STOCK_CATEGORY,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async createStockCategory(token: string, params: { name: string }) : Promise<Response> {
        const request : Request = {
            route: this.STOCK_CATEGORY,
            method: 'POST',
            body: params,
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async updateStockCategory(token: string, params: { id: number, name: string }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_CATEGORY}/${params.id}`,
            method: 'PUT',
            token: `${token}`,
            body: params
        }
        return this.call<Response>(request)
    }
    async readStockCategory(token: string, params: { id: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_CATEGORY}/${params.id}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async voidStockCategory(token: string, params: { reason: string, id: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_CATEGORY}/${params.id}`,
            method: 'DELETE',
            token: `${token}`,
            body: params
        }
        return this.call<Response>(request)
    }
    /**
     * stock suppliers
     */
    async getStockSupplier(token: string) : Promise<Response> {
        const request : Request = {
            route: this.STOCK_SUPPLIER,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async createStockSupplier(token: string, params: { name: string, address: string }) : Promise<Response> {
        const request : Request = {
            route: this.STOCK_SUPPLIER,
            method: 'POST',
            body: params,
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async updateStockSupplier(token: string, params: { id: number, name: string, address: string }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_SUPPLIER}/${params.id}`,
            method: 'PUT',
            token: `${token}`,
            body: params
        }
        return this.call<Response>(request)
    }
    async readStockSupplier(token: string, params: { id: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_SUPPLIER}/${params.id}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async voidStockSupplier(token: string, params: { reason: string, id: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_SUPPLIER}/${params.id}`,
            method: 'DELETE',
            token: `${token}`,
            body: params
        }
        return this.call<Response>(request)
    }
    /**
     * stock units
     */
    async getStockUnit(token: string) : Promise<Response> {
        const request : Request = {
            route: this.STOCK_UNIT,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async createStockUnit(token: string, params: { name: string }) : Promise<Response> {
        const request : Request = {
            route: this.STOCK_UNIT,
            method: 'POST',
            body: params,
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async updateStockUnit(token: string, params: { id: number, name: string }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_UNIT}/${params.id}`,
            method: 'PUT',
            token: `${token}`,
            body: params
        }
        return this.call<Response>(request)
    }
    async readStockUnit(token: string, params: { id: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_UNIT}/${params.id}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async voidStockUnit(token: string, params: { reason: string, id: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_UNIT}/${params.id}`,
            method: 'DELETE',
            token: `${token}`,
            body: params
        }
        return this.call<Response>(request)
    }
    /**
     * stock items
     */
    async getStockItem(token: string, param: string = "") : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ITEM}?${param}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async createStockItem(token: string, params: { name: string, description: string, stock_location_id: number, stock_category_id: number, measurement_unit: number, quantity_unit: number, strength: string, minimum_order_level: number }) : Promise<Response> {
        const request : Request = {
            route: this.STOCK_ITEM,
            method: 'POST',
            body: params,
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async updateStockItem(token: string, params: { id: number, name: string, description: string, stock_location_id: number, stock_category_id: number, measurement_unit: number, quantity_unit: number, strength: string, minimum_order_level: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ITEM}/${params.id}`,
            method: 'PUT',
            token: `${token}`,
            body: params
        }
        return this.call<Response>(request)
    }
    async readStockItem(token: string, params: { id: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ITEM}/${params.id}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async voidStockItem(token: string, params: { reason: string, id: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ITEM}/${params.id}`,
            method: 'DELETE',
            token: `${token}`,
            body: params
        }
        return this.call<Response>(request)
    }

    async importStockItems(token: string, formData: FormData) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ITEM}/import`,
            method: 'POST',
            body: formData,
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    /**
     * stock locations
     */
    async getStockLocation(token: string) : Promise<Response> {
        const request : Request = {
            route: this.STOCK_LOCATION,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async createStockLocation(token: string, params: { name: string, description: string }) : Promise<Response> {
        const request : Request = {
            route: this.STOCK_LOCATION,
            method: 'POST',
            body: params,
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async updateStockLocation(token: string, params: { id: number, name: string, description: string }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_LOCATION}/${params.id}`,
            method: 'PUT',
            token: `${token}`,
            body: params
        }
        return this.call<Response>(request)
    }
    async readStockLocation(token: string, params: { id: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_LOCATION}/${params.id}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    async voidStockLocation(token: string, params: { reason: string, id: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_LOCATION}/${params.id}`,
            method: 'DELETE',
            token: `${token}`,
            body: params
        }
        return this.call<Response>(request)
    }
    /**
     * stock orders
     */
    async checkStockOrder(token: string, params: { voucher_number: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ORDER}/check_voucher_number?voucher_number=${params.voucher_number}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async createStockOrder(token: string, params: StockOrder ) : Promise<Response> {
        const request : Request = {
            route: this.STOCK_ORDER,
            method: 'POST',
            body: params,
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async getStockOrder(token: string, stockId?: string, search?: string, stock_status_id?: string, pagination?: string) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ORDER}/${stockId}?search=${search}&stock_status_id=${stock_status_id}&${pagination}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async getStockOrderStatus(token: string) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ORDER}/stock_statuses`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async rejectStockOrder(token: string, params: { stock_order_id: string, stock_status_reason: string }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ORDER_STATUS}/reject_order`,
            method: 'PUT',
            token: `${token}`,
            body: params,
        }
        return this.call<Response>(request)
    }

    async verifyStockOrder(token: string, params: { stock_order_id: string, stock_requisition_ids: Array<number> }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ORDER_STATUS}/approve_order_request`,
            method: 'PUT',
            token: `${token}`,
            body: params,
        }
        return this.call<Response>(request)
    }

    async verifyStockOrderRequisition(token: string, params: { stock_order_id: string, stock_requisition_id: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ORDER_STATUS}/approve_stock_requisition_request`,
            method: 'PUT',
            token: `${token}`,
            body: params,
        }
        return this.call<Response>(request)
    }

    async receiveStockOrder(token: string, params: { stock_order_id: string }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ORDER_STATUS}/receive_stock_order`,
            method: 'PUT',
            token: `${token}`,
            body: params,
        }
        return this.call<Response>(request)
    }

    async approveStockOrder(token: string, params: { stock_order_id: string, stock_requisition_ids: Array<number> }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ORDER_STATUS}/approve_stock_order_receipt`,
            method: 'PUT',
            token: `${token}`,
            body: params,
        }
        return this.call<Response>(request)
    }


    async receiveStockOrderRequisition(
        token: string,
        params: {
            stock_requisition_id: string,
            requisition: { quantity_received: number, quantity_issued: number, quantity_not_collected?: number, not_collected_reason?: string},
            transaction: { lot?: string, batch?: string, expiry_date: string, remarks: string }
        }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ORDER_STATUS}/receive_requisition`,
            method: 'POST',
            token: `${token}`,
            body: params,
        }
        return this.call<Response>(request)
    }

    async approveStockOrderRequisition(token: string, params: { stock_requisition_id: number }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ORDER_STATUS}/approve_stock_requisition`,
            method: 'PUT',
            token: `${token}`,
            body: params,
        }
        return this.call<Response>(request)
    }


    async updateStockOrderStatus(token: string, params: any ) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ORDER_STATUS}/${params.route}`,
            method: 'PUT',
            body: params,
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async createStockOrderPharmacy(token: string, params: { stock_order_id: string, record_type: string, name: string, designation: string, phone_number: string, signature: string }) : Promise<Response> {
        const request : Request = {
            route: `${this.PHARMACY}`,
            method: 'POST',
            token: `${token}`,
            body: params,
        }
        return this.call<Response>(request)
    }

    async updateStockOrderPharmacy(token: string, params: { pharmacy_id: string, name: string, designation: string, phone_number: string, signature: string }) : Promise<Response> {
        const request : Request = {
            route: `${this.PHARMACY}/${params.pharmacy_id}`,
            method: 'PUT',
            token: `${token}`,
            body: params,
        }
        return this.call<Response>(request)
    }
    /**
     * stock orders
     */
    async getStock(token: string, params: string) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK}/${params}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }
    /**
     * stock transactions
     */
    async checkStockQuantity(token: string, params: { stock_item_id: number, quantity: number, batch: string, lot: string }) : Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_MOVEMENTS}/deduction_allowed?stock_item_id=${params.stock_item_id}&quantity=${params.quantity}&batch=${params.batch}&lot=${params.lot}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async getStockTransactions(token: string, params: string): Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_MOVEMENTS}/transactions?${params}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async getStockTransactionTypes(token: string): Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_TRANSACTION_TYPES}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async getStockIssues(token: string, params: string): Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_MOVEMENTS}/stock_movement_with_respective_transaction?${params}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async approveStockIssues(token: string, issueId: string): Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_MOVEMENTS}/approve_issue_out/`,
            method: 'PUT',
            token: `${token}`,
            body: {
                stock_movement_id: issueId
            }
        }
        return this.call<Response>(request)
    }

    async rejectStockIssues(token: string, issueId: string, reason: string): Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_MOVEMENTS}/reject_issue_out`,
            method: 'PUT',
            token: `${token}`,
            body: {
                stock_movement_id: issueId,
                stock_status_reason: reason
            }
        }
        return this.call<Response>(request)
    }


    async stockOutTransaction(token: string, params: {sending_to: string, stock_status_reason: string, stock_items: Array<{stock_item_id: number, quantity: number, lot: string, batch: string}>}): Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_MOVEMENTS}/issue_stock_out`,
            method: 'POST',
            token: `${token}`,
            body: params
        }
        return this.call<Response>(request)
    }

    async receiveExternalStock(token: string, params: {receiving_from: string, stock_status_reason: string, stock_items: Array<{stock_item_id: number, quantity: number, lot: string, batch: string}>}): Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_MOVEMENTS}/receive_external_stock`,
            method: 'POST',
            token: `${token}`,
            body: params
        }
        return this.call<Response>(request)
    }
    /**
     * stock adjustment
     */
    async getStockAdjustmentReasons(token: string): Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ADJUSTMENT_REASONS}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async adjustStock(token: string, body: Object): Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_MOVEMENTS}/adjust_stock`,
            method: 'PUT',
            token: `${token}`,
            body: body
        }
        return this.call<Response>(request)
    }

    async reverseStockAdjustment(token: string, body: Object): Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_MOVEMENTS}/reverse_stock_adjustment`,
            method: 'PUT',
            token: `${token}`,
            body: body
        }
        return this.call<Response>(request)
    }

    async getStockAdjustments(token: string): Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_ADJUSTMENT_REASONS}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async generateStockMovementReport(token: string, type: string, from: string, to: string): Promise<Response> {
        const request : Request = {
            route: `${this.STOCK_REPORTS}/stock_movement?transaction_type=${type}&from=${from}&to=${to}`,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

}

export default StockModule;
