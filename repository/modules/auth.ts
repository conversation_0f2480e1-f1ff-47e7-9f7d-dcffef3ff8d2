import HttpFactory from "@/repository/factory";
import { Request, UserPayloadInterface, Response } from "types";
class AuthModule{
    private RESOURCE = 'auth/login'
    constructor(private httpFactory: HttpFactory) {}
    async login(credentials: UserPayloadInterface) : Promise<Response> {
        const request : Request = {
            route: this.RESOURCE,
            method: 'POST',
            body: credentials
        }
        return this.httpFactory.call<Response>(request)
    }
}
export default AuthModule
