<template>
  <div class="flex flex-col items-center py-24">
    <img src="@/assets/icons/fever.svg" alt="sick-icon" class="w-32 h-32 object-cover" />
    <h3 class="mt-5 text-3xl font-semibold">{{ error?.statusCode }}</h3>
    <h1 class="text-xl mt-2 mb-2"><strong>{{ error?.message }}</strong></h1>
    <p class="mb-2">The page you are looking for is not found or has been removed or another unexpected error has occured.</p>
    <CoreActionButton :click="handleError" text="Go to home" :icon="ArrowLeftIcon" color="primary" type="button" />
  </div>
</template>

<script setup lang="ts">

import { ArrowLeftIcon } from '@heroicons/vue/24/solid/index.js';

const error = useError();
const handleError = () => {
  clearError({
    redirect: '/home',
  });
};
</script>
