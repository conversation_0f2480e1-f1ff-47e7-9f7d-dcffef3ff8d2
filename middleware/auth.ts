import { storeToRefs } from "pinia";
import { useRouteStore } from "@/store/route";
import { useAuthStore } from "@/store/auth";

export default defineNuxtRouteMiddleware(() => {
  const { authenticated } = storeToRefs(useAuthStore());
  const token = useCookie("token");
  if (token.value) {
    authenticated.value = true;
  }
  if (authenticated) {
    const { route } = useRouteStore();
    navigateTo(route)
  }else{
    return navigateTo("/");
  }

  return
});
