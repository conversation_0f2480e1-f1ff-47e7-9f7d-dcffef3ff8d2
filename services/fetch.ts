import type { UseFetchOptions } from "#app";
import { useNetworkStore } from "@/store/network";
import type { Request, Response } from "@/types";
import { useToken } from "@/store/token";

/**
 * Handles generic HTTP requests using the useFetch composable.
 * @param request Request object
 * @returns Promise<Response>
 */
export default async function fetchRequest(
  request: Request,
  cancellation?: RequestCancellation
): Promise<Response> {

  const { ip, port } = useNetworkStore();
  const { token } = useToken();

  const baseUrl = `http://${ip}:${port}/api/v1/`;

  const fetchOptions: UseFetchOptions<Record<string, any>> = {
    method: `${request.method}`,
    headers: {
      Authorization: `${token}`,
    },
    body:
      request.method !== "GET" && request.method !== "HEAD"
        ? request.body
        : null,
  };

  if (cancellation) {
    fetchOptions.signal = cancellation.initiate();
  }

  const { data, error, pending } = await useFetch(
    `${baseUrl}${request.route}`,
    fetchOptions
  );

  if (error.value) {
    handleError(error.value, request.route);
  }

  return { data, error, pending };
}

const handleError = (error: any, route: string): void => {
  if (interceptor(error.statusCode) && !route.includes("login")) {
    if (useNuxtApp().$toast) {
      useNuxtApp().$toast.error(SESSION_EXPIRY_MESSAGE);
      useNuxtApp().$router.push("/");
    }
  }
};

export class RequestCancellation {
  private controller: AbortController | null = null;

  initiate(): AbortSignal {
    this.controller = new AbortController();
    return this.controller.signal;
  }

  cancel(nuxtApp: typeof useNuxtApp): void {
    this.controller?.abort();
    this.controller = null;
    nuxtApp().$toast.warning("Request has been aborted!");
  }
}