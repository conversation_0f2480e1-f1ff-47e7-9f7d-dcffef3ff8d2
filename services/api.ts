import fetchRequest from './fetch';
import { isEmpty } from "@/utils/functions";


export function parameterizeURL(url: string, params: Record<string, string | number>) {
    if(isEmpty(params)) return url
    return url + '?' + Object.entries(params).map(([key, value]) => `${key}=${value}`).join('&')
}

export function expandUrl (url: string) {
    return url
}

export default new class Api {
    getJson(url: string, params = {} as Record<string, string | number>) {
        return fetchRequest({
            route: parameterizeURL(expandUrl(url), params),
            method: "GET",
            token: useCookie('token')?.value || '',
            body: {}
        })
        .then(({data, error}) => {
            if(error.value) throw error.value.data
            if(data.value) return data.value
        })
        .catch(err => {
            console.error(err)
            useNuxtApp().$toast.error(`${ERROR_MESSAGE}`)
        })
    }

    postJson(url: string, data: Record<string, any>, params = {} as Record<string, string | number>) {
        return fetchRequest({
            route: parameterizeURL(expandUrl(url), params),
            method: "POST",
            token: useCookie('token')?.value || '',
            body: data
        })
        .then(({data, error}) => {
            if(error.value) throw error.value.data
            if(data.value) return data.value
        })
        .catch(err => {
            console.error(err)
            useNuxtApp().$toast.error(`${ERROR_MESSAGE}`)
        })
    }
}
