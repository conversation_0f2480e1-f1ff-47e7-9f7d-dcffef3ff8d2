import Api from "./api";
import { useAuthStore } from "@/store/auth";
import moment from "moment";

export interface Test {
  specimen: number;
  test_type: string;
}

export interface Encounter {
  sending_facility: number;
  destination_facility?: number;
  encounter_type: number;
  facility_section: number;
}

export interface Client {
  id: string | number;
  uuid: string;
}

export interface Order {
  priority: number,
  requested_by: string,
  collected_by: string,
  sample_collected_time: string,
  tracking_number: ""
}

export class OrderService {
  private client: Client = {id: "", uuid: ""};
  private tests: Array<Test> = [];
  private order: Order = {
    priority: 1,
    requested_by: "",
    collected_by: useAuthStore().$state.department,
    sample_collected_time: "",
    tracking_number: "",
  }

  private encounter: Encounter = {
    sending_facility: 1,
    encounter_type: 1,
    facility_section: 1
  }

  constructor (patientId: number) {
    this.client.id = patientId
  }

  createEncounter (encounter_type: number, facility_section: number) {
    this.encounter = { ...this.encounter, encounter_type, facility_section }
  }

  buildOrder (requester = "", date = moment().format("YYYY-MM-DD HH:mm:ss")) {
    this.order.requested_by = requester;
    this.order.sample_collected_time = date
  }

  setTests (specimen: number, tests: Array<string>) {
    this.tests = tests.map(test => ({ specimen, test_type: test }))
  }

  async createOrder (labLocation: number) {
    return Api.postJson('orders', {
      tests: this.tests,
      order: this.order,
      encounter: this.encounter,
      client: this.client,
      lab_location: labLocation
    })
  }
}
