import Api from "./api"
import { useAlert } from "@/utils/useAlert";

const { showPrinterStatus, hidePrinterStatus } = useAlert()


export default  new class PrinterService {
  async writeLbl (url: string, params?: Record<string, string | number>, filename = `${Date.now()}.lbl`) {
    const statusModal = await showPrinterStatus()
    try {
      const res = await Api.getJson(url, params)
      url = URL.createObjectURL(res)
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', filename)
      link.click();
      URL.revokeObjectURL(url)
      await delay(2000);
    } catch (error) {
      console.error(error);
      useNuxtApp().$toast.error("Unable to print the label")
    }
    hidePrinterStatus(statusModal)
  }

  printSpecimenLabel (accession_number: string) {
    return this.writeLbl('printout/accession_number', { accession_number }, `${accession_number}.lbl`)
  }

  printTrackingNumber (tracking_number: string) {
    return this.writeLbl('printout/tracking_number', { tracking_number}, `${tracking_number}.lbl`);
  }
}
