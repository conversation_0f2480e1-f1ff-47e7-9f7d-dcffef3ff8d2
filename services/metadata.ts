import fetchRequest from '@/services/fetch';
import { endpoints } from '@/services/endpoints';
import type { Department, Request, Ward } from '@/types';

export const fetchMetadata = async (): Promise<{ departments: Department[], wards: Ward[] }> => {
  const requests: Request[] = [
    { route: endpoints.departments, method: 'GET' },
    { route: 'facility_sections', method: 'GET' },
  ];

  try {
    const responses = await Promise.all(
      requests.map(async (req: Request) => {
        try {
          return await fetchRequest(req);
        } catch (error) {
          console.error(`Error fetching ${req.route}:`, error);
          return null;
        }
      })
    );

    const [departmentsResponse, wardsResponse] = responses;
    return {
      departments: Array.isArray(departmentsResponse?.data?.value) 
        ? <Department[]>departmentsResponse.data.value 
        : [],
      wards: Array.isArray(wardsResponse?.data?.value?.data) 
        ? <Ward[]>wardsResponse.data.value.data 
        : [],
    };
  } catch (error) {
    console.error('Error fetching metadata:', error);
    return { departments: [], wards: [] };
  }
};
