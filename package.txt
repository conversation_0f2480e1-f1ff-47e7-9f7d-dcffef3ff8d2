{"private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@pinia-plugin-persistedstate/nuxt": "^1.1.1", "nuxt": "3.3.1"}, "dependencies": {"@chenfengyuan/vue-barcode": "2", "@formkit/icons": "^0.16.4", "@formkit/nuxt": "^0.16.2", "@formkit/vue": "^0.16.2", "@headlessui/vue": "^1.7.13", "@heroicons/vue": "^2.0.14", "@nuxt/types": "^2.16.3", "@nuxt/vite-builder": "^3.2.3", "@nuxtjs/axios": "^5.13.6", "@nuxtjs/tailwindcss": "^6.3.1", "@pinia/nuxt": "^0.4.7", "@tailwindcss/line-clamp": "^0.4.2", "@types/vue": "^2.0.0", "@unhead/vue": "^1.1.23", "@vueform/multiselect": "^2.5.8", "@vuelidate/core": "^2.0.0", "@vuelidate/validators": "^2.0.0", "@vuepic/vue-datepicker": "^5.1.2", "axios": "^1.3.4", "chart.js": "^4.2.1", "flag-icons": "^6.6.6", "fs-extra": "^11.1.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.1", "jsbarcode": "^3.11.5", "jspdf": "^2.5.1", "lodash": "^4.17.21", "moment": "^2.29.4", "next-auth": "^4.20.1", "nitro": "^2.2.28", "node-sass": "^8.0.0", "pinia": "^2.0.33", "quagga": "^0.12.1", "sass": "^1.58.0", "tailwindcss": "^3.2.4", "vee-validate": "^4.8.3", "vue": "3", "vue-chartjs": "^5.2.0", "vue-phone-number-input": "^1.12.13", "vue-router": "^4.1.6", "vue3-easy-data-table": "^1.5.31", "vue3-toastify": "^0.1.5", "vue3-xlsx": "^1.1.2", "vuelidate": "^0.7.7", "vuex": "^4.1.0"}}