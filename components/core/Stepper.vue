<template>
    <div class="flex flex-col">
  
      <div>
        <slot :key="index" :step="currentStep"></slot>
      </div>

      <div class="bg-white mt-3 w-full flex items-center space-x-3 justify-end">

        <button 
            class="flex items-center bg-gray-200 rounded px-4 py-1.5 focus:bg-gray-100 focus:ring-2 focus:ring-gray-100 text-gray-500" 
            @click="decrementStep" 
            v-if="currentStep > 1"
            >
            <ArrowLongLeftIcon class="w-5 h-5 mr-2"/>
            Previous
        </button>

        <button 
            class="flex items-center bg-sky-500 rounded px-4 py-1.5 focus:bg-sky-400 focus:ring-2 focus:ring-sky-400 text-white" 
            @click="incrementStep()" 
            v-if="currentStep !== steps"
        >
            Next
            <ArrowLongRightIcon class="w-5 h-5 ml-2"/>
        </button>

      </div>

    </div>
</template>
  
<script>

import { ArrowLongLeftIcon, ArrowLongRightIcon } from '@heroicons/vue/24/solid/index.js';


export default {
    name: "Stepper",
    props: {
        steps: {
            type: Number,
            required: true
        }
    },
    data() {
        return {
            currentStep: 1,
        };
    },
    methods: {
        incrementStep() {
            this.currentStep += 1;
        },
        decrementStep() {
            if (this.currentStep > 1) {
                this.currentStep -= 1;
            }
        },
    },
    components: { ArrowLongRightIcon, ArrowLongLeftIcon }
};
</script>
  