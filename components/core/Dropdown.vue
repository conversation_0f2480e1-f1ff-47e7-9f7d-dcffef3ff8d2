<template>
  <Listbox v-model="selectedItem" :class="{ 'required': isRequired && !selectedItem }">
    <div class="relative">
      <ListboxButton
        class="relative w-full cursor-default rounded border py-2 pl-3 pr-10 text-left focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none sm:text-sm">
        <span class="block truncate">{{ selectedItem.name || placeholder }}</span>
        <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
          <ChevronUpDownIcon class="h-5 w-5 text-gray-500" aria-hidden="true" />
        </span>
      </ListboxButton>

      <transition leave-active-class="transition duration-100 ease-in" leave-from-class="opacity-100"
        leave-to-class="opacity-0">
        <ListboxOptions
          class="absolute mt-0 w-full max-h-96 overflow-auto rounded-md bg-white py-1 text-base ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm drop-shadow-md divide-y divide-gray-100"
          style="z-index: 10000;">
          <div v-if="isSearchable" class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center p-3 pointer-events-none">
              <MagnifyingGlassIcon class="w-5 h-5" />
            </div>
            <input v-model="search" type="text" placeholder="Search..."
              class="w-full pl-10 px-4 py-2 focus:ring-none focus:outline-none">
          </div>

          <ListboxOption v-slot="{ active, selected }" v-for="(item, index) in filteredItems" :key="index" :value="item"
            as="template">
            <li :class="[
              active ? 'bg-green-100 text-green-500' : 'text-gray-900',
              'relative cursor-default select-none py-2 px-4',
            ]">
              <span :class="[
                selected ? 'font-medium' : 'font-normal',
                'block pl-6',
              ]">{{ item.name }}</span>
              <span v-if="selected" class="absolute inset-y-0 left-0 flex items-center pl-3 text-green-500">
                <CheckCircleIcon class="h-5 w-5" aria-hidden="true" />
              </span>
            </li>
          </ListboxOption>
        </ListboxOptions>
      </transition>
    </div>
  </Listbox>
</template>

<script lang="ts">
import {
  Listbox,
  ListboxLabel,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from '@headlessui/vue'
import { ChevronUpDownIcon, CheckIcon, CheckCircleIcon, MagnifyingGlassIcon } from '@heroicons/vue/24/solid/index.js';
import type { DropdownItem } from '@/types';

export default {
  components: {
    Listbox,
    ListboxLabel,
    ListboxButton,
    ListboxOptions,
    ListboxOption,
    ChevronUpDownIcon,
    CheckIcon,
    CheckCircleIcon,
    MagnifyingGlassIcon
  },
  props: {
    items: {
      required: true,
      type: Array<DropdownItem>
    },
    modelValue: {
      type: Object,
      default: false,
    },
    isSearchable: {
      type: Boolean,
      default: false,
      required: false
    },
    placeholder: {
      type: String,
      default: '',
      required: false
    }
  },

  data() {
    return {
      value: this.modelValue as DropdownItem,
      search: '' as string,
      isRequired: true
    }
  },
  computed: {
    selectedItem: {
      get() {
        return this.modelValue;
      },
      set(newValue: any) {
        this.$emit('update:modelValue', newValue);
      },
    },
    filteredItems() {
      return this.items.filter((item) => item.name.toLowerCase().includes(this.search.toLowerCase()))
    }
  }
}
</script>

<style>
.required .relative::before {
  content: '*';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  color: red;
  font-weight: bold;
}
</style>
