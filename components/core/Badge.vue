<template>
    <div :class="`bg-${color}-500 border-2 border-${color}-400`" class="text-white rounded px-2 py-1 text-sm  text-center capitalize">
        {{ text }}
    </div>
</template>
<script>
export default defineComponent({
    name: 'Badge',
    props: {
        text: {
            type: String,
            required: true
        },
        color: {
            type: String
        }
    }
})
</script>
<style lang="">

</style>
