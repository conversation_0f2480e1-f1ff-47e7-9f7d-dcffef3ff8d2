<template>
  <div class="relative">
    <FormKit
      label=""
      type="search"
      v-model.lazy="value"
      :placeholder="placeholder"
      :delay="1000"
      prefix-icon="searchIcon"
      autocomplete="off"
      @blur="hideSuggestions"
      @focus="showSuggestions = true"
    />
    <button
      v-show="value !== ''"
      @click="clearValue"
      class="absolute inset-y-0 right-0 flex items-center pr-3"
    >
      <XMarkIcon class="w-5 h-5 mt-2" />
    </button>
    <ul
      v-if="showSuggestions && suggestions.length > 0"
      class="absolute z-50 top-full left-0 w-full bg-white rounded-md shadow-md"
    >
      <li
        v-for="(suggestion, index) in suggestions"
        :key="index"
        @click="selectSuggestion(suggestion)"
        class="px-3 py-2 hover:bg-gray-100 cursor-pointer"
      >
        {{ suggestion }}
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { XMarkIcon } from "@heroicons/vue/24/solid/index.js";
import { useSearchStore } from "@/store/search";

const props = defineProps({
  search: {
    required: true,
    type: String,
  },
  placeholder: {
    required: false,
    type: String,
    default: "Search...",
  },
});

const emit = defineEmits(["update", "update:search"]);

const value = ref(props.search);
const history = ref<string[]>([]);
const showSuggestions = ref(false);

const searchStore = useSearchStore();

const emitValue = () => {
  let searchValue = value.value.trim();
  emit("update", searchValue);
  emit("update:search", searchValue);
  if (
    searchValue &&
    searchValue.length >= 4 &&
    !history.value.includes(searchValue)
  ) {
    history.value.unshift(searchValue);
    saveHistory(searchValue);
  }
};

/**
 * @method saveHistory save search into the search store
 * @param query string
 * @returns {void}
 */
const saveHistory = (query: string): void => {
  searchStore.addRecent(query);
  if (searchStore.recent.length > 20) {
    searchStore.recent.pop();
  }
};

const loadHistory = (): void => {
  history.value = searchStore.recent;
};

/**
 * @method filterOptions filters history items
 * @param userInput 
 * @returns @type {string[]}
 */
const filterOptions = (userInput: string): string[] => {
  return history.value.filter((item) =>
    item.toLowerCase().includes(userInput.toLowerCase().trim())
  );
};

/**
 * @method selectSuggestion shows suggestions
 * @param suggestion 
 * @returns {void}
 */
const selectSuggestion = (suggestion: string): void => {
  value.value = suggestion;
  showSuggestions.value = false;
};

/**
 * @method hideSuggestions minimal debounce timeout fn
 * @returns {void}
 */
const hideSuggestions = (): void => {
  setTimeout(() => {
    showSuggestions.value = false;
  }, 200);
};

/**
 * @method clearValue clears search value and hides suggestions
 * @returns {void}
 */
const clearValue = (): void => {
  value.value = "";
  showSuggestions.value = false;
};

/**
 * @method computed{suggestions} filter first 5 items
 * @returns {string[]}
 */
const suggestions = computed((): string[] => {
  return filterOptions(value.value).slice(0, 5);
});

watch(value, () => {
  emitValue();
});

onMounted(() => {
  loadHistory();
});
</script>
