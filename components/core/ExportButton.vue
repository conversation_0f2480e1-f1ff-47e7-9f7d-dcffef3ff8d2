<template>
  <button
    class="flex items-center rounded py-2 px-2 text-white text-sm"
    :class="type == 'excel' ? 'bg-green-500' : 'bg-red-500'"
  >
    <img
      v-if="type == 'excel'"
      src="@/assets/icons/excel.png"
      alt="excel-icon"
      class="w-5 h-5 mr-2"
    />
    <img
      v-if="type == 'pdf'"
      src="@/assets/icons/pdf.png"
      alt="pdf-icon"
      class="w-5 h-5 mr-2"
    />
    {{ props.text }}
  </button>
</template>

<script setup lang="ts">
const props = defineProps({
  text: {
    required: true,
    type: String,
  },
  type: {
    required: false,
    type: String as PropType<"excel" | "pdf">,
    default: "excel",
  },
});
</script>
