<template>
  <div>
    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>
        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
                <div class="border-b px-3 py-3 flex items-center justify-between">
                  <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                    <ExclamationTriangleIcon class="h-5 w-5 mr-2"/>
                    {{ title || "Confirmation" }}
                  </DialogTitle>
                  <button @click="() => $emit('cancel')">
                    <XMarkIcon class="w-5 h-5"/>
                  </button>
                </div>
                <div class="px-4 py-6">{{ message }} </div>
                <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t bg-gray-50">
                  <CoreActionButton :click="() => $emit('cancel')" :text="btnLabels.cancel" color="primary" />
                  <CoreActionButton :click="() => $emit('confirm')" :text="btnLabels.confirm" color="success" />
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
  </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { TransitionRoot, TransitionChild, Dialog, DialogPanel, DialogTitle } from '@headlessui/vue'
import { XMarkIcon, ExclamationTriangleIcon } from '@heroicons/vue/24/solid/index.js'

defineEmits(["cancel", "confirm"]);
const props = defineProps<{ title?: string; message: string; show: boolean; color?:string; useYesNoBtns?: boolean; }>();
const btnLabels = computed(() => props.useYesNoBtns === true 
  ? { confirm: "Yes", cancel: "No" }
  : { confirm: "Confirm", cancel: "Cancel" }
)
</script>