<template>
  <div>
    <nuxt-link>
      <button :type="type" :class="buttonClasses">
        <CoreLoader v-show="loading" color="white" style="height: 25px; width: 25px;"/>
        {{ !loading ? text : "  Please wait..." }}
      </button>
    </nuxt-link>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  color: {
    required: false,
    type: String
  },
  type: {
    required: false,
    type: String as PropType<'button' | 'submit' | 'reset'>,
    default: "button"
  },
  to: {
    required: false,
    type: String
  },
  text: {
    required: true,
    type: String
  },
  loading: {
    type: Boolean,
    required: false,
    default: false
  },
});

const buttonClasses = computed((): string => {
  const baseClasses = "w-full flex items-center justify-center focus:outline-none focus:ring-2 font-normal shadow-sm rounded px-2 py-2 text-white";
  const sizeClasses = "px-2 py-1.5 text-sm";
  const colorClasses: Record<string, string> = {
    primary: "bg-sky-600 focus:ring-sky-500",
    error: "bg-red-500 focus:ring-red-500",
    success: "bg-green-500 focus:ring-green-500",
    default: "bg-gray-200 focus:ring-gray-100 text-gray-500",
  };
  return `${baseClasses} ${
    props.color ? colorClasses[props.color] : colorClasses.default
  } ${props.color === "primary" ? "" : sizeClasses}`;
});
</script>

<style scoped></style>
