<template>
  <div class="inline-flex rounded overflow-hidden" :class="containerClasses">
    <template v-for="(item, index) in normalizedItems" :key="index">
      <div
        v-show="index > 0 && normalizedItems.length > 1"
        class="w-px bg-gray-300 self-stretch"
      ></div>
      <button
        :disabled="item.disabled"
        :class="buttonClasses(item, index)"
        @click="item.click?.(item, index, $event)"
      >
        <component
          v-show="item.icon && !iconPositions.right.includes(item.position || 'left')"
          :is="item.icon"
          :class="iconClasses(item, 'before')"
        />
        <span v-show="item.label" :class="labelClasses(item)">
          {{ item.label }}
        </span>
        <component
          v-show="item.icon && iconPositions.right.includes(item.position || 'left')"
          :is="item.icon"
          :class="iconClasses(item, 'after')"
        />
      </button>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { Component } from 'vue'

type Position = 'left' | 'right' | 'top' | 'bottom'
type Color = 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info'
type Size = 'small' | 'medium' | 'large'
type ClickHandler = (item: ButtonItem, index: number, event: Event) => void

interface ButtonItem {
  label?: string
  icon?: Component
  position?: Position
  color?: Color
  size?: Size
  disabled?: boolean
  outline?: boolean
  block?: boolean
  click?: ClickHandler
}

const props = defineProps<{
  items: ButtonItem[]
}>()

const iconPositions: Record<'right' | 'left', Position[]> = {
  right: ['right', 'bottom'],
  left: ['left', 'top']
}

const positionClasses: Record<Position, string> = {
  top: 'flex-col',
  bottom: 'flex-col-reverse',
  right: 'flex-row-reverse',
  left: 'flex-row'
}

const sizeClasses: Record<Size, string> = {
  small: 'px-2 py-1 text-sm',
  large: 'px-6 py-3 text-lg',
  medium: 'px-4 py-2 text-base'
}

const colorMaps: Record<'outline' | 'solid', Record<Color, string>> = {
  outline: {
    primary: 'bg-transparent text-sky-500 border border-sky-500 hover:bg-sky-500 hover:text-white',
    secondary: 'bg-transparent text-gray-500 border border-gray-500 hover:bg-gray-500 hover:text-white',
    success: 'bg-transparent text-green-500 border border-green-500 hover:bg-green-500 hover:text-white',
    danger: 'bg-transparent text-red-500 border border-red-500 hover:bg-red-500 hover:text-white',
    warning: 'bg-transparent text-yellow-500 border border-yellow-500 hover:bg-yellow-500 hover:text-black',
    info: 'bg-transparent text-cyan-500 border border-cyan-500 hover:bg-cyan-500 hover:text-white'
  },
  solid: {
    primary: 'bg-sky-500 text-white border border-sky-500 hover:bg-sky-700',
    secondary: 'bg-gray-500 text-white border border-gray-500 hover:bg-gray-700',
    success: 'bg-green-500 text-white border border-green-500 hover:bg-green-700',
    danger: 'bg-red-500 text-white border border-red-500 hover:bg-red-700',
    warning: 'bg-yellow-500 text-black border border-yellow-500 hover:bg-yellow-500',
    info: 'bg-cyan-500 text-white border border-cyan-500 hover:bg-cyan-700'
  }
}

const normalizedItems = computed((): ButtonItem[] =>
  props.items.length ? props.items : [{
    label: '',
    icon: undefined,
    position: 'left' as Position,
    color: 'primary' as Color,
    size: 'medium' as Size,
    disabled: false,
    outline: false,
    block: false,
    click: undefined
  }]
)

const containerClasses = computed((): Record<string, boolean> => ({
  'w-full': normalizedItems.value.some((item: ButtonItem) => item.block)
}))

const buttonClasses = (item: ButtonItem, index: number): (string | Record<string, boolean>)[] => [
  'relative inline-flex items-center justify-center font-regular transition-colors',
  positionClasses[item.position || 'left'],
  sizeClasses[item.size || 'medium'],
  getColorClasses(item),
  getBorderClasses(index),
  {
    'w-full': Boolean(item.block),
    'opacity-65 cursor-not-allowed': Boolean(item.disabled)
  }
]

const iconClasses = (item: ButtonItem, position: 'before' | 'after'): string => {
  const spacingMap: Record<'before' | 'after', Record<Position, string>> = {
    before: {
      left: item.label ? 'mr-2' : '',
      top: item.label ? 'mb-1' : '',
      right: '',
      bottom: ''
    },
    after: {
      right: item.label ? 'ml-2' : '',
      bottom: item.label ? 'mt-1' : '',
      left: '',
      top: ''
    }
  }

  const itemPosition: Position = item.position || 'left'
  const spacing: string = spacingMap[position][itemPosition] || ''

  return `w-5 h-5 ${spacing}`.trim()
}

const labelClasses = (item: ButtonItem): string => {
  const centerPositions: Position[] = ['top', 'bottom']
  return centerPositions.includes(item.position || 'left') ? 'text-center' : ''
}

const getColorClasses = (item: ButtonItem): string => {
  const colorType: 'outline' | 'solid' = item.outline ? 'outline' : 'solid'
  const color: Color = item.color || 'primary'
  return colorMaps[colorType][color] || colorMaps.solid.primary
}

const getBorderClasses = (index: number): string => {
  const isMultiple: boolean = normalizedItems.value.length > 1
  const isFirst: boolean = index === 0
  const isLast: boolean = index === normalizedItems.value.length - 1

  return !isMultiple ? '' :
    isFirst ? 'rounded-l rounded-r-none' :
    isLast ? 'rounded-r rounded-l-none' :
    'rounded-none'
}

</script>

<style scoped>
</style>
