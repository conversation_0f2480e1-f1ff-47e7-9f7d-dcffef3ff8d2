<template>
    <div class='flex items-center space-x-2 px-2 py-1 text-white rounded' :class="`bg-${color}-500`">
        <p class="text-sm">{{ value.value }}</p>
        <button v-show="close" @click="removeLabel" class="ml-2">
            <XMarkIcon class="w-4 h-4" />
        </button>
    </div>
</template>

<script lang="ts">
import { XMarkIcon } from '@heroicons/vue/24/solid/index.js';

export default {
    props: {
        value: {
            type: Object,
            required: true
        },
        close: {
            type: Boolean,
            required: true
        },
        color: {
            required: false,
            type: String,
            default: "green"
        },
        icon: {
            required: false,
            type: Object
        }
    },
    methods: {
        removeLabel(): void {
            this.$emit("update", this.value);
        }
    },
    components: { XMarkIcon }
}
</script>