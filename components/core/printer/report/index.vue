<template>
  <div>
    <CoreActionButton
      :click="init"
      color="primary"
      text="Print"
      :icon="printIcon"
    />

    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex top-0 items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-lg flex items-center font-medium leading-6"
                  >
                    <PrinterIcon class="h-5 w-5 mr-2" />
                    Print
                  </DialogTitle>
                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="generatePDF"
                  :actions="false"
                  #default="{ value }"
                >
                  <div class="py-5 px-5">
                    <FormKit
                      validation="required"
                      label="Select a printer"
                      type="radio"
                      v-model="selectedPrinter"
                      :options="printers"
                    />
                  </div>
                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreActionButton
                      :click="() => {}"
                      :loading="loading"
                      type="submit"
                      :icon="printIcon"
                      text="Print"
                      color="success"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";
import {
  XMarkIcon,
  PrinterIcon,
  ExclamationTriangleIcon,
} from "@heroicons/vue/24/solid/index.js";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response } from "@/types";

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
    ExclamationTriangleIcon,
    PrinterIcon,
  },
  data() {
    return {
      show: false as boolean,
      printIcon: PrinterIcon as Object,
      cookie: useCookie("token"),
      printers: new Array<string>(),
      selectedPrinter: "" as string,
      loading: false as boolean,
      zebraPrinting: false as boolean,
    };
  },
  methods: {
    async init(): Promise<void> {
      this.handleClick();
      const { data, error, pending }: Response = await fetchRequest({
        route: endpoints.printers,
        method: "GET",
        token: `${this.cookie}`,
      });
      if (data.value) {
        this.printers = new Array<string>();
        data.value.map((value: { name: string }) => {
          this.printers.push(value.name);
        });
      }
      if (error.value) {
        console.error(error.value);
      }
    },
    async generatePDF(): Promise<void> {
      this.loading = true;
      const printContainer = document.querySelector(
        ".print-container"
      ) as HTMLDivElement;

      const canvas = await html2canvas(printContainer, {
        scale: 2,
        onclone: (clone) => {},
      });
      const data = canvas.toDataURL("image/png");
      const pdf = new jsPDF({
        orientation: "portrait",
        unit: "px",
        format: "a4",
        hotfixes: ["px_scaling"],
      });
      const pageHeight: number = pdf.internal.pageSize.getHeight();
      const pageWidth: number = pdf.internal.pageSize.getWidth();
      const imgHeight: number = (canvas.height * pageWidth) / canvas.width;
      let totalPages: number = 0;
      const padding: number = 10;
      let currentPosition: number = padding;
      let isFirstPage: boolean = true;
      const addPage = (position = 0): void => {
        totalPages++;
        if (!isFirstPage) {
          pdf.addPage();
        }
        pdf.addImage(data, "PNG", 0, -position, pageWidth, imgHeight);
        currentPosition += pageHeight - 2 * padding;
        isFirstPage = false;
      };
      while (currentPosition < imgHeight + padding) {
        addPage(currentPosition);
      }
      for (let i = 1; i <= totalPages; i++) {
        pdf.setPage(i);
        pdf.setFontSize(10);
        pdf.text(`Page ${i} of ${totalPages}`, 10, pageHeight - 10, {
          align: "justify",
        });
      }
      const callback = async (doc: jsPDF) => {
        const pdfBlob = doc.output("blob");
        await this.submitForm(pdfBlob);
      };
      await callback(pdf);
    },
    async submitForm(file: Blob): Promise<void> {
      let formData = new FormData();

      formData.append("pdf", file, `patient-name`);
      formData.append("printer_name", this.selectedPrinter);

      const request: Request = {
        route: endpoints.generalPrint,
        method: "POST",
        token: `${this.cookie}`,
        body: formData,
      };
      const { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;
      if (data.value) {
        data.value.printed
          ? useNuxtApp().$toast.success("Report printed successfully!")
          : useNuxtApp().$toast.warning("Could not print report!");
        this.loading = false;
        this.$emit("update", true);
        this.handleClick();
      }

      if (error.value) {
        this.loading = false;
        console.error(error.value);
        useNuxtApp().$toast.error("An error occurred while printing report");
        this.handleClick();
      }
    },
    handleClick(): void {
      this.show = !this.show;
    },
  },
};
</script>
<style></style>
