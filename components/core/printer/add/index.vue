<template>
    <div>
        <div>
            <CoreActionButton :click="handleClick" text="Add printer" color="primary" :icon="addIcon"/>
        </div>
        <TransitionRoot appear :show="open" as="template">
            <Dialog as="div" @close="handleClick" class="relative z-10">
                <TransitionChild
                    as="template"
                    enter="duration-300 ease-out"
                    enter-from="opacity-0"
                    enter-to="opacity-100"
                    leave="duration-200 ease-in"
                    leave-from="opacity-100"
                    leave-to="opacity-0"
                >
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild
                            as="template"
                            enter="duration-300 ease-out"
                            enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100"
                            leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95"
                        >
                            <DialogPanel
                            class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
                            >

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle
                                        as="h3"
                                        class="text-lg flex items-center font-medium leading-6"
                                    >
                                        <PrinterIcon class="w-8 h-8 mr-2"/>
                                        Add printer
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5"/>
                                    </button>

                                </div>

                                <FormKit
                                    type="form"
                                    submit-label="Update"
                                    @submit="submitForm"
                                    :actions="false"
                                    #default="{ value }"
                                    >

                                    <div class="px-5 py-5">

                                        <FormKit
                                            type="text"
                                            label="Name"
                                            validation="required"
                                            v-model="name"
                                        />

                                        <FormKit
                                            type="textarea"
                                            label="Description"
                                            validation="required"
                                            v-model="description"
                                        />


                                    </div>
                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreActionButton
                                            :click="(() => {})"
                                            type="submit"
                                            color="success"
                                            :icon="saveIcon"
                                            text="Save chages"
                                            :loading="loading"
                                        />
                                    </div>
                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue'

import { PlusIcon, XMarkIcon, ArrowDownTrayIcon, PrinterIcon } from '@heroicons/vue/24/solid/index.js'
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import type { Request } from '@/types';

export default {
    components: {
        TransitionChild,
        TransitionRoot,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon,
        PrinterIcon
    },
    data(){

        return{
            addIcon: PlusIcon as Object,
            saveIcon: ArrowDownTrayIcon as Object,
            open: false as boolean,
            loading: false as boolean,
            cookie: useCookie('token'),
            name: '' as string,
            description: '' as string
        }
    },
    methods: {
        /**
         * @method submitForm creates a new facility
         * @param null
         * @returns promise @type void
         */
        async submitForm() : Promise<void> {

            this.loading = false;

            const request : Request = {
                route: endpoints.printers,
                method: 'POST',
                token: `${this.cookie}`,
                body: { 'name': this.name, description: this.description}
            };

            const { data, error, pending } = await fetchRequest(request);

            this.loading = pending;

            if(data.value){

                useNuxtApp().$toast.success('Facility created successfully!');

                this.$emit('update', true);

                this.loading = false;

                this.handleClick();

            }

            if(error.value){

                console.error(error.value);

                useNuxtApp().$toast.error(ERROR_MESSAGE);

                this.loading = false;
            }
        },
        /**
         * @method handleClick
         * @param null
         * @returns void
         */
        handleClick () : void {
            this.open = !this.open
        }
    }
}
</script>

<style>

</style>
