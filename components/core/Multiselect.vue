<template>
  <div class="w-full flex flex-col space-y-2">
    <label class="font-medium">{{ label }}</label>
    <multi-select
      v-model="selected"
      :options="items"
      :mode="mode"
      :searchable="true"
      clear
      :required="true"
      :classes="dropdownClasses"
      autocomplete
      :placehoder="placeholder || ''"
      :appendToBody="true"
      class="focus:ring-0 multiselect-green"
    />
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  itemsSelected: {
    type: [Array, String],
    default: () => []
  },
  items: {
    type: Array,
    default: () => []
  },
  mode: {
    type: String,
    default: 'single'
  },
  label: {
    type: String,
    default: ''
  },
  delay: {
    type: Number,
    default: 0,
    required: false
  },
  placeholder: {
    type: String,
    required: false
  }
});

const emit = defineEmits(["update:itemsSelected"]);
const selected = ref(props.itemsSelected);

const dropdownClasses = computed(() => ({
  dropdownTop: "-translate-y-full top-px bottom-auto rounded-b-none rounded-t",
  dropdownHidden: "hidden",
  dropdown:
    "max-h-60 z-[1000] absolute -left-px -right-px bottom-0 transform translate-y-full border -mt-px overflow-y-scroll bg-white flex flex-col rounded-b",
  container:
    "relative mx-auto w-full flex items-center justify-end box-border cursor-pointer border rounded bg-white text-base leading-snug outline-none",
}));

watch(
  selected,
  (val) => {
    emit("update:itemsSelected", val);
  },
  { deep: true }
);

watch(
  () => props.itemsSelected,
  (newVal) => {
    selected.value = newVal;
  },
  { deep: true }
);
</script>
