<template>
    <div class="bg-gray-50 p-1 flex items-center justify-end mt-4 space-x-2.5">
        <div class="flex items-center">
            <p class="font-normal mr-2">Per per page:</p>
            <Listbox v-model="selectedPages">
                <div class="relative mt-1">
                    <ListboxButton
                        class="relative cursor-default rounded bg-white py-2 pl-3 pr-8 text-left border focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm">
                        <span class="block truncate">{{ selectedPages.name }}</span>
                        <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                            <ChevronUpDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
                        </span>
                    </ListboxButton>

                    <transition leave-active-class="transition duration-100 ease-in" leave-from-class="opacity-100"
                        leave-to-class="opacity-0">
                        <ListboxOptions
                            class="absolute mt-1 max-h-60 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                            <ListboxOption v-slot="{ active, selected }" v-for="page in perPage" :key="page.name"
                                :value="page" as="template">
                                <li :class="[
                                    active ? 'bg-sky-100 text-sky-500' : 'text-gray-900',
                                    'relative cursor-default select-none py-2 pl-10 pr-4',
                                ]">
                                    <span :class="[
                                        selected ? 'font-medium' : 'font-normal',
                                        'block truncate',
                                    ]">{{ page.name }}</span>
                                </li>
                            </ListboxOption>
                        </ListboxOptions>
                    </transition>
                </div>
            </Listbox>
        </div>
        <nav class="block">
            <ul class="flex pl-0 list-none rounded">
                <li v-if="pagination.current_page > 1"
                    class="relative flex justify-center px-2 py-2 leading-tight bg-white border rounded-tl rounded-bl text-gray-600 border-r-0 cursor-pointer hover:bg-gray-200">
                    <button @click="changePage(pagination.current_page - 1)">
                        <ArrowLongLeftIcon class="w-5 h-5" />
                    </button>
                </li>
                <li v-for="page in pages" :key="page"
                    :class="page == pagination.current_page ? 'bg-sky-500 rounded text-white relative px-3 py-2 leading-tight' : 'relative hover:bg-sky-500 hover:rounded hover:text-white block px-3 py-2 leading-tight bg-white border  text-gray-600 border-r-0 cursor-pointer'">
                    <button @click="changePage(page)">{{ page }}</button>
                </li>
                <li v-if="pagination.current_page < pagination.last_page"
                    class="relative flex justify-center px-3 py-2 leading-tight bg-white border rounded-tr rounded-br  text-gray-600 cursor-pointer hover:bg-gray-200">
                    <button @click="changePage(pagination.current_page + 1)">
                        <ArrowLongRightIcon class="w-5 h-5" />
                    </button>
                </li>
            </ul>
        </nav>
    </div>
</template>

<script lang="ts">

import { ArrowRightIcon, ArrowLeftIcon, ArrowLongLeftIcon, ArrowLongRightIcon, CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/24/solid/index.js';
import {
    Listbox,
    ListboxLabel,
    ListboxButton,
    ListboxOptions,
    ListboxOption,
} from '@headlessui/vue'

export default {
    components: {
        ArrowRightIcon,
        ArrowLeftIcon,
        ArrowLongLeftIcon,
        ArrowLongRightIcon,
        Listbox,
        ListboxLabel,
        ListboxButton,
        ListboxOptions,
        ListboxOption,
        CheckIcon,
        ChevronUpDownIcon
    },
    props: {
        pagination: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            selectedPages: { name: String(25) }
        }
    },
    computed: {
        perPage() {
            const per: Array<number> = [25, 50, 100];
            let pagesData = new Array<{ name: string }>();
            per.forEach((p: number) => {
                pagesData.push({ name: String(p) })
            })
            return pagesData
        },
        pages() {
            const from: number = this.pagination.current_page - 2 > 0 ? this.pagination.current_page - 2 : 1;
            const to: number = from + 4 < this.pagination.last_page ? from + 4 : this.pagination.last_page;
            const pagesArray: Array<number> = new Array<number>();
            for (let i = from; i <= to; i++) {
                pagesArray.push(i);
            }
            return pagesArray;
        },

    },
    methods: {
        changePage(page: number): void {
            if (page !== this.pagination.current_page) {
                this.$emit('update', page);
            }
        },
    },
};
</script>
