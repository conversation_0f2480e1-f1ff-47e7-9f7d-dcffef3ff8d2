<template>
  <button
    @click="handleClick"
    :type="type"
    :class="buttonClasses"
    :disabled="loading ? true : disabled"
  >
    <div v-if="!loading">
      <component class="w-5 h-5 mr-1.5" :is="icon"></component>
    </div>
    <CoreLoader v-else color="white" style="height: 25px; width: 25px;"/>
    {{ !loading ? text : "  Please wait..." }}
  </button>
</template>

<script lang="ts">
import type { ButtonType } from "@/types";

export default {
  props: {
    text: {
      type: String,
      required: true,
    },
    icon: {
      required: false,
      type: [Object, Function],
    },
    color: {
      type: String,
      default: "gray",
    },
    click: {
      required: false,
      type: Function,
    },
    loading: {
      default: false,
      type: Boolean,
    },
    disabled: {
      default: false,
      type: Boolean,
      required: false,
    },
    type: {
      required: false,
      type: String as () => ButtonType,
      default: "button" as ButtonType,
    },
  },
  methods: {
    handleClick(): void {
      (this.click as () => void)();
    },
  },
  computed: {
    buttonClasses() {
      return {
        "bg-sky-500": this.color === "primary" && !this.disabled,
        "bg-red-500": this.color === "error" && !this.disabled,
        "bg-green-500": this.color === "success" && !this.disabled,
        "bg-orange-500": this.color === "warning" && !this.disabled,
        "bg-gray-100": !this.color && !this.disabled,
        "bg-gray-300": this.disabled,
        flex: true,
        "focus:outline-none": true,
        "focus:ring-2": true,
        "focus:ring-sky-500": this.color === "primary" && !this.disabled,
        "focus:ring-red-500": this.color === "error" && !this.disabled,
        "focus:ring-green-500": this.color === "success" && !this.disabled,
        "focus:ring-orange-500": this.color === "warning" && !this.disabled,
        "focus:ring-gray-100": !this.color && !this.disabled,
        "focus:ring-gray-300": this.disabled,
        "font-normal": true,
        "items-center": true,
        "shadow-sm": true,
        rounded: true,
        "px-2": true,
        "py-1.5": true,
        "text-sm": true,
        "text-white": true && this.color,
        "text-gray-500": !this.color,
      };
    },
  },
};
</script>

<style scoped></style>
