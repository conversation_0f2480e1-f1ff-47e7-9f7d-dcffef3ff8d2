<template>
  <nav class="flex bg-gray-50 py-2 px-2" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1">
      <li v-for="(page, index) in props.pages" :key="index">
        <nuxt-link :to="page.link.toString()">
          <div class="flex items-center">
            <HomeIcon v-show="index == 0" class="w-4 h-4 text-sky-500" />
            <svg v-show="index != 0" aria-hidden="true" class="w-6 h-6 "
              :class="page.link === '#' ? 'text-gray-400' : 'text-sky-500'" fill="currentColor" viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd"
                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                clip-rule="evenodd"></path>
            </svg>
            <p class="ml-1 capitalize font-medium md:ml-2"
              :class="page.link === '#' ? 'text-gray-400' : 'text-sky-500 hover:text-sky-600'">{{ page.name }}</p>
          </div>
        </nuxt-link>
      </li>

      <li class="inline-flex items-center">
        <p disabled href="#" class="capitalize inline-flex items-center font-medium text-gray-400">
          <svg aria-hidden="true" class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
              clip-rule="evenodd"></path>
          </svg>
          {{ currentPath() }}
        </p>
      </li>
    </ol>
  </nav>
</template>

<script setup lang="ts">

import { HomeIcon } from '@heroicons/vue/24/solid/index.js';
import type { Page } from '@/types';

const route = useRoute();
const props = defineProps({
  pages: {
    required: true,
    type: Array as () => Page
  }
})
const currentPath = (): string => {
  const segments = route.path.split('/');
  return segments[segments.length - 1].replace(/-/g, " ");
}
</script>

<style>
</style>
