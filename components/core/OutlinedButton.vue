<template>
  <button :type="type" @click="handleClick" class="border text-gray-500 px-2 py-1.5 rounded text-sm hover:bg-red-600 transition duration-150 hover:text-white hover:boder-none">
    {{ text }}
  </button>
</template>

<script>
export default {
  props: {
    text: {
      type: String,
      required: true
    },
    click: {
      required: false,
      type: Function
    },
    type: {
      required: false,
      type: String
    }
  },
  methods: {
    handleClick() {
      this.click();
    }
  }
}
</script>

<style>

</style>