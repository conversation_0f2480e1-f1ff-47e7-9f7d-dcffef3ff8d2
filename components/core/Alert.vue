<template>
  <div class="flex items-center space-x-3 rounded px-5 py-5 bg-red-200">
    <ExclamationTriangleIcon class="w-5 h-5 text-red-500"/>
    <p class="text text-red-500">{{ text }}</p>
  </div>
</template>

<script>

import { ExclamationTriangleIcon } from '@heroicons/vue/24/solid/index.js'

export default {
    components: {
        ExclamationTriangleIcon
    },
    props: {
        text: {
            required: true,
            type: String
        }
    }
}
</script>

<style>

</style>