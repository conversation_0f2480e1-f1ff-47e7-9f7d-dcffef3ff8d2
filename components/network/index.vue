<template>
    <div>

        <CoreActionButton text="Network Configuration" :color="props.isConnected ? 'success' : 'error'" :icon="$props.isConnected ? SignalIcon : SignalSlashIcon" :click="handleClick" />
        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        <SignalIcon class="h-5 w-5 mr-2" />
                                        Configure Network
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" submit-label="Update" @submit="updateConfig" :actions="false"
                                    #default="{ value }">

                                    <div class="mt-2 space-y-3 px-5">
                                        <div class="px-3 py-3 border-l-4 border-sky-500 shadow">
                                            Please make sure you enter the correct IBLIS API IP Address and the port that it is running. This page will refresh after updating changes.
                                        </div>
                                        <FormKit type="text" label="IP Address" v-model="ip_" validation="required|string"/>
                                        <FormKit type="number" label="Port" v-model="port_" validation="required|number"/>
                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t bg-gray-50">
                                        <CoreOutlinedButton type="button" :click="(() => { handleClick() })"
                                            text="Cancel" />
                                        <CoreActionButton :loading="loading" type="submit" :click="(() => { })"
                                            color="success" :icon="ArrowPathIcon" text="Update" />
                                    </div>

                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script setup lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

import { XMarkIcon, SignalIcon, SignalSlashIcon, ArrowPathIcon } from '@heroicons/vue/24/solid/index.js'
import { useNetworkStore } from '@/store/network';

const loading: Ref<boolean> = ref<boolean>(false);
const show: Ref<boolean> = ref<boolean>(false);

const { updateNetwork, ip, port } = useNetworkStore();

const props = defineProps({
    isConnected: {
        required: true,
        type: Boolean,
        default: false
    }
})

const ip_: Ref<string> = ref<string>(ip);
const port_: Ref<number> = ref<number>(port);

const handleClick = (): void => {
    show.value = !show.value;
}

const updateConfig = async() => {
    await updateNetwork(ip_.value, port_.value);
    handleClick();
    window.location.reload()
}
</script>

<style>
</style>
