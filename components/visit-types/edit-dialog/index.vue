<template>
  <div>
    <CoreActionButton
      :click="loadSections"
      text="Edit"
      :icon="editIcon"
      color="success"
    />

    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-lg flex items-center font-medium leading-6"
                  >
                    <img
                      src="~assets/icons/outpatient.svg"
                      class="w-8 h-8 mr-2"
                    />
                    Edit visit type
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  id="editForm"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  #default="{ value }"
                >
                  <div class="mt-2 space-y-3 px-5 py-5">
                    <FormKit
                      type="text"
                      label="Name"
                      v-model="data.name"
                      validation="required|text"
                    />

                    <FormKit
                      type="textarea"
                      label="Description"
                      v-model="data.description"
                      validation="required|text"
                    />

                    <div class="w-full flex flex-col space-y-2">
                      <label class="font-medium">Facility Sections</label>
                      <multi-select
                        style="--ms-max-height: none !important"
                        v-model="selectedSection"
                        :options="sections.map((section: any) => section.name)"
                        mode="tags"
                        :searchable="true"
                        :required="true"
                        clear
                        class="focus:ring-none fcus:border-none focus:outline-none multiselect-green"
                      />
                    </div>
                  </div>

                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton text="Clear form" />
                    <CoreActionButton
                      :click="() => {}"
                      :icon="saveIcon"
                      text="Save changes"
                      color="success"
                      type="submit"
                      :loading="loading"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  PencilSquareIcon,
  ArrowDownTrayIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request } from "@/types";

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
  },
  data() {
    return {
      editIcon: PencilSquareIcon as Object,
      show: false as boolean,
      saveIcon: ArrowDownTrayIcon as Object,
      cookie: useCookie("token"),
      loading: false as boolean,
      sections: new Array<any>(),
      selectedSection: new Array<string>(),
    };
  },
  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  methods: {
    async loadSections(): Promise<void> {

        this.handleClick();

        this.selectedSection = this.data.facility_sections.map((section: { name: string }) => section.name)

        const request: Request = {
            route: `${endpoints.sections}`,
            method: "GET",
            token: `${this.cookie}`,
        };

        const { data, error } = await fetchRequest(request);

        if (data.value) {
            this.sections = data.value.data;
        }

        if (error.value) {
            console.error(error.value);
        }
    },
    async submitForm(): Promise<void> {

        this.loading = true;

        let facilitySections = filterArrays(this.sections, this.selectedSection);

        const request: Request = {
            route: `${endpoints.visitTypes}/${this.data.id}`,
            method: "PUT",
            token: `${this.cookie}`,
            body: {
                name: this.data.name,
                description: this.data.description,
                facility_sections: facilitySections
            },
        };

        const { data, error, pending } = await fetchRequest(request);

        this.loading = pending;

        if (data.value) {
            useNuxtApp().$toast.success(`Visit type updated succcessfully!`);
            this.$emit("update", true);
            this.handleClick();

            this.loading = false;
        }

        if (error.value) {
            console.error(error.value.data);
            useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
            this.handleClick();

            this.loading = false;
        }
    },
    handleClick(): void {
      this.show = !this.show;
    },
  },
};
</script>

<style></style>
