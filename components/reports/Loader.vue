<template>
    <div>
        <div v-if="props.condition" class="mx-auto justify-center flex flex-col items-center space-y-3 py-10">
            <CoreLoader />
            <p class="text-base">
                Generating report, please wait<span class="animate-ping">...</span>
            </p>
            <button class="font-medium text-base text-sky-500" @click="props.cancelReportGeneration()">Cancel</button>
        </div>
    </div>
</template>

<script setup lang="ts">
const props = defineProps({
    condition: {
        type: [Boolean, Function],
        default: false,
    },
    cancelReportGeneration: {
        type: Function,
        default: () => { },
    },
})
</script>

<style scoped></style>