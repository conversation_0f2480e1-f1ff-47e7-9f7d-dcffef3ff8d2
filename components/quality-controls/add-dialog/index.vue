<template>
    <div>
        <div>
            <CoreActionButton text="Add control" color="new" :icon="addIcon" @click="adjustVisibility()"/>
        </div>
        <TransitionRoot appear :show="open" as="template">
        <Dialog as="div" @close="adjustVisibility" class="relative z-10">
            <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0"
            enter-to="opacity-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100"
            leave-to="opacity-0"
            >
            <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>
    
            <div class="fixed inset-0 overflow-y-auto">
            <div
                class="flex min-h-full items-center justify-center p-4 text-center"
            >
                <TransitionChild
                as="template"
                enter="duration-300 ease-out"
                enter-from="opacity-0 scale-95"
                enter-to="opacity-100 scale-100"
                leave="duration-200 ease-in"
                leave-from="opacity-100 scale-100"
                leave-to="opacity-0 scale-95"
                >
                <DialogPanel
                    class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
                >

                    <div class="border-b px-3 py-3 flex items-center justify-between">
                        <DialogTitle
                            as="h3"
                            class="text-xl text-black flex items-center font-medium leading-6"
                        >
                            Create control
                        </DialogTitle>

                        <button @click="adjustVisibility">
                            <XMarkIcon class="w-5 h-5"/>
                        </button>
                            
                    </div>

                    <div class="mt-2 space-y-3">
                    <div class="w-full flex flex-col items-center px-5 space-y-3">
                        <div class="w-full flex flex-col space-y-2">
                            <label class="font-medium">Name</label>
                            <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-1 focus:ring-sky-700 transition duration-150"/>
                            <!-- <span class="text-red-500 font-medium text-xs">The first name field is required</span> -->
                        </div>
                        <div class="w-full flex flex-col space-y-2">
                            <label class="font-medium">Description</label>
                            <textarea type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-1 focus:ring-sky-700 transition duration-150"/>
                        </div>
                    </div>
                    <div class="w-full flex flex-col items-center px-5 space-y-3">
                        <div class="w-full flex flex-col space-y-2">
                            <label class="font-medium">Lot</label>
                            <div>
                                <Listbox v-model="selectedLot">
                                    <div class="relative mt-1">
                                    <ListboxButton
                                        class="relative w-full cursor-default rounded border py-2 pl-3 pr-10 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 focus-visible:ring-offset-2 focus-visible:ring-offset-sky-300 sm:text-sm"
                                    >
                                        <span class="block truncate">{{ selectedLot.name }}</span>
                                        <span
                                        class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                                        >
                                        <ChevronUpDownIcon
                                            class="h-5 w-5 text-gray-600"
                                            aria-hidden="true"
                                        />
                                        </span>
                                    </ListboxButton>
                            
                                    <transition
                                        leave-active-class="transition duration-100 ease-in"
                                        leave-from-class="opacity-100"
                                        leave-to-class="opacity-0"
                                    >
                                        <ListboxOptions
                                        class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                                        >
                                        <ListboxOption
                                            v-slot="{ active, selected }"
                                            v-for="lot in lots"
                                            :key="lot.name"
                                            :value="lot"
                                            as="template"
                                        >
                                            <li
                                            :class="[
                                                active ? 'bg-gray-100' : 'text-gray-900',
                                                'relative cursor-default select-none py-2 pl-10 pr-4',
                                            ]"
                                            >
                                            <span
                                                :class="[
                                                selected ? 'font-medium' : 'font-normal',
                                                'block truncate',
                                                ]"
                                                >{{ lot.name }}</span
                                            >
                                            <span
                                                v-if="selected"
                                                class="absolute inset-y-0 left-0 flex items-center pl-3 text-sky-600"
                                            >
                                                <CheckIcon class="h-5 w-5" aria-hidden="true" />
                                            </span>
                                            </li>
                                        </ListboxOption>
                                        </ListboxOptions>
                                    </transition>
                                    </div>
                                </Listbox> 
                            </div>
                        </div>
                        <div class="w-full flex flex-col space-y-2">
                            
                            <label class="font-medium">Measures</label>
                            
                            <CoreActionButton @click="newMeasure()" color="edit" text="Add new measure" :icon="addIcon"/>

                            <div v-for="(item) in measures" :key="item" class="bg-gray-100 rounded border px-2 py-2 space-y-2">
                                
                                <button @click="removeMeasure()" class="ml-auto justify-end flex">
                                    <XMarkIcon class="w-5 h-5"/>
                                </button>
                            
                                <div class="w-full flex items-center space-x-3">
                                    <div class="w-1/3 flex flex-col space-y-2">
                                        <label class="font-medium">Name</label>
                                        <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-1 focus:ring-sky-700 transition duration-150"/>
                                        <!-- <span class="text-red-500 font-medium text-xs">The first name field is required</span> -->
                                    </div>
                                    <div class="w-1/3 flex flex-col space-y-2">
                                        <label class="font-medium">Measure Type</label>
                                        <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-1 focus:ring-sky-700 transition duration-150"/>
                                        <!-- <span class="text-red-500 font-medium text-xs">The first name field is required</span> -->
                                    </div>
                                    <div class="w-1/3 flex flex-col space-y-2">
                                        <label class="font-medium">Unit</label>
                                        <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-1 focus:ring-sky-700 transition duration-150"/>
                                        <!-- <span class="text-red-500 font-medium text-xs">The first name field is required</span> -->
                                    </div>
                                </div>
                                <div class="space-y-2">
                                    <p class="font-medium mb-2">Range values</p>
                                    <CoreActionButton color="edit" text="Add range" :icon="addIcon"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
    
                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                        <CoreOutlinedButton text="Clear form"/>
                        <CoreActionButton color="edit" :icon="saveIcon" text="Save"/>
                    </div>
                </DialogPanel>
                </TransitionChild>
            </div>
            </div>
        </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
  Listbox,
  ListboxLabel,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from '@headlessui/vue'

import { PlusIcon, XMarkIcon, ChevronUpDownIcon, UserIcon, ArrowDownTrayIcon, ArrowUturnLeftIcon } from '@heroicons/vue/24/solid/index.js'

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        Listbox,
        ListboxLabel,
        ListboxButton,
        ListboxOptions,
        ListboxOption,
        XMarkIcon,
        UserIcon,
        ChevronUpDownIcon
    },
    data(){

        return{
            open: false,
            addIcon: PlusIcon,
            saveIcon: ArrowDownTrayIcon,
            clearIcon: ArrowUturnLeftIcon,
            lots: [
                {
                    name: '001'
                }
            ],
            selectedLot: { name: "" },
            measures: 1,
        }
    },
    created() {
        this.selectedLot = this.lots[0];
    },
    methods:{
        adjustVisibility(){
            this.open = !this.open
        },
        newMeasure(){
            this.measures +=  1
        },
        removeMeasure(){
            this.measures -= 1
        }
    }
}
</script>

<style>

</style>