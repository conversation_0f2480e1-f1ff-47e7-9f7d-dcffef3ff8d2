<template>
    <div>
        <CoreActionButton color="success" :click="handleClick" text="Edit" :icon="editIcon"/>

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
            <TransitionChild
                as="template"
                enter="duration-300 ease-out"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="duration-200 ease-in"
                leave-from="opacity-100"
                leave-to="opacity-0"
            >
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>
        
            <div class="fixed inset-0 overflow-y-auto">
                <div
                class="flex min-h-full items-center justify-center p-4 text-center"
                >
                <TransitionChild
                    as="template"
                    enter="duration-300 ease-out"
                    enter-from="opacity-0 scale-95"
                    enter-to="opacity-100 scale-100"
                    leave="duration-200 ease-in"
                    leave-from="opacity-100 scale-100"
                    leave-to="opacity-0 scale-95"
                >
                    <DialogPanel
                    class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
                    >

                        <div class="border-b px-3 py-3 flex items-center justify-between">
                            <DialogTitle
                                as="h3"
                                class="text-lg flex items-center font-medium leading-6"
                            >
                                Edit
                                {{ data.name }}
                            </DialogTitle>

                            <button @click="handleClick">
                                <XMarkIcon class="w-5 h-5"/>
                            </button>
                                
                        </div>

                    <div class="mt-2 space-y-3 px-5">

                        <div class="w-full flex flex-col space-y-2">
                            <label class="font-medium">Name</label>
                            <input type="number" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-1 focus:ring-sky-700 transition duration-150"/>
                        </div>

                        <div class="w-full flex flex-col space-y-2">
                            <label class="font-medium">Description</label>
                            <textarea type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-1 focus:ring-sky-700 transition duration-150"/>
                        </div>

                        <div class="w-full flex flex-col space-y-2">
                            <label class="font-medium">IP Address</label>
                            <input type="number" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-1 focus:ring-sky-700 transition duration-150"/>
                        </div>

                        <div class="w-full flex flex-col space-y-2">
                            <label class="font-medium">Hostname</label>
                            <input type="number" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-1 focus:ring-sky-700 transition duration-150"/>
                        </div>

                        <div class="w-full flex flex-col space-y-2">
                            <label class="font-medium">Supported tests</label>
                            <input type="number" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-1 focus:ring-sky-700 transition duration-150"/>
                        </div>

                    </div>
        
                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t bg-gray-50">
                        <CoreOutlinedButton text="Clear form"/>
                        <CoreActionButton :icon="saveIcon" text="Save"/>
                    </div>
                    </DialogPanel>
                </TransitionChild>
                </div>
            </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script>

import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue'

import { XMarkIcon, PencilSquareIcon, ArrowDownTrayIcon } from '@heroicons/vue/24/solid/index.js'

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon
    },
    data(){

        return{
            editIcon: PencilSquareIcon,
            show: false,
            saveIcon: ArrowDownTrayIcon
        }
    },
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    methods: {
        handleClick(){
            this.show = !this.show
        }
    }
}
</script>

<style>

</style>