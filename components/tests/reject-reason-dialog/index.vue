<template>
  <div>
    <div>
      <CoreActionButton :text="text" color="error" :icon="icon" :click="init" />
    </div>

    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleClick" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-xl text-black flex items-center font-medium leading-6"
                  >
                    <img
                      src="~assets/icons/rdt_result_no_test.svg"
                      class="w-8 h-8 mr-2"
                    />
                    {{ text.charAt(0).toUpperCase() + text.slice(1) }} Reason
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  #default="{ value }"
                  id="rejectionForm"
                >
                  <div class="mt-2 space-y-3 px-5 py-5">
                    <div class="space-y-2">
                      <h3 class="font-medium">Test Type</h3>
                      <p class="border-b border-dotted text-gray-600">
                        {{ item.test_type_name }}
                      </p>
                    </div>
                    <div class="space-y-2">
                      <h3 class="font-medium">Specimen</h3>
                      <p class="border-b border-dotted text-gray-600">
                        {{ item.specimen_type }}
                      </p>
                    </div>
                    <div class="space-y-2">
                      <h3 class="font-medium">Accession Number</h3>
                      <p class="border-b border-dotted text-gray-600">
                        {{ item.accession_number }}
                      </p>
                    </div>
                    <div class="w-full flex items-center">
                      <div class="w-full flex flex-col space-y-2">
                        <FormKit
                          type="text"
                          label="Person Talked To"
                          :validation="text.toLowerCase() !== 'void' ? 'required' : ''"
                          v-model="name"
                        />
                      </div>
                    </div>

                    <div class="w-full flex flex-col space-y-2 pb-40">
                      <label class="font-medium">Select Reason(s)</label>
                      <multi-select
                        style="--ms-max-height: none !important"
                        v-model="reasonsSelected"
                        :options="reasons"
                        :searchable="true"
                        :required="true"
                        clear
                        class="focus:ring-none fcus:border-none focus:outline-none multiselect-green"
                      />
                    </div>
                  </div>

                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton
                      type="button"
                      text="Clear form"
                      :click="resetForm"
                    />
                    <CoreActionButton
                      :loading="loading"
                      type="submit"
                      :click="() => {}"
                      color="success"
                      :icon="saveIcon"
                      text="Save changes"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  PlusIcon,
  XMarkIcon,
  UserIcon,
  ArrowDownTrayIcon,
  ArrowUturnLeftIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response, Drug } from "@/types";

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
    UserIcon,
  },
  data() {
    return {
      open: false as boolean,
      addIcon: PlusIcon,
      saveIcon: ArrowDownTrayIcon,
      clearIcon: ArrowUturnLeftIcon,
      name: "" as string,
      description: "" as string,
      loading: false as boolean,
      cookie: useCookie("token"),
      reasons: new Array<string>(),
      reasonsSelected: "" as string,
      rawReasons: new Array<any>(),
    };
  },
  props: {
    item: {
      required: true,
      type: Object,
    },
    text: {
      type: String,
      required: true,
    },
    action: {
      type: String,
      required: true,
    },
    icon: {
      type: [Object, Function],
      required: true,
    },
  },
  methods: {
    /**
     * @method init opens dialog and loads reasons for rejection
     * @returns promise @type void
     */
    async init(): Promise<void> {
      this.handleClick();

      const request: Request = {
        route: endpoints.rejectionReasons,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { error, data }: Response = await fetchRequest(request);

      if (data.value) {
        this.rawReasons = data.value;

        data.value.map((reason: { description: string }) => {
          this.reasons.push(reason.description);
        });
      }

      if (error.value) {
        console.error(error.value);
      }
    },

    /**
     * @method submitForm changes test status with a reason
     * @returns promise @type void
     */
    async submitForm(): Promise<void> {
      this.loading = true;

      let reason_id = null;

      this.rawReasons.map((reason: { description: string; id: number }) => {
        if (reason.description === this.reasonsSelected) {
          reason_id = reason.id;
        }
      });

      const request: Request = {
        route: `${endpoints.testStatus}/${this.item.id}/${this.action}?status_reason_id=${reason_id}&person_talked_to=${this.name}`,
        method: "PUT",
        token: `${this.cookie}`,
        body: {},
      };

      const { data, error, pending }: any = await fetchRequest(request);

      this.loading = pending;

      if (data.value) {
        useNuxtApp().$toast.success(
          `Test action "${this.text}" done successfully!`
        );

        this.handleClick();

        this.loading = false;

        this.$emit("update", true);
      }

      if (error.value) {
        console.error(error.value);

        useNuxtApp().$toast.error(ERROR_MESSAGE);

        this.loading = false;
      }
    },

    /**
     * @method handleClick handles dialog visibility
     * @returns @type void
     */
    handleClick(): void {
      this.open = !this.open;
    },
    /**
     * @method resetForm resets the rejection form on click
     * @returns @type void
     */
    resetForm(): void {
      this.reasonsSelected = "";
      this.$formkit.reset("rejectionForm");
    },
  },
};
</script>

<style></style>
