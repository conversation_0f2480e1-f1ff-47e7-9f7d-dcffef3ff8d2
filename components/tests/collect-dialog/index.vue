<template>
  <div>
    <div>
      <CoreActionButton
        text="Collect"
        color="primary"
        :icon="FolderPlusIcon"
        :click="
          () => {
            init();
          }
        "
        :loading="loading"
      />
    </div>

    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleClick" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-xl text-black flex items-center font-medium leading-6"
                  >
                    <svg
                      class="w-8 h-8 mr-2"
                      xmlns="http://www.w3.org/2000/svg"
                      width="512"
                      height="512"
                      viewBox="0 0 512 512"
                    >
                      <path
                        fill="currentColor"
                        d="m118.2 55l-2.5 5.02C108.9 73.86 99.93 79 91.97 79s-16.99-5.17-23.92-19.03l-16.1 8.06C61.02 86.17 75.99 97 91.97 97c14.43 0 27.93-8.91 37.03-24h82v419h18V73h81.8c7.3 11.92 17.2 19.95 28.2 22.82V119h18V95.84c12.5-3.24 23.7-13.13 31.1-27.81l-16.2-8.06C365 73.83 356 79 348 79s-17-5.17-23.9-19.03L321.6 55zm171.1 82c-1.3 3-3.9 9.6-5.4 19.8c-1.9 12.9-2.9 29.5-2.9 47.2c0 33.5 3.8 70.9 10.5 93.5c14 6 35.3 9.5 56.5 9.5s42.5-3.5 56.5-9.5c6.7-22.6 10.5-60 10.5-93.5c0-17.7-1-34.3-2.9-47.2c-1.5-10.2-4.1-16.8-5.4-19.8zm5.3 77c35.3 7.2 70.6 10.5 105.9 0c-.5 24-2.4 45-10.2 69.6c-28.9 3-56.5 11.4-89.9-3.9zm91.1 107.7c-5.8 1.1-11.8 1.9-17.9 2.4c.7 3.1 1.5 6.6 2.3 10.6c2.4 11.4 4.4 25.3 3.9 32.7v.1c-.4 6.3-3 13.3-6.2 21.1s-6.9 16.5-7.7 26.7c-1.2 14.7 1.4 34.1 4.1 50.4s5.6 29.4 5.6 29.4l17.6-4s-2.9-12.6-5.5-28.3c-2.5-15.8-4.7-35-3.8-46.1c.5-6.4 3.2-13.4 6.4-21.3s6.9-16.6 7.5-26.8c.7-11.5-1.8-25.6-4.2-37.5c-.7-3.4-1.5-6.6-2.1-9.4m-74.7.1v24.5h18v-22.1c-6.1-.5-12.2-1.3-18-2.4"
                      />
                    </svg>
                    Blood collection
                  </DialogTitle>

                  <button type="button" @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  #default="{ value }"
                  id="collectionForm"
                >
                  <div class="mt-2 space-y-3 px-5 py-5">
                    <div>
                      <h3 class="text-lg font-semibold text-black">Results</h3>
                      <div class="w-full border border-dotted rounded">
                        <div
                          :class="
                            details?.indicators.length !== index + 1
                              ? 'w-full px-5 py-2 border-b border-dotted flex justify-between items-center'
                              : 'w-full px-5 py-2 flex justify-between items-center'
                          "
                          v-for="(indicator, index) in details?.indicators"
                          :key="index"
                        >
                          <h3>{{ indicator.name }}</h3>
                          <div class="flex items-center space-x-1">
                            <p
                              v-if="indicator.result"
                              v-html="
                                indicator.result.value
                                  ? indicator.result.value
                                  : 'Not done'
                              "
                            ></p>
                            <span
                              class="text-xs"
                              v-if="indicator.result?.value"
                            >
                              {{ indicator?.unit }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="w-full flex items-center">
                      <div class="w-full flex flex-col space-y-2">
                        <FormKit
                          type="text"
                          label="Person's Name"
                          validation="required"
                          v-model="collectorName"
                        />
                      </div>
                    </div>

                    <div class="w-full flex flex-col space-y-2">
                      <label class="font-medium">Designated ward</label>
                      <CoreDropdown
                        isSearchable
                        :items="wards"
                        v-model="wardSelected"
                        :class="
                          wardSelected.name == 'select ward' && 'text-gray-600'
                        "
                      />
                    </div>

                    <div class="w-full flex flex-col space-y-2">
                      <label class="font-medium">Collection Time</label>
                      <div class="flex items-center space-x-2">
                        <datepicker
                          required
                          position="left"
                          @cleared="cleared"
                          placeholder="select collection date and time"
                          :range="false"
                          format="dd/MM/yyyy HH:mm:ss"
                          input-class-name="datepicker"
                          v-model="collectionTime"
                          :minDate="details?.created_date"
                          :maxDate="new Date()"
                        />
                        <CoreActionButton
                          text="Now"
                          color="primary"
                          :click="
                            () => {
                              setTimeToNow();
                            }
                          "
                          :icon="ClockIcon"
                        />
                      </div>
                    </div>
                  </div>

                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton
                      type="button"
                      text="Clear form"
                      :click="resetForm"
                    />
                    <CoreActionButton
                      :loading="submitting"
                      type="submit"
                      color="success"
                      :icon="saveIcon"
                      :click="(() => {})"
                      text="Save details"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  ArrowDownTrayIcon,
  ClockIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { DropdownItem, Request, Response, Test, Ward } from "@/types";
import { FolderPlusIcon } from "@heroicons/vue/24/outline";

interface Props {
  item: Test;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update", value: boolean): void;
}>();

const details = ref<Test>();
const open = ref<boolean>(false);
const saveIcon = ArrowDownTrayIcon;
const collectorName = ref<string>("");
const collectionTime = ref<string>("");
const loading = ref<boolean>(false);
const submitting = ref<boolean>(false);
const zebraPrinting = ref<boolean>(false);
const wards = ref<DropdownItem[]>([]);
const { $toast } = useNuxtApp();
const wardSelected = ref<DropdownItem>({ name: "select ward", id: 0 });

const fetchWards = async (): Promise<void> => {
  const { data, error }: Response = await fetchRequest({
    route: endpoints.sections,
    method: "GET",
  });
  if (data.value) wards.value = data.value.data;
  if (error.value) console.error(error.value);
};

const init = async (): Promise<void> => {
  loading.value = true;

  await fetchWards();

  const request: Request = {
    route: `${endpoints.tests}/${props.item.id}`,
    method: "GET",
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;
  if (data.value) {
    loading.value = false;
    details.value = data.value;
    wardSelected.value = wards.value.find(
      (ward) => ward.name == data.value.requesting_ward
    ) || { name: "select ward", id: 0 };
    handleClick();
  }
  if (error.value) {
    loading.value = false;
    console.error(error.value);
    $toast.error(ERROR_MESSAGE);
  }
};

const cleared = (): void => {
  collectionTime.value = "";
};

const setTimeToNow = (): void => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");

  collectionTime.value = `${year}-${month}-${day}T${hours}:${minutes}`;
};

const submitForm = async (): Promise<void> => {
  submitting.value = true;

  const request: Request = {
    route: `${endpoints.processCrossMatch}`,
    method: "POST",
    body: {
      post_crossmatch_process: {
        facility_section_id: wardSelected.value.id,
        test_id: props.item.id,
        collected_by: collectorName.value,
        collection_date: collectionTime.value,
      },
    },
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  submitting.value = pending;

  if (data.value) {
    $toast.success("Collection details saved successfully!");
    handleClick();
    submitting.value = false;
    printBBResults();
    emit("update", true);
  }

  if (error.value) {
    console.error(error.value);
    $toast.error(ERROR_MESSAGE);
    submitting.value = false;
  }
};

const handleClick = (): void => {
  open.value = !open.value;
};

async function printBBResults(): Promise<void> {
  const request: Request = {
    route: endpoints.printOutZebra,
    method: "POST",
    body: {
      order_id: details.value?.order_id,
      tests: [details.value?.id],
      is_cross_match: true,
    },
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  zebraPrinting.value = pending;
  if (data.value) {
    zebraPrinting.value = false;
    const reader = new FileReader();
    reader.onload = () => {
      const url = URL.createObjectURL(data.value);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${Date.now()}.lbl`);
      link.click();
      URL.revokeObjectURL(url);
    };
    reader.readAsText(data.value);
    $toast.success("Results printed successfully!");
  }
  if (error.value) {
    zebraPrinting.value = false;
    $toast.error(ERROR_MESSAGE);
    console.error(error.value);
  }
}

const resetForm = (): void => {
  collectorName.value = "";
  collectionTime.value = "";
  wardSelected.value = { name: "select ward", id: 0 };
};
</script>
