<template>
  <div>
    <div>
        <CoreActionButton :click="handleClick" text="New test" color="primary" :icon="addIcon"/>
      </div>
      <TransitionRoot appear :show="open" as="template">
        <Dialog as="div" @close="handleClick" class="relative z-10">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0"
            enter-to="opacity-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100"
            leave-to="opacity-0"
          >
            <div class="fixed inset-0 bg-black bg-opacity-25" />
          </TransitionChild>
    
          <div class="fixed inset-0 overflow-y-auto">
            <div
              class="flex min-h-full items-center justify-center p-4 text-center"
            >
              <TransitionChild
                as="template"
                enter="duration-300 ease-out"
                enter-from="opacity-0 scale-95"
                enter-to="opacity-100 scale-100"
                leave="duration-200 ease-in"
                leave-from="opacity-100 scale-100"
                leave-to="opacity-0 scale-95"
              >
                <DialogPanel
                  class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
                >

                    <div class="border-b px-3 py-3 flex items-center justify-between">
                        <DialogTitle
                        as="h3"
                        class="text-lg flex items-center font-medium leading-6"
                        >
                          New visit type
                        </DialogTitle>

                        <button @click="handleClick">
                          <XMarkIcon class="w-5 h-5"/>
                        </button>
                            
                    </div>

                  <div class="mt-2 space-y-3 px-5">

                    <div class="w-full flex flex-col space-y-2">
                      <label class="font-medium">Name</label>
                      <input type="number" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-1 focus:ring-sky-700 transition duration-150"/>
                    </div>

                    <div class="h-72">
                      <multi-select
                        style="--ms-max-height: none !important;"
                        v-model="wardSelected"
                        :options="wards"
                        mode="tags"
                        clear
                        class="outline-none focus:outline-none multiselect-green"
                      />
                    </div>
                  

                  </div>
    
                  <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                    <CoreOutlinedButton text="Dismiss"/>
                    <CoreActionButton :click="handleClick" :icon="saveIcon" text="Save"/>
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue'

import { PlusIcon,WrenchScrewdriverIcon, XMarkIcon, UserIcon, ArrowDownTrayIcon, ArrowUturnLeftIcon } from '@heroicons/vue/24/solid/index.js'

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
    UserIcon
  },
  data(){

    return{
      open: false,
      driverIcon: WrenchScrewdriverIcon,
      addIcon: PlusIcon,
      saveIcon: ArrowDownTrayIcon,
      clearIcon: ArrowUturnLeftIcon,
      wardSelected: null,
      wards: [
        "A2",
        "A1",
        "Neonatal",
        "Peads"
      ]
    }
  },
  methods:{
    handleClick(){
      this.open = !this.open
    }
  }
}
</script>

<style scoped>
.multiselect-green {
  --ms-tag-bg: #bbf7d0;
  --ms-tag-color: #22c55e;
}

</style>