<template>
  <div>
    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
          leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center text-center">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95">
              <DialogPanel
                class="w-full max-w-7xl m-20 transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
                <div class="border-b px-3 py-3 flex items-center justify-between">
                  <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                    <img src="@/assets/icons/medical_records.svg" class="w-8 h-8 mr-2" />
                    NLIMS Test Details
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <div class="grid grid-cols-3 gap-4 px-5 py-5">
                  <div class="rounded border">
                    <div class="px-4 py-2 bg-gray-50 border-b">
                      <h3 class="text-lg font-semibold text-gray-600">
                        Patient
                      </h3>
                    </div>
                    <div class="w-full space-y-2 py-2">
                      <div class="w-full flex justify-between px-5 py-2">
                        <h3 class="font-semibold">Patient Number</h3>
                        <p>{{ data.patient_identifiers.npid }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Name</h3>
                        <p>
                          {{
                            capitalize(
                              `${data.patient.first_name} ${data.patient.middle_name
                                ? data.patient.middle_name
                                : ""
                              }
                          ${data.patient
                                .last_name
                              }`
                            )
                          }}
                        </p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2">
                        <h3 class="font-semibold">Sex</h3>
                        <p>{{ data.patient.sex }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Age</h3>
                        <p>{{ calculateAge(data.patient.date_of_birth) }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="rounded border">
                    <div class="px-4 py-2 bg-gray-50 border-b">
                      <h3 class="text-lg font-semibold text-gray-600">
                        Specimen
                      </h3>
                    </div>
                    <div class="w-full space-y-2 py-2">
                      <div class="w-full flex justify-between px-5 py-2">
                        <h3 class="font-semibold">Specimen Type</h3>
                        <p>{{ data.specimen }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Tracking Number</h3>
                        <p>{{ data.tracking_number }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2">
                        <h3 class="font-semibold">Accession Number</h3>
                        <p>{{ data.accession_number }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Status</h3>
                        <p>{{ data.order_status }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="rounded border max-h-72 overflow-y-auto">
                    <div class="px-4 py-2 bg-gray-50 border-b">
                      <h3 class="text-lg font-semibold text-gray-600">Test</h3>
                    </div>
                    <div class="w-full space-y-2 py-2">
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Name</h3>
                        <p v-for="(test, index) in data.tests" :key="index">
                          {{ test.test_type }}
                        </p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2">
                        <h3 class="font-semibold">Date Registered</h3>
                        <p>
                          {{
                            moment(data.order_created_date).format(DATE_FORMAT)
                          }}
                        </p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Receipt Date</h3>
                        <p>
                          {{
                            moment(data.order_created_date).format(DATE_FORMAT)
                          }}
                        </p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Ward/Location</h3>
                        <p>{{ data.facility_section }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Sending Facility</h3>
                        <p>{{ data.sending_facility }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2">
                        <h3 class="font-semibold">Registered By</h3>
                        <p>{{ data.collected_by }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Requested By</h3>
                        <p>{{ data.requested_by }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mx-5 rounded border mb-5">
                  <div class="flex items-center justify-between bg-gray-50 px-4 py-2 border-b rounded-t">
                    <h3 class="text-lg font-semibold text-gray-600">Results</h3>
                    <div class="justify-end flex items-center space-x-3">
                      <CoreActionButton :loading="loading" :icon="arrowIcon" color="success" text="Proceed"
                        :click="mergeOrder" />
                    </div>
                  </div>
                  <div v-for="(value, key) in getResults(data as any)[0]" :key="key">
                    <div v-if="key.toString().toLowerCase() !== 'result_date'"
                      :class="'w-full px-5 py-2 mt-2 mb-2 border-b border-dotted flex justify-between items-center'">
                      <h3>{{ key }}</h3>
                      <div>
                        <p>
                          {{ value }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  ArrowLongRightIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response } from "@/types";
import { useAuthStore } from "@/store/auth";

interface ClientInfo {
  id: string | number;
}

interface TestInfo {
  test_type: string;
}

interface OrderDetails {
  accession_number: string;
  tracking_number: string;
  client: ClientInfo;
  order_id: string | number;
  tests: TestInfo[];
  results: Record<string, any>;
}

interface Props {
  data: Record<string, any>;
  open: boolean;
  callback: (accessionNumber: string) => void;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'update', value: boolean): void;
}>();

const show = ref<boolean>(props.open || false);
const arrowIcon = <Object>ArrowLongRightIcon;
const loading = ref<boolean>(false);
const authStore = useAuthStore();

const handleClick = (): void => {
  show.value = !show.value;
};

const getResults = (data: OrderDetails): Array<any> => {
  const returnValue: Array<any> = [];

  for (const test of data.tests) {
    const testType: string = test.test_type;

    if (data.results.hasOwnProperty(testType)) {
      const resultData: any = data.results[testType];
      returnValue.push(resultData);
    }
  }

  return returnValue;
};

const mergeOrder = async (): Promise<void> => {
  loading.value = true;

  const request: Request = {
    route: endpoints.mergeOrder,
    method: "POST",
    body: {
      ...props.data,
      lab_location: authStore.locations.find((location) => location.name == authStore.selectedLocation)?.id
    },
  };

  const { data, error, pending }: Response = await fetchRequest(request);

  loading.value = pending;

  if (data.value) {
    useNuxtApp().$toast.success("Order merged successfully!");
    props.callback(data.value.accession_number);
    loading.value = false;
    emit("update", true);
    handleClick();
  }

  if (error.value) {
    loading.value = false;
    useNuxtApp().$toast.error(ERROR_MESSAGE);
    console.error(error.value);
    handleClick();
  }
};
</script>

<style></style>
