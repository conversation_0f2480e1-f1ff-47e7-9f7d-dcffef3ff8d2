<template>
  <div>
    <div>
      <CoreActionButton text="Add order to encounter" :color="props.color" :icon="editIcon" :click="() => {
        init();
      }
        " />
    </div>

    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleClick" class="relative z-10">
        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
          leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95">
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
                <div class="border-b px-3 py-3 flex items-center justify-between">
                  <DialogTitle as="h3" class="text-xl text-black flex items-center font-medium leading-6">
                    <svg class="w-6 h-6 mr-2" xmlns="http://www.w3.org/2000/svg" width="64" height="64"
                      viewBox="0 0 64 64">
                      <path fill="currentColor"
                        d="M43.191 40.403h5.498a.775.775 0 1 0 0-1.55h-5.498a.775.775 0 0 0 0 1.55m12.544-23.651H36.222V32.08h19.513zM54.319 30.78H37.622V18.059h16.697zM22.771 17.262H19.6v3.171h-3.171v3.171H19.6v3.171h3.171v-3.171h3.17v-3.171h-3.17z" />
                      <path fill="currentColor"
                        d="M43.191 22.323h5.498a.775.775 0 0 0 0-1.55h-5.498a.775.775 0 0 0 0 1.55" />
                      <path fill="currentColor"
                        d="M55.612.547H9.042C4.479.547.766 4.26.766 8.825v46.568c0 4.565 3.713 8.278 8.276 8.278h46.57c4.564 0 8.277-3.712 8.277-8.278V8.825c0-4.564-3.714-8.278-8.277-8.278M9.252 11.361v10.416H7.693a.533.533 0 0 1-.534-.534V7.802c0-.455.339-.825.805-.825h6.887c.464 0 .805.369.805.824v.789h10.648c.296 0 .534.239.534.532v3.023h-9.089v-.791c0-.454-.339-.823-.807-.823h-6.883c-.467 0-.806.375-.806.831zM30.49 28.617H11.881a.53.53 0 0 1-.534-.532V14.644c0-.455.339-.825.804-.825h6.885c.466 0 .807.369.807.823v.789H30.49c.296 0 .534.239.534.532v12.121a.53.53 0 0 1-.535.532zm26.644 27.286H34.828V15.316h22.306z" />
                      <path fill="currentColor"
                        d="M55.735 34.831H36.222v15.33h19.513zm-1.406 14.022H37.634V36.135h16.695z" />
                    </svg>
                    Add Order To Encounter
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit type="form" submit-label="Update" @submit="submitForm" :actions="false" id="submitForm"
                  #default="">
                  <div class="mt-2 space-y-3 pb-40">
                    <div class="w-full flex flex-col space-y-2 px-5">
                      <label class="font-medium">Department</label>
                      <multi-select style="--ms-max-height: none !important" v-model="selectedDepartment" :options="departments.map((department) => department.name)
                        " :searchable="true" :required="true" clear
                        class="focus:ring-none fcus:border-none focus:outline-none multiselect-green" />
                    </div>
                    <div class="w-full flex flex-col space-y-2 px-5">
                      <label class="font-medium">Specimen</label>
                      <multi-select style="--ms-max-height: none !important" v-model="selectedSpecimen"
                        :options="specimens" :searchable="true" :required="true" clear
                        class="focus:ring-none fcus:border-none focus:outline-none multiselect-green" />
                    </div>
                    <div class="w-full flex flex-col space-y-2 px-5">
                      <label class="font-medium">Test Types</label>
                      <multi-select style="--ms-max-height: none !important" v-model="selectedTestTypes"
                        :options="availableTestTypes" mode="tags" :searchable="true" :required="true" clear
                        class="focus:ring-none fcus:border-none focus:outline-none multiselect-green" />
                    </div>
                  </div>

                  <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                    <CoreOutlinedButton type="button" :click="() => resetForm()" text="Clear form" />
                    <CoreActionButton :loading="loading" type="submit" :click="() => { }" color="success"
                      :icon="saveIcon" text="Save changes" />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  PencilSquareIcon,
  ArrowDownTrayIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import type {
  Request,
  Response,
  Department,
  Drug,
  Location,
  Specimen,
  TestType,
  Order,
} from "@/types";
import fetchRequest from "@/services/fetch";
import { useAuthStore } from "@/store/auth";

interface ItemProps {
  color: string;
  item: {
    order_id: string;
    specimen_id: string;
    client?: {
      sex: string;
    };
    [key: string]: any;
  };
}

const emit = defineEmits<{
  (e: "update", value: Order): void;
}>();

const props = defineProps<ItemProps>();

const open = ref<boolean>(false);
const editIcon = PencilSquareIcon;
const saveIcon = ArrowDownTrayIcon;
const loading = ref<boolean>(false);
const specimens = ref<string[]>([]);
const rawSpecimens = ref<Specimen[]>([]);
const testTypes = ref<TestType[]>([]);
const departments = ref<Department[]>([]);
const facility = useAuthStore();
const { getSpecimenDisplayName } = useDisplayService();
const selectedDepartment = ref<string>("-- select department");
const selectedSpecimen = ref<string>("");
const selectedTestTypes = ref<string[]>([]);
const availableTestTypes = ref<string[]>([]);
const cookie = useCookie<string>("token");
const authStore = useAuthStore();
const departmentId = ref<number>(0);
const { $toast } = useNuxtApp();

async function init(): Promise<void> {
  handleClick();

  const selectedDept: string = authStore.department;
  const foundDepartment: Department | undefined =
    authStore.user.departments.find(
      (department: Department) => department.name === selectedDept
    );

  if (foundDepartment) {
    departmentId.value = Number(foundDepartment.id);
  }

  selectedDepartment.value = selectedDept;
  departments.value = authStore.user.departments;

  await getSpecimenTypes(departmentId.value);
}

async function getSpecimenTypes(deptId: number): Promise<void> {
  const patientSex: string = props.item.client?.sex.match(/f/i)
    ? "Female"
    : "Male";
  const request: Request = {
    route: `${endpoints.specimens}?department_id=${deptId}&specimen_id=${props.item.specimen_id}&sex=${patientSex}`,
    method: "GET",
    token: `${cookie.value}`,
  };

  const { error, data }: Response = await fetchRequest(request);

  if (data.value) {
    rawSpecimens.value = data.value;
    specimens.value = data.value.map((specimen: Specimen) =>
      getSpecimenDisplayName(specimen.name, specimen.preferred_name)
    );
  }

  if (error.value) {
    console.error("error: ", error.value);
  }
}

async function getTestTypesForSpecimen(): Promise<void> {
  if (!selectedSpecimen.value) {
    availableTestTypes.value = [];
    return;
  }

  // Find the specimen object by matching the display name
  const selectedSpecimenObj = rawSpecimens.value.find((specimen: Specimen) => {
    const displayName = getSpecimenDisplayName(specimen.name, specimen.preferred_name);
    return displayName === selectedSpecimen.value;
  });

  if (!selectedSpecimenObj) {
    availableTestTypes.value = [];
    return;
  }

  const patientSex: string = props.item.client?.sex.match(/f/i)
    ? "Female"
    : "Male";

  const request: Request = {
    route: `specimen/test_types?specimen_id=${selectedSpecimenObj.id}&department_id=${departmentId.value}&sex=${patientSex}`,
    method: "GET",
    token: `${cookie.value}`,
  };

  const { error, data }: Response = await fetchRequest(request);

  if (data.value) {
    testTypes.value = data.value;
    availableTestTypes.value = data.value;
    selectedTestTypes.value = [];
  }

  if (error.value) {
    console.error("error: ", error.value);
    availableTestTypes.value = [];
  }
}

async function submitForm(): Promise<void> {
  loading.value = true;

  // Find the specimen object by matching the display name
  const selectedSpecimenObj = rawSpecimens.value.find((specimen: Specimen) => {
    const displayName = getSpecimenDisplayName(specimen.name, specimen.preferred_name);
    return displayName === selectedSpecimen.value;
  });

  if (!selectedSpecimenObj) {
    $toast.error("Please select a valid specimen");
    loading.value = false;
    return;
  }

  interface TestOrder {
    specimen: string;
    test_type: string;
  }

  let tests: TestOrder[] = selectedTestTypes.value.map(
    (testType: string): TestOrder => ({
      specimen: selectedSpecimenObj.id.toString(),
      test_type: testType,
    })
  );

  interface OrderRequestBody {
    encounter_id: string;
    lab_location: number;
    tests: TestOrder[];
  }

  const locationObj = facility.locations.find(
    (location: Location) => location.name === facility.selectedLocation
  );

  if (!locationObj) {
    $toast.error("Location not found");
    loading.value = false;
    return;
  }

  const request: Request = {
    route: `${endpoints.addOrderToEncounter}`,
    method: "POST",
    token: `${cookie.value}`,
    body: {
      encounter_id: props.item.encounter_id,
      lab_location: locationObj?.id || 0,
      tests: tests,
    } as OrderRequestBody,
  };

  const { data, error }: Response = await fetchRequest(request);

  if (data.value) {
    loading.value = false;
    emit("update", data.value);
    handleClick();
  }

  if (error.value) {
    handleClick();
    console.error("error: ", error.value);
    $toast.error(ERROR_MESSAGE);
    loading.value = false;
  }
}

function handleClick(): void {
  open.value = !open.value;
}

function resetForm(): void {
  selectedSpecimen.value = "";
  selectedTestTypes.value = [];
  availableTestTypes.value = [];
}

watch(
  selectedDepartment,
  (newDepartment: string, oldDepartment: string): void => {
    if (newDepartment !== oldDepartment) {
      const foundDepartment: Department | undefined =
        authStore.user.departments.find(
          (department: Department) => department.name === newDepartment
        );

      if (foundDepartment) {
        departmentId.value = Number(foundDepartment.id);
        getSpecimenTypes(departmentId.value);
        selectedSpecimen.value = "";
        selectedTestTypes.value = [];
        availableTestTypes.value = [];
      }
    }
  }
);

watch(selectedSpecimen, () => {
  getTestTypesForSpecimen();
});
</script>

<style scoped></style>
