<template>
  <div>
    <div>
      <CoreActionButton
        text="Outcome"
        color="success"
        :icon="ChatBubbleLeftEllipsisIcon"
        :click="
          () => {
            init();
          }
        "
        :loading="loading"
      />
    </div>

    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleClick" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-xl text-black flex items-center font-medium leading-6"
                  >
                    <svg
                      class="w-8 h-8 mr-2"
                      xmlns="http://www.w3.org/2000/svg"
                      width="64"
                      height="64"
                      viewBox="0 0 64 64"
                    >
                      <path
                        fill="currentColor"
                        d="M55.407.547H8.833C4.271.547.559 4.26.559 8.824v46.568c0 4.565 3.713 8.279 8.274 8.279h46.574c4.564 0 8.276-3.713 8.276-8.279V8.824c0-4.564-3.712-8.277-8.276-8.277M35.358 16.592a6.583 6.583 0 0 1 6.584 6.584c0 3.634-2.949 6.586-6.584 6.586s-6.584-2.952-6.584-6.586a6.583 6.583 0 0 1 6.584-6.584M8.191 18.675v-5.269h5.269V8.137h5.269v5.269h5.269v5.269h-5.269v5.268H13.46v-5.268zM52.64 53.058c-.488 0-.957-.095-1.385-.267l-5.862-1.631v5.843H25.044V51.16l-5.86 1.631a3.7 3.7 0 0 1-1.385.267a3.755 3.755 0 0 1-3.112-5.853l9.4-12.889c1.02-1.398 2.445-2.955 5.45-2.955H40.9c3.004 0 4.43 1.558 5.449 2.955l9.399 12.889a3.74 3.74 0 0 1 .642 2.099a3.757 3.757 0 0 1-3.752 3.754z"
                      />
                      <path
                        fill="currentColor"
                        d="M25.043 35.868v7.816l.712-.24c5.242-1.545 7.281 5.376 2.04 6.918l-.168.047l7.592 2.513l7.593-2.513l-.171-.047c-5.238-1.542-3.199-8.463 2.039-6.918l.716.24v-7.816l-10.177 3.325z"
                      />
                    </svg>
                    Transfusion outcome
                  </DialogTitle>

                  <button type="button" @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  #default="{ value }"
                  id="collectionForm"
                >
                  <div class="mt-2 space-y-3 px-5 py-5">
                    <FormKit
                      type="text"
                      label="Transfusion Outcome"
                      v-model="transfusionOutcome"
                      validation="required"
                    />
                    <div class="mt-2">
                      <label>
                        <input type="checkbox" v-model="returned" />
                        Is it returned?
                      </label>
                    </div>
                    <div v-if="returned" class="space-y-3">
                      <FormKit
                        type="text"
                        label="Returned By"
                        v-model="returnedBy"
                        validation="required"
                      />
                      <div class="w-full relative flex flex-col space-y-2">
                        <label class="font-medium">Returned Date</label>
                        <div class="flex items-center space-x-2">
                        <datepicker
                          required
                          position="left"
                          @cleared="cleared"
                          placeholder="select collection date and time"
                          :range="false"
                          :teleport="true"
                          format="dd/MM/yyyy HH:mm:ss"
                          input-class-name="datepicker"
                          v-model="returnedDate"
                          :minDate="details?.created_date"
                          :maxDate="new Date()"
                        />
                        <CoreActionButton
                          text="Now"
                          color="primary"
                          :click="
                            () => {
                              setTimeToNow();
                            }
                          "
                          :icon="ClockIcon"
                        />
                      </div>
                      </div>
                      <FormKit
                        type="textarea"
                        label="Returned Reason"
                        v-model="returnedReason"
                        validation="required"
                      />
                    </div>
                  </div>

                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton
                      type="button"
                      text="Clear form"
                      :click="
                        () => {
                          resetForm();
                        }
                      "
                    />
                    <CoreActionButton
                      :loading="submitting"
                      type="submit"
                      color="success"
                      :icon="saveIcon"
                      :click="() => {}"
                      text="Save details"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import { XMarkIcon, ArrowDownTrayIcon, ClockIcon } from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response, Test } from "@/types";
import { ChatBubbleLeftEllipsisIcon } from "@heroicons/vue/24/outline";

interface Props {
  item: Test;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update", value: boolean): void;
}>();

const details = ref<Test>();
const open = ref<boolean>(false);
const saveIcon = ArrowDownTrayIcon;
const transfusionOutcome = ref<string>("");
const returned = ref<boolean>(false);
const returnedReason = ref<string>("");
const returnedBy = ref<string>("");
const returnedDate = ref<string>("");
const loading = ref<boolean>(false);
const submitting = ref<boolean>(false);
const zebraPrinting = ref<boolean>(false);
const { $toast } = useNuxtApp();

const init = async (): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: `${endpoints.tests}/${props.item.id}`,
    method: "GET",
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;
  if (data.value) {
    loading.value = false;
    details.value = data.value;
    handleClick();
  }
  if (error.value) {
    loading.value = false;
    console.error(error.value);
    $toast.error(ERROR_MESSAGE);
  }
};

const cleared = (): void => {
  returnedDate.value = "";
};

const setTimeToNow = (): void => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");

  returnedDate.value = `${year}-${month}-${day}T${hours}:${minutes}`;
};

const submitForm = async (): Promise<void> => {
  submitting.value = true;

  const request: Request = {
    route: `${endpoints.processCrossMatch}`,
    method: "POST",
    body: {
      post_crossmatch_process: {
        facility_section_id:
          props.item.post_crossmatch_process.facility_section_id,
        test_id: props.item.id,
        transfusion_outcome: transfusionOutcome.value,
        returned: returned.value,
        returned_by: returnedBy.value,
        returned_date: returnedDate.value,
        returned_reason: returnedReason.value,
      },
    },
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  submitting.value = pending;

  if (data.value) {
    $toast.success("Transfusion outcome saved successfully!");
    handleClick();
    submitting.value = false;
    printBBResults();
    emit("update", true);
  }

  if (error.value) {
    console.error(error.value);
    $toast.error(ERROR_MESSAGE);
    submitting.value = false;
  }
};

const handleClick = (): void => {
  open.value = !open.value;
};

async function printBBResults(): Promise<void> {
  const request: Request = {
    route: endpoints.printOutZebra,
    method: "POST",
    body: {
      order_id: details.value?.order_id,
      tests: [details.value?.id],
      is_cross_match: true,
    },
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  zebraPrinting.value = pending;
  if (data.value) {
    zebraPrinting.value = false;
    const reader = new FileReader();
    reader.onload = () => {
      const url = URL.createObjectURL(data.value);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${Date.now()}.lbl`);
      link.click();
      URL.revokeObjectURL(url);
    };
    reader.readAsText(data.value);
    $toast.success("Results printed successfully!");
  }
  if (error.value) {
    zebraPrinting.value = false;
    $toast.error(ERROR_MESSAGE);
    console.error(error.value);
  }
}

const resetForm = (): void => {
  transfusionOutcome.value = "";
  returned.value = true;
  returnedBy.value = "";
  returnedDate.value = "";
  returnedReason.value = "";
};
</script>
