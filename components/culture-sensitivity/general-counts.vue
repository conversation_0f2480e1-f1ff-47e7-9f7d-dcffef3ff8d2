<template>
  <div>
    <div class="w-full flex items-center justify-between mb-10">
      <form @submit.prevent="generateReport()" class="flex items-center space-x-5">
        <div class="flex flex-row items-center bg-gray-100 rounded-l pl-2 font-normal text-zinc-500">
          <FunnelIcon class="w-4 h-4 mr-2" />
          Filter by date
          <div class="w-44 ml-2">
            <datepicker placeholder="month & year" required input-class-name="custom-input" v-model="dateFrom"
              :range="false" format="M/yyyy" position="left" :maxDate="new Date()"/>
          </div>
        </div>
        <div class="w-48">
          <CoreActionButton type="submit" color="primary" text="Generate Report" :icon="ArrowPathIcon"
            :click="() => { }" :loading="loading" />
        </div>
      </form>
      <div>
        <excel class="btn btn-default" :header="[
          `CULTURE & SENSITIVITY GENERAL COUNTS REPORT ${dateFrom}`,
          facility.details.name,
          facility.details.address,
          facility.details.phone,
          '',
          '',
        ]" :data="exportData" worksheet="report-work-sheet" :name="`culture_sensitivity_general_counts_report_${moment(
          dateFrom
        ).format('M_YYYY')}.xls`">
          <CoreExportButton text="Export Excel" icon="excel.png" />
        </excel>
      </div>
    </div>

    <div class="rounded border" id="print-container">
      <div class="rounded-tr rounded-tl border-b px-5 py-5 flex items-center justify-between">
        <div class="flex flex-col space-y-2">
          <img src="@/assets/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
          <h3 class="text-xl font-semibold">
            CULTURE & SENSITIVITY GENERAL COUNTS REPORT
          </h3>
        </div>
        <ReportsAddress />
      </div>

      <div>
        <h3 class="px-4 py-2.5 font-medium">
          Data for period:
          <span class="font-normal">{{dateFrom == "" ? " - " : moment(dateFrom).format("M/yyyy") }}</span>
        </h3>
      </div>
      <table class="w-full mt-2" ref="table" v-if="!isObjectNull(reportData) && !loading">
        <thead class="w-full border-b border-t bg-gray-100 rounded-t">
          <tr class="w-full">
            <th v-for="(header, index) in headers" class="px-2 py-2 border-r text-left uppercase" :key="index">
              {{ header.name }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr class="border-b">
            <td
              class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
              @click="
                toEnumeration(
                  'Growth',
                  reportData['Growth'].total,
                  reportData['Growth'].associated_ids
                )
                ">
              {{ reportData["Growth"].total }}
            </td>
            <td
              class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
              @click="
                toEnumeration(
                  'No growth',
                  reportData['No growth'].total,
                  reportData['No growth'].associated_ids
                )
                ">
              {{ reportData["No growth"].total }}
            </td>
            <td
              class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
              @click="
                toEnumeration(
                  'Mixed growth; no predominant organism',
                  reportData['Mixed growth; no predominant organism'].total,
                  reportData['Mixed growth; no predominant organism']
                    .associated_ids
                )
                ">
              {{ reportData["Mixed growth; no predominant organism"].total }}
            </td>
            <td
              class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
              @click="
                toEnumeration(
                  'Growth of normal flora; no pathogens isolated',
                  reportData['Growth of normal flora; no pathogens isolated']
                    .total,
                  reportData['Growth of normal flora; no pathogens isolated']
                    .associated_ids
                )
                ">
              {{
                reportData["Growth of normal flora; no pathogens isolated"]
                  .total
              }}
            </td>
            <td
              class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
              @click="
                toEnumeration(
                  'Growth of contaminants',
                  reportData['Growth of contaminants'].total,
                  reportData['Growth of contaminants'].associated_ids
                )
                ">
              {{ reportData["Growth of contaminants"]?.total }}
            </td>
          </tr>
        </tbody>
      </table>
      <ReportsLoader :condition="loading" :cancelReportGeneration="() => cancelRequest()" />
      <div class="flex flex-col space-y-3 items-center justify-center py-10"
        v-if="isObjectNull(reportData) && !loading">
        <img src="@/assets/images/page.png" class="w-20 h-20 object-cover" alt="page-icon" />
        <p class="text-base">
          Please generate report data to preview the
          <span class="font-medium">General Counts</span>
          report.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowPathIcon, FunnelIcon } from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import { endpoints } from "@/services/endpoints";
import { useFacilityStore } from "@/store/facility";
import type { Request, Response } from "@/types";
import { showCultureEnumeration } from "./enumeration";
import { useCultureRoutesStore } from "@/store/culture";

interface CountData {
  total: number;
  associated_ids: string;
}

const cookie = useCookie("token");
const facility = useFacilityStore();
const cultureStore = useCultureRoutesStore();
const route = useRoute();
const routerQueryDateFrom = computed((): string => {
  const dateFrom = route.query.dateFrom
  if (dateFrom && typeof dateFrom === 'string' && dateFrom.trim() !== '') {
    return moment(dateFrom.split("/").join("-01-")).format(DATE_PICKER_FORMAT)
  }
  return ''
});
const updateCurrentPeriod = computed(async (): Promise<String> => {
  const discoveredRoute = cultureStore.routes.find(
    (r) => r.index == Number(0)
  );
  return discoveredRoute?.period || "";
});
const dateFrom = ref<string>(String(await updateCurrentPeriod.value));
const routerQuery = useRouterQuery();
const { $toast } = useNuxtApp();
const { loading, executeCancellableRequest, cancelRequest } = useCancellableRequest();

const reportData = ref<Record<string, CountData>>({
  Growth: { total: 0, associated_ids: "" },
  "No growth": { total: 0, associated_ids: "" },
  "Mixed growth; no predominant organism": { total: 0, associated_ids: "" },
  "Growth of normal flora; no pathogens isolated": {
    total: 0,
    associated_ids: "",
  },
  "Growth of contaminants": { total: 0, associated_ids: "" },
});

const headers = ref<Record<string, string>[]>([
  {
    name: "Growth",
  },
  {
    name: "No Growth",
  },
  {
    name: "Mixed Growth:  No Predominant Organism",
  },
  {
    name: "Growth Normal Flora: No Pathogens Isolated",
  },
  {
    name: "Growth: Contaminations",
  },
]);

const exportData = computed(() => {
  return headers.value.map((header: Record<string, string>) => ({
    NAME: header.name,
    COUNT: reportData.value[header.name] || 0,
  }));
});

const toEnumeration = (
  test: string,
  count: number,
  associated_ids: string
): void => {
    routerQuery.replaceOneQuery("revert", { "revert": "true" });
  showCultureEnumeration(
    {
      test: test,
      type: "General Counts",
      count: count,
      associated_ids: associated_ids,
      date_filter: moment(dateFrom.value).format("YYYY-MM-DD"),
    },
    useNuxtApp()
  );
};

const formattedYear = computed(() => {
  return dateFrom.value.length !== 0 ? moment(dateFrom.value).format("yyyy") : "";
});

const formattedMonth = computed(() => {
  return dateFrom.value.length !== 0 ? moment(dateFrom.value).format("M") : "";
});

async function generateReport(): Promise<void> {
  loading.value = true;
  const reportId = await updateCurrentRoute.value;
  const request: Request = {
    route: `${endpoints.aggregateReports}culture/general_counts?year=${formattedYear.value}&month=${formattedMonth.value}&report_id=${reportId || ''}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error, pending }: Response = await executeCancellableRequest(request);
  loading.value = pending;
  if (data.value) {
    reportData.value = data.value;
    loading.value = false;
    cultureStore.setCurrentRoute(Number(0), data.value.report_id, dateFrom.value);
    $toast.success("Report data generated successfully");
  }
  if (error.value) {
    console.error(error.value);
    loading.value = false;
    $toast.error(ERROR_MESSAGE);
  }
}

const updateCurrentRoute = computed(async (): Promise<String> => {
  const discoveredRoute = cultureStore.routes.find(
    (r) => r.index == Number(0)
  );
  return discoveredRoute?.reportId || "";
});

watch(
  dateFrom,
  () => {
    if (dateFrom) {
      cultureStore.setCurrentRoute(Number(0), "");
    }
  },
  { deep: true }
);

onMounted(async () => {
  if (await updateCurrentRoute.value || routerQueryDateFrom.value !== "") {
    generateReport();
  }else{
    loading.value = false;
  }
});
</script>

<style lang="scss">
.custom-input {
  font-family: "Inter";
  border-color: #ececec;

  &:hover {
    border-color: #ececec;
  }
}
</style>