import type { NuxtApp } from "#app";

interface Metadata {
  test: string;
  type: string;
  count: number;
  associated_ids: string;
  date_filter?: string;
}

/**
 * @method showCultureEnumeration
 * @param metadata
 * @param nuxtApp
 * @returns void
 */
export const showCultureEnumeration = (
  metadata: Metadata,
  nuxtApp: NuxtApp
): void => {
  if (metadata.count !== 0 && metadata.associated_ids !== "") {
    nuxtApp.$router.push(`/reports/${metadata.associated_ids}?origin=culture&type=culture-sensitivity-${metadata.type}-report&from=${metadata.date_filter}&to=${metadata.date_filter}&test=${metadata.test}&department=Microbiology&count=${metadata.count}`
    );
  } else nuxtApp.$toast.warning("No data found for this month");
};