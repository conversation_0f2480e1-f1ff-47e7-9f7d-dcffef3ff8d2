<template>
  <div>
    <CoreDropdown
      :items="dropdownItems"
      v-model="selectedResult"
      @update:modelValue="getUpdatedValue"
    />

    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-lg flex items-center font-medium leading-6"
                  >
                    <img
                      src="@/assets/icons/bacteria.svg"
                      class="w-8 h-8 mr-2"
                    />
                    Select organisms
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <div class="space-y-3 px-5 py-5 pb-20">
                  <div class="w-full flex flex-col space-y-2">
                    <label class="font-medium">Organisms</label>
                    <multi-select
                      style="--ms-max-height: none !important"
                      v-model="organismsSelected"
                      :options="organisms"
                      mode="tags"
                      required
                      clear
                      searchable
                      class="focus:ring-none focus:border-none focus:outline-none multiselect-green"
                    />
                  </div>

                  <div class="flex justify-end">
                    <CoreActionButton
                      :click="apply"
                      :icon="saveIcon"
                      text="Apply"
                      color="success"
                    />
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  ArrowTopRightOnSquareIcon,
  PencilSquareIcon,
  ArrowDownTrayIcon,
} from "@heroicons/vue/24/solid/index.js";

import moment from "moment";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { DropdownItem, Organism, Request, Response } from "@/types";

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
  },
  data() {
    return {
      viewIcon: ArrowTopRightOnSquareIcon as Object,
      show: false as boolean,
      editIcon: PencilSquareIcon as Object,
      saveIcon: ArrowDownTrayIcon as Object,
      moment: moment,
      cookie: useCookie("token"),
      rawOrganisms: new Array<{ id: number; name: string }>(),
      organisms: new Array<string>(),
      organismsSelected: new Array<String>(),
      selectedResult: <DropdownItem>{ name: "--select result--" },
    };
  },
  props: {
    data: {
      required: true,
      type: Array,
    },
    result: {
      required: true,
      type: Object,
    },
  },
  created() {
    this.init();
  },
  computed: {
    dropdownItems() {
      const dataCopy = [...this.data];
      dataCopy.unshift({ value: '--select result--' });
      return dataCopy.map((item: any) => {
        return {
          name: item.value,
        };
      });
    },
  },

  methods: {
    /**
     * @method init get current result value
     * @returns void
     */
    init(): void {
      if (this.result?.value !== null) {
        this.selectedResult = { name: this.result?.value ? this.result?.value : '--select result--' };
      }
    },
    /**
     * @method getUpdatedValue get latest value from core-dropdown result values
     * @param value dropdownitem @type
     * @returns void
     */
    getUpdatedValue(value: DropdownItem): void {
      if (value.name.toLowerCase() == "growth") {
        this.loadOrganisms();
      }
    },
    /**
     * @method loadOrganisms load organisms to the multi-select
     * @returns promise @type void
     */
    async loadOrganisms(): Promise<void> {
      this.handleClick();
      const request: Request = {
        route: endpoints.organisms,
        method: "GET",
        token: `${this.cookie}`,
      };
      const { data, error }: Response = await fetchRequest(request);
      if (data.value) {
        this.rawOrganisms = data.value;
        data.value.map((organism: { name: string }) => {
          this.organisms.push(organism.name);
        });
      }
      if (error.value) {
        console.error(error.value);
      }
    },
    /**
     * @method handleClick controls dialog visibility
     * @returns void
     */
    handleClick(): void {
      this.show = !this.show;
    },
    /**
     * @method apply after selecting organisms on growth
     * @returns void
     */
    apply(): void {
      let organisms: Organism[] = [];
      this.rawOrganisms.forEach((raw: Organism) => {
        this.organismsSelected.forEach((organism) => {
          if (raw.name === organism) {
            organisms.push(raw);
          }
        });
      });
      this.$emit("apply", organisms);
      this.organismsSelected = [];
      this.handleClick();
    },
  },
  watch: {
    selectedResult(__n, __o) {
      this.data.map((item: any) => {
        if (item.value === __n.name) {
          this.$emit("update", item);
        }
        else if(__n.name === '--select result--'){
          this.$emit("update", { value: null });
        }
      });
    },
  },
};
</script>

<style></style>
