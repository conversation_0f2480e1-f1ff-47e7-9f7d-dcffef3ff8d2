<template>
  <div>
    <div>
      <CoreActionButton
        :click="init"
        text="Add drug"
        color="primary"
        :icon="addIcon"
      />
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleClick" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-lg flex items-center font-medium leading-6"
                  >
                    <img
                      src="@/assets/icons/medicines.svg"
                      class="w-8 h-8 mr-2"
                    />
                    Add drug
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <div>
                  <div class="ma-5 px-4 py-4">
                    <multi-select
                      v-model="selectedDrugs"
                      label="Select drug(s)"
                      :options="drugs"
                      mode="tags"
                    clear
                    searchable
                    class="focus:ring-none fcus:border-none focus:outline-none multiselect-green"
                    />
                  </div>

                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreActionButton
                      :click="
                        () => {
                          pushDrugs(selectedDrugs);
                        }
                      "
                      type="submit"
                      color="success"
                      :icon="saveIcon"
                      text="Save chages"
                    />
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  PlusIcon,
  XMarkIcon,
  ArrowDownTrayIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Drug } from "@/types";

export default {
  components: {
    TransitionChild,
    TransitionRoot,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
  },
  data() {
    return {
      addIcon: PlusIcon as Object,
      saveIcon: ArrowDownTrayIcon as Object,
      open: false as boolean,
      loading: false as boolean,
      cookie: useCookie("token"),
      drugs: new Array<string>(),
      selectedDrugs: new Array<string>(),
      rawDrugs: new Array<{ id: number; name: string }>(),
    };
  },
  props: {
    index: {
      required: true,
      type: Number,
    },
  },
  methods: {
    async init(): Promise<void> {
      this.loading = false;
      const request: Request = {
        route: endpoints.drugs,
        method: "GET",
        token: `${this.cookie}`,
      };
      const { data, error, pending } = await fetchRequest(request);
      this.loading = pending;
      if (data.value) {
        this.handleClick();
        this.rawDrugs = data.value;
        data.value.map((drug: { name: string }) => {
          this.drugs.push(drug.name);
        });
      }
      if (error.value) {
        console.error(error.value);
      }
    },
    updatedDrugs(value: any): void {
      this.selectedDrugs = value;
    },
    pushDrugs(drugs: Array<string>): void {
      let formattedDrugs = new Array<Drug>();
      this.rawDrugs.map((drug: any) => {
        drugs.map((item) => {
          if (drug.name.toLowerCase() === item.toLowerCase()) {
            formattedDrugs.push(drug);
          }
        });
      });
      this.$emit("update", { drugs: formattedDrugs, index: this.index });
      this.handleClick();
    },
    /**
     * @method handleClick
     * @param null
     * @returns void
     */
    handleClick(): void {
      this.open = !this.open;
    },
  },
};
</script>

<style></style>
