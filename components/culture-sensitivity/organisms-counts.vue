<template>
  <div>
    <div class="w-full flex items-center justify-between mb-10">
      <FormKit type="form" submit-label="Update" @submit="generateReport" :actions="false" #default="">
        <div class="flex items-center space-x-5">
          <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
            <FunnelIcon class="w-5 h-5 mr-2" />
            Filter By Date Range
            <div class="w-56 ml-2">
              <datepicker @cleared="isCleared" required position="left" placeholder="select month & year" :range="false"
                input-class-name="datepicker" v-model="dateFrom" format="M/yyyy" :maxDate="new Date()"/>
            </div>
          </div>
          <div class="w-48">
            <CoreActionButton type="submit" color="primary" text="Generate Report" :icon="refreshIcon" :click="() => { }"
              :loading="loading" />
          </div>
        </div>
      </FormKit>
      <div>
        <excel class="btn btn-default" :header="[
          `CULTURE & SENSITIVITY ORGANISMS COUNTS REPORT ${moment(
            dateFrom
          ).format('M/yyyy')}`,
          facility.details.name,
          facility.details.address,
          facility.details.phone,
        ]" :data="exportData" worksheet="report-work-sheet" :name="`culture_sensitivity_organisms_counts_report_${moment(
            dateFrom
          ).format('M_yyyy')}.xls`">
          <CoreExportButton text="Export Excel" />
        </excel>
      </div>
    </div>

    <div class="rounded border" id="print-container">
      <div class="rounded-tr rounded-tl border-b px-5 py-5 flex items-center justify-between">
        <div class="flex flex-col space-y-2">
          <img src="@/assets/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
          <h3 class="text-xl font-semibold">
            CULTURE & SENSITIVITY ORGANISMS COUNTS REPORT
          </h3>
        </div>
        <ReportsAddress />
      </div>
      <div>
        <h3 class="px-4 mt-2">
          Data for period:
          <span class="font-semibold">{{
            dateFrom == "" ? " - " : moment(dateFrom).format("M/yyyy")
            }}</span>
        </h3>
      </div>
      <div v-if="reportData !== undefined && reportData.length > 0 && !loading">
        <table class="w-full mt-3" ref="table">
          <thead class="w-full border-b border-t bg-gray-50 rounded-t">
            <tr class="w-full">
              <th v-for="(header, index) in headers" class="px-2 py-2 border-r text-left uppercase" :key="index">
                {{ header.name }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(report, index) in reportData" :key="index" class="border-b">
              <td class="px-2 py-2 text-left border-r">
                {{ report.organism }}
              </td>
              <td class="px-2 py-2 text-left border-r">
                {{ dateFrom == "" ? " - " : moment(dateFrom).format("M/yyyy") }}
              </td>
              <td
                class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                @click="
                  toEnumeration(
                    `Organisms Based Counts (${report.organism})`,
                    report.count,
                    report.associated_ids
                  )
                  ">
                {{ report.count }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <ReportsLoader :condition="loading" :cancelReportGeneration="() => cancelRequest()" />
      <div class="flex flex-col space-y-3 items-center justify-center py-10" v-if="reportData !== undefined && reportData.length == 0 && !loading">
        <img src="@/assets/images/page.png" class="w-20 h-20 object-cover" alt="page-icon" />
        <p class="text-base">
          Please generate report data to preview the
          <span class="font-medium">Organisms Count</span>
          report.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowPathIcon as refreshIcon,
  FunnelIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import { endpoints } from "@/services/endpoints";
import { useFacilityStore } from "@/store/facility";
import type { Request, Response } from "@/types";
import { showCultureEnumeration } from "./enumeration";
import { useCultureRoutesStore } from "@/store/culture";

interface ReportData {
  organism: string;
  count: number;
  associated_ids: string;
}

const headers = ref<Array<Record<string, string>>>([
  {
    name: "Organism Name",
  },

  {
    name: "Period",
  },
  {
    name: "Total Count",
  },
]);
const cookie = useCookie("token");
const reportData = ref<ReportData[]>([]);
const facility = useFacilityStore();
const cultureStore = useCultureRoutesStore();
const { loading, executeCancellableRequest, cancelRequest } = useCancellableRequest();

const updateCurrentPeriod = computed(async (): Promise<String> => {
  const discoveredRoute = cultureStore.routes.find(
    (r) => r.index == Number(2)
  );
  return discoveredRoute?.period || "";
});
const routerQueryDateFrom = computed(async(): Promise<string> => {
  const dateFrom = await updateCurrentPeriod.value;
  if (dateFrom && typeof dateFrom === 'string' && dateFrom.trim() !== '') {
    return moment(dateFrom.split("/").join("-01-")).format(DATE_PICKER_FORMAT)
  }
  return ''
});
const dateFrom = ref<string>(String(await updateCurrentPeriod.value));
const exportData = computed((): any[] => {
  return reportData.value && reportData.value.length > 0
    ? reportData.value.map((report: ReportData) => ({
      ORGANISM: report.organism,
      "DATE PERIOD": moment(dateFrom.value).format("MMMM/yyyy"),
      COUNT: report.count,
    }))
    : [];
});


const toEnumeration = (
  test: string,
  count: number,
  associated_ids: string
): void => {
  showCultureEnumeration(
    {
      test: test,
      type: "General Counts",
      count: count,
      associated_ids: associated_ids,
      date_filter: moment(dateFrom.value).format("YYYY-MM-DD"),
    },
    useNuxtApp()
  );
};

const isCleared = (): void => {
  dateFrom.value = "";
};

const formattedYear = computed(() => {
  return dateFrom.value.length !== 0 ? moment(dateFrom.value).format("yyyy") : "";
});

const formattedMonth = computed(() => {
  return dateFrom.value.length !== 0 ? moment(dateFrom.value).format("M") : "";
});


async function generateReport(): Promise<void> {
  loading.value = true;
  let reportId = await updateCurrentRoute.value;
  const request: Request = {
    route: `${endpoints.aggregateReports}culture/organisms_based_counts?year=${formattedYear.value}&month=${formattedMonth.value}&report_id=${reportId || ''}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error, pending }: Response = await executeCancellableRequest(request);
  loading.value = pending;
  if (data.value) {
    reportData.value = data.value.data;
    loading.value = false;
    cultureStore.setCurrentRoute(Number(2), data.value.report_id, dateFrom.value);
    useNuxtApp().$toast.success("Report data generated successfully");
  }
  if (error.value) {
    console.error(error.value);
    loading.value = false;
    useNuxtApp().$toast.error(ERROR_MESSAGE);
  }
}

const updateCurrentRoute = computed(async (): Promise<String> => {
  const discoveredRoute = cultureStore.routes.find(
    (r) => r.index == Number(2)
  );
  return discoveredRoute?.reportId || "";
});

watch(
  dateFrom,
  () => {
    if (dateFrom) {
      cultureStore.setCurrentRoute(Number(2), "");
    }
  },
  { deep: true }
);

onMounted(async () => {
  if (await updateCurrentRoute.value || await routerQueryDateFrom.value !== "") {
    generateReport();
  }else{
    loading.value = false;
  }
});
</script>

<style></style>
