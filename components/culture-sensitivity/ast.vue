<template>
  <div>
    <div class="w-full flex items-center justify-between mb-10">
      <FormKit type="form" submit-label="Update" @submit="generateReport" :actions="false" #default="{ value }">
        <div class="flex items-center space-x-5">
          <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
            <FunnelIcon class="w-5 h-5 mr-2" />
            Filter By Date Range
            <div class="w-56 ml-2">
              <datepicker @cleared="isCleared" required position="left" placeholder="select month & year" :range="false"
                input-class-name="datepicker" v-model="dateFrom" format="M/yyyy" :maxDate="new Date()"/>
            </div>
          </div>
          <div class="w-48">
            <CoreActionButton type="submit" color="primary" text="Generate Report" :icon="refreshIcon"
              :click="() => { }" :loading="loading" />
          </div>
        </div>
      </FormKit>
      <div>
        <excel class="btn btn-default" :header="[
          `CULTURE & SENSITIVITY ANTIBIOTIC SUSCEPTIBILITY REPORT ${moment(
            dateFrom
          ).format('M/yyyy')}`,
          facility.details.name,
          facility.details.address,
          facility.details.phone,
        ]" :data="exportData" worksheet="report-work-sheet" :name="`culture_sensitivity_antibiotic_susceptibility_report_${moment(
          dateFrom
        ).format('M_yyyy')}.xls`">
          <CoreExportButton text="Export Excel" />
        </excel>
      </div>
    </div>
    <div class="rounded border" id="print-container">
      <div class="rounded-tr rounded-tl border-b px-5 py-5 flex items-center justify-between">
        <div class="flex flex-col space-y-2">
          <img src="@/assets/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
          <h3 class="text-xl font-semibold">
            CULTURE & SENSITIVITY ANTIBIOTIC SUSCEPTIBILITY REPORT
          </h3>
        </div>
        <ReportsAddress />
      </div>
      <div>
        <h3 class="px-4 mt-2 font-medium">
          Data for period:
          <span class="font-normal">{{
            dateFrom == "" ? "" : moment(dateFrom).format("M/yyyy")
          }}</span>
        </h3>
      </div>
      <div v-if="reportData.length > 0 && !loading">
        <template v-for="(report, index) in reportData" :key="index">
          <table class="w-full mt-3" ref="table">
            <thead class="w-full border-b border-t bg-gray-50 rounded-t">
              <tr>
                <th :colspan="4" class="text-left px-2 py-2">
                  {{ report.name }}
                </th>
              </tr>
              <tr class="border-r border-t">
                <th class="border-r px-2 py-2 text-left">Drug Name</th>
                <th class="border-r px-2 py-2 text-center">I</th>
                <th class="border-r px-2 py-2 text-center">R</th>
                <th class="px-2 py-2 text-center">S</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="drug in report.drugs" :key="drug" class="border-b">
                <td class="border-r px-2 py-2 text-left">
                  {{ drug.drug_name }}
                </td>
                <td
                  class="border-r px-2 py-2 text-center hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                  @click="
                    toEnumeration(
                      `Antimicrobial Subsceptibility Testing (${report.name}) - ${drug.drug_name}`,
                      drug.interpretations['I - Intermediate'].count,
                      drug.interpretations['I - Intermediate'].associated_ids
                    )
                    ">
                  {{ drug.interpretations['I - Intermediate'].count }}
                </td>
                <td
                  class="border-r px-2 py-2 text-center hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                  @click="
                    toEnumeration(
                      `Antimicrobial Subsceptibility Testing (${report.name}) - ${drug.drug_name}`,
                      drug.interpretations['R - Resistant'].count,
                      drug.interpretations['R - Resistant'].associated_ids
                    )
                    ">
                  {{ drug.interpretations['R - Resistant'].count }}
                </td>
                <td
                  class="px-2 py-2 text-center hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                  @click="
                    toEnumeration(
                      `Antimicrobial Subsceptibility Testing (${report.name}) - ${drug.drug_name}`,
                      drug.interpretations['S - Sensitive'].count,
                      drug.interpretations['S - Sensitive'].associated_ids
                    )
                    ">
                  {{ drug.interpretations['S - Sensitive'].count }}
                </td>
              </tr>
            </tbody>
          </table>
        </template>
      </div>
      <ReportsLoader :condition="loading" :cancelReportGeneration="() => cancelRequest()" />
      <div v-if="reportData.length == 0 && !loading"
        class="w-full flex flex-col items-center justify-center space-y-2 py-10">
        <img src="@/assets/images/page.png" alt="page-icon" class="object-cover w-20 h-20" />
        <p>Data not found, please generate report</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowPathIcon as refreshIcon,
  FunnelIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import { endpoints } from "@/services/endpoints";
import { useFacilityStore } from "@/store/facility";
import type { Request, Response } from "@/types";
import { showCultureEnumeration } from "./enumeration";
import { useCultureRoutesStore } from "@/store/culture";

const cookie = useCookie("token");
const reportData = ref<any[]>([]);
const facility = useFacilityStore();
const cultureStore = useCultureRoutesStore();
const { $toast } = useNuxtApp();
const { loading, executeCancellableRequest, cancelRequest } = useCancellableRequest();

const updateCurrentPeriod = computed(async (): Promise<String> => {
  const discoveredRoute = cultureStore.routes.find(
    (r) => r.index == Number(4)
  );
  return discoveredRoute?.period || "";
});
const routerQueryDateFrom = computed(async (): Promise<string> => {
  const dateFrom = await updateCurrentPeriod.value;
  if (dateFrom && typeof dateFrom === 'string' && dateFrom.trim() !== '') {
    return moment(dateFrom.split("/").join("-01-")).format(DATE_PICKER_FORMAT)
  }
  return ''
});
const dateFrom = ref<string>(String(await updateCurrentPeriod.value));

const exportData = computed(() => {
  return reportData.value.flatMap((report) => {
    return report.drugs.map(
      (drug: {
        drug_name: string;
        interpretations: {
          'I - Intermediate': { count: number; associated_ids: string };
          'R - Resistant': { count: number; associated_ids: string };
          'S - Sensitive': { count: number; associated_ids: string };
        };
      }) => ({
        "DRUG NAME": drug.drug_name,
        "INTERPRETATION I": drug.interpretations['I - Intermediate'].count,
        "INTERPRETATION R": drug.interpretations['R - Resistant'].count,
        "INTERPRETATION S": drug.interpretations['S - Sensitive'].count,
      })
    );
  });
});

const isCleared = (): void => {
  dateFrom.value = "";
};

const formattedYear = computed(() => {
  return dateFrom.value.length !== 0 ? moment(dateFrom.value).format("yyyy") : "";
});

const formattedMonth = computed(() => {
  return dateFrom.value.length !== 0 ? moment(dateFrom.value).format("M") : "";
});


const toEnumeration = (
  test: string,
  count: number,
  associated_ids: string
): void => {
  showCultureEnumeration(
    {
      test: test,
      type: "General Counts",
      count: count,
      associated_ids: associated_ids,
      date_filter: moment(dateFrom.value).format("YYYY-MM-DD"),
    },
    useNuxtApp()
  );
};

async function generateReport(): Promise<void> {
  loading.value = true;
  const reportId = await updateCurrentRoute.value;
  const request: Request = {
    route: `${endpoints.aggregateReports}culture/ast?year=${formattedYear.value}&month=${formattedMonth.value}&report_id=${reportId}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error, pending }: Response = await executeCancellableRequest(request);
  loading.value = pending;
  if (data.value) {
    reportData.value = data.value.data;
    loading.value = false;
    cultureStore.setCurrentRoute(Number(4), data.value.report_id, dateFrom.value);
    $toast.success("Report data generated successfully");
  }
  if (error.value) {
    console.error(error.value);
    loading.value = false;
    $toast.error(ERROR_MESSAGE);
  }
}

watch(
  dateFrom,
  () => {
    if (dateFrom) {
      cultureStore.setCurrentRoute(Number(4), "");
    }
  },
  { deep: true }
);

const updateCurrentRoute = computed(async (): Promise<String> => {
  const discoveredRoute = cultureStore.routes.find(
    (r) => r.index == Number(4)
  );
  return discoveredRoute?.reportId || "";
});

onMounted(async () => {
  if (await updateCurrentRoute.value || await routerQueryDateFrom.value !== "") {
    generateReport();
  } else {
    loading.value = false;
  }
});
</script>

<style></style>
