<template>
  <div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleDialog" class="relative z-10">
        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
          leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95">
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
                <div class="border-b px-3 py-3 flex items-center justify-between">
                  <DialogTitle as="h3" class="text-xl text-black flex items-center font-medium leading-6">
                    <img src="@/assets/icons/nausea.svg" class="w-8 h-8 mr-2" alt="nausea-icon" />
                    Create patient
                  </DialogTitle>

                  <button @click="handleDialog">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit type="form" id="patientForm" submit-label="Update" @submit="submitForm" :actions="false"
                  #default="{}">
                  <div class="mt-2 space-y-3 px-5 py-5">
                    <div class="w-full grid grid-cols-3 gap-2">
                      <FormKit type="text" label="First name" validation="required|matches:/^[a-zA-Z']+$/"
                        :validation-messages="{
                          matches:
                            'First name must contain only letters, no spaces or special characters',
                        }" v-model="firstName" />

                      <FormKit type="text" label="Middle name" v-model="middleName" />

                      <FormKit type="text" label="Last name" validation="required|matches:/^[a-zA-Z']+$/"
                        :validation-messages="{
                          matches:
                            'Last name must contain only letters, no spaces or special characters',
                        }" v-model="lastName" />
                    </div>
                    <div class="w-full flex flex-col space-y-2">
                      <label class="font-medium">National Patient ID</label>
                      <FormKit type="text" v-model="npid" />
                    </div>

                    <div class="grid grid-cols-2 gap-2">
                      <div class="w-full flex flex-col space-y-2">
                        <label class="font-medium">Date Of Birth</label>
                        <div class="w-full">
                          <datepicker placeholder="Click to select a date" v-model="dateOfBirth" :range="false"
                            :max-date="new Date()" @cleared="onCleared" :ignore-time-validation="true" :teleport="true"
                            input-class-name="datepicker" format="dd/MM/yyyy" required />
                        </div>
                      </div>
                      <PatientsAddDialogAgeInput @update:dateOfBirth="onEstimateDate" />
                    </div>

                    <div class="w-full grid grid-cols-2 gap-2">
                      <FormKit v-model="gender" type="radio" label="Sex" :options="['Male', 'Female']"
                        validation="required" />
                      <FormKit type="text" label="Physical Address" validation="required" v-model="physicalAddress" />
                    </div>
                  </div>

                  <div class="mt-3 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                    <CoreOutlinedButton type="button" :click="invalidateInputs" text="Clear form" />
                    <CoreActionButton type="submit" :click="() => { }" color="success" :icon="saveIcon"
                      text="Save changes" :loading="loading" />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";
import {
  XMarkIcon,
  ArrowDownTrayIcon as saveIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response } from "@/types";
import moment from "moment";

const emit = defineEmits(["onPatientCreated", "sethandleDialog"]);

const open: Ref<boolean> = ref<boolean>(false);
const loading: Ref<boolean> = ref<boolean>(false);
const cookie: Ref<string> = useCookie("token");
const $toast = useNuxtApp().$toast;

const props = defineProps<{
  openForm: boolean;
  defaultData: Record<string, any>;
}>();

const uuid = ref<string>("");
const firstName = ref<string>("");
const middleName = ref<string>("");
const lastName = ref<string>("");
const npid = ref<string>(""); // Added npid ref
const gender = ref<string>("");
const physicalAddress = ref<string>("");
const dateOfBirth = ref<string>("");
const estimatedDate = ref<boolean>(false);
const ageInputRef = ref<InstanceType<any> | null>(null);

const handleDialog = (): void => {
  open.value = !open.value;
  emit("sethandleDialog", open.value);
};

const onEstimateDate = (date: string): void => {
  dateOfBirth.value = new Date(String(date)).toString()
  estimatedDate.value = true;
};

const onCleared = (): void => {
  ageInputRef.value?.reset();
}

const submitForm = async (): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: endpoints.clients,
    method: "POST",
    token: `${cookie.value}`,
    body: {
      client: {
        uuid: "",
      },
      person: {
        first_name: firstName.value,
        middle_name: middleName.value,
        last_name: lastName.value,
        sex: gender.value.charAt(0),
        date_of_birth: dateOfBirth.value,
        birth_date_estimated: estimatedDate.value,
      },
      client_identifiers: {
        current_village: "",
        current_district: "",
        current_traditional_authority: "",
        physical_address: physicalAddress.value,
        npid: npid.value,
      },
    },
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;

  if (data.value) {
    $toast.success(`Patient created successfully!`);
    handleDialog();
    invalidateInputs();
    emit("onPatientCreated", true);
    loading.value = false;
  }

  if (error.value) {
    console.error(error.value);
    $toast.error(ERROR_MESSAGE);
    loading.value = false;
  }
};

const invalidateInputs = (): void => {
  uuid.value = "";
  firstName.value = "";
  middleName.value = "";
  lastName.value = "";
  gender.value = "";
  physicalAddress.value = "";
  dateOfBirth.value = "";
  estimatedDate.value = false;
  ageInputRef.value?.reset();
};

watch(() => props.defaultData, (data) => {
  if (data) {
    uuid.value = data.uuid;
    firstName.value = data.firstName;
    lastName.value = data.lastName;
    gender.value = data.gender.toUpperCase() == "M" ? "Male" : "Female";
  }
});

watch(
  () => props.openForm,
  (value) => {
    open.value = value;
  }
);
</script>