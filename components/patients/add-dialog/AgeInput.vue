<template>
    <div class="w-full flex flex-col space-y-2">
        <FormKit :label="inputLabel" type="number" :validation="currentValidation" v-model="ageValue"
            :validation-messages="{
                min: 'Age cannot be negative',
                max: `Age cannot exceed ${maxValues[selectedUnit]} ${selectedUnit}`
            }" />
        <div class="">
            <label v-for="unit in units" :key="unit.id" class="flex items-center space-x-2">
                <input type="radio" :checked="selectedUnit === unit.id" @change="handleUnitChange(unit.id)"
                    name="ageUnit" class="form-radio" />
                <span>{{ unit.label }}</span>
            </label>
        </div>
    </div>
</template>

<script setup lang="ts">
import moment from "moment";

interface Unit {
    id: AgeUnit;
    label: string;
}

type AgeUnit = "years" | "months" | "weeks" | "days";

interface Props {
    dateOfBirth?: string;
}

const props = withDefaults(defineProps<Props>(), {
    dateOfBirth: undefined,
});

const emit = defineEmits<{
    "update:modelValue": [value: number];
    "update:dateOfBirth": [value: string];
}>();

const units: Unit[] = [
    { id: "years", label: "Years" },
    { id: "months", label: "Months" },
    { id: "weeks", label: "Weeks" },
    { id: "days", label: "Days" },
];

const maxValues: Record<AgeUnit, number> = {
    years: 120,
    months: 1440,
    weeks: 6240,
    days: 43800,
};

const selectedUnit = ref<AgeUnit>("days");
const ageValue = ref<number>(0);
const isUpdatingDateOfBirth = ref<boolean>(false);

const currentValidation = computed(() => {
    return `required|min:0|max:${maxValues[selectedUnit.value]}`;
});

const inputLabel = computed(() => {
    return `Estimated ${selectedUnit.value}`;
});

const calculatedDate = computed(() => {
    if (!ageValue.value) return "";
    if (ageValue.value === 0) return "";
    const date = moment().subtract(ageValue.value, selectedUnit.value);
    return date.format("YYYY-MM-DD");
});

const convertAge = (value: number, fromUnit: AgeUnit, toUnit: AgeUnit): number => {
    const date = moment().subtract(value, fromUnit);
    return moment().diff(date, toUnit);
};

const handleUnitChange = (newUnit: AgeUnit) => {
    if (ageValue.value) {
        const convertedAge = convertAge(ageValue.value, selectedUnit.value, newUnit);
        selectedUnit.value = newUnit;
        ageValue.value = convertedAge;
    } else {
        selectedUnit.value = newUnit;
    }
};

const estimateAge = (dateOfBirth: string, unit: AgeUnit) => {
    const dob = moment(dateOfBirth, "YYYY-MM-DD");
    ageValue.value = moment().diff(dob, unit);
};

const reset = () => {
    ageValue.value = 0;
    selectedUnit.value = "days";
};

watch(ageValue, () => {
    if (!isUpdatingDateOfBirth.value && ageValue.value !== undefined) {
        isUpdatingDateOfBirth.value = true;
        emit("update:dateOfBirth", calculatedDate.value);
        isUpdatingDateOfBirth.value = false;
    }
});

watch(() => props.dateOfBirth, (newDateOfBirth) => {
    if (newDateOfBirth && !isUpdatingDateOfBirth.value) {
        estimateAge(newDateOfBirth, selectedUnit.value);
    }
});

onMounted(() => {
    if (props.dateOfBirth) {
        estimateAge(props.dateOfBirth, selectedUnit.value);
    }
});

defineExpose({ reset });
</script>