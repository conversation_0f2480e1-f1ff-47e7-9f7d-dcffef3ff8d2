<script setup lang="ts">
import {
	TransitionRoot,
	TransitionChild,
	Dialog,
	DialogPanel,
	DialogTitle,
} from '@headlessui/vue';

import {
	PlusIcon,
	XMarkIcon,
	ArrowRightCircleIcon,
} from '@heroicons/vue/24/solid/index.js';

import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import type { Response, Request } from '@/types';

interface ISearchPatient {
	firstName: string;
	lastName: string;
	gender: string;
}

const emit = defineEmits(['action-completed']);

const open: Ref<boolean> = ref<boolean>(false);
const loading: Ref<boolean> = ref<boolean>(false);
const cookie: Ref<string> = useCookie('token');
const { $toast } = useNuxtApp();

const details: Ref<ISearchPatient> = ref<ISearchPatient>({
	firstName: '' as string,
	lastName: '' as string,
	gender: '' as string,
});

const adjustVisibility = (): boolean => (open.value = !open.value );

const handleSearchPatient = async (): Promise<void> => {
	loading.value = true;
	const request: Request = {
		route: getParameterizedUrl(endpoints.client.search, {
			first_name: details.value.firstName,
			last_name: details.value.lastName,
			gender: details.value.gender,
		}),
		method: 'GET',
		token: `${cookie.value}`,
	};

	const { data, error, pending }: Response = await fetchRequest(request);
	loading.value = pending;

	if (data.value) {
		adjustVisibility();
		emit('action-completed', { patient: data.value, defaults: details.value });
		details.value = {
			firstName: '' as string,
			lastName: '' as string,
			gender: '' as string,
		};
	}

	if (error.value) {
		$toast.error(`${ERROR_MESSAGE}`);
	}

	loading.value = open.value = false;
};
</script>

<template>
	<div>
		<div>
			<CoreActionButton text="Find/Create Patient" color="primary" :icon="PlusIcon" :click="adjustVisibility" />
		</div>

		<TransitionRoot appear :show="open" as="template">
			<Dialog as="div" @close="adjustVisibility" class="relative z-10">
				<TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
					leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
					<div class="fixed inset-0 bg-black bg-opacity-25"></div>
				</TransitionChild>

				<div class="fixed inset-0 overflow-y-auto">
					<div class="flex min-h-full items-center justify-center p-4 text-center">
						<TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
							enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
							leave-to="opacity-0 scale-95">
							<DialogPanel
								class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
								<div class="border-b px-3 py-3 flex items-center justify-between">
									<DialogTitle as="h3" class="text-xl text-black flex items-center font-medium leading-6">
										<img src="~assets/icons/nausea.svg" class="w-8 h-8 mr-2" />
										{{ 'Find or Create patient' }}
									</DialogTitle>

									<button @click="adjustVisibility">
										<XMarkIcon class="w-5 h-5" />
									</button>
								</div>

								<FormKit type="form" @submit="handleSearchPatient" :actions="false" #default="{}">
									<div class="mt-2 space-y-3 px-5 py-5">
										<div class="w-full grid grid-cols-2 gap-2">
											<FormKit type="text" label="First name" validation="required"
												v-model="details.firstName" />

											<FormKit type="text" label="Last name" validation="required"
												v-model="details.lastName" />
										</div>

										<div class="items-center">
											<FormKit v-model="details.gender" type="radio" label="Gender" :options="sex"
												validation="required" />
										</div>
									</div>

									<div class="mt-3 justify-end flex items-center space-x-3 px-3 py-2 border-t">
										<CoreActionButton type="submit" :click="() => { }" color="success"
											:icon="ArrowRightCircleIcon" text="Find" :loading="loading" />
									</div>
								</FormKit>
							</DialogPanel>
						</TransitionChild>
					</div>
				</div>
			</Dialog>
		</TransitionRoot>
	</div>
</template>
