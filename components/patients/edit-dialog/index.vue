<template>
  <div>
    <div>
      <CoreActionButton text="Edit" color="primary" :icon="editIcon" :click="init" />
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleDialog" class="relative z-10">
        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
          leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95">
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
                <div class="border-b px-3 py-3 flex items-center justify-between">
                  <DialogTitle as="h3" class="text-xl text-black flex items-center font-medium leading-6">
                    <img src="@/assets/icons/nausea.svg" class="w-8 h-8 mr-2" alt="nausea-icon" />
                    Edit patient
                  </DialogTitle>

                  <button @click="handleDialog">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit type="form" id="editPatientForm" submit-label="Update" @submit="submitForm" :actions="false"
                  #default="">
                  <div class="mt-2 space-y-3 px-5 py-5">
                    <div class="w-full grid grid-cols-2 gap-2">
                      <FormKit type="text" label="Patient ID" v-model="details.uuid" />

                      <FormKit type="text" label="National Patient ID" v-model="details.npid" />
                    </div>

                    <div class="w-full grid grid-cols-3 gap-2">
                      <FormKit type="text" label="First name" validation="required|matches:/^[a-zA-Z']+$/"
                        :validation-messages="{
                          matches:
                            'First name must contain only letters, no spaces or special characters',
                        }" v-model="details.first_name" />

                      <FormKit type="text" label="Middle name" v-model="details.middle_name" />

                      <FormKit type="text" label="Last name" validation="required|matches:/^[a-zA-Z']+$/"
                        :validation-messages="{
                          matches:
                            'Last name must contain only letters, no spaces or special characters',
                        }" v-model="details.last_name" />
                    </div>

                    <div class="grid grid-cols-2 gap-2">
                      <div class="w-full flex flex-col space-y-2">
                        <label class="font-medium">Date Of Birth</label>
                        <div class="w-full">
                          <datepicker placeholder="Click to select a date" v-model="details.date_of_birth"
                            :range="false" :max-date="new Date()" @cleared="onCleared" :ignore-time-validation="true"
                            :teleport="true" input-class-name="datepicker" format="dd/MM/yyyy" required />
                        </div>
                      </div>
                      <PatientsAddDialogAgeInput v-model:dateOfBirth="details.date_of_birth"
                        @update:dateOfBirth="onEstimateDate" />
                    </div>

                    <div class="w-full grid grid-cols-2 gap-2">
                      <FormKit v-model="sex" type="radio" label="Sex" :options="['Male', 'Female']"
                        validation="required" />
                      <FormKit type="text" label="Physical Address" validation="required"
                        v-model="details.physical_address" />
                    </div>
                  </div>

                  <div class="mt-3 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                    <CoreOutlinedButton type="button" :click="invalidateInputs" text="Clear form" />
                    <CoreActionButton type="submit" :click="() => { }" color="success" :icon="saveIcon"
                      text="Save changes" :loading="loading" />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  PencilSquareIcon,
  ArrowDownTrayIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";

import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response, Patient } from "@/types";
import type { PropType } from "vue";


const props = defineProps({
  data: {
    required: true,
    type: Object as PropType<{ client_id: string; }>
  }
})

const emit = defineEmits<{
  (event: "update", value: boolean): void;
}>();

const details = ref<Patient>({
  uuid: "",
  npid: "",
  first_name: "",
  middle_name: "",
  last_name: "",
  date_of_birth: "",
  gender: "",
  physical_address: "",
  value: undefined,
  id: 0,
  client_id: 0,
  sex: "",
  phone: "",
  birth_date_estimated: false
});
const open = ref<boolean>(false);
const saveIcon = ArrowDownTrayIcon;
const editIcon = PencilSquareIcon;
const loading = ref<boolean>(false);
const cookie = useCookie<string>("token");
const updatingFromDob = ref<boolean>(false);
const updatingFromAge = ref<boolean>(false);
const estimatedDate = ref<boolean>(false);
const dateOfBirth = ref<string>("");
const sex = ref<string>("");
const ageInputRef = ref<{ reset: () => void } | null>(null);

const handleDialog = (): void => {
  open.value = !open.value;
};

const onEstimateDate = (date: string): void => {
  details.value.birth_date_estimated = true;
  estimatedDate.value = true;
};

const invalidateInputs = (): void => {
  dateOfBirth.value = "";
  estimatedDate.value = false;
  ageInputRef.value?.reset();
};

const onCleared = (): void => {
  ageInputRef.value?.reset();
};

const init = async (): Promise<void> => {
  const request: Request = {
    route: `${endpoints.clients}/${props.data.client_id}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error }: Response = await fetchRequest(request);
  if (data.value) {
    details.value = data.value;
    sex.value = data.value.gender == "M" ? "Male" : "Female";
    handleDialog();
  }
  if (error.value) {
    console.error(error.value);
    useNuxtApp().$toast.error(ERROR_MESSAGE);
  }
};

const submitForm = async (): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: `${endpoints.clients}/${props.data.client_id}`,
    method: "PUT",
    token: `${cookie.value}`,
    body: {
      client: {
        uuid: details.value.uuid,
      },
      person: {
        first_name: details.value.first_name,
        middle_name: details.value.middle_name,
        last_name: details.value.last_name,
        sex: sex.value.charAt(0),
        date_of_birth: details.value.date_of_birth,
        birth_date_estimated: estimatedDate.value,
      },
      client_identifiers: [
        {
          type: "physical_address",
          value: details.value.physical_address,
        },
        {
          type: "phone",
          value: details.value.phone,
        },
        {
          type: "npid",
          value: details.value.npid,
        },
      ],
    },
  };
  const { data: responseData, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;
  if (responseData.value) {
    loading.value = false;
    Object.assign(details, {});
    useNuxtApp().$toast.success(`Patient updated successfully!`);
    handleDialog();
    emit("update", true);
  }
  if (error.value) {
    loading.value = false;
    console.error(error.value);
    useNuxtApp().$toast.error(ERROR_MESSAGE);
  }
};

watch(
  () => details.value.date_of_birth,
  (newValue: string | undefined): void => {
    if (!updatingFromAge.value && newValue) {
      updatingFromDob.value = true;
      details.value.age = Number(calculateAge(newValue));
      updatingFromDob.value = false;
    }
  }
);

watch(
  () => details.value.age,
  (newAge, oldAge) => {
    if (newAge !== oldAge && newAge != null && !updatingFromDob.value && !details.value.date_of_birth) {
      updatingFromAge.value = true;
      details.value.date_of_birth = moment().subtract(newAge, "years").format("YYYY-MM-DD");
      updatingFromAge.value = false;
      estimatedDate.value = true;
    }
  }
);
</script>

<style></style>