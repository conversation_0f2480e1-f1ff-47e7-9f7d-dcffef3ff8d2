<template>
  <div>
    <div>
      <CoreActionButton text="View" color="success" :icon="viewIcon" :click="init"/>
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="adjustVisibility" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >

                <div class="border-b px-3 py-3 flex items-center justify-between">

                  <DialogTitle
                    as="h3"
                    class="text-xl text-black flex items-center font-medium leading-6"
                  >
                    <img src="~assets/icons/nausea.svg" class="w-8 h-8 mr-2"/>
                    View patient
                  </DialogTitle>

                  <button @click="adjustVisibility">
                    <XMarkIcon class="w-5 h-5"/>
                  </button>

                  </div>

                  <div v-show="loading" class="flex items-center justify-center mx-auto my-20">
                    <CoreLoader :loading="loading"/>
                  </div>

                  <div v-show="!loading" class="mt-2 space-y-3 px-5 py-5">

                    <div class="w-full flex flex-col space-y-1">
                      <label class="font-semibold text-lg">Name</label>
                      <p class="underline">{{ `${details.first_name} ${details.last_name}` }}</p>
                    </div>

                    <div class="w-full flex flex-col space-y-1">
                      <label class="font-semibold text-lg">National Patient ID</label>
                      <p class="underline">{{ details.npid }}</p>
                    </div>

                    <div class="w-full flex flex-col space-y-1">
                      <label class="font-semibold text-lg">Date Of Birth</label>
                      <p class="underline">{{ details.date_of_birth }}</p>
                    </div>

                    <div class="w-full flex flex-col space-y-1">
                      <label class="font-semibold text-lg">Sex</label>
                      <p class="underline">{{ details.sex }}</p>
                    </div>

                    <div class="w-full flex flex-col space-y-1">
                      <label class="font-semibold text-lg">Physical Address</label>
                      <p class="underline">{{ details.physical_address }}</p>
                    </div>

                  </div>

              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">

import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue'

import {
  PlusIcon,
  XMarkIcon,
  ArrowTopRightOnSquareIcon,
  UserIcon,
  ArrowDownTrayIcon,
  ArrowUturnLeftIcon
} from '@heroicons/vue/24/solid/index.js'
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import type { Patient, Request } from '@/types';

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
    UserIcon
  },
  props: {
    data: {
      required: true,
      type: Object
    }
  },
  /**
   * @returns model values
   */
  data(){

    return{
      details: new Object as Patient,
      open: false as boolean,
      addIcon: PlusIcon as Object,
      viewIcon: ArrowTopRightOnSquareIcon as Object,
      saveIcon: ArrowDownTrayIcon as Object,
      clearIcon: ArrowUturnLeftIcon as Object,
      cookie: useCookie('token'),
      loading: false as boolean
    }
  },
  methods:{
    /**
     * @method init
     * @returns Promise @type void
     */
    async init () : Promise<void> {

      this.adjustVisibility();

      this.loading = true;

      const request: Request = {
        route: `${endpoints.clients}/${this.data.client_id}`,
        method: 'GET',
        token: `${this.cookie}`
      };

      const { data, pending, error } = await fetchRequest(request);

      if(data.value){
        this.details = data.value;
        this.loading = false;
      }

      if(error.value){
        console.log(error.value);
        useNuxtApp().$toast.error(ERROR_MESSAGE)
      }
    },
    /**
     * @method adjustVisibility visibility of the dialog
     * @returns @type void
     */
    adjustVisibility() : void {
      this.open = !this.open
    },

  }
}
</script>

<style>

</style>
