<template>
    <div>

        <CoreActionButton :click="(() => { handleClick() })" color="primary" text="View" :icon="viewIcon" />

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-4xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        <img src="~assets/icons/ambulance.svg" class="w-8 h-8 mr-2" />
                                        View Stock Issue
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <div class="space-y-3 px-5 py-5">

                                    <div class="rounded border">
                                        <div class=" bg-gray-50 border-b px-2 py-2 font-semibold rounded-t text-lg">
                                            Details
                                        </div>
                                        <div class="w-full px-2 py-2 flex items-center space-x-2">
                                            <label class="font-medium">Destination:</label>
                                            <p>{{ data.movement_to }}</p>
                                        </div>

                                        <div class="w-full px-2 py-2 flex items-center space-x-2">
                                            <label class="font-medium">Movement Date:</label>
                                            <p>{{ data.movement_date }}</p>
                                        </div>
                                    </div>

                                    <table class="w-full">
                                        <thead>
                                            <tr class="border bg-gray-50">
                                                <th class="px-2 py-2 text-left border-r">Stock Item</th>
                                                <th class="px-2 py-2 text-left border-r">Quantity Issued</th>
                                                <th class="px-2 py-2 text-left border-r">Batch</th>
                                                <th class="px-2 py-2 text-left border-r">Lot</th>
                                                <th class="px-2 py-2 text-left border-r">Expiry Date</th>
                                                <th class="px-2 py-2 text-left border-r">Transaction Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="border" v-for="transaction, index in data.stock_transactions">
                                                <td class="px-2 py-2 text-left border-r">{{ transaction.name }}</td>
                                                <td class="px-2 py-2 text-left border-r">{{ transaction.transacted_quantity
                                                }}</td>
                                                <td class="px-2 py-2 text-left border-r">{{ transaction.batch }}</td>
                                                <td class="px-2 py-2 text-left border-r">{{ transaction.lot }}</td>
                                                <td class="px-2 py-2 text-left border-r">{{
                                                    moment(transaction.expiry_date).format(DATE_FORMAT) }}</td>
                                                <td class="px-2 py-2 text-left border-r">{{
                                                    moment(transaction.transaction_date).format(DATE_FORMAT) }}</td>
                                            </tr>
                                        </tbody>
                                    </table>

                                </div>

                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

import { XMarkIcon, ArrowTopRightOnSquareIcon, PencilSquareIcon } from '@heroicons/vue/24/solid/index.js'
import moment from 'moment'

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon
    },
    data() {

        return {
            moment: moment,
            viewIcon: ArrowTopRightOnSquareIcon,
            show: false as boolean,
            editIcon: PencilSquareIcon,
        }
    },
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    methods: {
        handleClick(): void {
            this.show = !this.show
        }
    }
}
</script>

<style>
</style>
