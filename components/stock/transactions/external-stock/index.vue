<template>
    <div>

        <CoreActionButton :click="handleClick" color="success" text="Receive" :icon="transferIcon" />

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        <img src="@/assets/icons/ambulance.svg" class="w-8 h-8 mr-2" alt="ambulance-svg"/>
                                        Checkout Stock Transfer
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" submit-label="Update" @submit="submitForm" :actions="false"
                                    #default="{ value }" id="submitForm">

                                    <div class="py-5 px-5">

                                        <div class="w-full flex items-center space-x-2">
                                            <p class="w-72 font-medium">From: </p>
                                            <span
                                                class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                    data.receiving_from }}</span>
                                        </div>

                                        <div class="w-full flex items-center space-x-2 mt-2">
                                            <p class="w-72 font-medium">Reason for transfer: </p>
                                            <span
                                                class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">
                                                {{ data.reason }}
                                            </span>
                                        </div>

                                        <div class="bg-gray-50 px-2 py-2 border rounded-t mt-4">
                                            <h3 class="font-semibold">Stock In Items</h3>
                                        </div>

                                        <table class="w-full">
                                            <thead clas="w-full border-l border-r">
                                                <tr class="border-b border-r border-l rounded">
                                                    <th class="px-2 py-2 text-left border-r">
                                                        Item
                                                    </th>
                                                    <th class="px-2 py-2 text-left">
                                                        Quantity
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="requisition, index in filteredRequisitions"
                                                    :key="index"
                                                    class="border-b border-t border-r border-l rounded">
                                                    <td class="px-2 py-2 border-r">{{ requisition.stock_item_name }}</td>
                                                    <td class="px-2 py-2 border-r">{{ requisition.quantity }} </td>
                                                </tr>
                                            </tbody>
                                        </table>

                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreActionButton v-if="filteredRequisitions.length > 0" :loading="loading" type="submit" :click="(() => { })"
                                            color="success" :icon="transferIcon" text="Continue" />
                                    </div>

                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import { XMarkIcon, ArrowTopRightOnSquareIcon, PencilSquareIcon, ArrowDownTrayIcon } from '@heroicons/vue/24/solid/index.js'
import type { RequisitionItem, Response } from '@/types'
import StockModule from '@/repository/modules/stock'

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon
    },
    data() {

        return {
            transferIcon: ArrowTopRightOnSquareIcon,
            saveIcon: ArrowDownTrayIcon,
            show: false as boolean,
            loading: false as boolean,
            editIcon: PencilSquareIcon,
            cookie: useCookie('token')
        }
    },
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    computed: {
        filteredRequisitions() {
            this.data.requisitions.map((requisition: RequisitionItem, index: number) => {
                if(requisition.stock_item.name == '-- select item --'){
                    this.data.requisitions.splice(index, 1)
                }
            })
            return this.data.requisitions.map((requisition: RequisitionItem) => ({
                stock_item_name: requisition.stock_item.name,
                stock_item_id: requisition.stock_item.id,
                quantity: requisition.quantity_requested,
                lot: requisition.lot_number,
                batch: requisition.batch_number,
                expiry_date: requisition.expiry_date
            }))
        }
    },
    methods: {
        async submitForm(): Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const params = { receiving_from: this.data.receiving_from, stock_status_reason: this.data.reason, stock_items: this.filteredRequisitions };
            const { data, error, pending }: Response = await stockModule.receiveExternalStock(`${this.cookie}`, params);
            this.loading = pending;
            if (data.value) {
                this.loading = false;
                useNuxtApp().$toast.success('Stock received successfully');
                this.handleClick();
                this.$router.push('/stock-management/transactions');
            }
            if (error.value) {
                this.loading = false;
                console.error(error.value)
                this.$toast.error(ERROR_MESSAGE);
                this.handleClick();
            }
        },
        handleClick(): void {
            this.show = !this.show
        }
    }
}
</script>

<style>
</style>
