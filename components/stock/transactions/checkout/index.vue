<template>
    <div>
        <CoreActionButton :click="handleClick" color="success" text="Transfer" :icon="transferIcon" :disabled="disableCheckout"/>
        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        <img src="~assets/icons/ambulance.svg" class="w-8 h-8 mr-2" />
                                        Checkout Stock Transfer
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" submit-label="Update" @submit="submitForm" :actions="false"
                                    #default="{ value }" id="submitForm">

                                    <div class="py-5 px-5">

                                        <div class="w-full flex items-center space-x-2">
                                            <p class="w-72 font-medium">To: </p>
                                            <span
                                                class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                    data.sendingTo }}</span>
                                        </div>

                                        <div class="w-full flex items-center space-x-2 mt-2">
                                            <p class="w-72 font-medium">Reason for transfer: </p>
                                            <span
                                                class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">
                                                {{ data.reason }}
                                            </span>
                                        </div>

                                        <div class="bg-blue-50 border border-blue-200 rounded px-3 py-2 mt-4">
                                            <div class="flex items-center">
                                                <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                                </svg>
                                                <p class="text-blue-700 text-sm">
                                                    Stock quantities will be validated before processing to ensure availability.
                                                </p>
                                            </div>
                                        </div>

                                        <div class="bg-gray-50 px-2 py-2 border rounded-t mt-4">
                                            <h3 class="font-semibold">Stock Out Items</h3>
                                        </div>

                                        <table class="w-full">
                                            <thead clas="w-full border-l border-r">
                                                <tr class="border-b border-r border-l rounded">
                                                    <th class="px-2 py-2 text-left border-r">
                                                        Item
                                                    </th>
                                                    <th class="px-2 py-2 text-left border-r">
                                                        Quantity
                                                    </th>
                                                    <th class="px-2 py-2 text-left border-r">
                                                        Batch
                                                    </th>
                                                    <th class="px-2 py-2 text-left">
                                                        Lot
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="requisition, index in filteredRequisitions"
                                                    class="border-b border-t border-r border-l rounded">
                                                    <td class="px-2 py-2 border-r">{{ requisition.stock_item_name }}</td>
                                                    <td class="px-2 py-2 border-r">{{ requisition.quantity }}</td>
                                                    <td class="px-2 py-2 border-r">{{ requisition.batch }}</td>
                                                    <td class="px-2 py-2">{{ requisition.lot }}</td>
                                                </tr>
                                            </tbody>
                                        </table>

                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreActionButton :loading="loading" type="submit" :click="(() => { })"
                                            color="success" :icon="transferIcon" text="Continue" />
                                    </div>

                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import { XMarkIcon, ArrowTopRightOnSquareIcon, PencilSquareIcon, ArrowDownTrayIcon } from '@heroicons/vue/24/solid/index.js'
import type { RequisitionItem, Response } from '@/types'
import StockModule from '@/repository/modules/stock'

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon
    },
    data() {

        return {
            transferIcon: ArrowTopRightOnSquareIcon,
            saveIcon: ArrowDownTrayIcon,
            show: false as boolean,
            loading: false as boolean,
            editIcon: PencilSquareIcon,
            cookie: useCookie('token')
        }
    },
    props: {
        data: {
            type: Object,
            required: true
        },
        disableCheckout: {
            type: Boolean,
            required: true
        }
    },
    computed: {
        filteredRequisitions() {
            return this.data.requisitions.map((requisition: RequisitionItem) => ({
                stock_item_name: requisition.stock_item.name,
                stock_item_id: requisition.stock_item.id,
                quantity: requisition.quantity_requested,
                lot: requisition.lot_number,
                batch: requisition.batch_number
            }))
        }
    },
    methods: {
        async submitForm(): Promise<void> {
            // Validate stock quantities before submission
            const validationPromises = this.filteredRequisitions.map(async (item: any) => {
                const stockModule = new StockModule();
                const params = {
                    stock_item_id: item.stock_item_id,
                    quantity: item.quantity,
                    batch: item.batch,
                    lot: item.lot
                };

                try {
                    const { data, error } = await stockModule.checkStockQuantity(`${this.cookie}`, params);
                    if (data.value && !data.value.deduction_allowed) {
                        throw new Error(data.value.message || `Insufficient stock for ${item.stock_item_name}`);
                    }
                    if (error.value) {
                        throw new Error(`Could not verify stock for ${item.stock_item_name}`);
                    }
                    return true;
                } catch (err) {
                    throw err;
                }
            });

            try {
                this.loading = true;
                // Wait for all validations to complete
                await Promise.all(validationPromises);

                // If all validations pass, proceed with the transaction
                const stockModule = new StockModule();
                const params = {
                    sending_to: this.data.sendingTo,
                    stock_status_reason: this.data.reason,
                    stock_items: this.filteredRequisitions
                };

                const { data, error, pending }: Response = await stockModule.stockOutTransaction(`${this.cookie}`, params);
                this.loading = pending;

                if (data.value) {
                    this.loading = false;
                    useNuxtApp().$toast.success(data.value.message);
                    this.handleClick();
                    this.$router.push('/stock-management/issue');
                }
                if (error.value) {
                    this.loading = false;
                    console.error(error.value);
                    useNuxtApp().$toast.error('Failed to process stock transaction');
                    this.handleClick();
                }
            } catch (validationError) {
                this.loading = false;
                console.error('Stock validation failed:', validationError);
                useNuxtApp().$toast.error(validationError.message || 'Stock validation failed. Please check quantities.');
            }
        },
        handleClick(): void {
            this.show = !this.show
        }
    }
}
</script>

<style>
</style>
