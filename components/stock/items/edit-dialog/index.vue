<template>
    <div>
        <CoreActionButton :click="(() => { init() })" text="Edit" color="success" :icon="editIcon" />

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0"
                    enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        <img src="@/assets/icons/stock_out.svg" class="w-8 h-8 mr-2" alt="stock-item-svg"/>
                                        Edit Stock Item
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" id="editForm" submit-label="Update" @submit="submitForm"
                                    :actions="false" #default="{ value }">

                                    <div class="mt-2 space-y-3 px-5">
                                        <FormKit type="text" label="Name" validation="required" v-model="name" />
                                        <FormKit type="text" label="Product code" v-model="productCode" />
                                        <FormKit type="textarea" label="Description" validation="required"
                                            v-model="description" />
                                        <div class="flex items-center">
                                            <CoreMultiselect mode="single" v-model:items-selected="selectedStockUnit"
                                                label="Measurement Unit" :items="stockUnits" />
                                        </div>
                                        <FormKit type="number" label="Quantity of measurement" validation="required"
                                            v-model="measurementQuantity" />
                                        <div class="bg-white">
                                            <CoreMultiselect mode="single" label="Category" :items="categories"
                                                v-model:items-selected="selectedCategory" />
                                        </div>

                                        <div>
                                            <label class="font-medium mb-2.5 text-lg">Location</label>
                                            <CoreDropdown :items="stockLocations" v-model="selectedLocation" />
                                        </div>

                                        <FormKit type="text" label="Strength" v-model="strength" />
                                        <FormKit type="number" label="Minimum order level" validation="required"
                                            v-model="minimumOrderLevel" />
                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreOutlinedButton type="button" :click="(() => { clearForm() })"
                                            text="Clear form" />
                                        <CoreActionButton :loading="loading" type="submit" :click="(() => { })"
                                            color="success" :icon="saveIcon" text="Save changes" />
                                    </div>
                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script setup lang="ts">
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import { XMarkIcon, PencilSquareIcon, ArrowDownTrayIcon } from '@heroicons/vue/24/solid/index.js'
import StockModule from '@/repository/modules/stock'
import type { Response } from '@/types';
import { reset } from '@formkit/core';

interface StockItemData {
    id: number
    name: string
    product_code: string
    description: string
    stock_location_id: number
    stock_category_id: number
    measurement_unit: number
    strength: string
    minimum_order_level: number
}

const props = defineProps<{
    data: StockItemData
}>();

const emit = defineEmits(['update']);
const show = ref(false);
const loading = ref(false);
const name = ref('');
const productCode = ref('');
const description = ref('');
const cookie = useCookie('token');
const rawCategories = ref<Array<{ id: number, name: string }>>([]);
const categories = ref<Array<string>>([]);
const selectedCategory = ref('');
const measurementQuantity = ref(0);
const rawStockUnits = ref<Array<{ id: number, name: string }>>([]);
const stockUnits = ref<Array<string>>([]);
const selectedStockUnit = ref('');
const strength = ref('');
const minimumOrderLevel = ref(0);
const stockLocations = ref<Array<{ id: number, name: string }>>([]);
const selectedLocation = ref<{ name: string, id: number }>({ name: '-- selected location --', id: 0 });

const editIcon = PencilSquareIcon
const saveIcon = ArrowDownTrayIcon

const getStockLocation = async (): Promise<void> => {
    const stockModule = new StockModule()
    const { data, error }: Response = await stockModule.getStockLocation(`${cookie.value}`)
    if (data.value) {
        stockLocations.value = data.value
        selectedLocation.value = data.value.filter(
            (location: { id: number }) => location.id == props.data.stock_location_id
        )[0]
    }
    if (error.value) {
        console.error(error.value)
    }
}

const getStockCategory = async (): Promise<void> => {
    const stockModule = new StockModule()
    const { data, error }: Response = await stockModule.getStockCategory(`${cookie.value}`)
    if (data.value) {
        rawCategories.value = data.value
        categories.value = data.value.map((category: { name: string }) => category.name)
        selectedCategory.value = rawCategories.value.filter(
            (c: { id: number }) => c.id == props.data.stock_category_id
        )[0].name
    }
    if (error.value) {
        console.error(error.value)
    }
}

const getStockUnit = async (): Promise<void> => {
    const stockModule = new StockModule()
    const { data, error }: Response = await stockModule.getStockUnit(`${cookie.value}`)
    if (data.value) {
        rawStockUnits.value = data.value
        stockUnits.value = data.value.map((unit: { name: string }) => unit.name)
        selectedStockUnit.value = rawStockUnits.value.filter(
            (c: { id: number }) => c.id == props.data.measurement_unit
        )[0].name
    }
    if (error.value) {
        console.error(error.value)
    }
}

const init = async (): Promise<void> => {
    await getStockCategory()
    await getStockUnit()
    await getStockLocation()
    handleClick()
    name.value = props.data.name
    description.value = props.data.description
    strength.value = props.data.strength
    minimumOrderLevel.value = props.data.minimum_order_level
}

const submitForm = async (): Promise<void> => {
    loading.value = true
    const stockModule = new StockModule()
    const params = {
        id: props.data.id,
        name: name.value,
        description: description.value,
        stock_location_id: selectedLocation.value.id,
        stock_category_id: rawCategories.value.filter(
            (category: { name: string }) => category.name === selectedCategory.value
        )[0].id,
        measurement_unit: rawStockUnits.value.filter(
            (unit: { name: string }) => unit.name === selectedStockUnit.value
        )[0].id,
        quantity_unit: measurementQuantity.value,
        strength: strength.value,
        minimum_order_level: minimumOrderLevel.value
    }

    const { data, error, pending }: Response = await stockModule.updateStockItem(`${cookie.value}`, params)
    loading.value = pending

    if (data.value) {
        handleClick()
        useNuxtApp().$toast.success(`${name.value} stock item updated successfully!`)
        loading.value = false
        description.value = ''
        name.value = ''
        emit('update', true)
    }

    if (error.value) {
        handleClick()
        console.error(error.value)
        useNuxtApp().$toast.error(ERROR_MESSAGE)
        loading.value = false
    }
}

const clearForm = (): void => {
    reset('editForm')
}

const handleClick = (): void => {
    show.value = !show.value
}
</script>

<style></style>
