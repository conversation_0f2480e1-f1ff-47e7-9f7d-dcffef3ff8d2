<template>
    <div>
        <div>
            <CoreImportingDialog ref="importDialog" />
            <CoreExportButton @click="handleDialog" text="Import OpenMLMIS Products" />
        </div>
        <TransitionRoot appear :show="open" as="template">
            <Dialog as="div" @close="handleDialog" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0"
                    enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-2xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">

                                    <DialogTitle as="h3"
                                        class="text-xl text-black flex items-center font-medium leading-6">
                                        <img src="@/assets/icons/excel.png" class="w-6 h-6 mr-2" alt="excel-icon" />
                                        Import OpenMLMIS Products
                                    </DialogTitle>

                                    <button @click="handleDialog">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" submit-label="Update" :actions="false" @submit="submitForm()"
                                    #default="{ value }" id="submitForm">

                                    <div class="w-full px-5 py-5">
                                        <div class="">
                                            <div @dragover.prevent="((e: any) => dragover(e))"
                                                @dragleave="((e: any) => dragleave(e))"
                                                @drop.prevent="((e: any) => drop(e))" :class="[
                                                    'border-2 border-dashed p-8 text-center cursor-pointer',
                                                    isDragging ? 'border-sky-500 bg-sky-50' : 'border-gray-300'
                                                ]">
                                                <input type="file" ref="fileInput"
                                                    @change="((e: any) => handleFileInput(e))" class="hidden"
                                                    :multiple="false" />
                                                <p class="mb-2">{{ isDragging ? 'Drop files here' : 'Drag and drop csv file here' }}</p>
                                                <button @click="$refs?.fileInput?.click()" type="button"
                                                    class="bg-sky-500 hover:bg-sky-600 rounded text-white font-normal py-1 px-4 border border-sky-500 hover:border-transparent">
                                                    Select a CSV file
                                                </button>
                                            </div>
                                            <div v-if="selectedFiles.length > 0" class="mt-4">
                                                <h3 class="font-semibold mb-2">Selected File:</h3>
                                                <ul class="list-disc pl-5 text-sky-500">
                                                    <li v-for="file in selectedFiles" :key="file.name">
                                                        {{ file.name }} ({{ formatFileSize(file.size) }})
                                                    </li>
                                                </ul>

                                            </div>
                                            <div v-if="headerError"
                                                class="mt-4 flex items-start text-red-500 bg-red-50 rounded p-4">
                                                <InformationCircleIcon
                                                    class="w-6 h-6 flex-shrink-0 text-red-500 mr-2" />
                                                <p class="text-base">{{ headerError }}</p>
                                            </div>

                                        </div>
                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreOutlinedButton type="button" :click="(() => { clearForm() })"
                                            text="Clear form" />
                                        <CoreActionButton :disabled="headerError !== null" :loading="loading"
                                            type="submit" :click="(() => { })" color="success" :icon="ArrowDownTrayIcon"
                                            text="Save changes" />
                                    </div>

                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script setup lang="ts">
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import { ArrowDownTrayIcon, XMarkIcon } from '@heroicons/vue/24/solid/index.js'
import StockModule from '@/repository/modules/stock';
import { InformationCircleIcon } from '@heroicons/vue/20/solid';

const emit = defineEmits(["update"]);
const open = ref<boolean>(false);
const loading = ref<boolean>(false);
const isDragging = ref<boolean>(false);
const selectedFiles = ref<File[]>([]);
const stockModule = new StockModule();
const { $toast } = useNuxtApp();
const importDialog = ref<InstanceType<any> | null>(null);
const headerError = ref<string | null>(null);
const cookie = useCookie("token");
const expectedHeaders: string[] = [
    "productCode",
    "name",
    "description",
    "categoryDescription",
    "packRoundingThreshold",
    "packSize",
    "roundToZero",
    "dispensable",
    "program",
    "dosesPerPatient",
    "active",
    "category",
    "fullSupply",
    "displayOrder",
    "pricePerPack"
];

const dragover = (e: any): void => {
    isDragging.value = true;
};

const dragleave = (e: any): void => {
    isDragging.value = false;
};

const drop = (e: { dataTransfer: { files: any; }; }): void => {
    isDragging.value = false;
    const files = e.dataTransfer.files;
    addFiles(files);
};

const handleFileInput = (e: Event): void => {
    const target = e.target as HTMLInputElement;
    if (target.files) {
        const files = target.files;
        addFiles(files);
    }
};

const addFiles = (files: FileList) => {
    if (files.length > 0) {
        const file = files[0];
        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        if (fileExtension === 'csv') {
            selectedFiles.value = [file];
            validateCSVHeaders(file);
        } else {
            $toast.error('Please select a CSV file');
        }
    }
};

const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const validateCSVHeaders = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e: ProgressEvent<FileReader>) => {
        const content = e.target?.result as string;
        const headers = content.split('\n')[0].split(',').map(header => header.trim());
        const missingHeaders = expectedHeaders.filter(header => !headers.includes(header));
        const unexpectedHeaders = headers.filter(header => !expectedHeaders.includes(header));
        if (missingHeaders.length > 0 || unexpectedHeaders.length > 0) {
            let errorMessage = 'CSV headers do not match the expected format.\n';
            if (missingHeaders.length > 0) {
                errorMessage += `Missing headers: ${missingHeaders.join(', ')}\n`;
            }
            errorMessage += 'Please check your file and ensure it matches the required format.';
            headerError.value = errorMessage;
        } else {
            headerError.value = null;
        }
    };
    reader.readAsText(file);
};

const clearSelectedFiles = (): void => {
    selectedFiles.value = [];
    headerError.value = null;
}

const clearForm = (): void => {
    clearSelectedFiles();
}

const handleDialog = (): void => {
    open.value = !open.value;
}

const submitForm = async (): Promise<void> => {
    loading.value = true;
    importDialog.value?.openModal();
    const formData = new FormData();
    formData.append('file', selectedFiles.value[0]);
    const { data, error, pending } = await stockModule.importStockItems(String(cookie.value), formData);
    loading.value = pending;
    if (data.value) {
        loading.value = false;
        importDialog.value?.closeModal();
        $toast.success("Products imported as stock items successfully");
        emit('update', true)
    }
    if (error.value) {
        console.error(error.value)
        loading.value = false;
        importDialog.value?.closeModal();
        $toast.error(ERROR_MESSAGE);
    }
    handleDialog();
}

</script>

<style></style>
