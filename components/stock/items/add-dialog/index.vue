<template>
    <div>
        <div>
            <CoreActionButton text="Create stock item" color="primary" :icon="PlusIcon" :click="init" />
        </div>
        <TransitionRoot appear :show="open" as="template">
            <Dialog as="div" @close="handleClick" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0"
                    enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        <img src="@/assets/icons/stock_out.svg" class="w-8 h-8 mr-2" alt="stock-item-svg"/>
                                        Create Stock Item
                                    </DialogTitle>
                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>
                                </div>

                                <FormKit ref="formkit" type="form" submit-label="Update" @submit="submitForm" :actions="false"
                                    #default="{ value }" id="submitForm">
                                    <div class="mt-2 space-y-3 px-5">
                                        <FormKit type="text" label="Name" validation="required" v-model="name" />
                                        <FormKit type="text" label="Product code"
                                            v-model="productCode" />
                                        <FormKit type="textarea" label="Description" validation="required"
                                            v-model="description" />
                                        <div class="flex items-center">
                                            <CoreMultiselect mode="single" v-model:items-selected="selectedStockUnit"
                                                label="Measurement Unit" :items="stockUnits" />
                                            <div class="flex items-center space-x-2 pt-8 ml-3">
                                                <CoreActionButton v-if="selectedStockUnit == ''" :icon="PlusIcon"
                                                    color="primary" text="Add"
                                                    :click="(() => { $router.push('/stock-management/metrics') })" />
                                                <CoreActionButton v-if="selectedStockUnit != ''"
                                                    :icon="PencilSquareIcon" color="success" text="Edit"
                                                    :click="(() => { $router.push('/stock-management/metrics') })" />
                                            </div>
                                        </div>
                                        <FormKit type="number" label="Quantity of measurement" validation="required"
                                            v-model="measurementQuantity" />
                                        <div class="bg-white">
                                            <CoreMultiselect mode="single" label="Category" :items="categories"
                                                v-model:items-selected="selectedCategory" />
                                        </div>
                                        <div>
                                            <label class="font-medium mb-2.5 text-lg">Location</label>
                                            <CoreDropdown :items="stockLocations" v-model="selectedLocation" />
                                        </div>
                                        <FormKit type="text" label="Strength" v-model="strength" />
                                        <FormKit type="number" label="Minimum order level" validation="required"
                                            v-model="minimumOrderLevel" />
                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreOutlinedButton type="button" :click="(() => { clearForm() })"
                                            text="Clear form" />
                                        <CoreActionButton :loading="loading" type="submit" :click="(() => { })"
                                            color="success" :icon="ArrowDownTrayIcon" text="Save changes" />
                                    </div>
                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script setup lang="ts">
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import {
    PlusIcon,
    XMarkIcon,
    PencilSquareIcon,
    ArrowDownTrayIcon
} from '@heroicons/vue/24/solid/index.js'
import StockModule from '@/repository/modules/stock'
import type { Response } from '@/types';
import { reset } from '@formkit/core';

const emit = defineEmits(['update']);
const { $toast } = useNuxtApp();
const open = ref<boolean>(false);
const loading = ref<boolean>(false);
const name = ref<string>('');
const productCode = ref<string>('');
const description = ref<string>('');
const cookie = useCookie('token');
const rawCategories = ref<Array<{ id: number, name: string }>>([]);
const categories = ref<Array<string>>([]);
const selectedCategory = ref<string>('');
const measurementQuantity = ref<number>(0);
const rawStockUnits = ref<Array<{ id: number, name: string }>>([]);
const stockUnits = ref<Array<string>>([]);
const stockLocations = ref<Array<{ id: number, name: string }>>([]);
const selectedLocation = ref<{ name: string, id: number }>({ name: '-- selected location --', id: 0 });
const selectedStockUnit = ref<string>('');
const strength = ref<string>('');
const minimumOrderLevel = ref<number>(0);

const getStockCategory = async (): Promise<void> => {
    const stockModule = new StockModule()
    const { data, error }: Response = await stockModule.getStockCategory(`${cookie.value}`)
    if (data.value) {
        rawCategories.value = data.value
        categories.value = data.value.map((category: { name: string }) => category.name)
    }
    if (error.value) {
        console.error(error.value)
    }
}

const getStockUnit = async (): Promise<void> => {
    const stockModule = new StockModule()
    const { data, error }: Response = await stockModule.getStockUnit(`${cookie.value}`)
    if (data.value) {
        rawStockUnits.value = data.value
        stockUnits.value = data.value.map((unit: { name: string }) => unit.name)
    }
    if (error.value) {
        console.error(error.value)
    }
}

const getStockLocation = async (): Promise<void> => {
    const stockModule = new StockModule()
    const { data, error }: Response = await stockModule.getStockLocation(`${cookie.value}`)
    if (data.value) {
        stockLocations.value = data.value
    }
    if (error.value) {
        console.error(error.value)
    }
}

const init = async (): Promise<void> => {
    await getStockCategory()
    await getStockUnit()
    await getStockLocation()
    handleClick()
}

const submitForm = async (): Promise<void> => {
    loading.value = true
    const stockModule = new StockModule()
    const params = {
        name: name.value,
        product_ccode: productCode.value,
        description: description.value,
        stock_location_id: selectedLocation.value.id,
        stock_category_id: rawCategories.value.filter(
            (category: { name: string }) => category.name === selectedCategory.value
        )[0].id,
        measurement_unit: rawStockUnits.value.filter(
            (unit: { name: string }) => unit.name === selectedStockUnit.value
        )[0].id,
        quantity_unit: measurementQuantity.value,
        strength: strength.value,
        minimum_order_level: minimumOrderLevel.value
    }

    const { pending, error, data }: Response = await stockModule.createStockItem(`${cookie.value}`, params)
    loading.value = pending

    if (data.value) {
        handleClick()
        $toast.success(`${name.value} stock item created successfully!`)
        loading.value = false
        description.value = ''
        name.value = ''
        measurementQuantity.value = 0
        emit('update', true)
    }

    if (error.value) {
        handleClick()
        console.error(error.value)
        $toast.error(ERROR_MESSAGE)
        loading.value = false
    }
}

const handleClick = (): void => {
    open.value = !open.value
}

const clearForm = (): void => {
    reset('submitForm')
}
</script>

<style></style>
