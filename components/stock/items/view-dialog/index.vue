<template>
    <div>

        <CoreActionButton :click="(() => { handleClick() })" color="primary" text="View"
            :icon="ArrowTopRightOnSquareIcon" />

        <TransitionRoot appear :show="open" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0"
                    enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        <img src="@/assets/icons/stock_out.svg" class="w-8 h-8 mr-2" />
                                        View Stock Item
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <div v-if="loading" class="flex flex-col space-y-2 items-center justify-center mx-auto my-20">
                                    <CoreLoader :loading="loading" />
                                    <p>Loading please wait <span class="animate-pulse">...</span></p>
                                </div>

                                <div v-else class="space-y-3 px-5 py-5">

                                    <div class="w-full flex flex-col space-y-1">
                                        <label class="font-semibold text-lg">Name</label>
                                        <p>{{ stockDetails?.name }}</p>
                                    </div>

                                    <div class="w-full flex flex-col space-y-1">
                                        <label class="font-semibold text-lg">Description</label>
                                        <p>{{ stockDetails?.description }}</p>
                                    </div>

                                    <div class="w-full flex flex-col space-y-1">
                                        <label class="font-semibold text-lg">OpenMLMIS Product Code</label>
                                        <p>{{ stockDetails?.product_code }}</p>
                                    </div>

                                    <div class="w-full flex flex-col space-y-1">
                                        <label class="font-semibold text-lg">Measurement Unit</label>
                                        <p>{{ stockDetails?.stock_unit }}</p>
                                    </div>

                                    <div class="w-full flex flex-col space-y-1">
                                        <label class="font-semibold text-lg">Category</label>
                                        <p>{{ stockDetails?.stock_category }}</p>
                                    </div>

                                    <div class="w-full flex flex-col space-y-1">
                                        <label class="font-semibold text-lg">Location</label>
                                        <p>{{ stockDetails?.stock_location }}</p>
                                    </div>

                                </div>

                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script setup lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

import { XMarkIcon, ArrowTopRightOnSquareIcon } from '@heroicons/vue/24/solid/index.js';
import StockModule from '@/repository/modules/stock';
import type { StockItem } from '@/types';

const props = defineProps({
    data: {
        type: Object,
        required: true
    }
});
const cookie = useCookie("token");
const loading = ref<boolean>(false);
const stockDetails = ref<StockItem>();
const stockModule = new StockModule();

const open = ref<boolean>(false);

const loadStockItem = async (): Promise<void> => {
    loading.value = true;
    const { data, error, pending } = await stockModule.readStockItem(String(cookie.value), { id: props.data.id });
    loading.value = pending;

    if (data.value) {
        stockDetails.value = data.value;
        loading.value = false;
    }

    if (error.value) {
        console.error(error.value);
        loading.value = false;
    }
}
const handleClick = (): void => {
    open.value = !open.value;
    if (open.value) loadStockItem();
}
</script>

<style></style>
