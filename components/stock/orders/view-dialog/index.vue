<template>
    <div>

        <CoreActionButton :click="(() => { openDialog() })" color="primary" text="View" :icon="viewIcon" />

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-4xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">

                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        <CorePrinter v-if="data.stock_order_status.toLowerCase() === 'approved'" id="print-container" :print-small-label="false" />
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <div class="py-5 px-5 space-y-3 print-container" id="print-container">
                                    <div class=" rounded-tr rounded-tl px-5 py-5 flex flex-col items-center">
                                        <img src="~assets/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
                                        <h3 class="mt-2 text-xl font-medium uppercase">Republic of Malawi</h3>
                                        <h3 class="mt-2 text-xl font-medium">Ministry of Health</h3>
                                        <h3 class="mt-2 text-2xl font-semibold">Requisition and Issue Voucher</h3>
                                    </div>
                                    <div
                                        class="flex bg-gray-50 border-l-4 border-l-100 rounded-r px-2 py-2 items-center space-x-2">
                                        <TicketIcon class="h-5 w-5" />
                                        <p>Voucher Number: <span class="text-lg text-sky-500 font-medium">B</span><strong>{{
                                            data.voucher_number }}</strong></p>
                                    </div>
                                    <div class="mt-3">
                                        <table class="w-full">
                                            <thead clas="w-full border-t border-l border-r">
                                                <tr class="border-b border-t border-r border-l rounded">
                                                    <th :colspan="2" class="text-left p-2">
                                                        Requisitions
                                                    </th>
                                                </tr>
                                                <tr class="border-b border-t border-r border-l rounded">
                                                    <th class="px-2 py-2 text-left border-r">
                                                        Stock Item
                                                    </th>
                                                    <th class="px-2 py-2 text-left">
                                                        Quantity Being Requested
                                                    </th>
                                                    <th class="px-2 py-2 text-left">
                                                        Quantity Isssued
                                                    </th>
                                                    <th class="px-2 py-2 text-left">
                                                        Quantity Collected
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="(requisition, index) in requisitions" :key="index"
                                                    class="border-b border-t border-r border-l rounded">
                                                    <td class="px-2 py-2 border-r">{{ requisition.item.name }}</td>
                                                    <td class="px-2 py-2 border-r">{{ requisition.quantity_requested }}</td>
                                                    <td class="px-2 py-2 border-r">{{ requisition.quantity_issued }}</td>
                                                    <td class="px-2 py-2 border-r">{{ requisition.quantity_collected }}</td>
                                                </tr>
                                            </tbody>
                                        </table>

                                        <div class="rounded border mt-5">
                                            <div
                                                class="flex items-center space-x-3 bg-gray-50 py-2 rounded-t px-2 border-b">
                                                <img src="~assets/icons/hematology_laboratory.svg" class="w-6 h-6" />
                                                <h3 class="text-lg font-semibold">Preparation of RIV</h3>
                                            </div>
                                            <div class="w-full grid grid-cols-2 gap-5 py-5 px-5">
                                                <div class="col-span-1 flex flex-col space-y-2">
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Prepared by: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                preparedBy }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Designation: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                preparedDesignation }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Signature: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                preparedSignature }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Date:</p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                preparedDate }}</span>
                                                    </div>
                                                </div>
                                                <div class="col-span-1 flex flex-col space-y-2">
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Certified by: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                certifiedBy }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Designation: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                certifiedDesignation }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Signature: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                certifiedSignature }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Date:</p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                certifiedDate }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="rounded border mt-5">
                                            <div
                                                class="flex items-center space-x-3 bg-gray-50 py-2 rounded-t px-2 border-b">
                                                <img src="~assets/icons/pharmacy_alt.svg" class="w-6 h-6" />
                                                <h3 class="text-lg font-semibold">Pharmacy</h3>
                                            </div>
                                            <div class="w-full grid grid-cols-2 gap-5 py-5 px-5">
                                                <div class="col-span-1 flex flex-col space-y-2">
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Issued by: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                issuerName }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Designation: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                issuerDesignation }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Signature: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                issuerSignature }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Date:</p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                issuedDate }}</span>
                                                    </div>
                                                </div>
                                                <div class="col-span-1 flex flex-col space-y-2">
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Approved by: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                approverName }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Designation: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                approverDesignation }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Signature: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                issuerSignature }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Date:</p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                approvedDate }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="rounded border mt-5">
                                            <div
                                                class="flex items-center space-x-3 bg-gray-50 py-2 rounded-t px-2 border-b">
                                                <img src="~assets/icons/hematology_laboratory.svg" class="w-6 h-6" />
                                                <h3 class="text-lg font-semibold">Finalisation of Order</h3>
                                            </div>
                                            <div class="w-full grid grid-cols-2 gap-5 py-5 px-5">
                                                <div class="col-span-1 flex flex-col space-y-2">
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Collected by: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                collectedBy }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Designation: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                collectedDesignation }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Signature: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                collectedSignature }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Date:</p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                collectedDate }}</span>
                                                    </div>
                                                </div>
                                                <div class="col-span-1 flex flex-col space-y-2">
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Verified by: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                verifiedBy }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Designation: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                verifiedDesignation }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Signature: </p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                verifiedSignature }}</span>
                                                    </div>
                                                    <div class="w-full flex items-center space-x-2">
                                                        <p class="w-72 font-medium">Date:</p>
                                                        <span
                                                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{
                                                                verifiedDate }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

import { XMarkIcon, ArrowTopRightOnSquareIcon, PencilSquareIcon, TicketIcon } from '@heroicons/vue/24/solid/index.js'
import { useFacilityStore } from '@/store/facility'
import StockModule from '@/repository/modules/stock'
import type { Response } from 'types'
import moment from 'moment'

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon,
        TicketIcon
    },
    data() {

        return {
            viewIcon: ArrowTopRightOnSquareIcon,
            show: false as boolean,
            editIcon: PencilSquareIcon,
            facility: useFacilityStore(),
            cookie: useCookie('token'),
            loading: false as boolean,
            requisitions: new Array<any>(),
            voidReason: '' as string,
            issuerName: '' as string,
            issuerDesignation: '' as string,
            issuerPhone: '' as string,
            issuerSignature: '' as string,
            approverName: '' as string,
            approverDesignation: '' as string,
            approverPhone: '' as string,
            approverSignature: '' as string,
            issuedDate: '' as string,
            approvedDate: '' as string,
            preparedBy: '' as string,
            preparedDesignation: '' as string,
            preparedSignature: '' as string,
            preparedDate: '' as string,
            certifiedBy: '' as string,
            certifiedDesignation: '' as string,
            certifiedSignature: '' as string,
            certifiedDate: '' as string,
            collectedBy: '' as string,
            collectedDesignation: '' as string,
            collectedSignature: '' as string,
            collectedDate: '' as string,
            verifiedBy: '' as string,
            verifiedDesignation: '' as string,
            verifiedSignature: '' as string,
            verifiedDate: '' as string,
        }
    },
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    methods: {
        async openDialog(): Promise<void> {
            await this.init();
            this.handleClick();
        },
        async init(): Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const { data, error, pending }: Response = await stockModule.getStockOrder(`${this.cookie}`, `${this.data.id}`);
            this.loading = pending;
            if (data.value) {
                this.requisitions = data.value.stock_requisitions
                this.loading = false;
                data.value.stock_pharmacy_approver_and_issuers.map((item: any) => {
                    if (item.record_type == 'issuer') {
                        this.issuerName = item.name;
                        this.issuerPhone = item.phone_number;
                        this.issuerDesignation = item.designation;
                        this.issuerSignature = item.signature;
                        this.issuedDate = moment(item.created_date).format(DATE_FORMAT)
                    } else {
                        this.approverName = item.name;
                        this.approverPhone = item.phone_number;
                        this.approverDesignation = item.designation;
                        this.approverSignature = item.signature;
                        this.approvedDate = moment(item.created_date).format(DATE_FORMAT)
                    }
                })
                data.value.stock_order_status_trail.map((trail: { stock_status: { name: string }; initiator: { first_name: any; last_name: any; username: string }; created_date: moment.MomentInput }) => {
                    if (trail.stock_status.name.toLowerCase() == 'draft') {
                        this.preparedBy = `${trail.initiator.first_name} ${trail.initiator.last_name}`;
                        this.preparedDesignation = 'Laboratory';
                        this.preparedSignature = trail.initiator.username;
                        this.preparedDate = moment(trail.created_date).format(DATE_FORMAT)
                    } else if (trail.stock_status.name.toLowerCase() == 'requested') {
                        this.certifiedBy = `${trail.initiator.first_name} ${trail.initiator.last_name}`;
                        this.certifiedDesignation = 'Laboratory';
                        this.certifiedSignature = trail.initiator.username;
                        this.certifiedDate = moment(trail.created_date).format(DATE_FORMAT)
                    } else if (trail.stock_status.name.toLowerCase() == 'received') {
                        this.collectedBy = `${trail.initiator.first_name} ${trail.initiator.last_name}`;
                        this.collectedDesignation = 'Laboratory';
                        this.collectedSignature = trail.initiator.username;
                        this.collectedDate = moment(trail.created_date).format(DATE_FORMAT)
                    } else if (trail.stock_status.name.toLowerCase() == 'approved') {
                        this.verifiedBy = `${trail.initiator.first_name} ${trail.initiator.last_name}`;
                        this.verifiedDesignation = 'Laboratory';
                        this.verifiedSignature = trail.initiator.username;
                        this.verifiedDate = moment(trail.created_date).format(DATE_FORMAT)
                    }
                })
            }
            if (error.value) {
                console.error(error.value)
                this.loading = false;
            }
        },
        handleClick(): void {
            this.show = !this.show
        }
    }
}
</script>

<style>
</style>
