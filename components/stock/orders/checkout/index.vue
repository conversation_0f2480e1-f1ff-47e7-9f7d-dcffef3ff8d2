<template>
    <div>

        <div>
            <CoreActionButton :icon="completeIcon" text="Complete" color="primary" :click="(() => { handleClick() })" />
        </div>

        <TransitionRoot appear :show="open" as="template">
            <Dialog as="div" @close="handleClick" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-4xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">

                                    <DialogTitle as="h3" class="text-xl text-black flex items-center font-medium leading-6">
                                        <TicketIcon class="w-8 h-8 mr-2" />
                                        Order Checkout
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" submit-label="Update" @submit="createStockOrder" :actions="false"
                                    #default="{ value }" id="submitForm">

                                    <div class="py-5 px-5 space-y-3">
                                        <div
                                            class="flex bg-gray-50 border-l-4 border-l-100 rounded-r px-2 py-2 items-center space-x-2">
                                            <TicketIcon class="h-5 w-5" />
                                            <p>Voucher Number: <strong>{{ $route.params.voucherId }}</strong></p>
                                        </div>
                                        <div class="mt-3">
                                            <table class="w-full">
                                                <thead clas="w-full border-t border-l border-r">
                                                    <tr class="border-b border-t border-r border-l rounded">
                                                        <th :colspan="2" class="text-left p-2">
                                                            Requisitions
                                                        </th>
                                                    </tr>
                                                    <tr class="border-b border-t border-r border-l rounded">
                                                        <th class="px-2 py-2 text-left border-r">
                                                            Stock Item
                                                        </th>
                                                        <th class="px-2 py-2 text-left">
                                                            Quantity Being Requested
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr v-for="(requisition, index) in requisitions" :key="index" class="border-b border-t border-r border-l rounded">
                                                        <td class="px-2 py-2 border-r">{{ requisition.stock_item.name }}</td>
                                                        <td class="px-2 py-2">{{  requisition.quantity_requested }}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreOutlinedButton type="button" :click="(() => { })"
                                            text="Cancel" />
                                        <CoreActionButton :loading="loading" type="submit" :click="(() => { })"
                                            color="success" :icon="saveIcon" text="Save changes" />
                                    </div>

                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import { PlusIcon, XMarkIcon, UserIcon, ArrowDownTrayIcon, TicketIcon, DocumentCheckIcon, ArrowUturnLeftIcon } from '@heroicons/vue/24/solid/index.js'
import type { RequisitionItem, Response, StockOrder } from '@/types'
import StockModule from '@/repository/modules/stock'

export default {
    props: {
        voucherId: {
            required: true,
            type: String
        },
        requisitions: {
            required: true,
            type: Array<RequisitionItem>,
        }
    },
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon,
        UserIcon,
        TicketIcon
    },
    data() {

        return {
            open: false as boolean,
            addIcon: PlusIcon,
            saveIcon: ArrowDownTrayIcon,
            clearIcon: ArrowUturnLeftIcon,
            completeIcon: DocumentCheckIcon,
            loading: false as boolean,
            name: "" as string,
            description: "" as string,
            cookie: useCookie('token')
        }
    },
    methods: {
        async createStockOrder() : Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const params: StockOrder = {
                voucher_number: Number(this.$route.params.voucherId),
                requisitions: this.requisitions.map((requisition) => ({
                    stock_item_id: requisition.stock_item.id,
                    quantity_requested: requisition.quantity_requested
                }))
            }
            const { data, error, pending } : Response = await stockModule.createStockOrder(`${this.cookie}`, params)
            this.loading = pending;
            if(data.value){
                this.loading = false;
                useNuxtApp().$toast.success(`Stock order ${this.$route.params.voucherId} created successfully`);
                this.handleClick();
                this.$emit('update', true)
            }
            if(error.value){
                this.loading = false;
                console.error(error.value)
            }
        },
        handleClick(): void {
            this.open = !this.open
        },
        clearForm(): void {
            this.$formkit.reset('submitForm')
        }

    }
}
</script>

<style>
</style>
