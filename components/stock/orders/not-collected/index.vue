<template>
    <div>

        <CoreActionButton :click="handleClick" color="error" text="Not Received" :icon="notCollectedIcon" />

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">

                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        <ArchiveBoxXMarkIcon class="h-5 w-5 mr-2" />
                                        Confirm Not Collected
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" submit-label="Update" @submit="acceptNotCollected()" :actions="false"
                                    #default="{ value }">

                                    <div class="mt-2 space-y-3 px-5">

                                        <div class="rounded px-2 py-2">
                                            Please provide a reason why <span class="font-semibold text-red-500">{{
                                                data.item.name }}</span> from the Order <strong>{{ orderId }}</strong> was not collected? Note that once this action is completed, it can not be
                                            undone
                                        </div>

                                        <FormKit type="textarea" label="Reason" validation="required" v-model="reason" />

                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreOutlinedButton :click="(() => { handleClick() })" type="button" text="Cancel" />
                                        <CoreActionButton :loading="statusLoading" type="submit" :click="(() => { })" color="error"
                                            :icon="notCollectedIcon" text="Reject" />
                                    </div>

                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

import { XMarkIcon, TrashIcon, ExclamationTriangleIcon, NoSymbolIcon, ArchiveBoxXMarkIcon } from '@heroicons/vue/24/solid/index.js'
import StockModule from '@/repository/modules/stock';
import type { Response } from '@/types';

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon,
        NoSymbolIcon,
        ExclamationTriangleIcon,
        ArchiveBoxXMarkIcon
    },
    data() {

        return {
            show: false as boolean,
            notCollectedIcon: ArchiveBoxXMarkIcon,
            loading: false as boolean,
            statusLoading: false as boolean,
            reason: "" as string,
            cookie: useCookie('token')
        }
    },
    props: {
        data: {
            type: Object,
            required: true
        },
        orderId: {
            type: String,
            required: true
        }
    },
    methods: {
        async acceptNotCollected(): Promise<void> {
            this.statusLoading = true;
            const stockModule = new StockModule();
            const params = {
                route: 'stock_requisition_not_collected',
                stock_requisition_id: this.data.id,
                stock_status_reason: this.reason
            }
            const { data, error, pending } : Response = await stockModule.updateStockOrderStatus(`${this.cookie}`, params);
            this.statusLoading = pending;
            if(data.value){
                this.$emit('update', true)
                this.statusLoading = false;
                this.reason = '';
                this.handleClick()
                useNuxtApp().$toast.success(`Stock order requisition not collected successfully!`);
            }
            if(error.value){
                console.error(error.value);
                this.statusLoading = false;
            }
        },
        handleClick(): void {
            this.show = !this.show
        }
    }
}
</script>

<style>
</style>
