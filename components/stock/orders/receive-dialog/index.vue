<template>
    <div>

        <div>
            <CoreActionButton :disabled="disabled" text="Receive" color="primary" :icon="checkIcon" :click="handleClick" />
        </div>

        <TransitionRoot appear :show="open" as="template">
            <Dialog as="div" @close="handleClick" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">

                                    <DialogTitle as="h3" class="text-xl text-black flex items-center font-medium leading-6">
                                        <img src="~assets/icons/spreadsheets.svg" class="w-8 h-8 mr-2" />
                                        Receive Stock Requisition
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" submit-label="Update" :actions="false" @submit="receiveRequisition"
                                    #default="{ value }" id="submitForm">
                                    <div class="px-5 py-5 space-y-3">
                                        <FormKit type="number" label="Quantity issued" v-model="quantityIssued"
                                            validation="required" />
                                        <FormKit type="number" label="Quantity collected" v-model="quantityCollected"
                                            validation="required" />
                                        <FormKit v-if="isNotCollected" disabled type="number" label="Quantity not collected" v-model="quantityNotCollected"
                                            validation="required" />
                                        <FormKit v-if="isNotCollected" type="textarea" label="Provide a reason for quantity not collected" v-model="notCollectedReason"
                                            validation="required" />
                                        <FormKit type="text" label="Lot number" v-model="lotNumber" />
                                        <FormKit type="text" label="Batch number" v-model="batchNumber" />
                                        <div class="relative flex flex-col space-y-2.5">
                                            <label class="font-medium">Date Expiry</label>
                                            <datepicker required :teleport="true" position="center" :range="false"
                                                placeholder="-- select expiry date --"
                                                :minDate="new Date()"
                                                input-classes="border font-none rounded px-2 py-1.5 block focus:outline-none focus:ring-2 focus:ring-sky-500 transition duration-150 focus:border-none"
                                                v-model="expirtyDate" format="dd/MM/yyyy" />
                                        </div>
                                        <FormKit type="textarea" label="Remarks" v-model="remarks" validation="required" />
                                    </div>

                                    <div class="mt-5 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreOutlinedButton text="Clear form" :click="(() => { })" />
                                        <CoreActionButton :loading="loading" type="submit" color="success" :icon="saveIcon"
                                            :click="(() => { })" text="Save changes" />
                                    </div>
                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

import { XMarkIcon, ArchiveBoxArrowDownIcon, UserIcon, ArrowDownTrayIcon, ArrowUturnLeftIcon } from '@heroicons/vue/24/solid/index.js'
import StockModule from '@/repository/modules/stock';
import type { Response } from '@/types';

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon,
        UserIcon
    },
    props: {
        data: {
            required: true,
            type: Object
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false
        }
    },
    data() {

        return {
            open: false as boolean,
            checkIcon: ArchiveBoxArrowDownIcon,
            saveIcon: ArrowDownTrayIcon,
            clearIcon: ArrowUturnLeftIcon,
            cookie: useCookie('token'),
            quantityRequested: 0 as number,
            quantityIssued: 0 as number,
            quantityCollected: 0 as number,
            notCollectedReason: '' as string,
            batchNumber: '' as string,
            lotNumber: '' as string,
            remarks: '' as string,
            expirtyDate: '' as string,
            loading: false as boolean,
        }
    },
    computed: {
        isNotCollected(){
            return this.quantityCollected < this.quantityIssued
        },
        quantityNotCollected(){
            return this.quantityIssued - this.quantityCollected
        }
    },
    methods: {
        async receiveRequisition(): Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const params = {
                stock_requisition_id: this.data.id,
                requisition: {
                    quantity_received: this.quantityCollected,
                    quantity_issued: this.quantityIssued,
                    quantity_not_collected: this.quantityNotCollected,
                    not_collected_reason: this.notCollectedReason
                },
                transaction: {
                    lot: this.lotNumber,
                    batch: this.batchNumber,
                    expiry_date: this.expirtyDate,
                    remarks: this.remarks
                }
            }
            const { data, error, pending }: Response = await stockModule.receiveStockOrderRequisition(`${this.cookie}`, params);
            this.loading = pending;
            if (data.value) {
                useNuxtApp().$toast.success('Stock order requisition received successfully');
                this.$emit('update', true)
                this.loading = false;
                this.handleClick();
            }
            if (error.value) {
                console.error(error.value)
                this.handleClick();
                useNuxtApp().$toast.error(ERROR_MESSAGE)
                this.loading = false;
            }
        },
        handleClick() {
            this.open = !this.open
        },

    }
}
</script>

<style>
</style>
