<template>
  <div>

    <div>
      <CoreActionButton text="Create Stock Adjustment" color="primary" :icon="addIcon" :click="init" />
    </div>

    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleClick" class="relative z-10">
        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
          leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95">
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                <div class="border-b px-3 py-3 flex items-center justify-between">

                  <DialogTitle as="h3" class="text-xl text-black flex items-center font-medium leading-6">
                    Create Stock Adjustment
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>

                </div>

                <FormKit type="form" submit-label="Update" @submit="submitForm" :actions="false" #default=""
                  id="submitForm">

                  <div class="py-5 px-5 space-y-3">
                    <div class="flex flex-col space-y-1.5">
                      <label class="font-medium">Reason</label>
                      <CoreDropdown :items="reasons" v-model="reasonSelected" />
                      <div class="mt-2">
                        <FormKit v-if="reasonSelected.name.toLowerCase() == 'other'" type="textarea"
                          label="Please write your reason for (Other)" validation="required" v-model="reason" />
                      </div>
                    </div>
                    <CoreMultiselect label="Stock item" mode="single" :items="stockItems.map((s) => s.name)"
                      v-model:items-selected="stockItemSelected.name" />
                    <FormKit type="number" label="In Stock" disabled v-model="inStock" />
                    <FormKit type="number" label="Adjustment" validation="required" v-model="adjustment" />
                    <FormKit type="number" label="Stock After" disabled v-model="afterStock" />
                    <CoreMultiselect label="Batch" mode="single" :items="batches" v-model:items-selected="batch"/>
                    <CoreMultiselect label="Lot" mode="single" :items="lots" v-model:items-selected="lot" />
                    <FormKit type="textarea" label="Notes" validation="required" v-model="notes" />
                  </div>
                  <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                    <CoreActionButton :loading="loading" type="submit" :click="(() => { })" color="success"
                      :icon="ajustIcon" text="Adjust" />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue';
import {
  PlusIcon,
  XMarkIcon,
  AdjustmentsVerticalIcon,
} from '@heroicons/vue/24/solid/index.js';
import moment from 'moment';
import StockModule from '@/repository/modules/stock';
import type { DropdownItem, Stock, StockItem } from '@/types';
import { adjustmentReasonsMapping } from './mapping';

const emit = defineEmits(["update"]);

const { $toast } = useNuxtApp();
const open = ref<boolean>(false);
const addIcon = PlusIcon;
const ajustIcon = AdjustmentsVerticalIcon;
const reasons = ref<DropdownItem[]>([]);
const loading = ref<boolean>(false);
const reasonSelected = ref<DropdownItem>({ name: 'select reason' });
const stockItemSelected = ref<DropdownItem>({ id: 0, name: 'select stock item' });
const stockItems = ref<StockItem[]>([]);
const cookie = useCookie<string>('token');
const inStock = ref<number>(0);
const afterStock = ref<number>(0);
const adjustment = ref<number>(0);
const stock = ref<Stock>();
const lot = ref<string>("");
const batch = ref<string>("");
const reason = ref<string>("");
const notes = ref<string>("");
const batches = ref<string[]>([]);
const lots = ref<string[]>([]);

const handleClick = (): void => {
  open.value = !open.value;
};

const getReasons = async (): Promise<void> => {
  const stockModule = new StockModule();
  const { data, error } = await stockModule.getStockAdjustmentReasons(`${cookie.value}`);
  if (data.value) {
    reasons.value = data.value;
  }
  if (error.value) {
    console.error(error.value);
    $toast.error("Error fetching stock adjustment reasons");
  }
};

const init = async (): Promise<void> => {
  handleClick();
  await getReasons();
  const stockModule = new StockModule();
  const { data, error } = await stockModule.getStockItem(`${cookie.value}`);
  if (data.value) {
    stockItems.value = data.value.map((item: { created_date: string }) => ({
      ...item,
      created_date: moment(item.created_date).format(DATE_FORMAT)
    }));
  }
  if (error.value) {
    console.error(error.value);
    $toast.error("Error fetching stock items");
  }
};

const getStock = async (stockId: number): Promise<void> => {
  const stockModule = new StockModule();
  const { data, error } = await stockModule.readStockItem(`${cookie.value}`, { id: stockId });
  if (data.value) {
    afterStock.value = 0;
    adjustment.value = 0;
    inStock.value = data.value.stock.quantity;
    stock.value = data.value.stock;
    batches.value = data.value.batches;
    lots.value = data.value.lots;
  }
  if (error.value) {
    console.error(error.value);
  }
};

const submitForm = async (): Promise<void> => {
  loading.value = true;
  const stockModule = new StockModule();
  const body = {
    stock_id: stock?.value?.id,
    lot: lot.value,
    batch: batch.value,
    quantity_to_adjusted: adjustment.value,
    reason: reason.value === '' ? reasonSelected.value.name : reason.value,
    notes: notes.value,
  };
  const { data, error, pending } = await stockModule.adjustStock(`${cookie.value}`, body);
  loading.value = pending;
  if (data.value) {
    afterStock.value = 0;
    adjustment.value = 0;
    loading.value = false;
    stockItems.value = [];
    reasons.value = [];
    reasonSelected.value = { name: 'select reason' };
    stockItemSelected.value = { name: 'select stock item' };
    emit('update');
    handleClick();
    $toast.success('Stock adjustment successfully done!');
  }
  if (error.value) {
    console.error(error.value);
    loading.value = false;
    $toast.error(ERROR_MESSAGE);
  }
};

watch(stockItemSelected, (newValue: DropdownItem) => {
  if (newValue.name !== 'select stock item') {
    const stockItemFoundId = stockItems.value.find((item) => item.name === newValue.name)?.id || 0;
    getStock(stockItemFoundId);
  }
}, { deep: true });

const applySign = (value: number, reason: string): number => {
  const arithmeticSign = adjustmentReasonsMapping(reason as any);
  return arithmeticSign === 'pos' ? Math.abs(value) : -Math.abs(value);
}

watch(
  adjustment,
  (value: number) => {
    const adjustmentValue = inStock.value + Number(value);
    afterStock.value = adjustmentValue;
    adjustment.value = applySign(value, reasonSelected.value.name);
  },
  { deep: true }
);

</script>

<style></style>