type AdjustmentReason = "Misplace stock" | "Incorrect entry" | "Negative reasons" | "Damaged" | "Phased out" | "Banned" | "Missing" | "Transfer to another facility" | "For trainings";
export const adjustmentReasonsMapping = (reason: AdjustmentReason): "pos" | "neg" => {
    // reasons corresponds to seeds.rb adjustments reasons
    const HashMap: Record<AdjustmentReason, "pos" | "neg"> = {
        "Misplace stock": "pos",
        "Incorrect entry": "pos",
        "Negative reasons": "neg",
        "Damaged": "neg",
        "Phased out": "neg",
        "Banned": "neg",
        "Missing": "neg",
        "Transfer to another facility": "neg",
        "For trainings": "neg",
    };
    return HashMap[reason] || "neg";
}