<template>
    <div>

        <CoreActionButton :click="handleClick" color="error" text="Delete" :icon="deleteIcon"/>

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild
                    as="template"
                    enter="duration-300 ease-out"
                    enter-from="opacity-0"
                    enter-to="opacity-100"
                    leave="duration-200 ease-in"
                    leave-from="opacity-100"
                    leave-to="opacity-0"
                >
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild
                            as="template"
                            enter="duration-300 ease-out"
                            enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100"
                            leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">

                            <DialogPanel class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle
                                        as="h3"
                                        class="text-lg flex items-center font-medium leading-6"
                                    >
                                        <ExclamationTriangleIcon class="h-5 w-5 mr-2"/>
                                        Confirm delete
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5"/>
                                    </button>

                                </div>

                                <FormKit
                                    type="form"
                                    submit-label="Update"
                                    @submit="voidStockSupplier(data.id)"
                                    :actions="false"
                                    #default="{ value }"
                                >

                                    <div class="mt-2 space-y-3 px-5">

                                        <div class="rounded px-2 py-2">
                                            Do you really want to delete <span class="font-semibold text-red-500">{{ data.name }}</span>? Note that once this action is completed, it can not be undone
                                        </div>

                                        <FormKit
                                            type="textarea"
                                            label="Reason"
                                            validation="required"
                                            v-model="reason"
                                        />

                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreOutlinedButton :click="(() => {handleClick()})" type="button" text="Cancel"/>
                                        <CoreActionButton :loading="loading" type="submit" :click="(() => {})" color="error" :icon="deleteIcon" text="Delete"/>
                                    </div>

                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue'

import { XMarkIcon, TrashIcon, ExclamationTriangleIcon } from '@heroicons/vue/24/solid/index.js'
import StockModule from '@/repository/modules/stock';
import type { Response } from '@/types';

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon,
        ExclamationTriangleIcon
    },
    data(){

        return{
            show: false as boolean,
            deleteIcon: TrashIcon,
            loading: false as boolean,
            reason: "" as string,
            cookie: useCookie('token')
        }
    },
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    methods: {
        /**
         * @method voidStockSupplier deletes test type
         * @param id test type id
         * @return promise @typeof void
         */
         async voidStockSupplier(id: number) : Promise<void> {

            this.loading = true;

            const stockModule = new StockModule();

            const { data, error, pending } : Response = await stockModule.voidStockSupplier(`${this.cookie}`, { 'reason': this.reason, id: id})

            this.loading = pending;

            if(data.value){
                this.handleClick()
                useNuxtApp().$toast.success(`Stock supplier deleted successfully!`);
                this.loading = false;
                this.reason = '';
                this.$emit('update', true);
            }
            if(error.value){
                console.error(error.value);
                useNuxtApp().$toast.error(ERROR_MESSAGE);
                this.loading = false;
            }
        },
        handleClick() : void {
            this.show = !this.show
        }
    }
}
</script>

<style>

</style>
