<template>
    <div>

        <div>
            <CoreActionButton text="Add metric" color="primary" :icon="addIcon" :click="handleClick" />
        </div>

        <TransitionRoot appear :show="open" as="template">
            <Dialog as="div" @close="handleClick" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">

                                    <DialogTitle as="h3" class="text-xl text-black flex items-center font-medium leading-6">
                                        Create Stock Metric
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" submit-label="Update" @submit="submitForm" :actions="false"
                                    #default="{ value }" id="submitForm">

                                    <div class="mt-2 space-y-3">
                                        <div class="w-full flex items-center px-5">
                                            <div class="w-full flex flex-col space-y-2">
                                                <FormKit type="text" label="Name" validation="required" v-model="name" />
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreOutlinedButton type="button" :click="(() => { clearForm() })"
                                            text="Clear form" />
                                        <CoreActionButton :loading="loading" type="submit" :click="(() => { })"
                                            color="success" :icon="saveIcon" text="Save changes" />
                                    </div>

                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import { PlusIcon, XMarkIcon, UserIcon, ArrowDownTrayIcon, ArrowUturnLeftIcon } from '@heroicons/vue/24/solid/index.js'
import StockModule from '@/repository/modules/stock'
import type { Response } from '@/types'

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon,
        UserIcon
    },
    data() {

        return {
            open: false as boolean,
            addIcon: PlusIcon,
            saveIcon: ArrowDownTrayIcon,
            clearIcon: ArrowUturnLeftIcon,
            loading: false as boolean,
            name: "" as string,
            description: "" as string,
            cookie: useCookie('token')
        }
    },
    methods: {

        async submitForm(): Promise<void> {

            this.loading = true;

            const stockModule = new StockModule();
            let params = {
                "name": this.name,
            }
            const { data, error, pending }: Response = await stockModule.createStockUnit(`${this.cookie}`, params)

            this.loading = pending;

            if (data.value) {

                this.handleClick();

                useNuxtApp().$toast.success(`${this.name} stock metric created successfully!`);

                this.loading = false;
                this.name = '';

                this.$emit('update', true);
            }

            if (error.value) {

                this.handleClick()

                console.error(error.value)

                useNuxtApp().$toast.error(ERROR_MESSAGE);

                this.loading = false;
            }
        },
        handleClick(): void {
            this.open = !this.open
        },
        clearForm(): void {
            this.$formkit.reset('submitForm')
        }

    }
}
</script>

<style>
</style>
