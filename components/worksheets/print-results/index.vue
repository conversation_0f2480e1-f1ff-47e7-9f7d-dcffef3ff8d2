<template>
    <div>

        <CoreActionButton :click="handleClick" color="success" text="Print" :icon="printIcon"/>

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
            <TransitionChild
                as="template"
                enter="duration-300 ease-out"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="duration-200 ease-in"
                leave-from="opacity-100"
                leave-to="opacity-0"
            >
                <div class="fixed inset-0 bg-black bg-opacity-25"></div>
            </TransitionChild>
        
            <div class="fixed inset-0 overflow-y-auto">
                <div
                class="flex min-h-full items-center justify-center p-4 text-center"
                >
                <TransitionChild
                    as="template"
                    enter="duration-300 ease-out"
                    enter-from="opacity-0 scale-95"
                    enter-to="opacity-100 scale-100"
                    leave="duration-200 ease-in"
                    leave-from="opacity-100 scale-100"
                    leave-to="opacity-0 scale-95"
                >
                    <DialogPanel
                    class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
                    >

                        <div class="border-b px-3 py-3 flex items-center justify-between">
                            <DialogTitle
                                as="h3"
                                class="text-lg flex items-center font-medium leading-6"
                            >
                                Select Printer
                            </DialogTitle>

                            <button @click="handleClick">
                                <XMarkIcon class="w-5 h-5"/>
                            </button>
                                
                        </div>

                    <div class="mt-2 space-y-3 px-5">

                        <div id="radio-group" class="mt-2 flex flex-col space-y-2">
                            <label class="flex items-center">
                                <input type="radio" v-model="selectedPrinter" value="printer1" class="mr-2">
                                Printer 1
                            </label>
                            <label class="flex items-center">
                                <input type="radio" v-model="selectedPrinter" value="printer2" class="mr-2">
                                Printer 2
                            </label>
                            <label class="flex items-center">
                                <input type="radio" v-model="selectedPrinter" value="printer3" class="mr-2">
                                Printer 3
                            </label>
                            <label class="flex items-center">
                                <input type="radio" v-model="selectedPrinter" value="printer4" class="mr-2">
                                Printer 4
                            </label>
                        </div>

                    </div>
        
                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t bg-gray-50">
                        <CoreActionButton :icon="printIcon" text="Continue" color="success"/>
                    </div>
                    </DialogPanel>
                </TransitionChild>
                </div>
            </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue'

import { XMarkIcon, PrinterIcon, PencilSquareIcon } from '@heroicons/vue/24/solid/index.js'

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon
    },
    data(){

        return{
            printIcon: PrinterIcon,
            show: false,
            editIcon: PencilSquareIcon,
            selectedPrinter: "" as string
        }
    },
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    methods: {
        handleClick(){
            this.show = !this.show
        }
    }
}
</script>

<style>

</style>