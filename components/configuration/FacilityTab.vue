<template>
  <div class="flex gap-6">
    <div class="flex-1">
      <FormKit
        type="form"
        submit-label="Update"
        @submit="submitForm"
        :actions="false"
        #default=""
      >
        <div class="grid grid-cols-2 gap-4">
          <FormKit label="Name" type="text" v-model="name"></FormKit>
          <FormKit label="Code" type="text" v-model="code"></FormKit>
          <FormKit label="District" type="text" v-model="district"></FormKit>
          <FormKit label="Address" type="text" v-model="address"></FormKit>
          <FormKit label="Phone Number" type="text" v-model="phone"></FormKit>
        </div>
        <div class="mt-4">
          <CoreActionButton
            :click="() => {}"
            type="submit"
            :loading="loading"
            :icon="saveIcon"
            text="Save Changes"
            color="success"
          ></CoreActionButton>
        </div>
      </FormKit>
    </div>

    <div class="flex-1">
      <div class="p-4">
        <h3 class="text-lg font-medium mb-3">Report Preview</h3>
        <div class="overflow-hidden">
          <div class="bg-gray-50 px-4 py-2 rounded border border-dotted">
            <address class="font-normal">
              <span class="flex items-center not-italic text-xl font-semibold border-b mb-2 border-dotted">
                {{ name || 'Facility Name' }}
              </span>
              <span class="flex items-center not-italic text-gray-600">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 mr-1">
                  <path fill-rule="evenodd" d="M8.161 2.58a1.875 1.875 0 0 1 1.678 0l4.993 2.498c.**************.336 0l3.869-1.935A1.875 1.875 0 0 1 21.75 4.82v12.485c0 .71-.401 1.36-1.037 1.677l-4.875 2.437a1.875 1.875 0 0 1-1.676 0l-4.994-2.497a.375.375 0 0 0-.336 0l-3.868 1.935A1.875 1.875 0 0 1 2.25 19.18V6.695c0-.71.401-1.36 1.036-1.677l4.875-2.437ZM9 6a.75.75 0 0 1 .75.75V15a.75.75 0 0 1-1.5 0V6.75A.75.75 0 0 1 9 6Zm6.75 3a.75.75 0 0 0-1.5 0v8.25a.75.75 0 0 0 1.5 0V9Z" clip-rule="evenodd" />
                </svg>
                {{ address || 'Address' }}
              </span>
              <span class="flex items-center not-italic text-gray-600">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 mr-1">
                  <path fill-rule="evenodd" d="M1.5 4.5a3 3 0 0 1 3-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 0 1-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 0 0 6.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 0 1 1.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 0 1-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5Z" clip-rule="evenodd" />
                </svg>
                {{ phone || 'Phone Number' }}
              </span>
            </address>
          </div>

          <div class="mt-4">
            <h4 class="text-lg font-medium mb-2">Accession Number Preview</h4>
            <div class="flex items-center bg-gray-50 p-2 rounded border border-dotted">
              <div class="text-md font-mono">
                {{ code || 'CODE' }}{{ currentYear }}000001
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowDownTrayIcon } from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import { useFacilityStore } from "@/store/facility";
import type { Request, Response } from "@/types";

const emit = defineEmits<{
  (e: "update", value: boolean): void;
}>();

const { $toast } = useNuxtApp();
const facility = useFacilityStore();
const saveIcon = ArrowDownTrayIcon;

const name = ref<string>("");
const code = ref<string>("");
const phone = ref<string>("");
const address = ref<string>("");
const district = ref<string>("");
const loading = ref<boolean>(false);
const currentYear = ref<string>(new Date().getFullYear().toString().slice(-2));

const init = (): void => {
  name.value = facility.details.name;
  phone.value = facility.details.phone;
  code.value = facility.details.code;
  address.value = facility.details.address;
  district.value = facility.details.district;
};

const submitForm = async (): Promise<void> => {
  loading.value = true;

  const request: Request = {
    route: `${endpoints.global}/${facility.details.id}`,
    method: "PUT",
    body: {
      name: name.value,
      code: code.value,
      address: address.value,
      phone: phone.value,
      district: district.value,
    },
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending as boolean;

  if (data.value) {
    $toast.success("Facility updated successfully!");
    emit("update", true);
    globals();
    loading.value = false;
  }

  if (error.value) {
    console.error(error.value);
    $toast.error("Error updating facility");
    loading.value = false;
  }
};

const globals = async (): Promise<void> => {
  const { fetchFacility } = useFacilityStore();
  const request: Request = {
    route: endpoints.global,
    method: "GET",
  };
  const { data, error }: Response = await fetchRequest(request);
  if (error.value) console.error(error.value);
  if (data.value) fetchFacility(data.value);
};

onMounted(() => {
  init();
});
</script>
