<template>
  <div>
    <div
      v-if="loadingPreferences"
      class="flex flex-col space-y-2 items-center justify-center py-10"
    >
      <CoreLoader :loading="loadingPreferences"></CoreLoader>
      <p class="text-base mt-2">Loading preferences, please wait...</p>
    </div>

    <div v-else>
      <div class="w-full flex flex-col border border-gray-200 rounded p-4 mb-4">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h3 class="font-bold text-gray-900 text-lg">Results Panel</h3>
            <p class="text-gray-500">
              Modify default layout when entering results
            </p>
          </div>
        </div>

        <div class="w-full flex flex-col sm:flex-row gap-6">
          <div
            class="w-full sm:w-1/2 cursor-pointer"
            @click="updateLayoutPreference('grid')"
          >
            <div
              class="bg-white border-2 rounded p-3 h-40 flex flex-col relative"
              :class="{
                'border-sky-500': layoutPreference === 'grid',
                'border-gray-100': layoutPreference !== 'grid',
              }"
            >
              <div
                v-if="layoutPreference === 'grid'"
                class="absolute -right-3 -top-3 text-sky-500 bg-white rounded-full"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="48"
                  height="48"
                  viewBox="0 0 48 48"
                  class="w-5 h-5"
                >
                  <defs>
                    <mask id="ipSCheckOne0">
                      <g fill="none" stroke-linejoin="round" stroke-width="4">
                        <path
                          fill="#fff"
                          stroke="#fff"
                          d="M24 44a19.94 19.94 0 0 0 14.142-5.858A19.94 19.94 0 0 0 44 24a19.94 19.94 0 0 0-5.858-14.142A19.94 19.94 0 0 0 24 4A19.94 19.94 0 0 0 9.858 9.858A19.94 19.94 0 0 0 4 24a19.94 19.94 0 0 0 5.858 14.142A19.94 19.94 0 0 0 24 44Z"
                        />
                        <path
                          stroke="#000"
                          stroke-linecap="round"
                          d="m16 24l6 6l12-12"
                        />
                      </g>
                    </mask>
                  </defs>
                  <path
                    fill="currentColor"
                    d="M0 0h48v48H0z"
                    mask="url(#ipSCheckOne0)"
                  />
                </svg>
              </div>
              <div class="flex mb-2 space-x-1">
                <div class="w-2 h-2 rounded-full bg-red-500"></div>
                <div class="w-2 h-2 rounded-full bg-yellow-500"></div>
                <div class="w-2 h-2 rounded-full bg-green-500"></div>
              </div>
              <div class="flex-1 grid grid-cols-3 gap-2">
                <div
                  v-for="i in 9"
                  :key="i"
                  class="bg-gray-100 rounded flex items-center justify-center"
                ></div>
              </div>
            </div>
            <div>
              <h3 class="text-base py-2.5 font-medium">Grid (Default)</h3>
            </div>
          </div>

          <div
            class="w-full sm:w-1/2 cursor-pointer"
            @click="updateLayoutPreference('list')"
          >
            <div
              class="bg-white border-2 rounded p-3 h-40 flex flex-col relative"
              :class="{
                'border-sky-500': layoutPreference === 'list',
                'border-gray-100': layoutPreference !== 'list',
              }"
            >
              <div
                v-if="layoutPreference === 'list'"
                class="absolute -right-3 -top-3 text-sky-500 bg-white rounded-full"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="48"
                  height="48"
                  viewBox="0 0 48 48"
                  class="w-5 h-5"
                >
                  <defs>
                    <mask id="ipSCheckOne0">
                      <g fill="none" stroke-linejoin="round" stroke-width="4">
                        <path
                          fill="#fff"
                          stroke="#fff"
                          d="M24 44a19.94 19.94 0 0 0 14.142-5.858A19.94 19.94 0 0 0 44 24a19.94 19.94 0 0 0-5.858-14.142A19.94 19.94 0 0 0 24 4A19.94 19.94 0 0 0 9.858 9.858A19.94 19.94 0 0 0 4 24a19.94 19.94 0 0 0 5.858 14.142A19.94 19.94 0 0 0 24 44Z"
                        />
                        <path
                          stroke="#000"
                          stroke-linecap="round"
                          d="m16 24l6 6l12-12"
                        />
                      </g>
                    </mask>
                  </defs>
                  <path
                    fill="currentColor"
                    d="M0 0h48v48H0z"
                    mask="url(#ipSCheckOne0)"
                  />
                </svg>
              </div>
              <div class="flex mb-2 space-x-1">
                <div class="w-2 h-2 rounded-full bg-red-500"></div>
                <div class="w-2 h-2 rounded-full bg-yellow-500"></div>
                <div class="w-2 h-2 rounded-full bg-green-500"></div>
              </div>
              <div class="flex-1 flex flex-col gap-2">
                <div
                  v-for="i in 3"
                  :key="i"
                  class="bg-gray-100 rounded flex items-center p-1"
                >
                  <div class="flex-1">
                    <div class="h-3 w-1/4 bg-gray-300 rounded"></div>
                    <div class="h-2 w-3/4 bg-gray-200 rounded mt-1"></div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h3 class="text-base py-2.5 font-medium">ListView</h3>
            </div>
          </div>
        </div>
      </div>

      <div class="w-full border border-gray-200 rounded p-4 mb-4">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h3 class="font-bold text-gray-900 text-lg">
              Tests Search Settings
            </h3>
            <p class="text-gray-500">Configure tests search accuracy</p>
          </div>
        </div>

        <div class="flex items-center space-x-4">
          <div class="form-control">
            <label class="flex items-center cursor-pointer space-x-2">
              <input
                type="radio"
                name="elasticsearch"
                class="radio checked:bg-sky-500"
                :checked="elasticsearchAccuracy === 'true'"
                @change="updateElasticsearchPreference('true')"
              />
              <span class="label-text"
                >High Accuracy, requires both first name and last name
                (Default)</span
              >
            </label>
          </div>
          <div class="form-control">
            <label class="flex items-center cursor-pointer space-x-2">
              <input
                type="radio"
                name="elasticsearch"
                class="radio checked:bg-sky-500"
                :checked="elasticsearchAccuracy === 'false'"
                @change="updateElasticsearchPreference('false')"
              />
              <span class="label-text">Standard Accuracy</span>
            </label>
          </div>
        </div>
      </div>

      <div class="w-full border border-gray-200 rounded p-4">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h3 class="font-bold text-gray-900 text-lg">Test Name Display</h3>
            <p class="text-gray-500">
              Choose how test names appear throughout the application
            </p>
          </div>
        </div>

        <div class="w-full flex flex-col sm:flex-row gap-6">
          <div
            class="w-full sm:w-1/2"
            :class="{
              'cursor-pointer': !can.manage('site_preferences'),
              'opacity-50 pointer-events-none': can.manage('site_preferences')
            }"
            @click="updateTestNameDisplayPreference('full_name')"
          >
            <div
              class="bg-white border-2 rounded p-3 h-40 flex flex-col relative"
              :class="{
                'border-sky-500': testNameDisplayPreference === 'full_name',
                'border-gray-100': testNameDisplayPreference !== 'full_name',
              }"
            >
              <div
                v-if="testNameDisplayPreference === 'full_name'"
                class="absolute -right-3 -top-3 text-sky-500 bg-white rounded-full"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="48"
                  height="48"
                  viewBox="0 0 48 48"
                  class="w-5 h-5"
                >
                  <defs>
                    <mask id="ipSCheckOne1">
                      <g fill="none" stroke-linejoin="round" stroke-width="4">
                        <path
                          fill="#fff"
                          stroke="#fff"
                          d="M24 44a19.94 19.94 0 0 0 14.142-5.858A19.94 19.94 0 0 0 44 24a19.94 19.94 0 0 0-5.858-14.142A19.94 19.94 0 0 0 24 4A19.94 19.94 0 0 0 9.858 9.858A19.94 19.94 0 0 0 4 24a19.94 19.94 0 0 0 5.858 14.142A19.94 19.94 0 0 0 24 44Z"
                        />
                        <path
                          stroke="#000"
                          stroke-linecap="round"
                          d="m16 24l6 6l12-12"
                        />
                      </g>
                    </mask>
                  </defs>
                  <path
                    fill="currentColor"
                    d="M0 0h48v48H0z"
                    mask="url(#ipSCheckOne1)"
                  />
                </svg>
              </div>
              <div class="flex mb-2 space-x-1">
                <div class="w-2 h-2 rounded-full bg-red-500"></div>
                <div class="w-2 h-2 rounded-full bg-yellow-500"></div>
                <div class="w-2 h-2 rounded-full bg-green-500"></div>
              </div>
              <div class="flex-1 flex flex-col justify-center items-center">
                <div class="bg-gray-50 rounded-lg p-3 w-4/5">
                  <div class="font-semibold text-sm text-center">
                    Full Blood Count
                  </div>
                  <div class="text-xs text-gray-500 text-center">
                    (Test Type Name)
                  </div>
                  <div class="mt-2 flex justify-between">
                    <div class="h-2 bg-gray-200 w-1/4 rounded"></div>
                    <div class="h-2 bg-gray-300 w-1/3 rounded"></div>
                  </div>
                </div>
                <div class="text-xs mt-2 text-gray-500">
                  Shows the official test type name
                </div>
              </div>
            </div>
            <div>
              <h3 class="text-base py-2.5 font-medium">
                Test Type Name (Default)
              </h3>
            </div>
          </div>

          <div
            class="w-full sm:w-1/2"
            :class="{
              'cursor-pointer': !can.manage('site_preferences'),
              'opacity-50 pointer-events-none': can.manage('site_preferences')
            }"
            @click="updateTestNameDisplayPreference('preferred_name')"
          >
            <div
              class="bg-white border-2 rounded p-3 h-40 flex flex-col relative"
              :class="{
                'border-sky-500':
                  testNameDisplayPreference === 'preferred_name',
                'border-gray-100':
                  testNameDisplayPreference !== 'preferred_name',
              }"
            >
              <div
                v-if="testNameDisplayPreference === 'preferred_name'"
                class="absolute -right-3 -top-3 text-sky-500 bg-white rounded-full"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="48"
                  height="48"
                  viewBox="0 0 48 48"
                  class="w-5 h-5"
                >
                  <defs>
                    <mask id="ipSCheckOne2">
                      <g fill="none" stroke-linejoin="round" stroke-width="4">
                        <path
                          fill="#fff"
                          stroke="#fff"
                          d="M24 44a19.94 19.94 0 0 0 14.142-5.858A19.94 19.94 0 0 0 44 24a19.94 19.94 0 0 0-5.858-14.142A19.94 19.94 0 0 0 24 4A19.94 19.94 0 0 0 9.858 9.858A19.94 19.94 0 0 0 4 24a19.94 19.94 0 0 0 5.858 14.142A19.94 19.94 0 0 0 24 44Z"
                        />
                        <path
                          stroke="#000"
                          stroke-linecap="round"
                          d="m16 24l6 6l12-12"
                        />
                      </g>
                    </mask>
                  </defs>
                  <path
                    fill="currentColor"
                    d="M0 0h48v48H0z"
                    mask="url(#ipSCheckOne2)"
                  />
                </svg>
              </div>
              <div class="flex mb-2 space-x-1">
                <div class="w-2 h-2 rounded-full bg-red-500"></div>
                <div class="w-2 h-2 rounded-full bg-yellow-500"></div>
                <div class="w-2 h-2 rounded-full bg-green-500"></div>
              </div>
              <div class="flex-1 flex flex-col justify-center items-center">
                <div class="bg-gray-50 rounded-lg p-3 w-4/5">
                  <div class="font-semibold text-sm text-center">FBC</div>
                  <div class="text-xs text-gray-500 text-center">
                    (Preferred Name)
                  </div>
                  <div class="mt-2 flex justify-between">
                    <div class="h-2 bg-gray-200 w-1/4 rounded"></div>
                    <div class="h-2 bg-gray-300 w-1/3 rounded"></div>
                  </div>
                </div>
                <div class="text-xs mt-2 text-gray-500">
                  Shows the shorter preferred name
                </div>
              </div>
            </div>
            <div>
              <h3 class="text-base py-2.5 font-medium">Preferred Name</h3>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import { useAuthStore } from "@/store/auth";
import type { Preference, Request, Response } from "@/types";

type PreferenceOption = {
  options: string[];
  default: string;
  description: string;
};

const { $toast } = useNuxtApp();
const { can } = usePermissions();
const authStore = useAuthStore();
const systemPreferences = ref<Preference[]>([]);
const layoutPreference = ref<string>("grid");
const elasticsearchAccuracy = ref<string>("true");
const testNameDisplayPreference = ref<string>("full_name");
const loadingPreferences = ref<boolean>(false);

const fetchSettings = async (): Promise<void> => {
  loadingPreferences.value = true;

  const request: Request = {
    route: endpoints.preferences,
    method: "GET",
  };

  const { data, error }: Response = await fetchRequest(request);

  if (error.value) {
    console.error(error.value);
    loadingPreferences.value = false;
    $toast.error("Failed to load preferences");
  }

  if (data.value) {
    systemPreferences.value = data.value;
    data.value.forEach((preference: Preference) => {
      try {
        if (preference.name === "grid_or_list_enter_result_view") {
          const valueObj = JSON.parse(preference.value) as PreferenceOption;
          const userPref = authStore.user.preferences.find(
            (p: Preference) => p.name === preference.name
          );

          layoutPreference.value = userPref?.value || valueObj.default;
        } else if (preference.name === "elasticsearch_enable_high_accuracy") {
          const valueObj = JSON.parse(preference.value) as PreferenceOption;
          const userPref = authStore.user.preferences.find(
            (p: Preference) => p.name === preference.name
          );

          elasticsearchAccuracy.value = userPref?.value || valueObj.default;
        } else if (preference.name === "test_name_display") {
          const valueObj = JSON.parse(preference.value) as PreferenceOption;
          const userPref = authStore.user.preferences.find(
            (p: Preference) => p.name === preference.name
          );
          testNameDisplayPreference.value = userPref?.value || valueObj.default;
        }
      } catch (e) {
        console.error("Error parsing preference:", e);
      }
    });

    loadingPreferences.value = false;
  }
};

const updateLayoutPreference = async (value: string): Promise<void> => {
  if (layoutPreference.value === value) return;

  layoutPreference.value = value;

  const preference = authStore.user.preferences.find(
    (p: Preference) => p.name === "grid_or_list_enter_result_view"
  );

  preference
    ? await update("user", { preferenceId: preference.id, name: preference.name, value: value})
    : await createUserPreference("grid_or_list_enter_result_view", value);
};

const updateElasticsearchPreference = async (value: string): Promise<void> => {
  if (elasticsearchAccuracy.value === value) return;
  elasticsearchAccuracy.value = value;

  const preference = authStore.user.preferences.find(
    (p: Preference) => p.name === "elasticsearch_enable_high_accuracy"
  );

  preference
    ? await update("user", { preferenceId: preference.id, name: preference.name, value: value})
    : await createUserPreference("elasticsearch_enable_high_accuracy", value);
};

const updateTestNameDisplayPreference = async (
  value: string
): Promise<void> => {
  if (testNameDisplayPreference.value === value) return;
  testNameDisplayPreference.value = value;

  const systemPreference = systemPreferences.value.find(
    (p: Preference) => p.name === "test_name_display"
  );

  if (systemPreference) {
    await updateGlobalSetting("test_name_display", value);
  } else {
    console.error("System preference 'test_name_display' not found");
    $toast.error("Failed to update preference: System setting not found");
  }
};

const updateGlobalSetting = async (
  setting: string,
  value: string
): Promise<void> => {
  const request: Request = {
    route: `${endpoints.global}/app_settings/${setting}`,
    method: "PUT",
    body: {
      app_setting_id: setting,
      value: value,
    },
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  if (data.value) {
    const updatedPreferences = [...authStore.user.preferences];
    const index = updatedPreferences.findIndex((p) => p.name === setting);
    if (index !== -1) {
      updatedPreferences[index] = { ...updatedPreferences[index], value };
      authStore.updateUserPreferences(updatedPreferences);
    }
    $toast.success("Preference updated successfully!");
  }

  if (error.value) {
    console.error(error.value);
  }
};

const updateUserPreference = async (
  preferenceId: number,
  name: string,
  value: string
): Promise<void> => {
  const request: Request = {
    route: `${endpoints.userPreferences}/${preferenceId}`,
    method: "PUT",
    body: {
      name: name,
      value: value,
    },
  };

  const { data, error }: Response = await fetchRequest(request);

  if (error.value) {
    console.error(error.value);
    $toast.error("Failed to update preference");
  }

  if (data.value) {
    const updatedPreferences = [...authStore.user.preferences];
    const index = updatedPreferences.findIndex((p) => p.id === preferenceId);
    if (index !== -1) {
      updatedPreferences[index] = { ...updatedPreferences[index], value };
      authStore.updateUserPreferences(updatedPreferences);
    }
    $toast.success("Preference updated successfully!");
  }
};

const createUserPreference = async (
  name: string,
  value: string
): Promise<void> => {
  const request: Request = {
    route: endpoints.userPreferences,
    method: "POST",
    body: {
      name: name,
      value: value,
    },
  };

  const { data, error }: Response = await fetchRequest(request);

  if (error.value) {
    console.error(error.value);
    $toast.error("Failed to create preference");
  }

  if (data.value) {
    const updatedPreferences = [...authStore.user.preferences, data.value];
    authStore.updateUserPreferences(updatedPreferences);

    $toast.success("Preference created successfully!");
  }
};

const update = async(target: "site" | "user", payload: {
  preferenceId?: number,
  name?: string;
  value: string;
  app_settting_id?: string;
}): Promise<void> => {
  const hashMap: Record<string, Promise<void>> = {
    "site" : updateGlobalSetting(String(payload.app_settting_id), payload.value),
    "user" : updateUserPreference(Number(payload.preferenceId), String(payload.name), payload.value),
  }
  hashMap[target]
}
onMounted(() => {
  fetchSettings();
});
</script>
