<template>
  <div>
    <div class="flex items-center justify-end mb-5">
      <CorePrinterAdd @update="loadPrinters" />
    </div>

    <div
      v-show="loadingPrinters"
      class="flex items-center mx-auto justify-center py-20"
    >
      <CoreLoader :loading="loadingPrinters"></CoreLoader>
    </div>

    <div v-show="!loadingPrinters" class="relative overflow-x-auto">
      <table class="w-full text-left border rounded-lg">
        <thead class="uppercase bg-gray-100">
          <tr>
            <th
              class="uppercase py-2 px-2"
              v-for="(printer, index) in printersHeader"
              :key="index"
            >
              {{ printer.text }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            class="bg-white border-b"
            v-for="(printer, index) in printers"
            :key="index"
          >
            <th class="px-2 py-2 font-normal">
              {{ printer.name }}
            </th>
            <td class="px-2 py-2">
              {{ printer.description }}
            </td>
            <td class="px-2 py-2">
              {{ moment(printer.created_date).format(DATE_FORMAT) }}
            </td>
            <td>
              <div class="flex items-center space-x-2">
                <CorePrinterEditDialog
                  :data="printer"
                  @update="loadPrinters"
                >
                </CorePrinterEditDialog>
                <CorePrinterDeleteDialog
                  :data="printer"
                  @update="loadPrinters"
                >
                </CorePrinterDeleteDialog>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response } from "@/types";
import moment from "moment";

type Printer = {
  name: string;
  description: string;
  date_created: string;
  [key: string]: any;
};

type TableHeader = {
  text: string;
  value: string;
};

const DATE_FORMAT = "YYYY-MM-DD";
const printersHeader = ref<TableHeader[]>([
  { text: "name", value: "name" },
  { text: "description", value: "description" },
  { text: "date created", value: "date_created" },
  { text: "Actions", value: "actions" },
]);

const printers = ref<Printer[]>([]);
const loadingPrinters = ref<boolean>(false);

const loadPrinters = async (): Promise<void> => {
  loadingPrinters.value = true;
  const request: Request = {
    route: endpoints.printers,
    method: "GET",
  };

  const { data, error, pending }: Response = await fetchRequest(request);

  loadingPrinters.value = pending as boolean;

  if (data.value) {
    printers.value = data.value;
    loadingPrinters.value = false;
  }

  if (error.value) {
    console.error(error.value);
    loadingPrinters.value = false;
  }
};

onMounted(() => {
  loadPrinters();
});
</script>
