<template>
    <div class="py-4 px-2 bg-white border-t text-gray-600 flex items-center justify-between">
        <div class="flex items-center">
            <img src="@/assets/images/logo.png" alt="app-logo" class="w-5 h-5 mr-2" />
           Malawi Ministry of Health
        </div>
        <div v-show="!loading" class="rounded bg-gray-100 py-1 px-2.5 border text-sm flex items-center">
            <img src="@/assets/icons/git-branch-outline.svg" class="w-4 h-4 text-red-500" alt="git-tag-icon/">
            {{ version }} / {{ apiVersion }}
        </div>
        <svg v-show="loading" class="w-5 h-5 text-sky-500 animate-spin mr-2" fill="none" viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                fill="currentColor"></path>
        </svg>
    </div>
</template>

<script setup lang="ts">

import Package from '@/package.json'
import { endpoints } from '@/services/endpoints';
import type { Request, Response } from '@/types';
import fetchRequest from '@/services/fetch';

const apiVersion: Ref<string> = ref<string>('');
const version: Ref<string> = ref<string>(Package.version);
const loading: Ref<boolean> = ref<boolean>(false);

async function getAPIVersion(): Promise<void> {
    loading.value = true;
    const request: Request = {
        route: `${endpoints.global}/current_api_tag`,
        method: 'GET',
    }
    const { data, error }: Response = await fetchRequest(request);
    if (data.value) {
        apiVersion.value = data.value.git_tag
        loading.value = false;
    }
    if (error.value) {
        console.error(error.value)
        loading.value = false;
    }
}

getAPIVersion();
</script>
