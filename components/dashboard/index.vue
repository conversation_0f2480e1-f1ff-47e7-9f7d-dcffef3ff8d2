<template>
    <div class="flex h-screen overflow-y-hidden bg-white">
        <NuxtLoadingIndicator/>
        <div v-show="menu" class="fixed inset-0 z-10 bg-black bg-opacity-20 lg:hidden"
            style="backdrop-filter: blur(14px); -webkit-backdrop-filter: blur(14px)"></div>
        <DashboardSidebar :menu="menu" />
        <div class="flex flex-col flex-1 h-full overflow-hidden">
            <DashboardHeader :click="toggleMenu" />
            <main class="flex-1 max-h-full overflow-hidden overflow-y-scroll">
                <NuxtPage />
            </main>
            <DashboardFooter/>
        </div>
    </div>
</template>

<script setup lang="ts">

const menu = ref<boolean>(true);
const toggleMenu = (): void => {
    menu.value = !menu.value;
}
</script>
