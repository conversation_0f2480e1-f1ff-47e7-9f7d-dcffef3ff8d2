<template>
  <div class="space-y-6">
    <!-- Sync Status Section -->
    <div class="bg-white border border-gray-200 rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Sync Status</h3>
        <p class="text-sm text-gray-500">Current synchronization status with NLIMS</p>
      </div>
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="flex-shrink-0">
              <div 
                :class="[
                  'w-4 h-4 rounded-full',
                  lastSync.status === 'success' ? 'bg-green-500' : 
                  lastSync.status === 'error' ? 'bg-red-500' : 'bg-yellow-500'
                ]"
              ></div>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">
                {{ lastSync.status === 'success' ? 'Successfully synced' : 
                   lastSync.status === 'error' ? 'Sync failed' : 'Sync in progress' }}
              </p>
              <p class="text-sm text-gray-500">
                Last sync: {{ formatDate(lastSync.timestamp) }}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span 
              :class="[
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                lastSync.status === 'success' ? 'bg-green-100 text-green-800' :
                lastSync.status === 'error' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
              ]"
            >
              {{ lastSync.status === 'success' ? 'Up to date' : 
                 lastSync.status === 'error' ? 'Failed' : 'Syncing' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Sync Actions Section -->
    <div class="bg-white border border-gray-200 rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Sync Actions</h3>
        <p class="text-sm text-gray-500">Synchronize test catalog data with NLIMS</p>
      </div>
      <div class="px-6 py-4 space-y-4">
        <!-- Full Sync -->
        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div>
            <h4 class="text-sm font-medium text-gray-900">Full Sync</h4>
            <p class="text-sm text-gray-500">
              Synchronize all test catalog data including organisms, specimens, drugs, and test types
            </p>
          </div>
          <button
            @click="performFullSync"
            :disabled="syncing"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
          >
            <ArrowPathIcon v-if="syncing" class="animate-spin -ml-1 mr-2 h-4 w-4" />
            {{ syncing ? 'Syncing...' : 'Start Full Sync' }}
          </button>
        </div>

        <!-- Selective Sync Options -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="p-4 border border-gray-200 rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-sm font-medium text-gray-900">Organisms</h4>
              <button
                @click="performSelectiveSync('organisms')"
                :disabled="syncing"
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-sky-700 bg-sky-100 hover:bg-sky-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
              >
                Sync
              </button>
            </div>
            <p class="text-xs text-gray-500">Last synced: {{ formatDate(syncHistory.organisms) }}</p>
          </div>

          <div class="p-4 border border-gray-200 rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-sm font-medium text-gray-900">Specimens</h4>
              <button
                @click="performSelectiveSync('specimens')"
                :disabled="syncing"
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-sky-700 bg-sky-100 hover:bg-sky-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
              >
                Sync
              </button>
            </div>
            <p class="text-xs text-gray-500">Last synced: {{ formatDate(syncHistory.specimens) }}</p>
          </div>

          <div class="p-4 border border-gray-200 rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-sm font-medium text-gray-900">Drugs</h4>
              <button
                @click="performSelectiveSync('drugs')"
                :disabled="syncing"
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-sky-700 bg-sky-100 hover:bg-sky-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
              >
                Sync
              </button>
            </div>
            <p class="text-xs text-gray-500">Last synced: {{ formatDate(syncHistory.drugs) }}</p>
          </div>

          <div class="p-4 border border-gray-200 rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-sm font-medium text-gray-900">Test Types</h4>
              <button
                @click="performSelectiveSync('testTypes')"
                :disabled="syncing"
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-sky-700 bg-sky-100 hover:bg-sky-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
              >
                Sync
              </button>
            </div>
            <p class="text-xs text-gray-500">Last synced: {{ formatDate(syncHistory.testTypes) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Sync Progress Dialog -->
    <TransitionRoot appear :show="showSyncDialog" as="template">
      <Dialog as="div" @close="() => {}" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-50"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all py-8 px-6">
                <div class="flex items-center space-x-4">
                  <div class="flex-shrink-0">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-sky-600"></div>
                  </div>
                  <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900">
                      {{ syncType === 'full' ? 'Syncing Test Catalog' : `Syncing ${syncType}` }}
                    </h3>
                    <p class="text-sm text-gray-500 mt-1">{{ syncStatus }}</p>
                    <div class="mt-3">
                      <div class="bg-gray-200 rounded-full h-2">
                        <div
                          class="bg-sky-600 h-2 rounded-full transition-all duration-300"
                          :style="{ width: `${syncProgress}%` }"
                        ></div>
                      </div>
                      <p class="text-xs text-gray-500 mt-1">{{ syncProgress }}% complete</p>
                    </div>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ArrowPathIcon } from '@heroicons/vue/24/solid/index.js'
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
} from '@headlessui/vue'
import moment from 'moment'
import { endpoints } from '@/services/endpoints'
import fetchRequest from '@/services/fetch'
import type { Request, Response } from '@/types'

const emit = defineEmits<{
  'sync-completed': []
}>()

interface SyncStatus {
  status: 'success' | 'error' | 'pending'
  timestamp: string
}

interface SyncHistory {
  organisms: string
  specimens: string
  drugs: string
  testTypes: string
}

const lastSync = ref<SyncStatus>({
  status: 'success',
  timestamp: '2024-01-15T10:30:00Z'
})

const syncHistory = ref<SyncHistory>({
  organisms: '2024-01-15T10:30:00Z',
  specimens: '2024-01-15T10:25:00Z',
  drugs: '2024-01-15T10:20:00Z',
  testTypes: '2024-01-15T10:15:00Z'
})

const syncing = ref<boolean>(false)
const showSyncDialog = ref<boolean>(false)
const syncProgress = ref<number>(0)
const syncStatus = ref<string>('')
const syncType = ref<string>('')
const cookie = useCookie('token')

const formatDate = (dateString: string): string => {
  return moment(dateString).format('MMM DD, YYYY HH:mm')
}

const performFullSync = async (): Promise<void> => {
  syncType.value = 'full'
  await startSync()
}

const performSelectiveSync = async (type: string): Promise<void> => {
  syncType.value = type
  await startSync()
}

const startSync = async (): Promise<void> => {
  syncing.value = true
  showSyncDialog.value = true
  syncProgress.value = 0
  syncStatus.value = 'Initializing sync...'

  try {
    const steps = [
      { progress: 20, status: 'Connecting to NLIMS...' },
      { progress: 40, status: 'Fetching test catalog data...' },
      { progress: 60, status: 'Processing data...' },
      { progress: 80, status: 'Updating local database...' },
      { progress: 100, status: 'Sync completed successfully!' },
    ]

    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, 1000))
      syncProgress.value = step.progress
      syncStatus.value = step.status
    }

    // Simulate API call
    const request: Request = {
      route: `${endpoints.global}/test_catalog_sync`,
      method: 'POST',
      token: `${cookie.value}`,
      body: {
        sync_type: syncType.value
      }
    }

    const { error, data }: Response = await fetchRequest(request)

    if (data.value) {
      useNuxtApp().$toast.success('Test catalog synced successfully!')
      lastSync.value = {
        status: 'success',
        timestamp: new Date().toISOString()
      }
      
      // Update sync history
      if (syncType.value === 'full') {
        const now = new Date().toISOString()
        syncHistory.value = {
          organisms: now,
          specimens: now,
          drugs: now,
          testTypes: now
        }
      } else {
        syncHistory.value[syncType.value as keyof SyncHistory] = new Date().toISOString()
      }
      
      emit('sync-completed')
    }

    if (error.value) {
      console.error(error.value)
      useNuxtApp().$toast.error('Sync failed. Please try again.')
      lastSync.value = {
        status: 'error',
        timestamp: new Date().toISOString()
      }
    }

  } catch (error) {
    console.error('Sync error:', error)
    useNuxtApp().$toast.error('Sync failed. Please try again.')
    lastSync.value = {
      status: 'error',
      timestamp: new Date().toISOString()
    }
  } finally {
    setTimeout(() => {
      showSyncDialog.value = false
      syncing.value = false
      syncProgress.value = 0
      syncStatus.value = ''
    }, 2000)
  }
}
</script>
