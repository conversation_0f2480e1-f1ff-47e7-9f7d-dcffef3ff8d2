<template>
  <div class="w-full">
    <!-- Tab Navigation -->
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
        <button
          v-for="(tab, index) in tabs"
          :key="tab.id"
          @click="activeTab = index"
          :class="[
            activeTab === index
              ? 'border-sky-500 text-sky-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
            'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm'
          ]"
        >
          {{ tab.name }}
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div class="p-6">
      <component :is="currentTabComponent" @sync-completed="handleSyncCompleted" />
    </div>
  </div>
</template>

<script setup lang="ts">
import TestCatalogVersions from './TestCatalogVersions.vue'
import TestCatalogSync from './TestCatalogSync.vue'

const emit = defineEmits<{
  'sync-completed': []
}>()

interface Tab {
  id: string
  name: string
  component: any
}

const tabs: Tab[] = [
  {
    id: 'versions',
    name: 'Versions',
    component: TestCatalogVersions
  },
  {
    id: 'sync',
    name: 'Sync',
    component: TestCatalogSync
  }
]

const activeTab = ref<number>(0)

const currentTabComponent = computed(() => {
  return tabs[activeTab.value]?.component
})

const handleSyncCompleted = (): void => {
  emit('sync-completed')
}
</script>
