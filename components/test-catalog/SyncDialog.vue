<template>
  <TransitionRoot appear :show="show" as="template">
    <Dialog as="div" @close="() => {}" class="relative z-10">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-50"></div>
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="w-full max-w-md transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all py-8 px-6">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <CoreLoader :width="80" :height="80" />
                </div>
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-gray-900">
                    {{ syncType === 'full' ? 'Syncing Test Catalog' : `Syncing ${capitalizeFirst(syncType)}` }}
                  </h3>
                  <p class="text-sm text-gray-500 mt-1">{{ status }}</p>
                  <div class="mt-3">
                    <div class="bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-gradient-to-r from-sky-300 to-sky-600 animate-pulse h-2 rounded-full transition-all duration-300"
                        :style="{ width: `${Math.floor(progress)}%` }"
                      ></div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">{{ Math.floor(progress) }}% complete</p>
                  </div>

                  <!-- Timeout Warning -->
                  <div v-if="showTimeoutWarning" class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                      </div>
                      <div class="ml-3">
                        <p class="text-sm text-yellow-800">
                          Syncing is taking longer than expected. This may indicate a network issue or heavy server load.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="mt-4 flex justify-end">
                    <button
                      @click="$emit('cancel')"
                      class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-0 focus:ring-offset-0 focus:ring-sky-500"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
} from '@headlessui/vue'

interface Props {
  show: boolean
  progress: number
  status: string
  syncType: string
}

const props = defineProps<Props>()
defineEmits<{
  cancel: []
}>()

const syncStartTime = ref<number | null>(null)
const showTimeoutWarning = ref<boolean>(false)

watch(() => props.show, (newShow) => {
  if (newShow) {
    syncStartTime.value = Date.now()
    showTimeoutWarning.value = false

    // Check every 30 seconds if sync is taking too long
    const timeoutCheck = setInterval(() => {
      if (syncStartTime.value && Date.now() - syncStartTime.value > 120000) { // 2 minutes
        showTimeoutWarning.value = true
        clearInterval(timeoutCheck)
      }
    }, 30000)

    // Clear interval when dialog closes
    watch(() => props.show, (show) => {
      if (!show) {
        clearInterval(timeoutCheck)
        syncStartTime.value = null
        showTimeoutWarning.value = false
      }
    })
  }
})

const capitalizeFirst = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1)
}
</script>
