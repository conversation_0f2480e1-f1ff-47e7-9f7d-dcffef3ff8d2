<template>
  <div class="space-y-6">
    <!-- Current Version Section -->
    <div class="bg-white border border-gray-200 rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Current Version</h3>
        <p class="text-sm text-gray-500">Currently active test catalog version</p>
      </div>
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="flex-shrink-0">
              <div class="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">
                Version {{ currentVersion.version }}
              </p>
              <p class="text-sm text-gray-500">
                Last updated: {{ formatDate(currentVersion.lastUpdated) }}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Active
            </span>
          </div>
        </div>
        <div class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="text-center">
            <p class="text-2xl font-semibold text-gray-900">{{ currentVersion.stats.organisms }}</p>
            <p class="text-sm text-gray-500">Organisms</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-semibold text-gray-900">{{ currentVersion.stats.specimens }}</p>
            <p class="text-sm text-gray-500">Specimens</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-semibold text-gray-900">{{ currentVersion.stats.drugs }}</p>
            <p class="text-sm text-gray-500">Drugs</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-semibold text-gray-900">{{ currentVersion.stats.testTypes }}</p>
            <p class="text-sm text-gray-500">Test Types</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Available Versions Section -->
    <div class="bg-white border border-gray-200 rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Available Versions</h3>
        <p class="text-sm text-gray-500">Test catalog versions available for activation</p>
      </div>
      <div class="divide-y divide-gray-200">
        <div
          v-for="version in availableVersions"
          :key="version.id"
          class="px-6 py-4 hover:bg-gray-50"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div 
                  :class="[
                    'w-3 h-3 rounded-full',
                    version.status === 'active' ? 'bg-green-500' : 
                    version.status === 'available' ? 'bg-blue-500' : 'bg-gray-400'
                  ]"
                ></div>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900">
                  Version {{ version.version }}
                </p>
                <p class="text-sm text-gray-500">
                  Created: {{ formatDate(version.createdAt) }}
                </p>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <span 
                :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  version.status === 'active' ? 'bg-green-100 text-green-800' :
                  version.status === 'available' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                ]"
              >
                {{ version.status === 'active' ? 'Active' : version.status === 'available' ? 'Available' : 'Draft' }}
              </span>
              <button
                v-if="version.status === 'available'"
                @click="activateVersion(version)"
                :disabled="activating"
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:opacity-50"
              >
                <ArrowPathIcon v-if="activating" class="animate-spin -ml-1 mr-1 h-3 w-3" />
                {{ activating ? 'Activating...' : 'Activate' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowPathIcon } from '@heroicons/vue/24/solid/index.js'
import moment from 'moment'

interface VersionStats {
  organisms: number
  specimens: number
  drugs: number
  testTypes: number
}

interface Version {
  id: string
  version: string
  status: 'active' | 'available' | 'draft'
  createdAt: string
  lastUpdated?: string
  stats: VersionStats
}

// Mock data - replace with actual API calls
const currentVersion = ref<Version>({
  id: '1',
  version: '2.1.4',
  status: 'active',
  createdAt: '2024-01-15T10:30:00Z',
  lastUpdated: '2024-01-15T10:30:00Z',
  stats: {
    organisms: 156,
    specimens: 89,
    drugs: 234,
    testTypes: 178
  }
})

const availableVersions = ref<Version[]>([
  {
    id: '1',
    version: '2.1.4',
    status: 'active',
    createdAt: '2024-01-15T10:30:00Z',
    lastUpdated: '2024-01-15T10:30:00Z',
    stats: {
      organisms: 156,
      specimens: 89,
      drugs: 234,
      testTypes: 178
    }
  },
  {
    id: '2',
    version: '2.1.5',
    status: 'available',
    createdAt: '2024-01-20T14:45:00Z',
    stats: {
      organisms: 162,
      specimens: 92,
      drugs: 241,
      testTypes: 185
    }
  },
  {
    id: '3',
    version: '2.2.0',
    status: 'available',
    createdAt: '2024-01-25T09:15:00Z',
    stats: {
      organisms: 168,
      specimens: 95,
      drugs: 248,
      testTypes: 192
    }
  }
])

const activating = ref<boolean>(false)

const formatDate = (dateString: string): string => {
  return moment(dateString).format('MMM DD, YYYY HH:mm')
}

const activateVersion = async (version: Version): Promise<void> => {
  activating.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Update current version
    currentVersion.value = { ...version, status: 'active', lastUpdated: new Date().toISOString() }
    
    // Update available versions
    availableVersions.value = availableVersions.value.map(v => ({
      ...v,
      status: v.id === version.id ? 'active' : v.status === 'active' ? 'available' : v.status
    }))
    
    useNuxtApp().$toast.success(`Version ${version.version} activated successfully!`)
  } catch (error) {
    console.error('Failed to activate version:', error)
    useNuxtApp().$toast.error('Failed to activate version. Please try again.')
  } finally {
    activating.value = false
  }
}
</script>
