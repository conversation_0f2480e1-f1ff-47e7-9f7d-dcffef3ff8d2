<template>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="{ }" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-50" @click.stop></div>
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-start justify-end top-0 p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-md transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
                            <DialogTitle as="h3"
                                class="text-red-500 px-2 py-2 border-b flex items-center font-medium leading-6">
                                <ExclamationCircleIcon class="h-5 w-5 mr-2" />
                                Session Expiry
                            </DialogTitle>
                            <div class="px-5 py-5">
                                <p class="text-black">
                                    Your session is about to expire, you will be logged out in <span
                                        class="font-semibold">{{ formattedTime }}</span> minutes,
                                    Do you wish to keep session or log out?
                                </p>
                            </div>

                            <div class="border-t px-5 py-2 flex items-center justify-end space-x-3">
                                <button @click="logOut" class="px-4 py-2 bg-red-500 text-white rounded">
                                    Log out
                                </button>
                                <button @click="refreshToken" :disabled="loading"
                                    class="px-4 py-2 bg-green-500 text-white rounded">
                                    {{ loading ? 'Refreshing...' : 'Stay logged in' }}
                                </button>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from "@headlessui/vue";
import { ExclamationCircleIcon } from "@heroicons/vue/24/solid";
import { useSessionStore } from "@/store/session";

const {
    loading,
    logOut,
    refreshToken,
} = useSessionManagement();

const time = ref('1:00');
const timer = ref<NodeJS.Timeout | null>(null);
const formattedTime = computed(() => time.value)

const { resetLastActivity } = useInactivityLogout();
const sessionStore = useSessionStore();
const isOpen = computed(() => sessionStore.isOpen);

const startCountdown = () => {
    const timeArray = time.value.split(':').map(Number)
    timer.value = setInterval(() => {
        let [hours, minutes] = timeArray
        if (minutes === 0) {
            if (hours === 0) {
                clearInterval(timer.value!)
                logOut()
                return
            }
            hours -= 1
            minutes = 59
        } else {
            minutes -= 1
        }
        timeArray[0] = hours
        timeArray[1] = minutes
        time.value = timeArray.map(t => t < 10 ? `0${t}` : `${t}`).join(':')
    }, 1000)
}

watch(sessionStore, (value) => {
    if (sessionStore.isOpen) {
        startCountdown()
    }
}, { deep: true });

onMounted(() => {
    resetLastActivity()
})
</script>
