<template>
    <div>
        <CoreActionButton :click="handleClick" text="Edit" color="success" :icon="editIcon" />

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-gray-900 bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        <img src="~assets/icons/virus.svg" class="w-8 h-8 mr-2" />
                                        Edit Disease
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit id="editForm" type="form" submit-label="Update" @submit="submitForm" :actions="false"
                                    #default="{ value }">

                                    <div class="mt-2 space-y-3">
                                        <div class="w-full flex items-center px-5">
                                            <div class="w-full flex flex-col space-y-2">
                                                <FormKit type="text" label="Name" validation="required"
                                                    v-model="data.name" />
                                            </div>
                                        </div>

                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreOutlinedButton text="Clear form" type="button" :click="(() => { clearForm() })" />
                                        <CoreActionButton :loading="loading" type="submit" :click="(() => { })"
                                            color="success" :icon="saveIcon" text="Save changes" />
                                    </div>
                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

import { XMarkIcon, PencilSquareIcon, ArrowDownTrayIcon } from '@heroicons/vue/24/solid/index.js'
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import type { Drug, Request, Response } from '@/types';

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon
    },
    data() {

        return {
            editIcon: PencilSquareIcon,
            show: false,
            saveIcon: ArrowDownTrayIcon,
            name: this.data.name,
            description: this.data.description,
            drugSelected: new Array<string>(),
            drugs: new Array<string>(),
            rawDrugs: new Array<Drug>(),
            loading: false as boolean,
            cookie: useCookie('token')
        }
    },
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    methods: {
        async submitForm(): Promise<void> {
            this.loading = true;
            const request: Request = {
                route: `${endpoints.disease.edit}/${this.data.id}`,
                method: 'PUT',
                token: `${this.cookie}`,
                body: this.data
            }
            const { pending, error, data }: Response = await fetchRequest(request);
            this.loading = pending;
            if (data.value) {
                this.show = false;
                useNuxtApp().$toast.success(`Disease updated successfully!`);
                this.loading = false;
                this.$emit('update', true);
            }

            if (error.value) {
                this.show = false;
                console.error(error.value)
                this.loading = false;
                useNuxtApp().$toast.error(ERROR_MESSAGE);
            }
        },
        handleClick (): void {
            this.show = !this.show
        },
        clearForm () : void {
            this.$formkit.reset('editForm');
        }
    }
}
</script>

<style>
</style>
