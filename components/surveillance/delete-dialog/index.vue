<script setup lang="ts">
	import {
		TransitionRoot,
		TransitionChild,
		Dialog,
		DialogPanel,
		DialogTitle,
	} from '@headlessui/vue';

	import {
		XMarkIcon,
		TrashIcon,
		ExclamationTriangleIcon,
	} from '@heroicons/vue/24/solid/index.js';
	import { endpoints } from '@/services/endpoints';
	import fetchRequest from '@/services/fetch';
	import type { Request } from '@/types';

	const props = defineProps<{
		data: {
			id: number;
			name: string;
		};
	}>();

	const emit = defineEmits(['action-completed']);

	const show: Ref<boolean> = ref<boolean>(false);
	const loading: Ref<boolean> = ref<boolean>(false);
	const cookie: Ref<string> = useCookie('token');
	const reason: Ref<string> = ref<string>('');

	const deleteData = async (): Promise<void> => {
		loading.value = true;

		let request: Request = {
			route: `${endpoints.surveillance.delete}/${props.data.id}`,
			method: 'DELETE',
			token: `${cookie.value}`,
			body: {
				retired_reason: reason.value,
			},
		};

		const { pending, error, data } = await fetchRequest(request);

		if (!data.value) {
			handleClick();

			useNuxtApp().$toast.success(`Visit type deleted successfully!`);

			loading.value = false;

			emit('action-completed', true);
		}

		if (error.value) {
			console.error(error.value);

			useNuxtApp().$toast.error(ERROR_MESSAGE);

			loading.value = false;
		}
	};

	const handleClick = (): any => {
		reason.value = '';
		show.value = !show.value;
	};
</script>

<template>
	<div>
		<CoreActionButton
			:click="handleClick"
			color="error"
			text="Delete"
			:icon="TrashIcon"
		/>

		<TransitionRoot appear :show="show" as="template">
			<Dialog as="div" class="relative z-10">
				<TransitionChild
					as="template"
					enter="duration-300 ease-out"
					enter-from="opacity-0"
					enter-to="opacity-100"
					leave="duration-200 ease-in"
					leave-from="opacity-100"
					leave-to="opacity-0"
				>
					<div class="fixed inset-0 bg-black bg-opacity-25" />
				</TransitionChild>

				<div class="fixed inset-0 overflow-y-auto">
					<div
						class="flex min-h-full items-center justify-center p-4 text-center"
					>
						<TransitionChild
							as="template"
							enter="duration-300 ease-out"
							enter-from="opacity-0 scale-95"
							enter-to="opacity-100 scale-100"
							leave="duration-200 ease-in"
							leave-from="opacity-100 scale-100"
							leave-to="opacity-0 scale-95"
						>
							<DialogPanel
								class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
							>
								<div
									class="border-b px-3 py-3 flex items-center justify-between"
								>
									<DialogTitle
										as="h3"
										class="text-lg flex text-red-500 items-center font-medium leading-6"
									>
										<ExclamationTriangleIcon class="h-5 w-5 mr-2" />
										Confirm delete
									</DialogTitle>

									<button @click="handleClick">
										<XMarkIcon class="w-5 h-5" />
									</button>
								</div>

								<FormKit
									id="deleteForm"
									type="form"
									submit-label="Update"
									@submit="deleteData"
									:actions="false"
									#default="{}"
								>
									<div class="mt-2 space-y-3 px-5">
										<div class="bg-red-50 rounded px-2 py-2">
											Do you really want to delete
											<span class="font-semibold text-red-500">{{
												props.data.name
											}}</span
											>? Note that once this action is completed, it can not be
											undone
										</div>

										<FormKit
											type="textarea"
											label="Reason"
											validation="required"
											v-model="reason"
										/>
									</div>

									<div
										class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
									>
										<CoreActionButton
											:loading="loading"
											type="submit"
											:click="() => {}"
											color="success"
											:icon="TrashIcon"
											text="Delete"
										/>
									</div>
								</FormKit>
							</DialogPanel>
						</TransitionChild>
					</div>
				</div>
			</Dialog>
		</TransitionRoot>
	</div>
</template>
