<template>
  <div>
    <div>
      <CoreActionButton
        text="Add Surveillance"
        color="primary"
        :icon="plusIcon"
        :click="loadTestTypes"
      />
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="closeForm" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-4xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-xl text-black flex items-center font-medium leading-6"
                  >
                    Surveillance Setup
                  </DialogTitle>

                  <button @click="adjustVisibility">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  submit-label="Save"
                  @submit="handleSubmitForm"
                  :actions="false"
                >
                  <div class="mt-2 space-y-3 px-5 py-5">
                    <div
                      class="grid grid-cols-5 gap-2"
                      v-for="(items, index) in surveillanceItems"
                      :key="index"
                    >
                      <div
                        class="w-full col-span-4 flex items-center space-x-3"
                      >
                        <div class="w-1/2">
                          <label for="">Test type</label>
                          <CoreDropdown
                            :items="testTypes"
                            :model-value="items.testType"
                          />
                        </div>
                        <div class="w-1/2">
                          <label for="">Disease</label>
                          <CoreDropdown
                            :items="diseases"
                            :model-value="items.diseases"
                          />
                        </div>
                        <div class="flex items-center space-x-3 pt-7">
                          <CoreActionButton
                            :icon="addIcon"
                            text="Add"
                            color="primary"
                            :click="
                              () => {
                                addItem();
                              }
                            "
                            type="button"
                          />
                          <CoreActionButton
                            :icon="removeIcon"
                            text="Remove"
                            color="error"
                            :click="
                              () => {
                                removeItem(index);
                              }
                            "
                            v-if="surveillanceItems.length > 1"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="mt-3 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton
                      type="button"
                      text="Close"
                      :click="closeForm"
                    />
                    <CoreActionButton
                      type="submit"
                      color="success"
                      :icon="saveIcon"
                      :click="() => {}"
                      text="Save"
                      :loading="saving"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  PlusIcon,
  ArrowDownTrayIcon
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response } from "@/types";

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
  },
  data() {
    return {
      open: false as boolean,
      loading: false as boolean,
	    saving: false as boolean,
      cookie: useCookie("token"),
      disabled: true as boolean,
      diseases: new Array<{ name: string; id: number }>(),
      testTypes: new Array<{ name: string; id: number }>(),
      selectedTestType: { name: "" },
      items: new Array<{ test_types_id: string; diseases_id: string }>(),
      plusIcon: PlusIcon as Object,
      saveIcon: ArrowDownTrayIcon,
      addIcon: PlusIcon,
      removeIcon: XMarkIcon,
      surveillanceItems: new Array<{
        testType: { name: string; id: number };
        diseases: { name: string; id: number };
      }>(),
    };
  },
  methods: {
    adjustVisibility(): void {
      this.open = !this.open;
    },
    addItem(): void {
      this.surveillanceItems.push({
        testType: this.testTypes[0],
        diseases: this.diseases[0],
      });
    },
    removeItem(index: number): void {
      this.surveillanceItems.splice(index, 1);
    },
    async loadDiseases(): Promise<Response> {
      this.loading = true;

      const request: Request = {
        route: endpoints.disease.index,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { data, error, pending }: Response = await fetchRequest(request);

      if (data.value) {
        this.diseases = data.value;
      }

      this.loading = false;

      if (error.value) {
        this.loading = false;
        useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
      }

      return { data, error, pending };
    },

    async loadTestTypes(): Promise<void> {
      this.adjustVisibility();

      const diseases: any = await this.loadDiseases();

      this.loading = true;

      const request: Request = {
        route: endpoints.testTypes,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { data, error, pending }: Response = await fetchRequest(request);

      if (data.value) {
        console.log(data.value);

        this.testTypes = data.value.test_types;
        this.surveillanceItems.push({
          testType: this.testTypes[0],
          diseases: diseases.data.value[0],
        });
      }

      this.loading = false;

      if (error.value) {
        this.loading = false;
        useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
      }
    },

    async handleSubmitForm(): Promise<void> {

      this.saving = true;

      const surveillance: { test_types_id: number; diseases_id: number }[] =
        this.surveillanceItems.map((item) => ({
          test_types_id: item.testType.id,
          diseases_id: item.diseases.id,
        }));

      const request: Request = {
        route: endpoints.surveillance.create,
        method: "POST",
        token: `${this.cookie}`,
        body: {
          surveillance: {
            data: surveillance,
          },
        },
      };

      const { data, pending, error } = await fetchRequest(request);

      this.saving = pending;

      if (data.value) {

        this.closeForm();

        useNuxtApp().$toast.success(`Surviellance add successfully!`);

        this.$emit("update", true);

		this.saving = false;
      }

      if (error.value) {
        useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);

        this.saving = false;
      }
    },

    closeForm(): void {
      this.open = false;
      this.items = [
        {
          test_types_id: "",
          diseases_id: "",
        },
      ];
    },
  },
};
</script>
