<template>
	<div>
		<div>
			<CoreActionButton text="New Disease" color="primary" :icon="addIcon" :click="handleClick" />
		</div>
		<TransitionRoot appear :show="open" as="template">
			<Dialog as="div" @close="handleClick" class="relative z-10">
				<TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
					leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
					<div class="fixed inset-0 bg-black bg-opacity-25"></div>
				</TransitionChild>

				<div class="fixed inset-0 overflow-y-auto">
					<div class="flex min-h-full items-center justify-center p-4 text-center">
						<TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
							enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
							leave-to="opacity-0 scale-95">
							<DialogPanel
								class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
								<div class="border-b px-3 py-3 flex items-center justify-between">
									<DialogTitle as="h3" class="text-xl text-black flex items-center font-medium leading-6">
										<img src="~assets/icons/virus.svg" class="w-8 h-8 mr-2" />
										Add Disease
									</DialogTitle>

									<button @click="handleClick">
										<XMarkIcon class="w-5 h-5" />
									</button>
								</div>

								<FormKit id="editForm" type="form" submit-label="Update" @submit="submitForm"
									:actions="false">
									<div class="mt-2 space-y-3 px-5 py-5">
										<div class="w-full grid grid-cols-1 gap-1">
											<FormKit type="text" label="Disease Name" v-model="name"
												validation="required|text" />
										</div>


									</div>
									<div class="mt-3 justify-end flex items-center space-x-3 px-3 py-2 border-t">
										<CoreOutlinedButton type="button" text="Clear form" :click="(() => clearForm())" />
										<CoreActionButton type="submit" color="success" :icon="saveIcon" :click="() => { }"
											text="Save changes" :loading="loading" />
									</div>
								</FormKit>
							</DialogPanel>
						</TransitionChild>
					</div>
				</div>
			</Dialog>
		</TransitionRoot>
	</div>
</template>


<script lang="ts">
import {
	TransitionRoot,
	TransitionChild,
	Dialog,
	DialogPanel,
	DialogTitle,
} from '@headlessui/vue';

import {
	PlusIcon,
	XMarkIcon,
	ArrowDownTrayIcon,
} from '@heroicons/vue/24/solid/index.js';
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import type {
	Request,
	Response,
} from '@/types';


export default {
	components: {
		TransitionRoot,
		TransitionChild,
		Dialog,
		DialogPanel,
		DialogTitle,
		XMarkIcon
	},
	data() {

		return {
			addIcon: PlusIcon as Object,
			saveIcon: ArrowDownTrayIcon as Object,
			open: false as boolean,
			loading: false as boolean,
			cookie: useCookie('token'),
			name: '' as string,
		}
	},
	methods: {
		async submitForm(): Promise<void> {

			this.loading = true;

			const request: Request = {
				route: endpoints.disease.create,
				method: 'POST',
				token: `${this.cookie}`,
				body: {
					disease: {
						data: [{ 'name': this.name }],
					},
				},
			};

			const { data, pending, error }: Response = await fetchRequest(request);

			this.loading = pending;

			if (data.value) {
				this.name = '';
				useNuxtApp().$toast.success(`Disease added successfully!`);
				this.$emit('action-completed', []);
				this.handleClick()
			}

			if (error.value) {
				useNuxtApp().$toast.error(`${error.value.data.error}`);
				this.loading = false;
				this.handleClick()
			}
		},
		handleClick(): void {
			this.open = !this.open;
		},
		clearForm(): void {
			this.$formkit.reset('editForm');
		}
	}
}
</script>
