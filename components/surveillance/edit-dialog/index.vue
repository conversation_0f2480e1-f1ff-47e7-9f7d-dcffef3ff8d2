<script setup lang="ts">
	import {
		TransitionRoot,
		TransitionChild,
		Dialog,
		DialogPanel,
		DialogTitle,
	} from '@headlessui/vue';

	import {
		XMarkIcon,
		ArrowDownTrayIcon,
		PencilSquareIcon,
		PlusIcon,
	} from '@heroicons/vue/24/solid/index.js';
	import { endpoints } from '@/services/endpoints';
	import fetchRequest from '@/services/fetch';
	import type { ISurveillanceItems, Request, Response } from '@/types';

	interface SelectOption {
		label: string;
		value: string | number;
	}

	const open: Ref<boolean> = ref<boolean>(false);
	const loading: Ref<boolean> = ref<boolean>(false);
	const cookie: Ref<string> = useCookie('token');
	const disabled: Ref<boolean> = ref<boolean>(true);
	const diseases: Ref<SelectOption[]> = ref<any>([]);
	const testTypes: Ref<SelectOption[]> = ref<any>([]);
	const selectedItem: Ref<{ test_type_id: number; disease_id: number }> =
		ref<any>({
			test_type_id: -1,
			disease_id: -1,
		});

	const items: Ref<ISurveillanceItems[] | Array<unknown>> = ref<
		ISurveillanceItems[]
	>([
		{
			test_types_id: '',
			diseases_id: '',
		},
	]);

	const props = defineProps<{
		id: number;
	}>();

	const emit = defineEmits(['action-completed']);

	const adjustVisibility = () => {
		loadDiseases();
		loadTestTypes();
		open.value = !open.value;
	};

	const loadDiseases = async (): Promise<void> => {
		loading.value = true;

		const request: Request = {
			route: endpoints.disease.index,
			method: 'GET',
			token: `${cookie.value}`,
		};

		const v: Response = await fetchRequest(request);

		if (v.data.value) {
			diseases.value = [];
			diseases.value.push({ label: '- Select from the list', value: '' });

			v.data.value.map((disease: { name: string; id: number }, index: any) =>
				diseases.value.push({
					label: disease.name,
					value: disease.id,
				})
			);
		}

		loading.value = false;

		if (v.error.value) {
			loading.value = false;
			useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
		}
	};

	const loadTestTypes = async (): Promise<void> => {
		loading.value = true;

		const request: Request = {
			route: endpoints.testTypes,
			method: 'GET',
			token: `${cookie.value}`,
		};

		const v: Response = await fetchRequest(request);

		if (v.data.value) {
			testTypes.value.push({ label: '- Select from the list', value: '' });

			v.data.value.test_types.map(
				(test: { name: string; id: number }, index: any) =>
					testTypes.value.push({
						label: test.name,
						value: test.id,
					})
			);
		}

		loading.value = false;

		if (v.error.value) {
			loading.value = false;
			useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
		}
	};

	const loadingInstrument = async (): Promise<void> => {
		loading.value = true;
		const request: Request = {
			route: `${endpoints.surveillance.edit}/${props.id}`,
			method: 'GET',
			token: `${cookie.value}`,
		};

		const v: Response = await fetchRequest(request);

		if (v.data.value) {
			selectedItem.value.test_type_id = v.data.value.test_types_id;
			selectedItem.value.disease_id = v.data.value.diseases_id;

			adjustVisibility();
		}

		loading.value = false;

		if (v.error.value) {
			loading.value = false;
			useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
		}
	};

	const handleSubmitForm = async (): Promise<void> => {
		loading.value = true;

		const request: Request = {
			route: endpoints.surveillance.update,
			method: 'PATCH',
			token: cookie.value,
			body: {
				surveillance: {
					data: [
						{
							diseases_id: selectedItem.value.disease_id,
							test_types_id: selectedItem.value.test_type_id,
						},
					],
				},
			},
		};

		const { data, pending, error } = await fetchRequest(request);

		loading.value = pending;

		if (data.value) {
			closeForm();
			useNuxtApp().$toast.success(`Surveillance add successfully!`);
			emit('action-completed', []);
		}

		if (error.value) {
			useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);

			loading.value = false;
		}
	};

	const closeForm = () => {
		open.value = loading.value = false;
		items.value = [
			{
				test_types_id: selectedItem.value.test_type_id,
				diseases_id: selectedItem.value.disease_id,
			},
		];
	};
</script>

<template>
	<div>
		<div>
			<CoreActionButton
				text="Edit"
				color="primary"
				:icon="PencilSquareIcon"
				:click="loadingInstrument"
			/>
		</div>
		<TransitionRoot appear :show="open" as="template">
			<Dialog as="div" @close="closeForm" class="relative z-10">
				<TransitionChild
					as="template"
					enter="duration-300 ease-out"
					enter-from="opacity-0"
					enter-to="opacity-100"
					leave="duration-200 ease-in"
					leave-from="opacity-100"
					leave-to="opacity-0"
				>
					<div class="fixed inset-0 bg-black bg-opacity-25" />
				</TransitionChild>

				<div class="fixed inset-0 overflow-y-auto">
					<div
						class="flex min-h-full items-center justify-center p-4 text-center"
					>
						<TransitionChild
							as="template"
							enter="duration-300 ease-out"
							enter-from="opacity-0 scale-95"
							enter-to="opacity-100 scale-100"
							leave="duration-200 ease-in"
							leave-from="opacity-100 scale-100"
							leave-to="opacity-0 scale-95"
						>
							<DialogPanel
								class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
							>
								<div
									class="border-b px-3 py-3 flex items-center justify-between"
								>
									<DialogTitle
										as="h3"
										class="text-xl text-black flex items-center font-medium leading-6"
									>
										{{ 'Edit Surveillance Details' }}
									</DialogTitle>

									<button @click="closeForm">
										<XMarkIcon class="w-5 h-5" />
									</button>
								</div>

								<FormKit
									type="form"
									submit-label="Save"
									@submit="handleSubmitForm"
									:actions="false"
								>
									<div class="mt-2 space-y-3 px-5 py-5">
										<div class="w-full grid grid-cols-1 gap-1">
											<div
												class="relative shadow-none border-2 p-3 mb-3 border-gray-100 text-left box-border space-x-4 grid grid-cols-4 rounded"
												v-for="(item, index) in items"
												:key="index"
											>
												<FormKit
													type="select"
													label="Test type"
													v-if="testTypes.length"
													v-model="selectedItem.test_type_id"
													placeholder="Select test type"
													validation="required"
													:options="testTypes"
													class="w-full"
												/>

												<FormKit
													type="select"
													label="Disease"
													v-if="diseases.length"
													v-model="selectedItem.disease_id"
													placeholder="Select disease"
													validation="required"
													:options="diseases"
												/>
											</div>
										</div>
									</div>

									<div
										class="mt-3 justify-end flex items-center space-x-3 px-3 py-2 border-t"
									>
										<CoreOutlinedButton
											type="button"
											text="Close"
											:click="closeForm"
										/>
										<CoreActionButton
											type="submit"
											color="success"
											:icon="ArrowDownTrayIcon"
											:click="() => {}"
											text="Save"
											:loading="loading"
										/>
									</div>
								</FormKit>
							</DialogPanel>
						</TransitionChild>
					</div>
				</div>
			</Dialog>
		</TransitionRoot>
	</div>
</template>
