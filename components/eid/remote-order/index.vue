<template>
    <div>
        <div class="flex items-center border rounded">

          <div class="border-r px-2 p-2 bg-gray-50">
            <QrCodeIcon class="w-5 h-5"/>
          </div>
        </div>

    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="changeVisibility" class="relative z-10" static>
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white shadow-xl transition-all"
              >
                <div class="bg-gray-50 border-b px-2 py-2 flex items-center justify-between">
                  <DialogTitle
                    as="h3"
                    class="text-lg font-medium leading-6 text-gray-900"
                  >
                    Verify EID Remote Order
                  </DialogTitle>
                  <button @click="changeVisibility" class="focus:outline-none">
                    <XMarkIcon class="w-5 h-5"/>
                  </button>
                </div>

                <div class="">
                  <div class="px-4 py-4 w-full grid grid-cols-2 gap-2">
                    <div class="space-y-2 flex flex-col items-start">
                      <label class="font-medium text-left">Surname</label>
                      <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                    </div>
                    <div class="space-y-2 flex flex-col items-start">
                      <label class="font-medium">First Name</label>
                      <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                    </div>
                    <div class="space-y-2 flex flex-col items-start">
                      <label class="font-medium">First Name</label>
                      <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                    </div>
                    <div class="space-y-2 flex flex-col items-start">
                      <label class="font-medium">First Name</label>
                      <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                    </div>
                    <div class="space-y-2 flex flex-col items-start">
                      <label class="font-medium">First Name</label>
                      <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                    </div>
                    <div class="space-y-2 flex flex-col items-start">
                      <label class="font-medium">First Name</label>
                      <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                    </div>
                    <div class="space-y-2 flex flex-col items-start">
                      <label class="font-medium">First Name</label>
                      <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                    </div>
                    <div class="space-y-2 flex flex-col items-start">
                      <label class="font-medium">First Name</label>
                      <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                    </div>
                    <div class="space-y-2 flex flex-col items-start">
                      <label class="font-medium">First Name</label>
                      <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                    </div>
                    <div class="space-y-2 flex flex-col items-start">
                      <label class="font-medium">First Name</label>
                      <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                    </div>
                  </div>

                  <div class="flex space-x-3 items-center justify-end mt-5 border-t px-3 py-2 bg-gray-50">
                    <CoreActionButton :text="'Save'" :icon="saveIcon" color="primary"/>
                    <CoreActionButton :text="'Save'" :icon="saveIcon" color="primary"/>
                    <CoreActionButton :text="'Save'" :icon="saveIcon" color="primary"/>
                  </div>

                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
</template>

<script lang="ts">

import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue'
import { ArrowDownOnSquareIcon, QrCodeIcon, XMarkIcon } from '@heroicons/vue/24/solid/index.js';

export default{
    components: {
      TransitionRoot,
      TransitionChild,
      Dialog,
      DialogPanel,
      DialogTitle,
      QrCodeIcon,
      XMarkIcon
    },

    data(){

      return{
        open: false,
        saveIcon: ArrowDownOnSquareIcon,
      }
    },
    methods: {
      changeVisibility(){
        this.open = !this.open
      },
      getOrder(value: boolean){
        this.open = value
      }
    }
}
</script>
