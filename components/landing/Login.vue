<template>
  <div class="my-20">
    <div class="mx-auto max-w-sm bg-white border rounded mt-10">
      <div>
        <div class="px-2 py-2 flex items-center justify-between">
          <div
            class="rounded bg-gray-100 py-1 px-2.5 border border-gray-50 text-sm flex items-center text-gray-500"
          >
            <img
              src="@/assets/icons/git-branch-outline.svg"
              class="w-5 h-5 text-red-500"
              alt="git-tag-icon/"
            />
            {{ version }} / {{ apiVersion }}
          </div>
        </div>

        <div class="px-16 py-5 flex flex-col items-center">
          <img
            src="@/assets/images/logo.png"
            alt="app-logo"
            class="w-28 h-28 object-cover"
          />

          <div
            class="mt-5 w-full text-3xl font-bold text-sky-500 text-center uppercase"
          >
            {{ appName }}
          </div>

          <h3 class="mt-3 text-xl font-medium text-center">{{ facility }}</h3>
        </div>
        <FormKit
          type="form"
          submit-label="Update"
          @submit="login"
          :actions="false"
          #default="{ value }"
        >
          <div class="space-y-2 px-5 py-3">
            <FormKit
              type="text"
              label="Username"
              v-model="username"
              validation="required"
            />
            <FormKit
              :type="showPassword ? 'text' : 'password'"
              label="Password"
              v-model="password"
              validation="required"
              suffix-icon="eyeClosed"
              @suffix-icon-click="togglePasswordVisibility"
              suffix-icon-class="hover:text-blue-500"
              :classes="{
                label: 'font-medium formkit-invalid:text-red-500 mb-4',
                input:
                  'w-full text-gray-600 border px-1.5 py-1.5 rounded focus:ring-none focus:outline-none mt-2',
                message: 'text-red-500 text-sm mt-1',
                outer: 'w-full',
                inner: 'relative',
                suffixIcon:
                  'w-6 h-6 absolute right-2 top-7 transform -translate-y-1/2 text-gray-400',
              }"
            />
          </div>

          <div class="space-y-2 px-5 mb-5">
            <label class="font-medium text-base"> Laboratory Section </label>
            <Listbox v-model="selectedDepartment">
              <div class="relative mt-1">
                <ListboxButton
                  class="relative w-full cursor-default rounded border py-2.5 pl-3 pr-10 text-left focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none sm:text-sm"
                >
                  <span class="block truncate">{{
                    selectedDepartment.name
                  }}</span>
                  <span
                    class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                  >
                    <ChevronUpDownIcon
                      class="h-5 w-5 text-gray-500"
                      aria-hidden="true"
                    />
                  </span>
                </ListboxButton>

                <transition
                  leave-active-class="transition duration-100 ease-in"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-2 text-base ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                  >
                    <ListboxOption
                      v-slot="{ active, selected }"
                      v-for="department in departments"
                      :key="department.name"
                      :value="department"
                      as="template"
                    >
                      <li
                        :class="[
                          active ? 'bg-gray-50' : 'text-gray-900',
                          'relative cursor-default select-none py-2 pl-10 pr-4',
                        ]"
                      >
                        <span
                          :class="[
                            selected ? 'font-medium' : 'font-normal',
                            'block truncate',
                          ]"
                          >{{ department.name }}</span
                        >
                        <span
                          v-if="selected"
                          class="absolute inset-y-0 left-0 flex items-center pl-3 text-sky-600"
                        >
                          <CheckIcon class="h-5 w-5" aria-hidden="true" />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
          </div>

          <div class="px-5 mb-5">
            <CoreButton
              color="primary"
              type="submit"
              text="Login"
              :loading="loading"
            />
          </div>
        </FormKit>
      </div>
    </div>

    <div class="mt-5 flex flex-col space-y-2 items-center justify-center">
      <p class="text-center text-gray-500">
        Malawi Ministry of Health &copy; {{ new Date().getFullYear() }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/vue/20/solid/index.js";
import { endpoints } from "@/services/endpoints";
import { useAuthStore } from "@/store/auth";
import {
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from "@headlessui/vue";

import { useFacilityStore } from "@/store/facility";
import type { Request, Response, Department } from "@/types";
import fetchRequest from "@/services/fetch";
import Package from "@/package.json";
import { useWardStore } from "@/store/ward";

const appName = ref<string>(Package.name);
const username = ref<string>("");
const password = ref<string>("");
const showPassword = ref<boolean>(false);
const loading: Ref<boolean> = ref<boolean>(false);
const facility = ref<string>("");
const version = ref<string>(Package.version);
const apiVersion = ref<string>("-:-");
const departments = ref<Array<Department>>(new Array<Department>());
const { start, finish } = useLoadingIndicator();
const selectedDepartment = ref<Department>({
  name: "select department",
  id: 0,
});

const emit = defineEmits(["connection"]);

interface FormKitNode {
  props: {
    suffixIcon: string;
    type: string;
  };
}

const togglePasswordVisibility = (node: FormKitNode, e: Event): void => {
  node.props.suffixIcon =
    node.props.suffixIcon === "eyeClosed" ? "eyeOpen" : "eyeClosed";
  node.props.type = node.props.type === "password" ? "text" : "password";
};
/**
 * @method globals load facility details
 * @param null
 * @returns promise
 */
async function globals(): Promise<void> {
  const { fetchFacility, details } = useFacilityStore();
  const request: Request = {
    route: endpoints.global,
    method: "GET",
  };
  const { data, error }: Response = await fetchRequest(request);
  if (error.value) {
    console.error(error.value);
  }
  if (data.value) {
    fetchFacility(data.value);
    facility.value = details.name;
  }
}

async function getAPIVersion(): Promise<void> {
  start();
  const request: Request = {
    route: `${endpoints.global}/current_api_tag`,
    method: "GET",
  };
  const { data, error }: Response = await fetchRequest(request);
  if (data.value) {
    apiVersion.value = data.value.git_tag;
    emit("connection", true);
    finish();
  }
  if (error.value) {
    console.error(error.value);
    emit("connection", false);
    finish();
  }
}
/**
 * @method init load departments data
 * @param null
 * @return promise @type void
 */
async function getDepartments(): Promise<void> {
  start();
  const request: Request = {
    route: endpoints.departments,
    method: "GET",
  };
  const { data, error }: Response = await fetchRequest(request);
  if (data.value) {
    departments.value = data.value;
    finish();
  }
  if (error.value) {
    console.error(error.value);
    finish();
  }
}
/**
 * @method login calls auth from store
 * @param null
 * @returns promise @type void
 */
async function login(): Promise<void> {
  start();
  const { authenticateUser, removeUser } = useAuthStore();
  const { removeSelectedWard } = useWardStore();
  removeUser();
  removeSelectedWard();
  let user = {
    username: username.value,
    password: password.value,
    department: selectedDepartment.value.name,
  };
  if (selectedDepartment.value.name !== "select department") {
    loading.value = true;
    await authenticateUser(user);
    loading.value = false;
    finish();
  } else {
    useNuxtApp().$toast.warning("Please select a department!");
    finish();
  }
}

onMounted(async () => {
  await getAPIVersion();
  await getDepartments();
  await globals();
});
</script>

<style></style>
