<template>
  <div v-show="loading" class="py-3 px-3">
    <div
      :class="{
        'grid grid-cols-3 gap-3': isGrid,
        'flex flex-col gap-3': !isGrid,
      }"
    >
      <div
        v-for="i in 6"
        :key="i"
        :class="{
          'w-full col-span-1 h-8 bg-gray-100 rounded animate-pulse': isGrid,
          'w-full h-8 bg-gray-100 rounded animate-pulse': !isGrid,
        }"
      ></div>
    </div>
    <div class="w-full h-32 bg-gray-100 rounded animate-pulse mt-3"></div>
    <div class="w-32 h-8 bg-gray-100 rounded animate-pulse mt-3"></div>
  </div>
</template>
<script lang="ts">
export default {
  props: {
    loading: {
      required: true,
      type: Boolean,
    },
    isGrid: {
      required: true,
      type: Boolean,
    },
  },
};
</script>
