<template>
    <div class="flex flex-col space-y-2.5">
        <div class="w-full bg-gray-100 animate-pulse h-10 rounded"></div>
        <div class="w-1/2 bg-gray-100 animate-pulse h-10 rounded"></div>
        <div class="w-1/3 bg-gray-100 animate-pulse h-10 rounded"></div>
        <div class="flex items-center space-x-2.5" v-for="i in 12" :key="i">
            <div class="w-1/2 bg-gray-100 animate-pulse h-10 rounded"></div>
            <div class="w-1/2 bg-gray-100 animate-pulse h-10 rounded"></div>
        </div>
        <div class="w-48 flex items-center justify-end bg-gray-100 animate-pulse h-10 rounded"></div>
    </div>
</template>
<script>
export default {

}
</script>
<style>

</style>
