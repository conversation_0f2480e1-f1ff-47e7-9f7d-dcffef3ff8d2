<template>
    <Pie :data="data" :options="options" />
</template>

<script lang="ts">

import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js'
import { Pie } from 'vue-chartjs'
import type { PieData } from '../../../types'

ChartJS.register(ArcElement, Tooltip, Legend)

export default {
    components: {
        Pie
    },
    props: {
        chartData: {
            required: true,
            type: Object
        }
    },
    data() {
        return {
            data: this.chartData as PieData,
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        }
    }
}
</script>
