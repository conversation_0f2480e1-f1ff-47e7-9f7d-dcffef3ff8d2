<template>
    <Line :data="data" :options="options" />
</template>

<script lang="ts">
import {
    Chart as ChartJS,
    Title,
    Tooltip,
    Legend,
    LineElement,
    CategoryScale,
    PointElement,
    LinearScale
} from 'chart.js'
import { Line } from 'vue-chartjs'
import type { PieData } from '../../../types'

ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend
)

export default {
    components: {
        Line
    },
    props: {
        chartData: {
            required: true,
            type: Object
        }
    },
    data() {
        return {
            data: this.chartData as PieData,
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        }
    },
    watch: {
        chartData: {
            handler(newValue) {
                this.data = newValue
            },
            deep: true
        }
    }
}
</script>
