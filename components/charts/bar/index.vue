<template>
    <Bar :data="data" :options="options" />
</template>

<script lang="ts">
import {
    Chart as ChartJS,
    Title,
    Tooltip,
    Legend,
    BarElement,
    CategoryScale,
    LinearScale
} from 'chart.js'
import { Bar } from 'vue-chartjs'
import type { PieData } from '../../../types'

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

export default {
    components: {
        Bar
    },
    props: {
        chartData: {
            required: true,
            type: Object
        }
    },
    data() {
        return {
            data: this.chartData as PieData,
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        }
    },
    watch: {
        chartData: {
            handler(newValue){
                this.data = newValue
            },
            deep: true
        }
    }
}
</script>
