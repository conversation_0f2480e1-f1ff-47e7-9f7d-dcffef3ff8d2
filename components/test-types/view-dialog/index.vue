<template>
  <div>
    <CoreActionButton :click="init" color="primary" text="View" :icon="viewIcon" />

    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
          leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-black bg-opacity-50"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95">
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
                <div class="border-b px-3 py-3 flex items-center justify-between">
                  <DialogTitle as="h3" class="text-2xl flex items-center font-semibold leading-6">
                    <svg class="w-12 h-12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
                      <path fill="currentColor" fill-rule="evenodd"
                        d="M31 10a1 1 0 1 0 0 2v7h-3v-7a1 1 0 1 0 0-2h-8a1 1 0 1 0 0 2v7h-3v-7a1 1 0 1 0 0-2H9a1 1 0 1 0 0 2v7H7v-3H5v3H4v2h1v13H4v2h1v2h2v-2h34v2h2v-2h1v-2h-1V21h1v-2h-1v-3h-2v3h-2v-7a1 1 0 1 0 0-2zm6 9v-7h-4v7zm-4 2h4v5h-4zm-2 0v8a4 4 0 0 0 8 0v-8h2v13H7V21h2v8a4 4 0 0 0 8 0v-8h3v8a4 4 0 0 0 8 0v-8zm-5-2v-7h-4v7zm-11 0v-7h-4v7zm-4 2h4v2h-4z"
                        clip-rule="evenodd" />
                    </svg>
                    {{ details?.name }}
                  </DialogTitle>

                  <button @click="
                    () => {
                      handleDialog();
                    }
                  ">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <div v-show="loading" class="flex items-center justify-center mx-auto my-20">
                  <CoreLoader :loading="loading" />
                </div>

                <div v-show="!loading" class="space-y-3 px-5 py-5">
                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Preferred name</label>
                    <p class="text-gray-600">
                      {{
                        details?.preferred_name || "No preferred name available"
                      }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Short Name</label>
                    <p class="text-gray-600">
                      {{ details?.short_name || "No short name available" }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Description</label>
                    <div class="text-gray-600" v-html="details?.description || 'No description available'">
                    </div>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Laboratory Section</label>
                    <p class="text-gray-600">
                      {{
                        details?.department?.name ||
                        "No laboratory section assigned"
                      }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">NLIMS Code</label>
                    <p class="text-gray-600">
                      {{
                        details?.nlims_code ||
                        "No nlims code assigned"
                      }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Specific Sex</label>
                    <div class="flex items-center gap-2">
                      <template v-if="String(details?.sex).toLowerCase() === 'male'">
                        <svg class="w-5 h-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                          <path fill="currentColor"
                            d="M12 2a2 2 0 0 0-.5 3.929V8.5h-3a.5.5 0 0 0 0 1h3v2.571a5 5 0 1 0 1 0V9.5h3a.5.5 0 0 0 0-1h-3V5.929A2 2 0 0 0 12 2M12 3a1 1 0 1 1 0 2a1 1 0 0 1 0-2m0 16a4 4 0 1 1 0-8a4 4 0 0 1 0 8" />
                        </svg>
                        <p class="text-gray-600">Male</p>
                      </template>
                      <template v-else-if="String(details?.sex).toLowerCase() === 'female'">
                        <svg class="w-5 h-5 text-pink-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                          <path fill="currentColor"
                            d="M12 2a5 5 0 0 0-5 5c0 2.222 1.457 4.108 3.5 4.74V14h-3a.5.5 0 0 0 0 1h3v2.5a.5.5 0 0 0 1 0V15h3a.5.5 0 0 0 0-1h-3v-2.26c2.043-.631 3.5-2.518 3.5-4.74a5 5 0 0 0-5-5m0 1a4 4 0 1 1 0 8a4 4 0 0 1 0-8" />
                        </svg>
                        <p class="text-gray-600">Female</p>
                      </template>
                      <template v-else-if="String(details?.sex).toLowerCase() === 'both'">
                        <div class="flex items-center gap-1">
                          <svg class="w-5 h-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path fill="currentColor"
                              d="M12 2a2 2 0 0 0-.5 3.929V8.5h-3a.5.5 0 0 0 0 1h3v2.571a5 5 0 1 0 1 0V9.5h3a.5.5 0 0 0 0-1h-3V5.929A2 2 0 0 0 12 2M12 3a1 1 0 1 1 0 2a1 1 0 0 1 0-2m0 16a4 4 0 1 1 0-8a4 4 0 0 1 0 8" />
                          </svg>
                          <svg class="w-5 h-5 text-pink-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path fill="currentColor"
                              d="M12 2a5 5 0 0 0-5 5c0 2.222 1.457 4.108 3.5 4.74V14h-3a.5.5 0 0 0 0 1h3v2.5a.5.5 0 0 0 1 0V15h3a.5.5 0 0 0 0-1h-3v-2.26c2.043-.631 3.5-2.518 3.5-4.74a5 5 0 0 0-5-5m0 1a4 4 0 1 1 0 8a4 4 0 0 1 0-8" />
                          </svg>
                          <p class="text-gray-600">Both</p>
                        </div>
                      </template>
                      <template v-else>
                        <svg class="w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                          <path fill="currentColor"
                            d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10s10-4.486 10-10S17.514 2 12 2m0 2c4.411 0 8 3.589 8 8s-3.589 8-8 8s-8-3.589-8-8s3.589-8 8-8m-1 3v2h2V7zm0 4v6h2v-6z" />
                        </svg>
                        <p class="text-gray-600">Not specified</p>
                      </template>
                    </div>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Compatible Specimen</label>
                    <div v-if="details.specimens && details.specimens.length > 0" class="flex flex-wrap gap-2">
                      <div v-for="i in details.specimens" :key="i"
                        class="flex items-center gap-1 bg-gray-100 rounded px-2 py-1">
                        <svg class="w-5 h-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" width="256" height="256"
                          viewBox="0 0 256 256">
                          <g fill="currentColor">
                            <path
                              d="M52.3 160h89l-38.62 38.63a32 32 0 0 1-32.06 8l-20 8.74a8 8 0 0 1-8.86-1.67a5.74 5.74 0 0 1-1.2-6.36l9.19-21.06A32.07 32.07 0 0 1 52.3 160M207.23 47.51c-11.07-10.49-28.65-9.83-39.44 1l-25 25.1l-4.89-4.88a16 16 0 0 0-22.63 0l-9 9a8 8 0 0 0 0 11.31L167 149.66a8 8 0 0 0 11.31 0l9-9a16 16 0 0 0 0-22.63l-4.88-4.89l25.37-25.48a28 28 0 0 0-.57-40.15"
                              opacity="0.2" />
                            <path
                              d="M224 67.3a35.8 35.8 0 0 0-11.26-25.66c-14-13.28-36.72-12.78-50.62 1.13L142.8 62.2a24 24 0 0 0-33.14.77l-9 9a16 16 0 0 0 0 22.64l2 2.06l-51 51a39.75 39.75 0 0 0-10.53 38l-8 18.41A13.65 13.65 0 0 0 36 219.29a15.9 15.9 0 0 0 17.71 3.36L71.24 215a39.9 39.9 0 0 0 37.05-10.75l51-51l2.06 2.06a16 16 0 0 0 22.62 0l9-9a24 24 0 0 0 .74-33.18l19.75-19.87A35.75 35.75 0 0 0 224 67.3M97 193a24 24 0 0 1-24 6a8 8 0 0 0-5.55.31l-18.1 7.9l7.65-17.8a8 8 0 0 0 .25-5.75a24 24 0 0 1 .1-15.69H122Zm41-41H70.07l44-44l33.94 34Zm64.18-70l-25.37 25.52a8 8 0 0 0 0 11.31l4.89 4.88a8 8 0 0 1 0 11.32l-9 9L112 83.26l9-9a8 8 0 0 1 11.31 0l4.89 4.89a8 8 0 0 0 5.65 2.34a8 8 0 0 0 5.66-2.36l24.94-25.09c7.81-7.82 20.5-8.18 28.29-.81a20 20 0 0 1 .39 28.7Z" />
                          </g>
                        </svg>
                        <span class="text-gray-600">{{ i.name }}</span>
                      </div>
                    </div>
                    <div v-else class="flex items-center gap-1 text-gray-500">
                      <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="32" height="32"
                        viewBox="0 0 32 32">
                        <path fill="currentColor"
                          d="M21 30H11c-5 0-9-4-9-9V11c0-5 4-9 9-9h10c5 0 9 4 9 9v10c0 5-4 9-9 9M11 4c-3.9 0-7 3.1-7 7v10c0 3.9 3.1 7 7 7h10c3.9 0 7-3.1 7-7V11c0-3.9-3.1-7-7-7zm-1 18v-1.823h1.582v-8.358H10V9.997h5.451v1.822h-1.599v8.358h1.6V22zm8.09-2.287v-4.949h-1.324V13.01h.688c.654 0 .86-.31.86-.928v-1.517h1.978v2.445h1.84v1.754h-1.84v5.482h1.702V22h-1.582c-1.513 0-2.321-.825-2.321-2.287" />
                      </svg>
                      <span class="text-gray-600">No compatible specimens found</span>
                    </div>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Organisms</label>
                    <div v-if="details.organisms && details.organisms.length > 0" class="flex flex-wrap gap-2">
                      <div v-for="i in details.organisms" :key="i"
                        class="flex items-center gap-1 bg-gray-100 rounded-md px-2 py-1">
                        <svg class="w-5 h-5 text-green-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                          <path fill="currentColor"
                            d="M19 15v-3a3 3 0 0 0-3-3h-.17l.22-.77a1 1 0 0 0-.12-.9a1 1 0 0 0-.8-.43c-1.48 0-2.51.86-3.13 2.1a3.44 3.44 0 0 0-3.62.58a5.34 5.34 0 0 0-1.93 2.44a1 1 0 0 0 1.93.56c.15-.54.69-1.77 1.52-1.77h1a1 1 0 0 1 1 1h-3a1 1 0 0 0-1 1v4a3 3 0 0 0 3 3a2.25 2.25 0 0 0 2.14-1.54A2.25 2.25 0 0 0 16 20h2a1 1 0 0 0 1-1v-3a1 1 0 0 0 0-1zm-4-4h2a1 1 0 0 1 1 1v2h-3zm-3 10a1 1 0 0 1-1-1v-3h2v2a2 2 0 0 0 .31 1zm5 0h-2.14a2.15 2.15 0 0 0 .14-.69V15h2z" />
                        </svg>
                        <span class="text-gray-600">{{ i.name }}</span>
                      </div>
                    </div>
                    <div v-else class="flex items-center gap-1 text-gray-500">
                      <span class="text-gray-600">No organisms associated with this test</span>
                    </div>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Measure</label>
                    <div v-if="details.indicators && details.indicators.length > 0" class="flex flex-wrap gap-2">
                      <div v-for="(i, index) in details.indicators" :key="i"
                        class="flex items-center gap-1 bg-gray-100 rounded px-2 py-1 group relative cursor-pointer">
                        <svg class="w-5 h-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" width="48" height="48"
                          viewBox="0 0 48 48">
                          <path fill="currentColor" fill-rule="evenodd"
                            d="M22.02 7.876c1.114-.068 1.936-.866 1.968-1.982a32 32 0 0 0 0-1.788c-.032-1.116-.854-1.914-1.969-1.982C20.96 2.058 19.356 2 17 2s-3.96.058-5.02.124c-1.114.068-1.936.866-1.968 1.982a32 32 0 0 0 0 1.788c.032 1.116.854 1.914 1.969 1.982l2.2.091S14 15.774 14 24s.18 16.033.18 16.033l-2.2.09c-1.114.07-1.936.867-1.968 1.983a32 32 0 0 0 0 1.788c.032 1.116.854 1.913 1.969 1.982C13.04 45.942 14.646 46 17 46s3.96-.058 5.02-.124c1.114-.069 1.936-.866 1.968-1.982a32 32 0 0 0 0-1.788c-.032-1.116-.854-1.913-1.969-1.982l-2.2-.091S20 32.226 20 24s-.18-16.033-.18-16.033zm2.954 8.353a2 2 0 1 1 .006-4c7.964.012 13.296.168 16.371.297c2.777.116 5.348 2.002 5.761 5.045c.214 1.574.388 3.697.388 6.43s-.174 4.855-.388 6.429c-.413 3.044-2.984 4.929-5.76 5.045c-3.076.13-8.408.286-16.372.297a2 2 0 1 1-.006-4c7.915-.011 13.192-.166 16.21-.293c1.14-.048 1.85-.746 1.964-1.586c.188-1.387.352-3.33.352-5.893s-.164-4.505-.352-5.892c-.114-.84-.825-1.538-1.965-1.586c-3.017-.127-8.294-.282-16.209-.294m-15.696-3.8a2 2 0 1 1 .126 3.999q-1.492.047-2.583.094c-1.143.048-1.856.748-1.97 1.587C4.665 19.495 4.5 21.439 4.5 24c0 2.563.164 4.506.352 5.892c.113.839.826 1.54 1.969 1.587q1.09.046 2.583.094a2 2 0 1 1-.126 3.998q-1.513-.048-2.625-.095c-2.774-.117-5.352-2-5.765-5.046C.675 28.856.5 26.733.5 24s.175-4.855.388-6.429c.413-3.046 2.991-4.93 5.765-5.046q1.112-.046 2.625-.095"
                            clip-rule="evenodd" />
                        </svg>
                        <span class="text-gray-600">{{ i.name }}</span>
                        <div :class="[
                          'opacity-0 group-hover:opacity-100 transition-opacity rounded bg-black text-white text-sm py-2 px-3 absolute z-[99999] -top-12 pointer-events-none w-max max-w-xs text-center tooltip',
                          index === 0 ? 'left-0' : index === details.indicators.length - 1 ? 'right-0' : 'left-1/2 -translate-x-1/2'
                        ]">
                          Mapping ID: {{ i.id || 'Not available' }}<br>NLIMS Code: {{ i.nlims_code }}
                        </div>
                      </div>
                    </div>
                    <div v-else class="flex items-center gap-1 text-gray-500">
                      <span class="text-gray-600">No measures associated with this test</span>
                    </div>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Expected Turn Around Time</label>
                    <p class="text-gray-600">
                      {{
                        details.expected_turn_around_time?.value &&
                          details.expected_turn_around_time?.unit
                          ? `${details.expected_turn_around_time.value} ${details.expected_turn_around_time.unit}`
                          : "Turn around time not defined"
                      }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Date Created</label>
                    <p class="text-gray-600">
                      {{
                        details.created_date
                          ? moment(details.created_date).format(DATE_FORMAT)
                          : "Date not available"
                      }}
                    </p>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts" setup>
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";
import {
  XMarkIcon,
  ArrowTopRightOnSquareIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import moment from "moment";
import type { Request, Response, TestType } from "@/types";
import fetchRequest from "@/services/fetch";
import type { PropType } from "vue";

const props = defineProps({
  data: {
    type: Object as PropType<TestType>,
    required: true,
  },
});

const viewIcon = ArrowTopRightOnSquareIcon;
const show = ref(false);
const loading = ref(false);
const details = ref<TestType | any>({});

const init = async (): Promise<void> => {
  handleDialog();
  loading.value = true;
  const request: Request = {
    route: `${endpoints.viewTestType}/${props.data.id}`,
    method: "GET",
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;
  if (data.value) {
    details.value = data.value;
    loading.value = false;
  }
  if (error.value) {
    loading.value = false;
    useNuxtApp().$toast.error(`An error occurred, please try again!`);
  }
};

const handleDialog = (): void => {
  show.value = !show.value;
};
</script>

<style>
.tooltip {
  left: 50%;
  transform: translateX(-80%);
}

@media screen and (min-width: 768px) {
  .group:hover .tooltip {
    max-width: 90vw;
  }

  .group:nth-last-child(-n+3) .tooltip {
    left: auto;
    right: 0;
    transform: translateX(0);
  }

  .group:nth-child(-n+3) .tooltip {
    left: 0;
    transform: translateX(0);
  }
}
</style>
