<template>
  <div>
    <div>
      <CoreActionButton
        :click="() => init()"
        text="View"
        color="primary"
        :icon="viewIcon"
      />
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="() => {}" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-2xl flex items-center font-medium leading-6"
                  >
                    <svg
                      class="w-12 h-12 mr-2"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                    >
                      <path
                        fill="currentColor"
                        d="m22.7 2.14l-11.98.03v20H1.37v.76H22.7Zm-7.604 2.158h3.065l-.006 4.234h-3.072zm.125.163v3.908h2.789V4.461zm.119.129h2.55v3.6h-2.55Zm-.285 5.25l3.127.007l.007 4.309h-3.147zm.166.21v3.91h2.789v-3.91zm.119.14h2.55v3.6h-2.55zm-.305 5.563l3.188.007l-.04 4.325l-3.169-.014zm.186.218v3.91h2.789v-3.91zm.119.138h2.55v3.602h-2.55z"
                      />
                    </svg>
                    {{ details?.name }}
                  </DialogTitle>

                  <button @click="() => handleClick()">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <div
                  v-show="loading"
                  class="flex items-center justify-center mx-auto my-20"
                >
                  <CoreLoader :loading="loading" />
                </div>

                <div v-show="!loading" class="space-y-3 px-5 py-5">
                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Preferred name</label>
                    <p class="text-gray-600">
                      {{ details.preferred_name || "Not specified" }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Short Name</label>
                    <p class="text-gray-600">
                      {{ details.short_name || "Not specified" }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Description</label>
                    <div
                      class="text-gray-600"
                      v-html="details.description"
                    ></div>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold list text-lg"
                      >Test Types ({{ details.test_types?.length || 0 }})</label
                    >

                    <div
                      class="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-2 max-h-60 overflow-y-auto"
                    >
                      <div
                        v-for="test in details.test_types"
                        :key="test.id"
                        class="flex items-center p-2 rounded bg-gray-50"
                      >
                        <svg
                          class="w-5 h-5 text-black mr-2 flex-shrink-0"
                          xmlns="http://www.w3.org/2000/svg"
                          width="512"
                          height="512"
                          viewBox="0 0 512 512"
                        >
                          <path
                            fill="currentColor"
                            d="M276.95 18.752c-17.61.005-29.2 5.172-33.776 13.1c-5.232 9.06-2.762 24.25 9.775 42.494c12.536 18.243 34.302 38.05 61.864 53.963c27.562 15.91 55.6 24.856 77.666 26.592c22.068 1.736 36.456-3.72 41.688-12.78c5.23-9.06 2.762-24.25-9.775-42.493s-34.303-38.05-61.866-53.964c-27.562-15.913-55.598-24.858-77.666-26.594a101 101 0 0 0-7.91-.32zm9.818 21.453c16.105.134 40.723 8.224 65.804 22.705c38.22 22.067 63.046 50.616 55.453 63.768s-44.732 5.925-82.95-16.14c-38.22-22.068-63.047-50.618-55.454-63.77c2.61-4.52 8.71-6.633 17.148-6.563zm-50.784 42.352L79.594 392.385c-10.137 17.762-10.692 36.284-4.504 51.6c6.224 15.41 18.583 27.613 33.222 35.6c14.64 7.99 31.752 11.89 48.39 9.743c16.64-2.145 32.87-10.827 43.554-27.033l.01-.018L388.914 173.33c-6.485-.61-13.232-1.71-20.172-3.29l-32.846 50.308c-.272-.25-.55-.5-.878-.77c-3.27-2.697-8.986-5.776-16.44-8.377c-14.908-5.2-36.63-8.684-60.63-8.684c-23.997 0-45.72 3.484-60.628 8.685a81 81 0 0 0-6.683 2.667l57.967-114.84c-4.098-4.665-7.81-9.377-11.055-14.097c-.542-.788-1.047-1.582-1.566-2.373zM415.03 184.553l-8.794 33.5c-7.48 28.495-19.135 51.737-29.22 71.646s-19.258 36.267-19.14 53.5c.217 31.9 26.61 57.75 58.634 57.505l-.008.002c32.01-.217 58.057-26.384 57.836-58.29c-.076-11.126-4-21.653-9.54-32.974c-.62-1.593-1.43-3.186-2.41-4.797c-2.39-4.645-4.986-9.447-7.656-14.505c-10.25-19.42-22.206-42.452-30.453-72.21zm-232.85 46.07c3.385 2.44 8.59 5.096 15.14 7.38c14.908 5.202 36.63 8.685 60.63 8.685c23.998 0 45.72-3.483 60.628-8.684a79 79 0 0 0 7.893-3.22l-16.365 25.068c-15.16 3.556-32.977 5.53-52.156 5.53c-25.762 0-49.088-3.553-66.788-9.728c-6.642-2.317-12.488-4.99-17.47-8.215l8.488-16.817zm233.242 19.498c.32.83.65 1.62.973 2.437c-1.073 34.75-13.116 59.906-8.944 75.015c4.384 15.93 20.963 25.358 36.974 20.852a30.15 30.15 0 0 0 11.226-5.83c.126 21.712-17.307 39.275-39.275 39.424h-.007c-21.97.167-39.654-17.217-39.8-38.944v-.002c-.067-9.577 7.017-24.98 17.12-44.927c6.888-13.598 14.798-29.615 21.735-48.024zm-191.04 29.74c9.492 0 17.186 7.697 17.186 17.19c0 9.49-7.694 17.184-17.185 17.184s-17.186-7.694-17.186-17.185c0-9.493 7.695-17.19 17.186-17.19zm-54.35 13.44c12.148 0 21.997 9.85 21.997 22s-9.85 21.997-22 21.997c-12.147 0-21.997-9.848-21.997-21.996c0-12.15 9.85-22 21.998-22zm22.007 57.81c13.287 0 24.058 10.775 24.058 24.064c0 13.287-10.77 24.058-24.06 24.058c-13.286 0-24.06-10.77-24.06-24.058c0-13.29 10.774-24.063 24.06-24.063z"
                          />
                        </svg>
                        <span
                          class="text-gray-700 truncate font-medium"
                          :title="test.name"
                          >{{ test.name }}</span
                        >
                      </div>
                    </div>

                    <p
                      v-if="!details.test_types?.length"
                      class="text-gray-500 italic text-sm"
                    >
                      No test types associated with this panel
                    </p>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  ArrowTopRightOnSquareIcon,
  XMarkIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Response, TestPanel } from "@/types";

const props = defineProps({
  data: {
    required: true,
    type: Object,
  },
});

const open = ref(false);
const viewIcon = ArrowTopRightOnSquareIcon;
const loading = ref(false);
const details = ref<TestPanel>({} as TestPanel);
const nuxtApp = useNuxtApp();

const init = async (): Promise<void> => {
  open.value = true;
  loading.value = true;

  const { pending, error, data }: Response = await fetchRequest({
    route: `${endpoints.testPanels}/${props.data.id}`,
    method: "GET",
  });

  loading.value = pending;

  if (data.value) {
    details.value = data.value;
    loading.value = false;
  }

  if (error.value) {
    console.error(error.value);
    loading.value = false;
    nuxtApp.$toast.error(`An error occurred, please try again!`);
  }
};

const handleClick = () => {
  open.value = !open.value;
};
</script>
