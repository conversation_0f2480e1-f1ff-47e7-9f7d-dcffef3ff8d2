<template>
  <div>
    <div>
      <CoreActionButton :click="init" text="Edit" color="success" :icon="editIcon"/>
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleClick" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >

                <div class="border-b px-3 py-3 flex items-center justify-between">

                  <DialogTitle
                  as="h3"
                  class="text-lg flex items-center font-medium leading-6"
                  >
                    Edit Test Panel
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5"/>
                  </button>

                </div>

                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  #default="{ value }"
                  id="submitForm"
                >

                  <div class="mt-2 space-y-3 px-5">

                    <div class="w-full flex">
                      <FormKit
                        type="text"
                        label="Name"
                        validation="required"
                        v-model="data.name"
                      />
                    </div>

                    <div class="w-full flex">
                      <FormKit
                        type="text"
                        label="Short Name"
                        validation="required"
                        v-model="data.short_name"
                      />
                    </div>

                    <div class="w-full flex">
                      <FormKit
                        type="textarea"
                        label="Description"
                        validation="required"
                        v-model="data.description"
                      />
                    </div>

                    <div class="h-72 w-full flex flex-col space-y-2">
                      <label class="font-medium">Select Test Types</label>
                      <multi-select
                        style="--ms-max-height: none !important;"
                        v-model="testTypesSelected"
                        :options="testTypes"
                        mode="tags"
                        clear
                        :searchable="true"
                        :required="true"
                        class="outline-none focus:outline-none multiselect-green"
                      />
                    </div>


                  </div>

                  <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                    <CoreOutlinedButton text="Clear form" type="button" :click="(() => {clearForm()})"/>
                    <CoreActionButton type="submit" :click="(() => {})" :loading="loading" :icon="saveIcon" text="Save changes" color="success"/>
                  </div>

                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">

import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue'

import { PencilSquareIcon, XMarkIcon, UserIcon, ArrowDownTrayIcon } from '@heroicons/vue/24/solid/index.js'
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import type { Response, Request } from '@/types';

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
    UserIcon
  },
  props: {
    data: {
      required: true,
      type: Object
    }
  },
  data(){

    return{
      open: false as boolean,
      editIcon: PencilSquareIcon,
      saveIcon: ArrowDownTrayIcon,
      name: "" as string,
      shortName: "" as string,
      description: "" as string,
      testTypes: new Array<string>() as Array<string>,
      testTypesSelected: new Array<string>(),
      rawTestTypes: new Array<Object>(),
      loading: false as boolean,
      cookie:  useCookie('token')
    }
  },
  methods:{
    async loadTestTypes() : Promise<void> {
      const request : Request = {
        route: endpoints.testTypes,
        method: 'GET',
        token: `${this.cookie}`
      }
      const { data, error } : Response = await fetchRequest(request);
      if(data.value){
        this.rawTestTypes = data.value.test_types;
        data.value.test_types.map((test: { name: string}) => {
          this.testTypes.push(test.name)
        })
      };

      if(error.value){
        console.error(error.value)
      };

    },
    async init() : Promise<void> {
      this.open = true;
      await this.loadTestTypes();
      this.testTypesSelected = new Array<string>();
      const request : Request = {
        route: `${endpoints.testPanels}/${this.data.id}`,
        method: 'GET',
        token: `${this.cookie}`,
      }
      const { data, error } : Response = await fetchRequest(request);

      if(data.value){
        data.value.test_types.map((test : { name: string }) => {
          this.testTypesSelected.push(test.name)
        });
      }
      if(error.value){
        console.error(error.value)
      }
    },
    /**
     * @method submitForm updates test panel data
     * @params null
     * @returns promise @type void
     */
    async submitForm() : Promise<void> {

      this.loading = true;
      let testTypesId = new Array<number>()

      this.testTypesSelected.map((name: string) => {
        this.rawTestTypes.filter((item: any) => { name == item.name && testTypesId.push(item.id) })
      });

      this.data.test_types = testTypesId;

      const request : Request = {
        route: `${endpoints.testPanels}/${this.data.id}`,
        method: 'PUT',
        token: `${this.cookie}`,
        body: this.data
      }

      const { data, error, pending } : Response = await fetchRequest(request);
      this.loading = pending;

      if(data.value){
        this.open = false;
        useNuxtApp().$toast.success(`Test panel updated successfully!`);
        this.$emit('update', true);

      }

      if(error.value){
        console.error(error.value)
        useNuxtApp().$toast.error(ERROR_MESSAGE);
      }
    },
    handleClick() : void {
      this.open = !this.open
    },
    clearForm () : void {
      this.$formkit.reset('submitForm');
    }
  }
}
</script>

<style>

</style>
