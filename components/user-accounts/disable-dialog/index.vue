<template>
  <div>
    <div>
      <div v-if="data.is_active">
        <CoreActionButton
          :click="init"
          color="error"
          text="Disable"
          :icon="disableIcon"
        />
      </div>
      <div v-if="!data.is_active">
        <CoreActionButton
          :click="init"
          color="success"
          text="Enable"
          :icon="disableIcon"
        />
      </div>
    </div>

    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-lg flex items-center font-medium leading-6"
                  >
                    <ExclamationTriangleIcon class="h-5 w-5 mr-2" />
                    {{
                      data.is_active ? "Confirm disable" : "Confirm activate"
                    }}
                  </DialogTitle>

                  <button @click="handleDialog">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="
                    data.is_active ? deleteData(data.id) : activate(data.id)
                  "
                  :actions="false"
                  #default="{ value }"
                >
                  <div class="mt-2 space-y-3 px-5">
                    <div class="rounded px-2 py-2">
                      Do you want to
                      {{ data.is_active ? "disable" : "activate" }}
                      <span class="font-semibold text-red-500">{{
                        data.username
                      }}</span>
                      account? Note that once this action is completed, it can
                      not be undone
                    </div>

                    <FormKit
                      type="textarea"
                      label="Reason"
                      validation="required"
                      v-model="reason"
                    />
                  </div>

                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton
                      :click="handleDialog"
                      type="button"
                      text="Cancel"
                    />
                    <CoreActionButton
                      :loading="loading"
                      type="submit"
                      :click="() => {}"
                      color="success"
                      :icon="disableIcon"
                      text="Continue"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response } from "@/types";

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
    ExclamationTriangleIcon,
  },
  data() {
    return {
      show: false as boolean,
      disableIcon: XCircleIcon,
      loading: false as boolean,
      reason: "" as string,
      cookie: useCookie("token"),
      details: {
        id: "" as string,
        username: "" as string,
        first_name: "" as string,
        last_name: "" as string,
        middle_name: "" as string,
        sex: "" as string,
        date_of_birth: "" as string,
        lab_locations: new Array<Location>(),
        roles: new Array<{ role_name: string}>(),
        departments: new Array<{ name: string}>()
      }
    };
  },
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  methods: {
    async init() : Promise<void> {
      this.loading = true;
      const request: Request = {
        route: `${endpoints.users}/${this.data.id}`,
        method: "GET",
        token: `${this.cookie}`
      }
      const { data, error, pending } : Response  = await fetchRequest(request);
      this.loading = pending;
      if(data.value){
        this.details = data.value;
        this.loading = false;
        this.handleDialog();
      }
      if(error.value){
        console.error(error.value);
        this.loading = false;
      }

    },
    /**
     * @method deleteData deletes test type
     * @param id test type id
     * @return promise @typeof void
     */
    async deleteData(id: number): Promise<void> {
      this.loading = true;

      const request: Request = {
        route: `${endpoints.users}/${id}`,
        method: "DELETE",
        token: `${this.cookie}`,
      };

      const { pending, error, data }: Response = await fetchRequest(request);

      this.loading = pending;

      if (data.value) {
        this.handleDialog();

        useNuxtApp().$toast.success(`${data.value.message}`);

        this.loading = false;

        this.$emit("update", true);

        this.reason = "";
      }

      if (error.value) {
        console.log(data.value);

        this.loading = false;
      }
    },
    async activate(id: number): Promise<void> {
      this.loading = true;

      const request: Request = {
        route: `${endpoints.users}/activate/${id}`,
        method: "PUT",
        token: `${this.cookie}`,
        body: {
          retired_reason: this.reason,
        },
      };

      const { pending, error, data }: Response = await fetchRequest(request);

      this.loading = pending;

      if (data.value) {
        this.handleDialog();

        useNuxtApp().$toast.success(`${data.value.message}`);

        this.loading = false;

        this.$emit("update", true);

        this.reason = "";
      }

      if (error.value) {
        console.log(data.value);

        this.loading = false;
      }
    },
    handleDialog(): void {
      this.show = !this.show;
    },
  },
};
</script>

<style></style>
