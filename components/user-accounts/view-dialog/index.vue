<template>
  <div>
    <div>
      <CoreActionButton
        text="View"
        color="primary"
        :icon="viewIcon"
        :click="init"
      />
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleDialog" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-xl text-black flex items-center font-medium leading-6"
                  >
                    <img src="~assets/icons/person.svg" class="w-6 h-6 mr-2" />
                    View User Account
                  </DialogTitle>

                  <button @click="handleDialog">
                    <XMarkIcon class="w-5 h-5"/>
                  </button>
                </div>

                <div
                  v-show="loading"
                  class="flex items-center justify-center mx-auto my-20"
                >
                  <CoreLoader :loading="loading" />
                </div>

                <div v-show="!loading" class="space-y-3 px-5 py-5">
                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Username</label>
                    <p class="text-base text-gray-600">
                      {{ details.username }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">First name</label>
                    <p class="text-base text-gray-600">
                      {{ details.first_name }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Last name</label>
                    <p class="text-base text-gray-600">
                      {{ details.last_name }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Roles</label>
                    <p
                      class="text-base text-gray-600"
                      v-for="(role, index) in details.roles"
                      :key="index"
                    >
                      {{ role.role_name }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Locations</label>
                    <p
                      class="text-base text-gray-600"
                      v-for="(location, index) in details.lab_locations"
                      :key="index"
                    >
                      {{ location.name }}
                    </p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Departments</label>
                    <p
                      class="text-base text-gray-600"
                      v-for="(department, index) in details.departments"
                      :key="index"
                    >
                      {{ department.name }}
                    </p>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";
import {
  PlusIcon,
  XMarkIcon,
  ArrowTopRightOnSquareIcon,
  UserIcon,
  ArrowUturnLeftIcon,
  PencilSquareIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import fetchRequest from "@/services/fetch";
import { endpoints } from "@//services/endpoints";
import type { Request, Response, Location } from "@/types";

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
    UserIcon,
  },
  props: {
    data: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      moment: moment,
      open: false as boolean,
      addIcon: PlusIcon,
      viewIcon: ArrowTopRightOnSquareIcon,
      editIcon: PencilSquareIcon,
      clearIcon: ArrowUturnLeftIcon,
      details: {
        id: "" as string,
        username: "" as string,
        first_name: "" as string,
        last_name: "" as string,
        middle_name: "" as string,
        sex: "" as string,
        date_of_birth: "" as string,
        lab_locations: new Array<Location>(),
        roles: new Array<{ role_name: string }>(),
        departments: new Array<{ name: string }>(),
      },
      loading: false as boolean,
      cookie: useCookie("token"),
    };
  },
  methods: {
    async init(): Promise<void> {
      this.loading = true;
      const request: Request = {
        route: `${endpoints.users}/${this.data.id}`,
        method: "GET",
        token: `${this.cookie}`,
      };
      const { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;
      if (data.value) {
        this.details = data.value;
        this.loading = false;
        this.handleDialog();
      }
      if (error.value) {
        console.error(error.value);
        this.loading = false;
      }
    },
    handleDialog() : void {
      this.open = !this.open
    },
  },
};
</script>

<style></style>
