<template>
  <div>
    <div>
      <CoreActionButton
        text="Edit"
        color="primary"
        :icon="editIcon"
        :click="init"
        :loading="fetching"
      />
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleClick" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div
          class="mx-auto flex justify-center py-20 bg-white"
          v-show="fetching"
        >
          <CoreLoader />
        </div>

        <div class="fixed inset-0 overflow-y-auto" v-show="!fetching">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-xl text-black flex items-center font-medium leading-6"
                  >
                    <img
                      src="@/assets/icons/person.svg"
                      class="w-8 h-8 mr-2"
                      alt="person-icon"
                    />
                    Edit user
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  #default=""
                >
                  <div class="mt-2 space-y-3 px-5 py-5">
                    <div class="grid grid-cols-2 gap-4">
                      <FormKit
                        type="text"
                        label="Username"
                        v-model="details.username"
                        validation="required|matches:/^[a-zA-Z']+$/"
                        :validation-messages="{
                          matches:
                            'Username must contain only letters and spaces',
                        }"
                      />
                      <FormKit
                        type="text"
                        label="First name"
                        v-model="details.first_name"
                        validation="required|matches:/^[a-zA-Z']+$/"
                        :validation-messages="{
                          matches:
                            'First name must contain only letters and spaces',
                        }"
                      />
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                      <FormKit
                        type="text"
                        label="Middle name"
                        v-model="details.middle_name"
                      />
                      <FormKit
                        type="text"
                        label="Last name"
                        v-model="details.last_name"
                        validation="required|matches:/^[a-zA-Z']+$/"
                        :validation-messages="{
                          matches:
                            'Last name must contain only letters and spaces',
                        }"
                      />
                    </div>

                    <FormKit type="group">
                      <div class="grid grid-cols-2 gap-4">
                        <div class="w-full flex flex-col space-y-2">
                          <FormKit
                            type="password"
                            label="New password"
                            name="password"
                            v-model="details.new_password"
                            validation-visibility="live"
                          />
                        </div>
                        <div class="w-full flex flex-col space-y-2">
                          <FormKit
                            type="password"
                            label="Confirm new password"
                            name="password_confirm"
                            :validation="
                              details.new_password && 'required|confirm'
                            "
                            validation-label="Password confirmation"
                            validation-visibility="live"
                          />
                        </div>
                      </div>
                    </FormKit>

                    <div class="flex items-center">
                      <label class="mr-5 font-medium text-lg">Sex</label>
                      <label class="flex items-center">
                        <input
                          required
                          v-model="details.sex"
                          type="radio"
                          value="M"
                          name="gender"
                          class="w-4 h-4 rounded-full bg-sky-500 border-sky-800 text-sky-500 focus:ring-sky-800"
                        />
                        <span class="ml-2">Male</span>
                      </label>
                      <label class="flex items-center ml-2">
                        <input
                          required
                          v-model="details.sex"
                          type="radio"
                          value="F"
                          name="gender"
                          class="w-4 h-4 rounded-full bg-sky-500 border-sky-800 text-sky-500 focus:ring-sky-800"
                        />
                        <span class="ml-2">Female</span>
                      </label>
                    </div>

                    <CoreMultiselect
                      label="Select Role(s)"
                      v-model:items-selected="roleSelected"
                      :items="roles"
                      mode="tags"
                    />

                    <CoreMultiselect
                      label="Select Laboratory Location(s)"
                      v-model:items-selected="locationSelected"
                      :items="locations"
                      mode="tags"
                    />

                    <CoreMultiselect
                      label="Select Lab Section(s)"
                      v-model:items-selected="departmentSelected"
                      :items="departments"
                      mode="tags"
                    />
                  </div>

                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton
                      type="button"
                      :click="() => {}"
                      text="Clear form"
                    />
                    <CoreActionButton
                      :loading="loading"
                      type="submit"
                      :click="() => {}"
                      color="success"
                      :icon="saveIcon"
                      text="Save Changes"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  ArrowDownTrayIcon,
  PencilSquareIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response, Department, Location, Role } from "@/types";

const props = defineProps<{
  data: {
    id: string;
    username: string;
    first_name: string;
    last_name: string;
    middle_name: string;
    old_password: string;
    new_password: string;
    sex: string;
    date_of_birth: string;
    roles: any[];
    lab_locations: any[];
    departments: any[];
  };
}>();

const open = ref(false);
const editIcon = PencilSquareIcon;
const saveIcon = ArrowDownTrayIcon;

const departmentSelected = ref<string[]>([]);
const departments = ref<string[]>([]);
const rawDepartments = ref<Department[]>([]);

const rawLocations = ref<Location[]>([]);
const locations = ref<string[]>([]);
const locationSelected = ref<string[]>([]);

const cookie = useCookie("token");
const roles = ref<string[]>([]);
const rawRoles = ref<Role[]>([]);
const roleSelected = ref<string[]>([]);

const loading = ref(false);
const fetching = ref(false);

const emit = defineEmits(["update"]);
const { $toast, $metadata } = useNuxtApp();

const details = ref({
  id: "",
  username: "",
  first_name: "",
  last_name: "",
  middle_name: "",
  old_password: "",
  new_password: "",
  sex: "",
  date_of_birth: "",
  roles: [] as any[],
  lab_locations: [] as any[],
  departments: [] as any[],
});

const handleClick = () => {
  open.value = !open.value;
};

const fetchRoles = async (): Promise<void> => {
  const request: Request = {
    route: endpoints.roles,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error }: Response = await fetchRequest(request);
  if (data.value) {
    rawRoles.value = data.value;
    roles.value = data.value.map((item: Role) => item.name);
  }
  if (error.value) {
    console.error(error.value);
  }
}

const fetchLocations = async (): Promise<void> => {
  const request: Request = {
    route: endpoints.locations,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error }: Response = await fetchRequest(request);
  if (data.value) {
    rawLocations.value = data.value;
    locations.value =data.value.map((item: Location) => item.name);
  }
  if (error.value) {
    console.error(error.value);
  }
}

const getMetadata = () => {
  rawDepartments.value = $metadata.departments;
  departments.value = rawDepartments.value.map((item: Department) => item.name);
};

const init = async () => {
  await fetchRoles();
  await fetchLocations();
  getMetadata();
  handleClick();

  fetching.value = true;

  const request: Request = {
    route: `${endpoints.users}/${props.data.id}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  fetching.value = pending;
  if (data.value) {
    details.value = data.value;
    fetching.value = false;

    roleSelected.value = data.value.roles
      .map(
        ({ role_name }: { role_name: string }) =>
          rawRoles.value.find((item) => role_name === item.name)?.name
      )
      .filter((name: string): name is string => name !== undefined);

    departmentSelected.value = data.value.departments
      .map(
        ({ name }: { name: string }) =>
          rawDepartments.value.find((item) => name === item.name)?.name
      )
      .filter((name: string): name is string => name !== undefined);

    locationSelected.value = data.value.lab_locations
      .map(
        ({ name }: { name: string }) =>
          rawLocations.value.find((item) => name === item.name)?.name
      )
      .filter((name: string): name is string => name !== undefined);
  }
  if (error.value) {
    console.error(error.value);
    fetching.value = false;
  }
};

const submitForm = async (): Promise<void> => {
  loading.value = true;

  const departmentId: number[] = departmentSelected.value
    ? departmentSelected.value
        .map(
          (name) => rawDepartments.value.find((item) => name === item.name)?.id
        )
        .filter((id): id is number => id !== undefined)
        .map(Number)
    : [];

  const rolesId: number[] = roleSelected.value
    ? roleSelected.value
        .map((name) => rawRoles.value.find((item) => name === item.name)?.id)
        .filter((id): id is number => id !== undefined)
    : [];

  const locationsId: number[] = locationSelected.value
    ? locationSelected.value
        .map(
          (name) => rawLocations.value.find((item) => name === item.name)?.id
        )
        .filter((id): id is number => id !== undefined)
        .map(Number)
    : [];

  const request: Request = {
    route: `${endpoints.users}/${props.data.id}`,
    method: "PUT",
    token: `${cookie.value}`,
    body: {
      person: {
        first_name: details.value.first_name,
        middle_name: details.value.middle_name,
        last_name: details.value.last_name,
        sex: details.value.sex,
        date_of_birth: details.value.date_of_birth,
      },
      user: {
        username: details.value.username,
        old_password: details.value.old_password,
        password: details.value.new_password,
      },
      roles: rolesId,
      departments: departmentId,
      lab_locations: locationsId,
    },
  };
  const { data, error, pending } = await fetchRequest(request);
  loading.value = pending;
  if (data.value) {
    handleClick();
    $toast.success(`User updated successfully!`);
    loading.value = false;
    emit("update", true);
  }
  if (error.value) {
    console.error(error.value);
    handleClick();
    $toast.error(ERROR_MESSAGE);
    loading.value = false;
  }
};
</script>

<style></style>