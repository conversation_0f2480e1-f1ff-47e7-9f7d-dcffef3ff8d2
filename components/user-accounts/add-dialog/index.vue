<template>
  <div>
    <div>
      <CoreActionButton
        text="Add user"
        color="primary"
        :loading="isOpening"
        :icon="addIcon"
        :click="
          () => {
            init();
          }
        "
      />
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="handleClick" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-xl text-black flex items-center font-medium leading-6"
                  >
                    <img
                      src="@/assets/icons/person.svg"
                      alt="user-icon"
                      class="w-8 h-8 mr-2"
                    />
                    Add user
                  </DialogTitle>

                  <button @click="closeDialog">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  id="submitForm"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  #default=""
                >
                  <div class="mt-2 space-y-3 px-5">
                    <div class="grid grid-cols-2 gap-4">
                      <FormKit
                        type="text"
                        label="Username"
                        validation="required|matches:/^[a-zA-Z']+$/"
                        :validation-messages="{
                          matches:
                            'Username must contain only letters, no spaces or special characters',
                        }"
                        v-model="username"
                      />

                      <FormKit
                        type="text"
                        label="First name"
                        v-model="firstName"
                        validation="required|matches:/^[a-zA-Z']+$/"
                        :validation-messages="{
                          matches:
                            'First name must contain only letters, no spaces or special characters',
                        }"
                      />
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                      <FormKit
                        type="text"
                        label="Middle name"
                        v-model="middleName"
                      />

                      <FormKit
                        type="text"
                        label="Last name"
                        v-model="lastName"
                        validation="required|matches:/^[a-zA-Z']+$/"
                        :validation-messages="{
                          matches:
                            'Last name must contain only letters, no spaces or special characters',
                        }"
                      />
                    </div>

                    <FormKit type="group">
                      <div class="grid grid-cols-2 gap-4">
                        <FormKit
                          type="password"
                          label="Password"
                          name="password"
                          validation="required"
                          v-model="password"
                          validation-visibility="live"
                        />

                        <FormKit
                          type="password"
                          label="Confirm password"
                          name="password_confirm"
                          validation="required|confirm"
                          validation-label="Password confirmation"
                          validation-visibility="live"
                        />
                      </div>
                    </FormKit>

                    <div class="w-full flex items-center">
                      <label class="mr-5 font-medium text-lg">Sex</label>
                      <label class="flex items-center">
                        <input
                          required
                          v-model="sex"
                          type="radio"
                          value="M"
                          name="gender"
                          class="w-4 h-4 rounded-full bg-sky-500 border-sky-800 text-sky-500 focus:ring-sky-800"
                        />
                        <span class="ml-2">Male</span>
                      </label>
                      <label class="flex items-center ml-2">
                        <input
                          required
                          v-model="sex"
                          type="radio"
                          value="F"
                          name="gender"
                          class="w-4 h-4 rounded-full bg-sky-500 border-sky-800 text-sky-500 focus:ring-sky-800"
                        />
                        <span class="ml-2">Female</span>
                      </label>
                    </div>

                    <CoreMultiselect
                      label="Select Role(s)"
                      v-model:items-selected="roleSelected"
                      :items="roles"
                      mode="tags"
                    />

                    <CoreMultiselect
                      label="Select Laboratory Location(s)"
                      v-model:items-selected="locationSelected"
                      :items="locations"
                      mode="tags"
                    />

                    <div>
                      <CoreMultiselect
                        label="Select Lab Section(s)"
                        v-model:items-selected="departmentSelected"
                        :items="departments"
                        mode="tags"
                        direction="top"
                        :loading="loadingDepartments"
                        :placeholder="
                          loadingDepartments
                            ? 'Loading departments...'
                            : 'Select lab sections'
                        "
                      />
                      <div
                        v-if="departmentLoadError && !loadingDepartments"
                        class="mt-2"
                      >
                        <p class="text-red-600 text-sm mb-2">
                          Failed to load lab sections.
                        </p>
                        <button
                          @click="retryLoadDepartments"
                          class="text-blue-600 hover:text-blue-800 text-sm underline"
                          type="button"
                        >
                          Retry loading
                        </button>
                      </div>
                    </div>
                  </div>

                  <div
                    class="mt-10 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton
                      type="button"
                      :click="
                        () => {
                          clearForm();
                        }
                      "
                      text="Clear form"
                    />
                    <CoreActionButton
                      :loading="loading"
                      type="submit"
                      :click="() => {}"
                      color="success"
                      :icon="saveIcon"
                      text="Save Changes"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  PlusIcon,
  XMarkIcon,
  ArrowDownTrayIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import { reset } from "@formkit/core";
import type { Department, Response, Request, Location, Role } from "@/types";

const open = ref<boolean>(false);
const addIcon = PlusIcon;
const saveIcon = ArrowDownTrayIcon;
const departmentSelected = ref<string[]>([]);
const departments = ref<string[]>([]);
const rawDepartments = ref<Department[]>([]);
const cookie = useCookie("token");
const roles = ref<string[]>([]);
const rawRoles = ref<Role[]>([]);
const roleSelected = ref<string[]>([]);
const rawLocations = ref<Location[]>([]);
const locations = ref<string[]>([]);
const locationSelected = ref<string[]>([]);
const firstName = ref<string>("");
const middleName = ref<string>("");
const lastName = ref<string>("");
const username = ref<string>("");
const sex = ref<string>("");
const password = ref<string>("");
const loading = ref<boolean>(false);
const isOpening = ref<boolean>(false);
const loadingDepartments = ref<boolean>(false);
const departmentLoadError = ref<boolean>(false);
const { $metadata } = useNuxtApp();

const submitForm = async (): Promise<void> => {
  loading.value = true;
  const departmentId: number[] = departmentSelected.value
    ? departmentSelected.value
        .map(
          (name: string) =>
            rawDepartments.value.find((item: Department) => name === item.name)
              ?.id
        )
        .filter((id): id is number => id !== undefined)
    : [];

  const rolesId: number[] = roleSelected.value
    ? roleSelected.value
        .map(
          (name: string) =>
            rawRoles.value.find((item: any) => name === item.name)?.id
        )
        .filter((id): id is number => id !== undefined)
    : [];

  const locationsId: number[] = locationSelected.value
    ? locationSelected.value
        .map(
          (name: string) =>
            rawLocations.value.find((item: Department) => name === item.name)
              ?.id
        )
        .filter((id): id is number => id !== undefined)
        .map(Number)
    : [];
  const request: Request = {
    route: endpoints.users,
    method: "POST",
    token: `${cookie.value}`,
    body: {
      person: {
        first_name: firstName.value,
        middle_name: middleName.value,
        last_name: lastName.value,
        sex: sex.value,
      },
      user: {
        username: username.value,
        password: password.value,
      },
      roles: rolesId,
      departments: departmentId,
      lab_locations: locationsId,
    },
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;
  const { $toast } = useNuxtApp();
  if (data.value) {
    handleClick();
    $toast.success(`User created successfully!`);
    loading.value = false;
  }
  if (error.value) {
    handleClick();
    $toast.error(error.value);
    loading.value = false;
    console.error(error.value);
  }
};

const fetchRoles = async (): Promise<void> => {
  try {
    const request: Request = {
      route: endpoints.roles,
      method: "GET",
      token: `${cookie.value}`,
    };
    const { data, error }: Response = await fetchRequest(request);
    if (data.value && Array.isArray(data.value)) {
      rawRoles.value = data.value;
      roles.value = data.value.map((item: Role) => item.name);
    } else if (error.value) {
      console.error("Error fetching roles:", error.value);
      rawRoles.value = [];
      roles.value = [];
    }
  } catch (error) {
    console.error("Unexpected error fetching roles:", error);
    rawRoles.value = [];
    roles.value = [];
  }
};

const fetchLocations = async (): Promise<void> => {
  try {
    const request: Request = {
      route: endpoints.locations,
      method: "GET",
      token: `${cookie.value}`,
    };
    const { data, error }: Response = await fetchRequest(request);
    if (data.value && Array.isArray(data.value)) {
      rawLocations.value = data.value;
      locations.value = data.value.map((item: Location) => item.name);
    } else if (error.value) {
      console.error("Error fetching locations:", error.value);
      rawLocations.value = [];
      locations.value = [];
    }
  } catch (error) {
    console.error("Unexpected error fetching locations:", error);
    rawLocations.value = [];
    locations.value = [];
  }
};

/**
 * Fetches departments with fallback mechanism to handle cases where metadata fails to load.
 * This fixes the issue where lab departments sometimes don't load by:
 * 1. First trying to use cached metadata if available
 * 2. Falling back to direct API call if metadata is unavailable or empty
 * 3. Providing proper error handling and user feedback
 * 4. Adding retry functionality for failed loads
 */
const fetchDepartments = async (): Promise<void> => {
  loadingDepartments.value = true;
  departmentLoadError.value = false;

  try {
    if (
      $metadata?.departments &&
      Array.isArray($metadata.departments) &&
      $metadata.departments.length > 0
    ) {
      rawDepartments.value = $metadata.departments;
      departments.value = rawDepartments.value.map(
        (item: Department) => item.name
      );
      return;
    }

    const request: Request = {
      route: endpoints.departments,
      method: "GET",
      token: `${cookie.value}`,
    };
    const { data, error }: Response = await fetchRequest(request);
    if (data.value && Array.isArray(data.value)) {
      rawDepartments.value = data.value;
      departments.value = rawDepartments.value.map(
        (item: Department) => item.name
      );
    } else if (error.value) {
      console.error("Error fetching departments:", error.value);
      departmentLoadError.value = true;
      rawDepartments.value = [];
      departments.value = [];
    }
  } catch (error) {
    console.error("Unexpected error fetching departments:", error);
    departmentLoadError.value = true;
    rawDepartments.value = [];
    departments.value = [];
  } finally {
    loadingDepartments.value = false;
  }
};

const retryLoadDepartments = async (): Promise<void> => {
  await fetchDepartments();
};

const init = async (): Promise<void> => {
  isOpening.value = true;
  try {
    await Promise.all([fetchRoles(), fetchLocations(), fetchDepartments()]);
    handleClick();
  } catch (error) {
    console.error("Error initializing dialog:", error);
    handleClick();
  } finally {
    isOpening.value = false;
  }
};

const clearForm = (): void => {
  reset("submitForm");
  departmentSelected.value = [];
  roleSelected.value = [];
  locationSelected.value = [];
  sex.value = "";
  departmentLoadError.value = false;
};

const closeDialog = (): void => {
  open.value = false;
  clearForm();
};

const handleClick = (): void => {
  open.value = !open.value;
};
</script>
