<template>
    <div>
        <table class="w-full text-left">
            <thead class="uppercase bg-gray-100">
                <tr class="border-b">
                    <th class="uppercase py-2 px-2 border-r">
                        SN
                    </th>
                    <th class="uppercase py-2 px-2 border-r">
                        PATIENT NAME
                    </th>
                    <th class="uppercase py-2 px-2 border-r">
                        DATE OF BIRTH
                    </th>
                    <th class="uppercase py-2 px-2 border-r">
                        WARD
                    </th>
                    <th class="uppercase py-2 px-2 border-r">
                        ACCESSION NUMBER
                    </th>
                    <th class="uppercase py-2 px-2 border-r">
                        SPECIMEN
                    </th>
                    <th class="uppercase py-2 px-2 border-r">
                        RECEIPT DATE
                    </th>
                    <th class="uppercase py-2 px-2 border-r">
                        TESTS
                    </th>
                    <th class="uppercase py-2 px-2 border-r">
                        LAB selection
                    </th>
                    <th class="uppercase py-2 px-2 border-r">
                        REJECTION REASON
                    </th>
                    <th class="uppercase py-2 px-2 border-r">
                        PERSON TALKED TO
                    </th>
                    <th class="uppercase py-2 px-2 border-r">
                        DATE REJECTED
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="report, index in details" :key="index">
                    <th class="px-2 py-2 font-normal border-r border-b">{{ index + 1 }}</th>
                    <th class="px-2 py-2 font-normal border-r border-b">{{ report.patient_name || '' }}</th>
                    <th class="px-2 py-2 font-normal border-r border-b">{{ report.dob ?
                        moment(report.dob).format('DD/MM/YYYY') : '' }}</th>
                    <th class="px-2 py-2 font-normal border-r border-b">{{ report.ward || '' }}</th>
                    <th class="px-2 py-2 font-normal border-r border-b">{{ report.accession_number }}</th>
                    <th class="px-2 py-2 font-normal border-r border-b">{{ report.specimen }}</th>
                    <th class="px-2 py-2 font-normal border-r border-b">{{ report.receipt_date }}</th>
                    <th class="px-2 py-2 font-normal border-r border-b">{{ report.test }}</th>
                    <th class="px-2 py-2 font-normal border-r border-b">{{ report.department }}</th>
                    <th class="px-2 py-2 font-normal border-r border-b">{{ report.rejection_reason }}</th>
                    <th class="px-2 py-2 font-normal border-r border-b">{{ report.person_talked_to }}</th>
                    <th class="px-2 py-2 font-normal border-r border-b">{{ report.test_status_date }}</th>
                </tr>
            </tbody>
        </table>

    </div>
</template>

<script lang="ts">
export default defineComponent({
    props: {
        data: {
            required: true,
            type: Object
        }
    },
    data() {
        return {
            details: this.data?.data
        }
    },
})
</script>
