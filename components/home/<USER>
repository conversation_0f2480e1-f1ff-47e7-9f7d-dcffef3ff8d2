<template>
  <div class="border rounded col-span-1" v-if="data.tests_by_status">
    <div>
      <div
        class="flex rounded-t items-center justify-between bg-gray-50 px-2 py-2 border-b"
      >
        <h3 class="text-xl font-semibold">
          Tests <span class="text-base font-medium">(Past 30 Days - {{ getDepartment() }})</span>
        </h3>
        <Menu
          as="div"
          class="relative inline-block text-left justify-center items-center"
        >
          <MenuButton>
            <EllipsisVerticalIcon class="w-6 h-6" />
          </MenuButton>

          <transition
            enter-active-class="transition duration-100 ease-out"
            enter-from-class="transform scale-95 opacity-0"
            enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition duration-75 ease-in"
            leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-95 opacity-0"
          >
            <MenuItems
              class="absolute right-0 mt-2 w-48 origin-top-right divide-y divide-gray-100 rounded bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
            >
              <div class="px-1 py-1">
                <MenuItem v-slot="{ active }">
                  <button
                    @click="viewChart = !viewChart"
                    :class="[
                      active ? 'bg-sky-500 text-white' : 'text-gray-900',
                      'group flex w-full items-center rounded px-2 py-2 font-normal',
                    ]"
                  >
                    <DocumentTextIcon v-if="viewChart" class="w-5 h-5 mr-2" />
                    <ChartPieIcon v-if="!viewChart" class="w-5 h-5 mr-2" />
                    {{ viewChart ? "View summary" : "View as Pie Chart" }}
                  </button>
                </MenuItem>
              </div>
            </MenuItems>
          </transition>
        </Menu>
      </div>
      <div v-show="!viewChart">
        <div class="flex items-center space-x-2 px-5 py-2">
          <img src="@/assets/icons/clinical_fe.svg" alt="clinical-fe-icon" />
          <h3
            class="text-3xl hover:text-sky-500 transition duration-150 font-semibold hover:underline cursor-pointer"
            @click="navigateFilterTests('', from, to)"
            v-if="data?.tests !== null && data?.tests !== undefined"
          >
            {{ data?.tests.toLocaleString() }}
          </h3>
        </div>
        <div class="w-full px-5 py-2 space-y-2">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <div
                class="w-3 h-3 rounded-full bg-sky-500"
              ></div>
              <h3 class="font-medium">Pending</h3>
            </div>
            <a
              class="hover:text-sky-500 transition duration-150 hover:underline cursor-pointer"
              @click="navigateFilterTests('pending', from, to)"
            >
              {{
                data?.tests_by_status["pending"]
                  ? data?.tests_by_status["pending"]
                  : 0
              }}
            </a>
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <div :class="`bg-amber-500`" class="w-3 h-3 rounded-full"></div>
              <h3 class="font-medium">Started</h3>
            </div>
            <a
              class="hover:text-sky-500 transition duration-150 hover:underline cursor-pointer"
              @click="navigateFilterTests('started', from, to)"
            >
              {{ data?.tests_by_status["started"] }}
            </a>
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <div
                :class="`bg-green-500`"
                class="w-3 h-3 rounded-full"
              ></div>
              <h3 class="font-medium">Completed</h3>
            </div>
            <a
              class="hover:text-sky-500 transition duration-150 hover:underline cursor-pointer"
              @click="navigateFilterTests('completed', from, to)"
            >
              {{ data?.tests_by_status["completed"] }}
            </a>
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <div
                :class="`bg-emerald-700`"
                class="w-3 h-3 rounded-full"
              ></div>
              <h3 class="font-medium">Verified</h3>
            </div>
            <a
              class="hover:text-sky-500 transition duration-150 hover:underline cursor-pointer"
              @click="navigateFilterTests('verified', from, to)"
            >
              {{ data?.tests_by_status["verified"] }}
            </a>
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <div
                :class="`bg-red-600`"
                class="w-3 h-3 rounded-full"
              ></div>
              <h3 class="font-medium">Rejected</h3>
            </div>
            <a
              class="hover:text-sky-500 transition duration-150 hover:underline cursor-pointer"
              @click="navigateFilterTests('rejected', from, to)"
            >
              {{ data?.tests_by_status["rejected"] }}
            </a>
          </div>
        </div>
      </div>
    </div>
    <div v-if="data?.tests_by_status.data !== 0 && viewChart">
      <ChartsPie :chartData="proccessedAnalyticsData()" />
    </div>
  </div>
</template>

<script lang="ts">
import type { Statuses } from "@/types";
import {
  EllipsisVerticalIcon,
  ChartPieIcon,
  DocumentTextIcon,
  CheckBadgeIcon,
} from "@heroicons/vue/24/solid/index.js";
import { Menu, MenuItem, MenuItems, MenuButton } from "@headlessui/vue";
import { getStatusColor } from "@/utils/functions";
import { useAuthStore } from "@/store/auth";
import moment from "moment";

export default {
  components: {
    EllipsisVerticalIcon,
    Menu,
    MenuItem,
    MenuItems,
    MenuButton,
    ChartPieIcon,
    DocumentTextIcon,
  },
  props: {
    data: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      viewChart: false as boolean,
      applyIcon: CheckBadgeIcon,
      from: '' as string,
      to: '' as string
    };
  },
  created() {
    this.formatDateRanges();
  },
  methods: {
    getColor(status: Statuses): string {
      return getStatusColor(status);
    },
    /***
     * @method proccessedAnalyticsData process tests data analytics
     * @returns typeof Object
     */
    proccessedAnalyticsData(): Object {
      return {
        labels: [
          "Not Received",
          "Pending",
          "Started",
          "Completed",
          "Verified",
          "Rejected",
        ],
        datasets: [
          {
            backgroundColor: [
              "#ec4899",
              "#0ea5e9",
              "#f59e0b",
              "#22c55e",
              "#047857",
              "#dc2626",
            ],
            data: [
              this.data?.tests_by_status["not-received"],
              this.data?.tests_by_status["pending"],
              this.data?.tests_by_status["started"],
              this.data?.tests_by_status["completed"],
              this.data?.tests_by_status["verified"],
              this.data?.tests_by_status["rejected"],
            ],
          },
        ],
      };
    },
    /**
     * @method formatDateRanges formats date ranges to and from on mounted, uses moment.js
     * @return void
     */
    formatDateRanges(): void {
      const today = moment();
      const formattedToday = today.format(DATE_PICKER_FORMAT);
      const thirtyDaysAgo = moment().subtract(30, "days");
      const formattedThirtyDaysAgo = thirtyDaysAgo.format(DATE_PICKER_FORMAT);
      this.from = this.data?.from || formattedThirtyDaysAgo;
      this.to = this.data?.to || formattedToday;
    },
    /***
     * @method navigateFilterTests navigates to tests page with test status and date ranges filters
     * @param from start date
     * @param to end date
     * @param status tests status
     * @returns void
     */
    navigateFilterTests(status: string, from: string, to: string): void {
      this.$router.push(`/tests?from=${from}&to=${to}&status=${status}`);
    },
    /**
     * @method getDepartment returns the current department of the user
     * @returns void
     */
    getDepartment(): string {
      const { department } = useAuthStore()
      return department == 'Lab Reception' ? 'All' : department
    }
  },
};
</script>
<style lang=""></style>
