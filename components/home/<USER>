<template>
  <div class="col-span-1 rounded border relative">
    <div v-if="!loading">
      <div
        class="flex items-center justify-between bg-gray-50 border-b px-2 py-2 rounded-t"
      >
        <h3 class="text-lg font-semibold">Recent patients <span class="text-base font-medium">(All)</span></h3>
        <div>
          <Menu
            as="div"
            class="relative inline-block text-left justify-center items-center"
          >
            <MenuButton>
              <EllipsisVerticalIcon class="w-5 h-5" />
            </MenuButton>

            <transition
              enter-active-class="transition duration-100 ease-out"
              enter-from-class="transform scale-95 opacity-0"
              enter-to-class="transform scale-100 opacity-100"
              leave-active-class="transition duration-75 ease-in"
              leave-from-class="transform scale-100 opacity-100"
              leave-to-class="transform scale-95 opacity-0"
            >
              <MenuItems
                class="absolute right-0 mt-2 w-48 origin-top-right divide-y divide-gray-100 rounded bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
              >
                <div class="py-1 border-y">
                  <MenuItem v-slot="{ active }">
                    <NuxtLink to="/reports/daily/patient-report">
                      <button
                        :class="[
                          active ? 'bg-sky-500 text-white' : 'text-gray-900',
                          'group flex w-full items-center px-2 py-2 font-normal',
                        ]"
                      >
                        <DocumentTextIcon class="w-5 h-5 mr-2" />
                        {{ "Generate Reports" }}
                      </button>
                    </NuxtLink>
                  </MenuItem>
                </div>
              </MenuItems>
            </transition>
          </Menu>
        </div>
      </div>
      <div class="">
        <table class="w-full">
          <tbody>
            <tr
              class="border-b border-dotted"
              v-for="(client, index) in clients"
              :key="index"
              :class="index % 2 !== 0 ? 'bg-gray-50' : ''"
            >
              <td class="px-2 py-2 capitalize flex items-center">
                {{
                  `${capitalizeStr(client.first_name.toLowerCase())} ${
                    client.middle_name !== null
                      ? capitalizeStr(client.middle_name.toLowerCase())
                      : ""
                  } ${capitalizeStr(client.last_name.toLowerCase())}`
                }}
              </td>
              <td class="px-2 py-2">
                {{ moment(client.created_at).format(DATE_FORMAT) }}
              </td>
              <td class="px-2 py-2">
                <CoreActionButton
                  text="New order &rarr;"
                  color="primary"
                  :click="
                    () => {
                      newOrder(client);
                    }
                  "
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div v-if="loading">
      <div class="w-full flex items-center justify-between rounded-t px-2 py-2">
        <div class="h-8 w-48 bg-gray-100 animate-pulse rounded"></div>
        <div class="rounded-full h-7 w-7 bg-gray-100 animate-pulse"></div>
      </div>
      <div class="mt-2 space-y-2 px-2">
        <div class="flex items-center space-x-2" v-for="i in 10" :key="i">
          <div class="w-full bg-gray-100 h-8 animate-pulse rounded"></div>
        </div>
      </div>
      <div class="w-32 bg-gray-100 rounded-t h-8 animate-pulse m-2"></div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  EllipsisVerticalIcon,
  ChartBarIcon,
  DocumentTextIcon,
  CalendarDaysIcon,
  CalendarIcon,
} from "@heroicons/vue/20/solid/index.js";
import type { Patient, Request, Response } from "@/types";
import fetchRequest from "@/services/fetch";
import { endpoints } from "@/services/endpoints";
import moment from "moment";
import { Menu, MenuItem, MenuItems, MenuButton } from "@headlessui/vue";
import { useAuthStore } from "@/store/auth";

export default {
  components: {
    EllipsisVerticalIcon,
    ChartBarIcon,
    Menu,
    MenuItem,
    MenuItems,
    MenuButton,
    DocumentTextIcon,
    CalendarDaysIcon,
    CalendarIcon,
  },
  data() {
    return {
      moment: moment,
      cookie: useCookie("token"),
      clients: new Array<Patient>(),
      loading: true as boolean,
    };
  },
  created() {
    this.getClients();
  },
  methods: {
    async getClients(): Promise<void> {
      const authStore = useAuthStore();
      const labLocationId = getIdByName(authStore.locations, authStore.selectedLocation);
      const request: Request = {
        route: `${endpoints.clients}?page=1&per_page=9&status=&search=&start_date=&end_date=&lab_location=${labLocationId}&dashboard=true`,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;
      if (data.value) {
        this.clients = data.value.clients;
        this.loading = false;
      }
      if (error.value) {
        console.error(error.value);
        this.loading = false;
      }
    },
    /***
     * @method newOrder navigate to new test
     * @param null
     * @returns void
     */
    async newOrder(patient: Patient): Promise<void> {
      this.$router.push(`/tests/new-test?patient_id=${patient.client_id}`);
    },
  },
};
</script>
<style></style>
