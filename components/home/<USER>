<template>
  <div class="w-full border rounded">
    <div class="bg-gray-50 border-b rounded-t px-2 py-2 text-lg font-semibold">
      Available Printers <span class="text-base font-medium">(All)</span>
    </div>
    <div class="px-3 py-3 space-y-2">
      <div
        class="flex items-center space-x-5 bg-white border-b pb-2 border-dotted"
        v-for="printer in props.data?.printers"
        v-bind:key="printer.name"
      >
        <div class="p-3 bg-gray-100 rounded border">
          <PrinterIcon class="w-7 h-7" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-black">{{ printer.name }}</h3>
          <p class="text-base text-gray-500">{{ printer.description }}</p>
        </div>
      </div>
      <CoreActionButton v-if="can.manage('lab_configurations')" :click="() => $router.push('configuration??tab=printers')" text="Printer Configuration" :icon="WrenchScrewdriverIcon" color="primary"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PrinterIcon, WrenchScrewdriverIcon } from "@heroicons/vue/24/solid/index.js";

const { can } = usePermissions();
const props = defineProps({
  data: {
    required: true,
    type: Object,
  },
});
</script>
