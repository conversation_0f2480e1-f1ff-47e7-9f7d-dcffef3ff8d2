<template>
  <div class="border rounded">
    <div>
      <div
        class="flex items-center justify-between rounded-t bg-gray-50 px-2 py-2 border-b text-xl font-semibold"
      >
        <h3>Patients <span class="text-base font-medium">(All)</span></h3>
        <Menu
          as="div"
          class="relative inline-block text-left justify-center items-center"
        >
          <MenuButton>
            <EllipsisVerticalIcon class="w-5 h-5" />
          </MenuButton>

          <transition
            enter-active-class="transition duration-100 ease-out"
            enter-from-class="transform scale-95 opacity-0"
            enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition duration-75 ease-in"
            leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-95 opacity-0"
          >
            <MenuItems
              class="absolute right-0 mt-2 w-48 origin-top-right divide-y divide-gray-100 rounded bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
            >
              <div class="py-1 border-y">
                <MenuItem v-slot="{ active }">
                  <NuxtLink to="/patients">
                    <button
                      :class="[
                        active ? 'bg-sky-500 text-white' : 'text-gray-900',
                        'group flex w-full items-center px-2 py-2 font-normal',
                      ]"
                    >
                      <UserGroupIcon class="w-5 h-5 mr-2" />
                      View Patients
                    </button>
                  </NuxtLink>
                </MenuItem>
                <MenuItem v-slot="{ active }">
                  <NuxtLink to="/reports/daily/patient-report">
                    <button
                      :class="[
                        active ? 'bg-sky-500 text-white' : 'text-gray-900',
                        'group flex w-full items-center px-2 py-2 font-normal',
                      ]"
                    >
                      <DocumentTextIcon class="w-5 h-5 mr-2" />
                      Generate Reports
                    </button>
                  </NuxtLink>
                </MenuItem>
              </div>
            </MenuItems>
          </transition>
        </Menu>
      </div>
      <div class="">
        <div class="flex items-center space-x-2 px-2 py-2">
          <img src="~assets/icons/patients.svg" />
          <h3 @click="$router.push('/patients')" class="text-2xl font-semibold hover:text-sky-500 cursor-pointer hover:underline transition duration-150" v-if="data?.clients !== null && data?.clients !== undefined">
            {{ data?.clients.toLocaleString() }}
          </h3>
        </div>
        <div class="px-5" v-if="data?.clients > 0">
          <h3 class="mb-2 font-medium">By Gender</h3>
          <div class="w-full flex items-center">
            <div
              :style="{
                width:
                  getPercentage(
                    data?.by_sex.M,
                    data?.clients
                  ) + '%',
              }"
              class="h-4 bg-sky-500 rounded-tl-full rounded-bl-full"
            ></div>
            <div
              :style="{
                width:
                  getPercentage(
                    data?.by_sex.F,
                    data?.clients
                  ) + '%',
              }"
              class="h-4 bg-green-500 rounded-br-full rounded-tr-full"
            ></div>
          </div>
          <div class="mt-2">
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-sky-500 rounded-full"></div>
              <p>
                {{
                  getPercentage(
                    data?.by_sex.M,
                    data?.clients
                  )
                }}% Males
              </p>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-green-500 rounded-full"></div>
              <p>
                {{
                  getPercentage(
                    data?.by_sex.F,
                    data?.clients
                  )
                }}% Females
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script lang="ts">
import {
  DocumentTextIcon,
  EllipsisVerticalIcon,
  UserGroupIcon,
} from "@heroicons/vue/20/solid/index.js";
import { Menu, MenuItem, MenuItems, MenuButton } from "@headlessui/vue";

export default {
  components: {
    EllipsisVerticalIcon,
    Menu,
    MenuItem,
    MenuItems,
    MenuButton,
    UserGroupIcon,
    DocumentTextIcon,
  },
  props: {
    data: {
      required: true,
      type: Object,
    }
  },
  data() {
    return {
      cookie: useCookie("token")
    };
  },
  methods: {
    getPercentage(count: number, total: number): number {
      return total == 0 ? 0 : Math.round((count / total) * 100);
    },
  },
};
</script>
<style lang=""></style>
