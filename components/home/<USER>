<template>
    <div class="rounded border">
        <div class="bg-gray-50 px-2 py-2 border-b rounded-t flex items-center justify-between">
            <h3 class="font-semibold text-lg">Current stock levels</h3>
            <div>
                <Menu as="div" class="relative inline-block text-left justify-center items-center">

                    <MenuButton>
                        <EllipsisVerticalIcon class="w-5 h-5" />
                    </MenuButton>

                    <transition enter-active-class="transition duration-100 ease-out"
                        enter-from-class="transform scale-95 opacity-0" enter-to-class="transform scale-100 opacity-100"
                        leave-active-class="transition duration-75 ease-in"
                        leave-from-class="transform scale-100 opacity-100" leave-to-class="transform scale-95 opacity-0">
                        <MenuItems
                            class="absolute right-0 mt-2 w-48 origin-top-right divide-y divide-gray-100 rounded bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                            <div class="py-1 border-y">
                                <MenuItem v-slot="{ active }">
                                <NuxtLink to="/stock-management/stock">
                                    <button :class="[
                                        active ? 'bg-sky-500 text-white' : 'text-gray-900',
                                        'group flex w-full items-center px-2 py-2 font-normal',
                                    ]">
                                        <ClipboardDocumentListIcon class="w-5 h-5 mr-2" />
                                        {{ 'View stock' }}
                                    </button>
                                </NuxtLink>
                                </MenuItem>
                                <MenuItem v-slot="{ active }">
                                <NuxtLink to="/stock-management/reports">
                                    <button :class="[
                                        active ? 'bg-sky-500 text-white' : 'text-gray-900',
                                        'group flex w-full items-center px-2 py-2 font-normal',
                                    ]">
                                        <DocumentTextIcon class="w-5 h-5 mr-2" />
                                        {{ 'Generate Reports' }}
                                    </button>
                                </NuxtLink>
                                </MenuItem>
                            </div>
                        </MenuItems>
                    </transition>
                </Menu>
            </div>
        </div>
        <div class="chartStyle">
            <ChartsLine :chartData="chartData" />
        </div>
    </div>
</template>

<script lang="ts">

import StockModule from '@/repository/modules/stock';
import type { Response } from '@/types';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { ClipboardDocumentListIcon, DocumentTextIcon, EllipsisVerticalIcon } from '@heroicons/vue/24/solid/index.js';
import type { ServerOptions } from 'vue3-easy-data-table';

export default {
    components: { Menu, MenuButton, EllipsisVerticalIcon, MenuItems, MenuItem, DocumentTextIcon, ClipboardDocumentListIcon },
    data() {
        return {
            loading: false as boolean,
            serverItemsLength: 0 as number,
            stocks: new Array<any>(),
            cookie: useCookie('token'),
            serverOptions: <ServerOptions>{
                page: 1,
                rowsPerPage: 25,
                sortBy: "name",
            },
        };
    },
    computed: {
        chartData() {
            let data = {
                labels: new Array<string>(),
                datasets: [
                    {
                        label: 'Stock level',
                        backgroundColor: '#4ade80',
                        data: new Array<number>(),
                        cubicInterpolationMode: "monotone"
                    },
                    {
                        label: 'Minimum order level',
                        backgroundColor: '#ef4444',
                        data: new Array<number>(),
                        cubicInterpolationMode: "monotone"
                    }
                ]
            };
            this.stocks.map((stock: {
                minimum_order_level: number;
                quantity: number;
                stock_item: {
                    name: string;
                };
            }) => {
                data.labels.push(stock.stock_item.name);
                data.datasets[0].data.push(stock.quantity);
                data.datasets[1].data.push(stock.minimum_order_level);
            });
            return data;
        }
    },
    created() {
        this.init();
    },
    methods: {
        async init(): Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const { page, rowsPerPage } = this.serverOptions;
            let params = `?page=${page}&per_page=${rowsPerPage}&search=`;
            const { data, error, pending }: Response = await stockModule.getStock(`${this.cookie}`, params);
            this.loading = pending;
            if (data.value) {
                this.loading = false;
                this.stocks = data.value.data;
            }
            if (error.value) {
                this.loading = false;
                console.error(error.value);
            }
        }
    }
}
</script>
<style scoped>
.chartStyle {
    padding-top: 10px;
    height: 250px;
}
</style>
