<template>
  <div class="col-span-1 border rounded">
    <div class="bg-gray-50 border-b rounded-t px-2 py-2 text-lg font-semibold">
      Test Catalog <span class="text-base font-medium">(All)</span>
    </div>
    <div class="px-2 py-2 grid grid-cols-3 gap-4">
      <NuxtLink to="/test-catalog/organisms">
        <div
          class="bg-gray-50 rounded px-5 py-5 hover:bg-sky-500 transition duration-150 hover:text-white text-black"
        >
          <img
            src="@/assets/icons/virus.svg"
            class="w-10 h-10 mb-5s"
            alt="virus-svg"
          />
          <h3 class="text-2xl font-semibold mt-2">
            {{ props.data?.organisms }}
          </h3>
          <p>Organisms</p>
        </div>
      </NuxtLink>

      <NuxtLink to="/test-catalog/drugs">
        <div
          class="bg-gray-50 rounded px-5 py-5 hover:bg-sky-500 transition duration-150 hover:text-white text-black"
        >
          <img
            src="@/assets/icons/medicines.svg"
            class="w-10 h-10 mb-5s"
            alt="medicines-svg"
          />
          <h3 class="text-2xl font-semibold mt-2">{{ props.data?.drugs }}</h3>
          <p>Drugs</p>
        </div>
      </NuxtLink>

      <NuxtLink to="/test-catalog/diseases">
        <div
          class="bg-gray-50 rounded px-5 py-5 hover:bg-sky-500 transition duration-150 hover:text-white text-black"
        >
          <img
            src="@/assets/icons/bacteria.svg"
            class="w-10 h-10 mb-5s"
            alt="bacteria-svg"
          />
          <h3 class="text-2xl font-semibold mt-2">
            {{ props.data?.diseases }}
          </h3>
          <p>Diseases</p>
        </div>
      </NuxtLink>

      <NuxtLink to="/test-catalog/test-panels">
        <div
          class="bg-gray-50 rounded px-5 py-5 hover:bg-sky-500 transition duration-150 hover:text-white text-black"
        >
          <img
            src="@/assets/icons/emergency_post.svg"
            class="w-10 h-10 mb-5s"
            alt="virus-svg"
          />
          <h3 class="text-2xl font-semibold mt-2">
            {{ props.data?.test_panels }}
          </h3>
          <p>Test Panels</p>
        </div>
      </NuxtLink>

      <NuxtLink to="/test-catalog/test-types">
        <div
          class="bg-gray-50 rounded px-5 py-5 hover:bg-sky-500 transition duration-150 hover:text-white text-black"
        >
          <img
            src="@/assets/icons/cone_test_on_nets.svg"
            class="w-10 h-10 mb-5s"
            alt="cone-test-on-nets-svg"
          />
          <h3 class="text-2xl font-semibold mt-2">
            {{ props.data?.test_types }}
          </h3>
          <p>Test Types</p>
        </div>
      </NuxtLink>

      <NuxtLink to="/test-catalog/specimen-types">
        <div
          class="bg-gray-50 rounded px-5 py-5 hover:bg-sky-500 transition duration-150 hover:text-white text-black"
        >
          <img
            src="@/assets/icons/blood_drop.svg"
            class="w-10 h-10 mb-5s"
            alt="blood-drop-svg"
          />
          <h3 class="text-2xl font-semibold mt-2">
            {{ props.data?.specimen_types }}
          </h3>
          <p>Specimen Types</p>
        </div>
      </NuxtLink>

      <NuxtLink to="/test-catalog/lab-sections">
        <div
          class="bg-gray-50 rounded px-5 py-5 hover:bg-sky-500 transition duration-150 hover:text-white text-black"
        >
          <img
            src="@/assets/icons/admissions.svg"
            class="w-10 h-10 mb-5s"
            alt="admissions-svg"
          />
          <h3 class="text-2xl font-semibold mt-2">
            {{ props.data?.lab_sections }}
          </h3>
          <p>Laboratory Sections</p>
        </div>
      </NuxtLink>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  data: {
    required: false,
    type: Object,
  },
});
</script>
<style lang=""></style>
