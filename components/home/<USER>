<template>
  <div class="border rounded">
    <div>
      <div
        class="flex items-center justify-between rounded-t bg-gray-50 px-2 py-2 border-b text-xl font-semibold"
      >
        <h3>Lab Configuration <span class="text-base font-medium">(All)</span></h3>
      </div>
      <div class="px-3 py-3 space-y-4">
        <div class="flex items-center space-x-5">
          <div class="px-2 py-2 bg-gray-100 rounded">
            <img src="@/assets/icons/microscope.svg" class="w-8 h-8" />
          </div>
          <p class="text-3xl font-bold">
            {{ props.data?.instruments
            }}<span
              class="text-sm font-medium hover:text-sky-500 hover:underline ml-2"
              ><nuxt-link to="/lab-configuration/instruments"
                >instruments</nuxt-link
              ></span
            >
          </p>
        </div>
        <div class="flex items-center space-x-5">
          <div class="px-2 py-2 bg-gray-100 rounded">
            <img src="@/assets/icons/hospital.svg" class="w-8 h-8" />
          </div>
          <p class="text-3xl font-bold">
            {{ props.data?.facilities
            }}<span
              class="text-sm font-medium hover:text-sky-500 hover:underline ml-2"
              ><nuxt-link to="/lab-configuration/facilities"
                >facilities</nuxt-link
              ></span
            >
          </p>
        </div>
        <div class="flex items-center space-x-5">
          <div class="px-2 py-2 bg-gray-100 rounded">
            <img src="@/assets/icons/ambulatory_clinic.svg" class="w-8 h-8" />
          </div>
          <p class="text-3xl font-bold">
            {{ props.data?.wards
            }}<span
              class="text-sm font-medium hover:text-sky-500 hover:underline ml-2"
              ><nuxt-link to="/lab-configuration/facility-wards"
                >wards</nuxt-link
              ></span
            >
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
    data: {
        required: true,
        type: Object
    }
})
</script>
<style lang=""></style>
