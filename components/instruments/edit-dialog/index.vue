<script setup lang="ts">
import {
	TransitionRoot,
	TransitionChild,
	Dialog,
	DialogPanel,
	DialogTitle,
} from '@headlessui/vue';

import {
	XMarkIcon,
	ArrowDownTrayIcon as saveIcon,
	PencilSquareIcon as editIcon,
} from '@heroicons/vue/24/solid/index.js';
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import type { IEquipmentDetails, Request, Response } from '@/types';

const props = defineProps<{
	id: number;
}>();

const emit = defineEmits(['action-completed']);

const open: Ref<boolean> = ref<boolean>(false);
const loading: Ref<boolean> = ref<boolean>(false);

const testTypes: Ref<Array<{ name: string, id: number }>> = ref<Array<{ name: string, id: number }>>([]);

const supportedTests: Ref<Array<string>> = ref<Array<string>>([]);

const supportedTest: Ref<Array<string>> = ref<Array<string>>([]);

const body: Ref<IEquipmentDetails> = ref<IEquipmentDetails>({
	name: '',
	description: '',
	ip_address: '',
	hostname: '',
	created_date: '',
	supported_tests: new Array<number>()
});

const cookie: Ref<string> = useCookie('token');

const loadTests = async (): Promise<void> => {

	const request: Request = {
		route: `${endpoints.testTypes}`,
		method: 'GET',
		token: `${cookie.value}`,
	};

	const v: Response = await fetchRequest(request);

	if (v.data.value) {

		testTypes.value = v.data.value.test_types;

		testTypes.value.map((test) => {
			supportedTests.value.push(test.name)
		})

	}

	loading.value = false;

	if (v.error.value) {

		console.error(v.error.value);

	}

}

const loadingInstrument = async (): Promise<void> => {

	loading.value = true;

	supportedTest.value = new Array<string>();

	await loadTests();

	const request: Request = {
		route: `${endpoints.instrument.edit}/${props.id}`,
		method: 'GET',
		token: `${cookie.value}`,
	};

	const v: Response = await fetchRequest(request);

	if (v.data.value) {

		body.value = v.data.value;
		open.value = true;

		v.data.value.supported_tests.map((test: { name: string }) => {
			supportedTest.value.push(test.name)
		})

		console.log(body.value)
	}

	loading.value = false;

	if (v.error.value) {
		loading.value = false;
		useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
	}
};

const adjustVisibility = () => (open.value = !open.value);

const submitForm = async (): Promise<void> => {

	loading.value = true;

	let supported_tests = new Array<string>();

	testTypes.value.map((test) => {
		supportedTest.value.map((item) => {
			if (test.name == item) {
				supported_tests.push(test.id.toString())
			}
		})
	});

	body.value.supported_tests = supported_tests as any;

	const request: Request = {
		route: `${endpoints.instrument.update}/${props.id}`,
		method: 'PATCH',
		token: cookie.value,
		body: body.value,
	};

	const { data, pending, error } = await fetchRequest(request);

	loading.value = pending;

	if (data.value) {
		adjustVisibility();
		useNuxtApp().$toast.success(`Instrument details updated successfully!`);
		emit('action-completed', []);
	}

	if (error.value) useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);

	loading.value = false;
};</script>

<template>
	<div>
		<div>
			<CoreActionButton text="Edit" color="primary" :icon="editIcon" :click="loadingInstrument" />
		</div>
		<TransitionRoot appear :show="open" as="template">
			<Dialog as="div" @close="adjustVisibility" class="relative z-10">
				<TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0"
					enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
					<div class="fixed inset-0 bg-black bg-opacity-25"></div>
				</TransitionChild>

				<div class="fixed inset-0 overflow-y-auto">
					<div class="flex min-h-full items-center justify-center p-4 text-center">
						<TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
							enter-to="opacity-100 scale-100" leave="duration-200 ease-in"
							leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
							<DialogPanel
								class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
								<div class="border-b px-3 py-3 flex items-center justify-between">
									<DialogTitle as="h3"
										class="text-xl text-black flex items-center font-medium leading-6">
										<img src="~assets/icons/microscope.svg" class="w-8 h-8 mr-2" />
										Edit Instrument Details
									</DialogTitle>

									<button @click="adjustVisibility">
										<XMarkIcon class="w-5 h-5" />
									</button>
								</div>

								<FormKit type="form" id="patientForm" submit-label="Update" @submit="submitForm"
									:actions="false">
									<div class="mt-2 space-y-3 px-5 py-5">
										<div class="w-full grid grid-cols-1 gap-1">
											<FormKit type="text" label="Name" v-model="body.name" class="w-full"
												validation="required|text" />
										</div>

										<div class="w-full grid grid-cols-1 gap-1">
											<FormKit type="text" label="Host Name" v-model="body.hostname"
												class="w-full" validation="required|text" />
										</div>

										<div class="w-full grid grid-cols-1">
											<FormKit type="text" label="IP Address" v-model="body.ip_address"
												:validation="[
													['required'],
													[
														'matches',
														/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/i,
													],
												]" />
										</div>

										<div class="w-full grid grid-cols-1 gap-1">
											<FormKit type="textarea" v-model="body.description" label="Description"
												validation="required" />
										</div>

										<div class="w-full flex flex-col space-y-2 pb-40">
											<label class="font-medium">Supported tests</label>
											<multi-select style="--ms-max-height: none !important"
												v-model="supportedTest" :options="supportedTests" mode="tags"
												:searchable="true" :required="true" clear
												class="focus:ring-none fcus:border-none focus:outline-none multiselect-green" />
										</div>
									</div>

									<div class="mt-3 justify-end flex items-center space-x-3 px-3 py-2 border-t">
										<CoreOutlinedButton type="button" :click="adjustVisibility" text="Close" />
										<CoreActionButton type="submit" color="success" :icon="saveIcon"
											:click="() => { }" text="Save changes" :loading="loading" />
									</div>
								</FormKit>
							</DialogPanel>
						</TransitionChild>
					</div>
				</div>
			</Dialog>
		</TransitionRoot>
	</div>
</template>
