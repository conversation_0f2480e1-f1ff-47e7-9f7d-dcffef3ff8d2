<script setup lang="ts">
	import {
		TransitionRoot,
		TransitionChild,
		Dialog,
		DialogPanel,
		DialogTitle,
	} from '@headlessui/vue';

	import {
		XMarkIcon,
		UserIcon,
		ArrowDownTrayIcon as saveIcon,
		ArrowUturnLeftIcon as clearIcon,
		ArrowTopRightOnSquareIcon as viewIcon,
	} from '@heroicons/vue/24/solid/index.js';
	import moment from 'moment';
	import { endpoints } from '@/services/endpoints';
	import fetchRequest from '@/services/fetch';
	import type { IEquipmentDetails, Request, Response } from '@/types';

	const props = defineProps<{
		id: number;
	}>();

	const open: Ref<boolean> = ref<boolean>(false);
	const loading: Ref<boolean> = ref<boolean>(false);

	const data: Ref<IEquipmentDetails> = ref<IEquipmentDetails>({
		name: '',
		description: '',
		ip_address: '',
		hostname: '',
		supported_tests: '',
		created_date: '',
	});

	const cookie: Ref<string> = useCookie('token');

	const loadingInstrument = async (): Promise<void> => {
		loading.value = true;
		const request: Request = {
			route: `${endpoints.instrument.show}/${props.id}`,
			method: 'GET',
			token: `${cookie.value}`,
		};

		const v: Response = await fetchRequest(request);

		if (v.data.value) {
			data.value = v.data.value;
			open.value = true;
		}

		loading.value = false;

		if (v.error.value) {
			loading.value = false;
			useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
		}
	};

	const adjustVisibility = () => (open.value = !open.value);
</script>

<template>
	<div>
		<div>
			<CoreActionButton
				text="View"
				color="success"
				:icon="viewIcon"
				:click="loadingInstrument"
			/>
		</div>
		<TransitionRoot appear :show="open" as="template">
			<Dialog as="div" @close="adjustVisibility" class="relative z-10">
				<TransitionChild
					as="template"
					enter="duration-300 ease-out"
					enter-from="opacity-0"
					enter-to="opacity-100"
					leave="duration-200 ease-in"
					leave-from="opacity-100"
					leave-to="opacity-0"
				>
					<div class="fixed inset-0 bg-black bg-opacity-25"></div>
				</TransitionChild>

				<div class="fixed inset-0 overflow-y-auto">
					<div
						class="flex min-h-full items-center justify-center p-4 text-center"
					>
						<TransitionChild
							as="template"
							enter="duration-300 ease-out"
							enter-from="opacity-0 scale-95"
							enter-to="opacity-100 scale-100"
							leave="duration-200 ease-in"
							leave-from="opacity-100 scale-100"
							leave-to="opacity-0 scale-95"
						>
							<DialogPanel
								class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
							>
								<div
									class="border-b px-3 py-3 flex items-center justify-between"
								>
									<DialogTitle
										as="h3"
										class="text-xl text-black flex items-center font-medium leading-6"
									>
										<img src="~assets/icons/microscope.svg" class="w-8 h-8 mr-2"/>
										Instrument Details
									</DialogTitle>

									<button @click="adjustVisibility">
										<XMarkIcon class="w-5 h-5" />
									</button>
								</div>

								<FormKit
									type="form"
									id="patientForm"
									submit-label="Update"
									:actions="false"
								>
									<div class="mt-2 space-y-3 px-5 py-5">
										<div class="w-full grid grid-cols-1 gap-1">
											<label class="font-semibold text-lg">{{ 'Name' }}</label>
											<p>{{ data.name }}</p>
										</div>

										<div class="w-full grid grid-cols-1 gap-1">
											<label class="font-semibold text-lg">{{
												'Host Name'
											}}</label>
											<p>{{ data.hostname ?? '--' }}</p>
										</div>

										<div class="w-full grid grid-cols-1 gap-1">
											<label class="font-semibold text-lg">{{
												'IP Address'
											}}</label>
											<p>{{ data.ip_address ?? '--' }}</p>
										</div>

										<div class="w-full grid grid-cols-1 gap-1">
											<label class="font-semibold text-lg">{{
												'Can Perform'
											}}</label>
											<p v-for="test, index in data.supported_tests" :key="index">
												{{ test.name.charAt(0).toUpperCase() + test.name.slice(1) }}
											</p>
										</div>

										<div class="w-full grid grid-cols-1 gap-1">
											<label class="font-semibold text-lg">{{
												'Registration Date'
											}}</label>
											<p>
												{{ moment(data.created_date).format('DD/MMM/YYYY') }}
											</p>
										</div>

										<div class="w-full grid grid-cols-1 gap-1">
											<label class="font-semibold text-lg">{{
												'Equiment Description'
											}}</label>
											<p>{{ data.description ?? '--' }}</p>
										</div>
									</div>

									<div
										class="mt-3 justify-end flex items-center space-x-3 px-3 py-2 border-t"
									>
										<CoreOutlinedButton
											type="button"
											:click="adjustVisibility"
											text="Close"
										/>
									</div>
								</FormKit>
							</DialogPanel>
						</TransitionChild>
					</div>
				</div>
			</Dialog>
		</TransitionRoot>
	</div>
</template>
