<script setup lang="ts">
	import {
		TransitionRoot,
		TransitionChild,
		Dialog,
		DialogPanel,
		DialogTitle,
	} from '@headlessui/vue';

	import {
		XMarkIcon,
		TrashIcon as deleteIcon,
		ExclamationTriangleIcon,
	} from '@heroicons/vue/24/solid/index.js';
	import { endpoints } from '@/services/endpoints';
	import fetchRequest from '@/services/fetch';
	import type { Request, Response } from '@/types';

	const props = defineProps<{
		id: number;
		name: string;
	}>();

	const emit = defineEmits(['action-completed']);

	const show: Ref<boolean> = ref<boolean>(false);
	const loading: Ref<boolean> = ref<boolean>(false);
	const retired_reason: Ref<string> = ref<string>('');
	const cookie: Ref<string> = useCookie('token');

	const handleModal = (): void => {
		show.value = !show.value;
		retired_reason.value = '';
		loading.value = false;
	};

	const submitForm = async (): Promise<void> => {
		loading.value = true;

		const request: Request = {
			route: getParameterizedUrl(`${endpoints.instrument.delete}/${props.id}`, {
				retired_reason: retired_reason.value,
			}),
			method: 'DELETE',
			token: cookie.value,
		};

		const { data, pending, error } = await fetchRequest(request);

		loading.value = pending;

		if (data.value) {
			handleModal();
			useNuxtApp().$toast.success(`Instrument deleted successfully!`);
			emit('action-completed', []);
		}

		if (error.value) {
			useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
			loading.value = false;
		}
	};
</script>

<template>
	<div>
		<CoreActionButton
			:click="handleModal"
			color="error"
			text="Delete"
			:icon="deleteIcon"
		/>

		<TransitionRoot appear :show="show" as="template">
			<Dialog as="div" class="relative z-10">
				<TransitionChild
					as="template"
					enter="duration-300 ease-out"
					enter-from="opacity-0"
					enter-to="opacity-100"
					leave="duration-200 ease-in"
					leave-from="opacity-100"
					leave-to="opacity-0"
				>
					<div class="fixed inset-0 bg-black bg-opacity-25" />
				</TransitionChild>

				<div class="fixed inset-0 overflow-y-auto">
					<div
						class="flex min-h-full items-center justify-center p-4 text-center"
					>
						<TransitionChild
							as="template"
							enter="duration-300 ease-out"
							enter-from="opacity-0 scale-95"
							enter-to="opacity-100 scale-100"
							leave="duration-200 ease-in"
							leave-from="opacity-100 scale-100"
							leave-to="opacity-0 scale-95"
						>
							<DialogPanel
								class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
							>
								<div
									class="border-b px-3 py-3 flex items-center justify-between"
								>
									<DialogTitle
										as="h3"
										class="text-lg flex items-center font-medium leading-6"
									>
										<ExclamationTriangleIcon class="h-5 w-5 mr-2" />
										Confirm delete
									</DialogTitle>

									<button @click="handleModal">
										<XMarkIcon class="w-5 h-5" />
									</button>
								</div>

								<div class="mt-2 space-y-3 px-5">
									Do you really want to delete
									<strong class="text-red-500">{{ props.name }}</strong> ? Note that once this action is completed, it can not be undone
								</div>

								<FormKit
									type="form"
									id="patientForm"
									submit-label="Update"
									@submit="submitForm"
									:actions="false"
								>
									<div class="mt-2 space-y-3 px-5 py-5">
										<div class="w-full grid grid-cols-1 gap-1">
											<FormKit
												type="textarea"
												v-model="retired_reason"
												label="Reason"
												validation="required"
											/>
										</div>
									</div>

									<div
										class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t bg-gray-50"
									>
										<CoreOutlinedButton
											type="button"
											text="Cancel"
											:click="handleModal"
										/>

										<CoreActionButton
											type="submit"
											color="error"
											text="Delete"
											:icon="deleteIcon"
											:click="() => {}"
											:loading="loading"
										/>
									</div>
								</FormKit>
							</DialogPanel>
						</TransitionChild>
					</div>
				</div>
			</Dialog>
		</TransitionRoot>
	</div>
</template>
