<template>
  <div>
    <div>
      <CoreActionButton
        text="Add instrument"
        color="primary"
        :icon="addIcon"
        :click="loadTests"
      />
    </div>
    <TransitionRoot appear :show="open" as="template">
      <Dialog as="div" @close="closeForm" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-xl text-black flex items-center font-medium leading-6"
                  >
                    <img src="~assets/icons/microscope.svg" class="w-8 h-8 mr-2"/>
                    Add Instrument
                  </DialogTitle>

                  <button @click="adjustVisibility">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  id="patientForm"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                >
                  <div class="mt-2 space-y-3 px-5 py-5">
                    <div class="w-full grid grid-cols-1 gap-1">
                      <FormKit
                        type="text"
                        label="Name"
                        v-model="equipmentDetails.name"
                        class="w-full"
                        validation="required|text"
                      />
                    </div>

                    <div class="w-full grid grid-cols-1 gap-1">
                      <FormKit
                        type="textarea"
                        v-model="equipmentDetails.description"
                        label="Description"
                        validation="required"
                      />
                    </div>

                    <div class="w-full flex flex-col space-y-2 pb-40">
                      <label class="font-medium">Supported tests</label>
                      <multi-select
                        style="--ms-max-height: none !important"
                        v-model="supportedTest"
                        :options="supportedTests"
                        mode="tags"
                        :searchable="true"
                        :required="true"
                        clear
                        class="focus:ring-none fcus:border-none focus:outline-none multiselect-green"
                      />
                    </div>
                  </div>

                  <div
                    class="mt-3 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton
                      type="button"
                      text="Close"
                      :click="(() => { closeForm })"
                    />
                    <CoreActionButton
                      type="submit"
                      color="success"
                      :icon="saveIcon"
                      :click="() => {}"
                      text="Save changes"
                      :loading="loading"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  PlusIcon,
  XMarkIcon,
  ArrowDownTrayIcon,
  ArrowUturnLeftIcon,
} from "@heroicons/vue/24/solid/index.js";
import type { Item } from "vue3-easy-data-table";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { IEquipmentDetails, Request, Response, TestType } from "@/types";

export default{

	components: {
		TransitionRoot,
		TransitionChild,
		Dialog,
		DialogPanel,
		DialogTitle,
		XMarkIcon
	},

	data() {
		return {
			addIcon: PlusIcon as Object,
			saveIcon: ArrowDownTrayIcon as Object,
			clearIcon: ArrowUturnLeftIcon as Object,
			open: false as boolean,
			loading: false as boolean,
			equipmentDetails: { name: "", description: "", supported_tests: new Array<number>() } as IEquipmentDetails,
			instruments: new Array<Item>(),
			testTypes: new Array<TestType>(),
			supportedTests: new Array<String>(),
			supportedTest:  new Array<String>(),
			cookie: useCookie('token')
		}
	},
	methods: {

		/**
		 * @method adjustVisibility
		 * @returns void
		 */
		adjustVisibility() : void {

			this.open = !this.open;

		},

		/**
		 * @method loadTests
		 * @returns @type Promise void
		 */
		async loadTests (): Promise<void> {

			this.adjustVisibility();

			const request: Request = {
				route: endpoints.testTypes,
				method: "GET",
				token: `${this.cookie}`
			};

			const { data, pending, error } : Response = await fetchRequest(request);

			if(data.value){

				this.testTypes = data.value.test_types;

				data.value.test_types.map((testType: { name: string }) => {
					this.supportedTests.push(testType.name)
				})

			}

			if(error.value){
				console.error(error.value)
			}
		},

		/**
		 * @method submitForm
		 * @return @type Promise void
		 */
		async submitForm () : Promise<void> {

			this.loading = true;

			let supported_tests = new Array<number>();

			this.testTypes.map((testType) => {
				this.supportedTest.map((test) => {
					if(testType.name === test){
						supported_tests.push(testType.id)
					}
				})
			});

			this.equipmentDetails.supported_tests = supported_tests;

			const request: Request = {
				route: endpoints.instrument.create,
				method: "POST",
				token: `${this.cookie}`,
				body: this.equipmentDetails,
			};

			const { data, pending, error } : Response = await fetchRequest(request);

			this.loading = pending;

			if (data.value) {
				this.closeForm();
				useNuxtApp().$toast.success(`Instrument added successfully!`);
				this.$emit("action-completed", []);
			}

			if (error.value) {

        error.value.data.error == "Validation failed: Name has already been taken" ? useNuxtApp().$toast.error('Name has already been taken') : useNuxtApp().$toast.error(ERROR_MESSAGE)

				this.loading = false;

				console.error(error.value);

			}
		},

		/**
		 * @method closeForm
		 * @returns void
		 */
		closeForm () : void {
			this.open = false;
			this.equipmentDetails = { name: "", description: "", supported_tests: new Array<number>() }
      this.supportedTest = new Array<string>();
		}
	}
}
</script>
