<template>
    <div>
        <div>
            <CoreActionButton :disabled="true" :click="handleClick" text="New driver" color="warning" :icon="driverIcon"/>
          </div>
          <TransitionRoot appear :show="open" as="template">
            <Dialog as="div" @close="handleClick" class="relative z-10">
              <TransitionChild
                as="template"
                enter="duration-300 ease-out"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="duration-200 ease-in"
                leave-from="opacity-100"
                leave-to="opacity-0"
              >
                <div class="fixed inset-0 bg-black bg-opacity-25" />
              </TransitionChild>

              <div class="fixed inset-0 overflow-y-auto">
                <div
                  class="flex min-h-full items-center justify-center p-4 text-center"
                >
                  <TransitionChild
                    as="template"
                    enter="duration-300 ease-out"
                    enter-from="opacity-0 scale-95"
                    enter-to="opacity-100 scale-100"
                    leave="duration-200 ease-in"
                    leave-from="opacity-100 scale-100"
                    leave-to="opacity-0 scale-95"
                  >
                    <DialogPanel
                      class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
                    >

                        <div class="border-b px-3 py-3 flex items-center justify-between">
                            <DialogTitle
                                as="h3"
                                class="text-lg flex items-center font-medium leading-6"
                            >
                                Add new equipment drivers
                            </DialogTitle>

                            <button @click="handleClick">
                                <XMarkIcon class="w-5 h-5"/>
                            </button>

                        </div>

                      <div class="mt-2 space-y-3 px-5">

                        <CoreAlert text="Warning: Do not install plugins from untrusted sources!"/>

                        <div class="flex items-center justify-center w-full">
                            <label for="dropzone-file" class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-bray-800 hover:bg-gray-100 ">
                                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                    <svg aria-hidden="true" class="w-10 h-10 mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path></svg>
                                    <p class="mb-2 text-sm text-gray-500 "><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                    <p class="text-xs text-gray-500 ">EXE, ZIP, TG (Max 500MB)</p>
                                </div>
                                <input id="dropzone-file" type="file" class="hidden" />
                            </label>
                        </div>

                      </div>

                      <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                        <CoreOutlinedButton text="Dismiss"/>
                        <CoreActionButton :click="handleClick" :icon="saveIcon" text="Save"/>
                      </div>
                    </DialogPanel>
                  </TransitionChild>
                </div>
              </div>
            </Dialog>
          </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue'

import { PlusIcon,WrenchScrewdriverIcon, XMarkIcon, UserIcon, ArrowDownTrayIcon, ArrowUturnLeftIcon } from '@heroicons/vue/24/solid/index.js'

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon,
        UserIcon
    },
    data(){

        return{
            open: false,
            driverIcon: WrenchScrewdriverIcon,
            addIcon: PlusIcon,
            saveIcon: ArrowDownTrayIcon,
            clearIcon: ArrowUturnLeftIcon
        }
    },
    methods:{
        handleClick(){
            this.open = !this.open
        }
    }
}
</script>

<style>

</style>
