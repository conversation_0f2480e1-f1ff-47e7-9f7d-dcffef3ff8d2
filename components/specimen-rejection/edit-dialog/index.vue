<template>
    <div>

        <CoreActionButton :click="handleClick" color="success" text="Edit" :icon="editIcon" />

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        <img src="~assets/icons/clinical_fe.svg" class="w-8 h-8 mr-2" />
                                        Edit Sample Rejection Reason
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" submit-label="Update" @submit="submitForm" :actions="false"
                                    #default="{ value }" id="submitForm">

                                    <div class="mt-2 space-y-3">
                                        <div class="w-full flex items-center px-5 space-x-3">
                                            <div class="w-full flex flex-col space-y-2">
                                                <FormKit type="textarea" label="Reason" validation="required"
                                                    v-model="data.description" />
                                            </div>
                                        </div>

                                        <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                            <CoreOutlinedButton type="button" :click="(() => { clearForm()})" text="Clear form" color="error" />
                                            <CoreActionButton type="submit" :click="(() => { })" color="success"
                                                :loading="loading" :icon="saveIcon" text="Save changes" />
                                        </div>

                                    </div>
                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

import { XMarkIcon, ArrowDownTrayIcon, ArrowTopRightOnSquareIcon, PencilSquareIcon } from '@heroicons/vue/24/solid/index.js'
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import type { Request, Response } from '@/types';

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon
    },
    data() {

        return {
            viewIcon: ArrowTopRightOnSquareIcon,
            show: false,
            editIcon: PencilSquareIcon,
            saveIcon: ArrowDownTrayIcon,
            cookie: useCookie("token"),
            loading: false as boolean
        }
    },
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    methods: {
        /**
         * @method submitForm updates rejection reason
         * @param null
         * @returns promise @typeof void
         */
        async submitForm(): Promise<void> {

            this.loading = true;

            const request : Request = {
                route: `${endpoints.rejectionReasons}/${this.data.id}`,
                method: "PUT",
                token: `${this.cookie}`,
                body: this.data
            }

            const { pending, error, data }: Response = await fetchRequest(request);

            this.loading = pending;

            if (data.value) {

                this.handleClick();

                useNuxtApp().$toast.success(`Specimen rejection reason updated successfully!`);

                this.loading = false;

                this.$emit('update', true);
            }

            if (error.value) {

                useNuxtApp().$toast.success(`An error occurred, please try again!`);

                this.handleClick();

                console.error(error.value);

                this.loading = false;
            }
        },
        /**
         * @method handleClick
         * @param null
         * @returns void
         */
        handleClick(): void {
            this.show = !this.show
        },
        /**
         * @method clearForm
         * @param null
         * @returns void
         */
        clearForm(): void {
            this.$formkit.reset('submitForm');
        }
    }
}
</script>

<style>
</style>
