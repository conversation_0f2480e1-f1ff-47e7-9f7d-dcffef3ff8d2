<template>
    <div>
        <CoreActionButton :click="init" text="Create role" color="success" :icon="addIcon" />

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        Create role
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" id="submitForm" submit-label="Update" @submit="submitForm"
                                    :actions="false" #default="{ value }">

                                    <div class="mt-2 space-y-3">
                                        <div class="w-full flex items-center px-5">
                                            <div class="w-full flex flex-col space-y-2">
                                                <FormKit type="text" label="Name" validation="required" v-model="name" />
                                            </div>
                                        </div>

                                        <div class="w-full flex flex-col space-y-2 px-5 pb-36">
                                            <label class="font-medium">Permissions</label>
                                            <multi-select style="--ms-max-height: none !important;"
                                                v-model="previlegesSelected" :options="privileges" mode="tags" required
                                                clear searchable
                                                class="focus:ring-none fcus:border-none focus:outline-none multiselect-green" />
                                        </div>
                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreOutlinedButton type="button" :click="(() => clearForm())" text="Clear form" />
                                        <CoreActionButton :loading="loading" type="submit" :click="(() => { })"
                                            color="success" :icon="saveIcon" text="Save changes" />
                                    </div>

                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import { XMarkIcon, ArrowDownTrayIcon, PlusIcon } from '@heroicons/vue/24/solid/index.js'
import { endpoints } from '@/services/endpoints';
import type { Request, Response } from '@/types';
import fetchRequest from '@/services/fetch';

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon
    },
    data() {

        return {
            addIcon: PlusIcon,
            show: false as boolean,
            saveIcon: ArrowDownTrayIcon,
            name: "" as string,
            loading: false as boolean,
            cookie: useCookie('token'),
            rawPrivileges: new Array<any>(),
            privileges: new Array<any>(),
            previlegesSelected: new Array<string>()
        }
    },
    methods: {
        async init(): Promise<void> {
            this.handleClick();
            const request: Request = {
                route: endpoints.privileges,
                method: 'GET',
                token: `${this.cookie}`
            }
            const { data, error }: Response = await fetchRequest(request);
            if (data.value) {
                this.rawPrivileges = data.value;
                data.value.map((item: any) => {
                    this.privileges.push(item.display_name)
                })
            }
            if(error.value){
                console.error(error.value)
            }
        },
        async submitForm() {
            this.loading = true;
            let privileges = new Array<number>();
            this.previlegesSelected.map((item: any) => {
                this.rawPrivileges.map((privilege: any) => {
                    if (item.name === privilege.name) {
                        this.privileges.push(privilege.id)
                    }
                })
            })

            const request: Request = {
                route: endpoints.roles,
                method: 'POST',
                token: `${this.cookie}`,
                body: {
                    "name": this.name,
                    "privileges": privileges
                }
            }
            const { data, error }: Response = await fetchRequest(request);
            if (data.value) {
                this.handleClick()
                useNuxtApp().$toast.success(`Role created successfully!`);
                this.loading = false;
                this.$emit('update', true);
            }

            if (error.value) {
                this.handleClick()
                useNuxtApp().$toast.error(ERROR_MESSAGE)
                this.loading = false;
                console.error(error.value)
            }
        },
        clearForm(): void {
            this.$formkit.reset('submitForm')
        },
        handleClick() {
            this.show = !this.show
        }
    }
}
</script>

<style>
</style>
