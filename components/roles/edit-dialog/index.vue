<template>
    <div>
        <CoreActionButton :click="init" text="Edit" color="success" :icon="editIcon" />

        <TransitionRoot appear :show="show" as="template">
            <Dialog as="div" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">
                                    <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                                        <img src="@/assets/icons/group.svg" class="w-8 h-8 mr-2"/>
                                        Edit role
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" submit-label="Update" @submit="submitForm" :actions="false"
                                    #default="{ value }">

                                    <div class="mt-2 space-y-3">
                                        <div class="w-full flex items-center px-5">
                                            <div class="w-full flex flex-col space-y-2">
                                                <FormKit type="text" label="Name" validation="required" v-model="name" />
                                            </div>
                                        </div>
                                    </div>

                                    <div class="w-full flex flex-col space-y-2 px-5 pb-36 mt-3">
                                        <label class="font-medium">Permissions</label>
                                        <multi-select style="--ms-max-height: none !important;" v-model="previlegesSelected"
                                            :options="privileges" mode="tags" required clear searchable
                                            class="focus:ring-none fcus:border-none focus:outline-none multiselect-green" />
                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreOutlinedButton text="Clear form" />
                                        <CoreActionButton :loading="loading" type="submit" :click="(() => { })"
                                            color="success" :icon="saveIcon" text="Save changes" />
                                    </div>

                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import { XMarkIcon, PencilSquareIcon, ArrowDownTrayIcon } from '@heroicons/vue/24/solid/index.js'
import fetchRequest from '@/services/fetch';
import { endpoints } from '@/services/endpoints';
import type { Request, Response } from '@/types';

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon
    },
    data() {

        return {
            editIcon: PencilSquareIcon,
            show: false,
            saveIcon: ArrowDownTrayIcon,
            name: this.data.name as string,
            loading: false as boolean,
            cookie: useCookie('token'),
            rawPrivileges: new Array<any>(),
            privileges: new Array<any>(),
            previlegesSelected: new Array<string>()
        }
    },
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    created() {
        this.loadPrivileges();
    },
    methods: {
        async loadPrivileges(): Promise<void> {
            const request: Request = {
                route: endpoints.privileges,
                method: 'GET',
                token: `${this.cookie}`
            }
            const { data, error }: Response = await fetchRequest(request);

            if (data.value) {
                this.rawPrivileges = data.value
                data.value.map((item: { display_name: string }) => {
                    this.privileges.push(item.display_name)
                })
            }

            if (error.value) {
                console.error(error.value)
            }
        },
        async init(): Promise<void> {

            this.handleClick();

            const request: Request = {
                route: `${endpoints.roles}/${this.data.id}`,
                method: 'GET',
                token: `${this.cookie}`
            }
            const { data, error }: Response = await fetchRequest(request);

            if (data.value) {
                data.value.privileges.map((item: { display_name: string }) => {
                    this.previlegesSelected.push(item.display_name);
                })
            }

            if (error.value) {
                console.error(error.value);
            }
        },
        async submitForm(): Promise<void> {
            this.loading = true;
            const privileges = this.rawPrivileges
                .filter(item => this.previlegesSelected.includes(item.display_name))
                .map(item => item.id);

            const request: Request = {
                route: `${endpoints.roles}/${this.data.id}`,
                method: 'PUT',
                token: `${this.cookie}`,
                body: {
                    "name": this.name,
                    "privileges": privileges
                }
            }
            const { data, error, pending }: Response = await fetchRequest(request);
            this.loading = pending;
            if (data.value) {
                this.handleClick();
                this.previlegesSelected = new Array<string>()
                useNuxtApp().$toast.success(`Role updated successfully!`);
                this.loading = false;
                this.$emit('update', true);
            }

            if (error.value) {
                this.handleClick()
                console.error(error.value)
                useNuxtApp().$toast.success(ERROR_MESSAGE);
                this.loading = false;
            }
        },
        handleClick() {
            this.show = !this.show
        }
    }
}
</script>

<style>
</style>
