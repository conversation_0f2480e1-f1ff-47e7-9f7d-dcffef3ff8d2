<template>
  <div>
    <CoreActionButton
      :click="handleClick"
      text="Edit"
      color="success"
      :icon="editIcon"
    />

    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-lg flex items-center font-medium leading-6"
                  >
                    <img
                      src="@/assets/icons/hospital.svg"
                      class="w-8 h-8 mr-2"
                      alt="hospital-icon"
                    />
                    Edit Laboratory Section
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>
                <FormKit
                  id="submitForm"
                  type="form"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  #default="{ value }"
                >
                  <div class="mt-2 space-y-3 px-5">
                    <FormKit
                      type="text"
                      label="Preferred name"
                      validation="required"
                      v-model="props.data.preferred_name"
                    />
                  </div>

                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton
                      text="Clear form"
                      type="button"
                      :click="clearForm"
                    />
                    <CoreActionButton
                      :loading="loading"
                      type="submit"
                      :click="() => {}"
                      color="success"
                      :icon="saveIcon"
                      text="Save changes"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";
import {
  XMarkIcon,
  PencilSquareIcon,
  ArrowDownTrayIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response } from "@/types";
import type { LabSection } from "@/types";
import { reset } from "@formkit/core";

const props = defineProps<{
  data: LabSection;
}>();

const emit = defineEmits<{
  update: [updated: boolean];
}>();

const editIcon = PencilSquareIcon;
const show = ref<boolean>(false);
const saveIcon = ArrowDownTrayIcon;
const loading = ref<boolean>(false);

const handleClick = (): void => {
  show.value = !show.value;
};

const clearForm = (): void => {
  reset("submitForm");
};

const submitForm = async (): Promise<void> => {
  loading.value = true;

  const request: Request = {
    route: `${endpoints.departments}/${props.data.id}`,
    method: "PUT",
    body: { preferred_name: props.data.preferred_name },
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;

  if (data.value) {
    handleClick();
    useNuxtApp().$toast.success("Laboratory section updated successfully!");
    loading.value = false;
    emit("update", true);
  }

  if (error.value) {
    handleClick();
    useNuxtApp().$toast.error(
      "An error occurred while updating the laboratory section."
    );
    console.error(error.value);
    loading.value = false;
  }
};
</script>

<style></style>
