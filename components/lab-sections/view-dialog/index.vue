<template>
  <CoreActionButton
    :click="handleClick"
    color="primary"
    text="View"
    :icon="viewIcon"
  />

  <TransitionRoot appear :show="show" as="template">
    <Dialog as="div" class="relative z-10">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-900 bg-opacity-25"></div>
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div
          class="flex min-h-full items-center justify-center p-4 text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
            >
              <div class="border-b px-3 py-3 flex items-center justify-between">
                <DialogTitle
                  as="h3"
                  class="text-lg flex items-center font-medium leading-6"
                >
                  <img
                    src="@/assets/icons/hospital.svg"
                    class="w-8 h-8 mr-2"
                    alt="hospital-icon"
                  />
                  View Laboratory Section
                </DialogTitle>

                <button @click="handleClick">
                  <XMarkIcon class="w-5 h-5" />
                </button>
              </div>

              <div class="space-y-3 px-5 py-5">
                <div class="w-full flex flex-col space-y-4">
                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Name</label>
                    <p class="text-base">{{ data.name }}</p>
                  </div>

                  <div class="grid grid-cols-2 gap-4">
                    <div class="flex flex-col space-y-1">
                      <label class="font-semibold">NLIMS Code</label>
                      <p class="text-gray-600">{{ data.nlims_code || "Not specified" }}</p>
                    </div>

                    <div class="flex flex-col space-y-1">
                      <label class="font-semibold">MOH Code</label>
                      <p class="text-gray-600">{{ data.moh_code || "Not specified" }}</p>
                    </div>

                    <div class="flex flex-col space-y-1">
                      <label class="font-semibold">LOINC Code</label>
                      <p class="text-gray-600">{{ data.loinc_code || "Not specified" }}</p>
                    </div>

                    <div class="flex flex-col space-y-1">
                      <label class="font-semibold">Scientific Name</label>
                      <p class="text-gray-600">{{ data.scientific_name || "Not specified" }}</p>
                    </div>

                    <div class="flex flex-col space-y-1">
                      <label class="font-semibold">Preferred Name</label>
                      <p class="text-gray-600">{{ data.preferred_name || "Not specified" }}</p>
                    </div>

                    <div class="flex flex-col space-y-1">
                      <label class="font-semibold">Short Name</label>
                      <p class="text-gray-600">{{ data.short_name || "Not specified" }}</p>
                    </div>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold">Description</label>
                    <div
                      class="text-gray-700"
                      v-html="data.description || 'No description available'"
                    ></div>
                  </div>

                  <div class="">
                    <div class="items-start flex flex-col space-y-1">
                      <label class="font-semibold">Status</label>
                      <div
                        class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                        :class="
                          data.retired
                            ? 'bg-red-100 text-red-800'
                            : 'bg-green-100 text-green-800'
                        "
                      >
                        <div
                          class="w-2 h-2 rounded-full text-sm mr-2 border"
                          :class="
                            data.retired
                              ? 'bg-red-600 border-red-400'
                              : 'bg-green-600 border-green-400'
                          "
                        ></div>
                        {{ data.retired ? "Retired" : "Active" }}
                      </div>
                    </div>
                  </div>

                  <div
                    v-if="data.retired"
                    class="w-full flex flex-col space-y-1 border-t pt-3"
                  >
                    <label class="font-semibold text-red-600"
                      >Retirement Information</label
                    >
                    <div class="grid grid-cols-2 gap-4">
                      <div class="flex flex-col space-y-1">
                        <label class="font-semibold">Retired By</label>
                        <p class="text-gray-600">{{ data.retired_by || "Not specified" }}</p>
                      </div>

                      <div class="flex flex-col space-y-1">
                        <label class="font-semibold">Retired Date</label>
                        <p class="text-gray-600">
                          {{
                            data.retired_date
                              ? new Date(data.retired_date).toLocaleString()
                              : "Not specified"
                          }}
                        </p>
                      </div>

                      <div class="col-span-2 flex flex-col space-y-1">
                        <label class="font-semibold">Retired Reason</label>
                        <p class="text-gray-600">
                          {{ data.retired_reason || "No reason provided" }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  ArrowTopRightOnSquareIcon,
} from "@heroicons/vue/24/solid/index.js";
import { ref } from "vue";
import type { LabSection } from "@/types";

defineProps<{
  data: LabSection;
}>();

const show = ref<boolean>(false);
const viewIcon = ArrowTopRightOnSquareIcon;

const handleClick = (): void => {
  show.value = !show.value;
};
</script>

<style></style>
