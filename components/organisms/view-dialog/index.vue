<template>
  <div>
    <CoreActionButton
      :click="init"
      color="primary"
      text="View"
      :icon="viewIcon"
    />

    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-lg flex items-center font-medium leading-6"
                  >
                    <img
                      src="@/assets/icons/bacteria.svg"
                      class="w-8 h-8 mr-2"
                    />
                    View {{ organism?.name }}
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <div
                  v-show="loading"
                  class="flex items-center justify-center mx-auto my-20"
                >
                  <CoreLoader :loading="loading" />
                </div>

                <div class="space-y-3 px-5 py-5" v-show="!loading">
                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Preferred name</label>
                    <p class="text-gray-600">{{ organism.preferred_name }}</p>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Description</label>
                    <p class="text-gray-600" v-if="organism.description">
                      {{ organism.description }}
                    </p>
                    <div
                      class="bg-red-50 py-2 px-2 flex items-center space-x-2"
                      v-else
                    >
                      <ExclamationTriangleIcon
                        class="w-5 h-5 text-red-500 rounded-md"
                      />
                      <p class="text-red-500 font-medium">
                        Description not found
                      </p>
                    </div>
                  </div>

                  <div class="w-full flex flex-col space-y-1">
                    <label class="font-semibold text-lg">Drugs</label>
                    <div v-if="organism.drugs && organism.drugs.length > 0" class="text-gray-600">
                      <ul class="list-disc pl-5 space-y-1">
                        <li v-for="drug in organism.drugs" :key="drug.id">{{ drug.name }}</li>
                      </ul>
                    </div>
                    <p v-else class="text-gray-600">No drugs associated with this organism</p>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  ArrowTopRightOnSquareIcon,
  PencilSquareIcon,
  ExclamationTriangleIcon,
} from "@heroicons/vue/24/solid/index.js";

import moment from "moment";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Organism, Request, Response } from "@/types";

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
    ExclamationTriangleIcon,
  },
  data() {
    return {
      viewIcon: ArrowTopRightOnSquareIcon,
      show: false as boolean,
      loading: false as boolean,
      editIcon: PencilSquareIcon,
      moment: moment,
      cookie: useCookie("token"),
      organism: {} as Organism,
    };
  },
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  methods: {
    handleClick(): void {
      this.show = !this.show;
    },
    /**
     * @method init load organisms data
     * @returns promise @type void
     */
    async init(): Promise<void> {
      this.handleClick();
      this.loading = true;
      const request: Request = {
        route: `${endpoints.organisms}/${this.data.id}`,
        method: "GET",
        token: `${this.cookie}`,
      };
      const { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;
      if (data.value) {
        this.organism = data.value;
        this.loading = false;
      }
      if (error.value) {
        console.error('error loading organism data: ', error.value);
        this.loading = false;
      }
    },
  },
};
</script>

<style></style>
