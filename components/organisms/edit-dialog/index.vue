<template>
  <div>
    <CoreActionButton
      :click="init"
      text="Edit"
      color="success"
      :icon="editIcon"
    />

    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-gray-900 bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div
                  class="border-b px-3 py-3 flex items-center justify-between"
                >
                  <DialogTitle
                    as="h3"
                    class="text-lg flex items-center font-medium leading-6"
                  >
                    <img
                      src="@/assets/icons/bacteria.svg"
                      class="w-8 h-8 mr-2"
                    />
                    Edit organism
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  #default="{ value }"
                  id="submitForm"
                >
                  <div class="mt-2 space-y-3 px-5">
                    <FormKit
                      type="text"
                      label="Preferred name"
                      validation="required"
                      v-model="data.preferred_name"
                    />
                  </div>

                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreOutlinedButton
                      text="Clear form"
                      type="button"
                      :click="
                        () => {
                          clearForm();
                        }
                      "
                    />
                    <CoreActionButton
                      :loading="loading"
                      type="submit"
                      :click="() => {}"
                      color="success"
                      :icon="saveIcon"
                      text="Save changes"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  PencilSquareIcon,
  ArrowDownTrayIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response } from "@/types";
import { reset } from "@formkit/vue";

interface Props {
  data: {
    id: string | number;
    [key: string]: any;
  };
}

const props = defineProps<Props>();

const emit = defineEmits<{
  update: [value: boolean];
}>();

const editIcon = PencilSquareIcon;
const show = ref<boolean>(false);
const saveIcon = ArrowDownTrayIcon;
const drugSelected = ref<string[]>([]);
const loading = ref<boolean>(false);

const init = async (): Promise<void> => {
  handleClick();
  drugSelected.value = [];

  const request: Request = {
    route: `${endpoints.organisms}/${props.data.id}`,
    method: "GET",
  };
  const { error, data }: Response = await fetchRequest(request);
  if (data.value) {
    data.value.drugs.map((item: any) => {
      drugSelected.value.push(item.name);
    });
  }
  if (error.value) {
    console.error(error.value);
  }
};

const submitForm = async (): Promise<void> => {
  loading.value = true;

  const request: Request = {
    route: `${endpoints.organisms}/${props.data.id}`,
    method: "PUT",
    body: props.data,
  };

  const { pending, error, data }: Response = await fetchRequest(request);

  loading.value = pending;

  if (data.value) {
    show.value = false;
    useNuxtApp().$toast.success(`Organism updated successfully!`);
    loading.value = false;
    emit("update", true);
  }

  if (error.value) {
    show.value = false;
    console.error(error.value);
    loading.value = false;
    useNuxtApp().$toast.error(ERROR_MESSAGE);
  }
};

const handleClick = (): void => {
  show.value = !show.value;
};

const clearForm = (): void => {
  reset("submitForm");
};

defineExpose({
  init,
  handleClick,
  clearForm,
});
</script>

<style></style>
