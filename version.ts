const { execSync } = require('child_process');
const fs = require('fs-extra');

function getGitTag() {
  try {
    const tag = execSync('git describe --tags --abbrev=0', { encoding: 'utf8' }).trim();
    return tag;
  } catch (error) {
    if (error instanceof Error) {
      console.error('Error getting Git tag:', error.message);
    } else {
      console.error('Unknown error occurred while getting Git tag.');
    }
    process.exit(1);
  }
}

function updatePackageJson(tag: string) {
  try {
    const packageJsonPath = 'package.json';
    const packageJson = fs.readJSONSync(packageJsonPath);
    packageJson.version = tag;
    fs.writeJSONSync(packageJsonPath, packageJson, { spaces: 2 });
    console.log(`Updated package.json version to ${tag}`);
  } catch (error) {
    if (error instanceof Error) {
      console.error('Error updating package.json:', error.message);
    } else {
      console.error('Unknown error occurred while updating package.json.');
    }
    process.exit(1);
  }
}

function main() {
  const tag = getGitTag();
  updatePackageJson(tag);
}

main();
