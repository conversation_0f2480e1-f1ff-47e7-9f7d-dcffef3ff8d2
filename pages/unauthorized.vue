<template>
  <div>
    <div
      class="w-full flex-col items-center py-20 justify-center mx-auto my-auto flex"
    >
      <ExclamationTriangleIcon
        class="w-20 h-20 object-cover text-red-600 animate-pulse"
      />
      <h3 class="text-xl font-medium mt-3">
        You do not have permission to access the requested page!
      </h3>
      <p class="text-base text-gray-700">Please contact your administrator</p>
      <CoreActionButton
        :icon="ArrowLeftIcon"
        text="Go back"
        color="primary"
        class="mt-2"
        :click="
          () => {
            navigateBack();
          }
        "
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowLeftIcon,
  ExclamationTriangleIcon,
} from "@heroicons/vue/24/solid/index.js";
const navigateBack = () => {
  const router = useRouter();
  router.back();
};
</script>

<style scoped></style>
