<template>
  <div class="px-5 py-5 mx-auto">
    <div class="mb-8">
      <CoreBreadcrumb :pages="pages" />
    </div>

    <div class="bg-white rounded border p-6 mb-8">
      <div class="flex items-center space-x-5">
        <div class="relative">
          <div
            class="bg-gradient-to-r from-gray-200 to-zinc-300 rounded-full p-1"
          >
            <img
              src="@/assets/images/user.png"
              class="w-20 h-20 object-cover rounded-full border border-white"
              alt="user-icon"
            />
          </div>
        </div>
        <div class="space-y-1">
          <h3 class="text-xl font-bold text-gray-800">
            {{ authStore.user.username }}
          </h3>
          <p class="text-gray-600 text-sm">
            {{ `${authStore.user.first_name} ${authStore.user.last_name}` }}
          </p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded border overflow-hidden">
      <div class="border-b">
        <div class="flex">
          <button
            v-for="(tab, index) in tabs"
            :key="index"
            @click="activeTab = index"
            class="px-6 py-4 font-medium focus:outline-none transition duration-200 ease-in-out relative"
            :class="
              activeTab === index
                ? 'text-sky-500 hover:text-sky-600'
                : 'text-gray-600 hover:text-gray-800'
            "
          >
            <div class="flex items-center">
              <IdentificationIcon v-if="index == 0" class="w-5 h-5 mr-2" />
              <PencilSquareIcon v-if="index == 1" class="w-5 h-5 mr-2" />
              {{ tab }}
            </div>
            <div
              v-if="activeTab === index"
              class="absolute bottom-0 left-0 w-full h-0.5 bg-sky-600"
            ></div>
          </button>
        </div>
      </div>
      <div v-if="activeTab === 0" class="p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-6">
          Personal Information
        </h2>
        <div class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-gray-50 rounded-lg p-4">
              <label class="text-sm font-medium text-gray-500">Username</label>
              <p class="mt-1 text-gray-900 font-semibold">
                {{ authStore.user.username }}
              </p>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
              <label class="text-sm font-medium text-gray-500"
                >First name</label
              >
              <p class="mt-1 text-gray-900 font-semibold">
                {{ authStore.user.first_name }}
              </p>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
              <label class="text-sm font-medium text-gray-500"
                >Middle name</label
              >
              <p class="mt-1 text-gray-900 font-semibold">
                {{ authStore.user.middle_name || "-" }}
              </p>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
              <label class="text-sm font-medium text-gray-500">Last name</label>
              <p class="mt-1 text-gray-900 font-semibold">
                {{ authStore.user.last_name }}
              </p>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
              <label class="text-sm font-medium text-gray-500"
                >Date of Birth</label
              >
              <p class="mt-1 text-gray-900 font-semibold">
                {{ authStore.user.date_of_birth || "-" }}
              </p>
            </div>
          </div>

          <div class="bg-gray-50 rounded-lg p-4">
            <label class="text-sm font-medium text-gray-500">Roles</label>
            <div class="mt-2 flex flex-wrap gap-2">
              <div
                v-for="(role, index) in authStore.user.roles"
                :key="index"
                class="bg-blue-50 text-blue-700 text-sm px-3 py-1 rounded-full border border-blue-200"
              >
                {{ role.role_name }}
              </div>
              <div
                v-if="
                  !authStore.user.roles || authStore.user.roles.length === 0
                "
                class="text-gray-500 italic"
              >
                No roles assigned
              </div>
            </div>
          </div>

          <div class="bg-gray-50 rounded-lg p-4">
            <label class="text-sm font-medium text-gray-500">Departments</label>
            <div class="mt-2 flex flex-wrap gap-2">
              <div
                v-for="(department, index) in authStore.user.departments"
                :key="index"
                class="bg-green-50 text-green-700 text-sm px-3 py-1 rounded-full border border-green-200"
              >
                {{ department.name }}
              </div>
              <div
                v-if="
                  !authStore.user.departments ||
                  authStore.user.departments.length === 0
                "
                class="text-gray-500 italic"
              >
                No departments assigned
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="activeTab === 1" class="p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-6">Credentials</h2>

        <div class="bg-white border border-gray-200 rounded-lg mb-8">
          <div class="p-5 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Update Username</h3>
            <p class="mt-1 text-sm text-gray-500">
              Please enter your desired username below and save changes.
            </p>
          </div>
          <div class="p-5">
            <FormKit
              type="form"
              submit-label="Update"
              @submit="changeUsername"
              :actions="false"
              #default="{ value }"
            >
              <FormKit
                type="text"
                name="username"
                label="Username"
                validation="required"
                v-model="username"
              />
              <div class="flex items-center justify-end mt-5">
                <CoreActionButton
                  type="submit"
                  :icon="saveIcon"
                  text="Update Username"
                  color="success"
                  :click="() => {}"
                  :loading="updating"
                />
              </div>
            </FormKit>
          </div>
        </div>

        <div class="bg-white border border-gray-200 rounded-lg">
          <div class="p-5 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Change Password</h3>
            <p class="mt-1 text-sm text-gray-500">
              Please enter your current password to change your password.
            </p>
          </div>
          <div class="p-5">
            <FormKit
              type="form"
              submit-label="Update"
              @submit="changePassword"
              :actions="false"
              #default="{ value }"
            >
              <div class="space-y-4">
                <FormKit
                  type="password"
                  label="Current Password"
                  validation="required"
                  v-model="oldPassword"
                />
                <FormKit type="group">
                  <FormKit
                    type="password"
                    name="password"
                    label="New Password"
                    validation="required"
                    v-model="newPassword"
                    validation-visibility="live"
                  />
                  <FormKit
                    type="password"
                    label="Confirm New Password"
                    name="password_confirm"
                    validation="required|confirm"
                    validation-label="Password confirmation"
                    validation-visibility="live"
                  />
                </FormKit>
              </div>
              <div class="flex items-center justify-end mt-6">
                <CoreActionButton
                  type="submit"
                  :icon="saveIcon"
                  text="Update Password"
                  color="success"
                  :click="() => {}"
                  :loading="loading"
                />
              </div>
            </FormKit>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowDownTrayIcon,
  IdentificationIcon,
  PencilSquareIcon,
} from "@heroicons/vue/24/solid/index.js";
import fetchRequest from "@/services/fetch";
import { endpoints } from "@/services/endpoints";
import { useAuthStore } from "@/store/auth";
import { useRouteStore } from "@/store/route";
import type { Page, Request, Response } from "@/types";
import Package from "@/package.json";
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";

definePageMeta({
  layout: "dashboard",
  middleware: ["auth"],
});

useHead({
  title: `${Package.name.toUpperCase()} - Settings`,
});

const saveIcon = ArrowDownTrayIcon;
const pages: Page = [
  {
    name: "Home" as String,
    link: "/home" as String,
  },
];

const tabs = ["Personal Information", "Credentials"] as string[];

const activeTab = ref<number>(0);
const loading = ref<boolean>(false);
const updating = ref<boolean>(false);
const cookie = useCookie("token");
const oldPassword = ref<string>("");
const newPassword = ref<string>("");
const username = ref<string>("");
const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();
const routeStore = useRouteStore();

const changePassword = async (): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: `${endpoints.users}/change_password/${authStore.user.id}`,
    method: "PUT",
    token: `${cookie.value}`,
    body: {
      user: {
        old_password: oldPassword.value,
        password: newPassword.value,
      },
    },
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;

  if (data.value) {
    if (route.path && route.path !== "/" && route.path !== "/login") {
      routeStore.lastKnownRoute(route.path);
    }

    loading.value = false;
    useNuxtApp().$toast.success("Password changed successfully!");
    authStore.logUserOut();
    router.push("/");
  }

  if (error.value) {
    loading.value = false;
    useNuxtApp().$toast.error(`${error.value.data.error}`);
    console.error(error.value);
  }
};

const changeUsername = async (): Promise<void> => {
  updating.value = true;
  const request: Request = {
    route: `${endpoints.users}/change_username/${authStore.user.id}`,
    method: "PUT",
    token: `${cookie.value}`,
    body: {
      user: {
        username: username.value,
      },
    },
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  updating.value = pending;

  if (data.value) {
    if (route.path && route.path !== "/" && route.path !== "/login") {
      routeStore.lastKnownRoute(route.path);
    }

    updating.value = false;
    useNuxtApp().$toast.success("Username changed successfully!");
    authStore.logUserOut();
    router.push("/");
  }

  if (error.value) {
    updating.value = false;
    useNuxtApp().$toast.error(`${error.value.data.error}`);
    console.error(error.value);
  }
};
</script>
