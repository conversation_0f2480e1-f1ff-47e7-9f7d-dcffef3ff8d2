<template>
  <div class="px-5 py-5">
    <div class="grid grid-cols-3 gap-4" v-show="!loading">
      <home-tests :data="data" />
      <home-lab-configuration :data="data" />
      <home-patients :data="data" />
    </div>
    <div class="w-full grid grid-cols-3 gap-4" v-show="loading">
      <div class="col-span-1 border rounded" v-for="i in 3" :key="i">
        <div class="flex items-center justify-between px-2 py-2">
          <div class="w-48 bg-gray-100 animate-pulse rounded h-8"></div>
          <div class="rounded-full h-8 w-8 bg-gray-100 animate-pulse"></div>
        </div>
        <div class="flex items-center space-x-3 px-2 pb-4">
          <div class="h-14 w-14 bg-gray-100 rounded animate-pulse"></div>
          <div class="w-48 bg-gray-100 animate-pulse rounded h-8"></div>
        </div>
        <div v-for="i in 3" :key="i">
          <div class="flex items-center space-x-3 px-2 pb-2">
            <div class="h-5 w-5 bg-gray-100 rounded-full animate-pulse"></div>
            <div class="h-8 w-8 bg-gray-100 rounded animate-pulse"></div>
            <div class="w-48 bg-gray-100 animate-pulse rounded h-8"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-2 gap-4 py-5">
      <home-recent-tests />
      <home-recent-patients />
      <home-test-catalog v-if="showRoute(accessRoles)" v-show="!loading" />
      <div class="cols-span-1 border rounded" v-show="loading">
        <div class="w-full h-10 bg-gray-100 transition duration-150 animate-pulse"></div>
        <div class="w-full grid grid-cols-3 gap-5 py-2 px-2">
          <div class="col-span-1 w-auto h-36 bg-gray-100 transition-all animate-pulse rounded" v-for="i in 7" :key="i">
          </div>
        </div>
      </div>
      <div class="col-span-1 flex flex-col space-y-3">
        <home-available-printers :data="data" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/store/auth";
import Package from "@/package.json";
import type { DropdownItem } from "@/types";
import { endpoints } from "@/services/endpoints";
import type { Request, Response } from "@/types";
import fetchRequest from "@/services/fetch";

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: `${Package.name.toUpperCase()} - Home`,
});

const authStore = useAuthStore();
const cookie = useCookie("token");

const loading = ref<boolean>(true);
const data = ref<Record<string, any>>({});
const accessRoles = ref<Array<DropdownItem>>([
  { name: "superadmin" },
  { name: "superuser" },
]);

async function init(): Promise<void> {
  loading.value = true;

  const locationId: number = getIdByName(authStore.locations, authStore.selectedLocation);
  const request: Request = {
    route: `${endpoints.analytics}/home?department=${authStore.department}&lab_location=${locationId}`,
    method: "GET",
    token: `${cookie.value}`,
  };

  const { data: responseData, error }: Response = await fetchRequest(request);
  if (responseData.value) {
    data.value = responseData.value.data;
    loading.value = false;
  }
  if (error.value) {
    console.error(error.value);
    loading.value = false;
  }
}

const showRoute = (accessRoles: Array<DropdownItem>): boolean => {
  const userRoleNames = new Set(
    authStore.user.roles.map(role => role.role_name.toLowerCase())
  );
  return accessRoles.some(accessRole =>
    userRoleNames.has(accessRole.name.toLowerCase())
  );
}

onMounted(() => {
  init();
});

watch(() => authStore, () => {
  init();
}, { deep: true });
</script>

<style scoped></style>