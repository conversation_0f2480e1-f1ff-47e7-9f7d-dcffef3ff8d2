<template>
    <div class="px-5 py-5">

        <CoreBreadcrumb :pages="pages" />

        <div class="py-5">
            <div class="text-2xl font-semibold flex items-center uppercase">
                <img src="@/assets/icons/report.png" alt="report-icon" class="w-8 h-8 mr-2" />
                Haematology Report
            </div>
        </div>

        <div class="w-full flex justify-between py-5">
            <div class="flex items-center space-x-3">
                <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
                    <FunnelIcon class="w-5 h-5 mr-2" />
                    Filter By Year
                    <div class="w-36 ml-2 bg-white">
                        <CoreDropdown :items="years" v-model="yearSelected" />
                    </div>
                </div>
                <CoreActionButton :loading="loading" :click="(() => { getReportData() })" color="primary"
                    :icon="viewIcon" text="Generate report" />
            </div>
            <div>
                <excel class="btn btn-default"
                    :header="[`HAEMATOLOGY MoH LABORATORY REPORT ${yearSelected.name}`, facility.details.name, facility.details.address, facility.details.phone]"
                    :data="reportData" worksheet="report-work-sheet"
                    :name="`moh_haematology_report_${yearSelected.name}.xls`">
                    <CoreExportButton text="Export Excel" />
                </excel>
            </div>
        </div>

        <div class="border rounded">
            <div class="rounded-tr rounded-tl border-b px-5 py-5 flex items-center justify-between">
                <div class="flex flex-col space-y-2">
                    <img src="@/assets/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
                    <h3 class="text-xl font-semibold">
                        HAEMATOLOGY MoH LABORATORY REPORT
                    </h3>
                </div>
                <ReportsAddress />
            </div>

            <div>
                <h3 class="px-4 py-2.5 font-medium">Data for the year: <span class="font-normal">{{ yearSelected.name ==
                    'select year' ? '-:-' : yearSelected.name }}</span></h3>
            </div>

            <div class="overflow-x-auto rounded border-t relative">
                <div v-if="loading"
                    class="w-full bg-black bg-opacity-5 absolute flex space-y-2 justify-center py-20 mx-auto h-full">
                    <div class="flex flex-col items-center space-y-">
                        <CoreLoader :width="100" :height="100" />
                    </div>
                </div>
                <table class="w-full overflow-x-auto">
                    <thead class="border-b">
                        <tr class="w-full bg-gray-50">
                            <th class="px-4 py-2 uppercase border-r">Laboratory Service</th>
                            <th class="px-4 py-2 uppercase border-r">Jan</th>
                            <th class="px-4 py-2 uppercase border-r">Feb</th>
                            <th class="px-4 py-2 uppercase">March</th>
                            <th class="px-4 py-2 uppercase bg-sky-50 text-sky-500 border border-sky-100">Total Q1</th>
                            <th class="px-4 py-2 uppercase border-r">Apr</th>
                            <th class="px-4 py-2 uppercase border-r">May</th>
                            <th class="px-4 py-2 uppercase">Jun</th>
                            <th class="px-4 py-2 uppercase bg-sky-50 text-sky-500 border border-sky-100">Total Q2</th>
                            <th class="px-4 py-2 uppercase border-r">Jul</th>
                            <th class="px-4 py-2 uppercase border-r">Aug</th>
                            <th class="px-4 py-2 uppercase">Sep</th>
                            <th class="px-4 py-2 uppercase bg-sky-50 text-sky-500 border border-sky-100">Total Q3</th>
                            <th class="px-4 py-2 uppercase  border-r">Oct</th>
                            <th class="px-4 py-2 uppercase border-r">Nov</th>
                            <th class="px-4 py-2 uppercase">Dec</th>
                            <th
                                class="px-4 py-2 uppercase bg-sky-50 text-sky-500 border-t border-l border-b border-sky-100">
                                Total
                                Q4
                            </th>
                            <th class="px-4 py-2 uppercase bg-green-600 text-white border border-green-400">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="px-2" v-for="i, index in items" :key="index">
                            <td class="px-4 py-2 text-center  border-r border-b">
                                {{ i.indicator }}</td>
                            <td class="px-4 py-2 text-center border-r border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                                @click="showEnumeration(i.indicator, `01-01-${yearSelected.name}`, Number(i.jan.count), i.jan.associated_ids)">
                                {{ i.jan.count }}</td>
                            <td class="px-4 py-2 text-center border-r border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                                @click="showEnumeration(i.indicator, `02-01-${yearSelected.name}`, Number(i.feb.count), i.feb.associated_ids)">
                                {{ i.feb.count }}</td>
                            <td class="px-4 py-2 text-center border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                                @click="showEnumeration(i.indicator, `03-01-${yearSelected.name}`, Number(i.mar.count), i.mar.associated_ids)">
                                {{ i.mar.count }}</td>
                            <td class="px-4 py-2 text-center  bg-sky-50 text-sky-500 border border-sky-100">{{ i.totalQ1
                                }}
                            </td>
                            <td class="px-4 py-2 text-center border-r border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                                @click="showEnumeration(i.indicator, `04-01-${yearSelected.name}`, Number(i.apr.count), i.apr.associated_ids)">
                                {{ i.apr.count }}</td>
                            <td class="px-4 py-2 text-center border-r border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                                @click="showEnumeration(i.indicator, `05-01-${yearSelected.name}`, Number(i.may.count), i.may.associated_ids)">
                                {{ i.may.count }}</td>
                            <td class="px-4 py-2 text-center border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                                @click="showEnumeration(i.indicator, `06-01-${yearSelected.name}`, Number(i.june.count), i.june.associated_ids)">
                                {{ i.june.count }}</td>
                            <td class="px-4 py-2 text-center  bg-sky-50 text-sky-500 border border-sky-100">{{ i.totalQ2
                                }}
                            </td>
                            <td class="px-4 py-2 text-center border-r border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                                @click="showEnumeration(i.indicator, `07-01-${yearSelected.name}`, Number(i.jul.count), i.jul.associated_ids)">
                                {{ i.jul.count }}</td>
                            <td class="px-4 py-2 text-center border-r border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                                @click="showEnumeration(i.indicator, `08-08-${yearSelected.name}`, Number(i.aug.count), i.aug.associated_ids)">
                                {{ i.aug.count }}</td>
                            <td class="px-4 py-2 text-center border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                                @click="showEnumeration(i.indicator, `09-9-${yearSelected.name}`, Number(i.sept.count), i.sept.associated_ids)">
                                {{ i.sept.count }}</td>
                            <td class="px-4 py-2 text-center  bg-sky-50 text-sky-500 border border-sky-100">{{ i.totalQ3
                                }}
                            </td>
                            <td class="px-4 py-2 text-center border-r border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                                @click="showEnumeration(i.indicator, `10-10-${yearSelected.name}`, Number(i.oct.count), i.oct.associated_ids)">
                                {{ i.oct.count }}</td>
                            <td class="px-4 py-2 text-center border-r border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                                @click="showEnumeration(i.indicator, `11-11-${yearSelected.name}`, Number(i.nov.count), i.nov.associated_ids)">
                                {{ i.nov.count }}</td>
                            <td class="px-4 py-2 text-center border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                                @click="showEnumeration(i.indicator, `12-12-${yearSelected.name}`, Number(i.dec.count), i.dec.associated_ids)">
                                {{ i.dec.count }}</td>
                            <td
                                class="px-4 py-2 text-center  bg-sky-50 text-sky-500 border-t border-l border-b border-sky-100">
                                {{ i.totalQ4 }}
                            </td>
                            <td class="px-4 py-2 text-center  bg-green-600 border text-white border-green-400">{{
                                i.total
                            }}
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div v-if="items.length == 0 && !loading"
                    class="w-full flex flex-col items-center justify-center space-y-2 py-10">
                    <img src="@/assets/images/page.png" alt="page-icon" class="object-cover w-20 h-20" />
                    <p class="text-base">Could not load data, please try again</p>
                </div>
            </div>

        </div>
    </div>
</template>

<script setup lang="ts">
import { ArrowPathIcon, FunnelIcon } from '@heroicons/vue/24/solid/index.js'
import { endpoints } from '@/services/endpoints'
import fetchRequest from '@/services/fetch'
import { useFacilityStore } from '@/store/facility'
import type { DropdownItem, Page, ReportData, Request, Response } from '@/types'
import Package from '@/package.json'
import moment from 'moment'

definePageMeta({
    layout: 'dashboard',
    middleware: ['reports']
})

useHead({
    title: `${Package.name.toUpperCase()} - Biochemistry Report`
})

const viewIcon = ArrowPathIcon
const pages = reactive([
    { name: "Home", link: "/home" },
    { name: "Reports", link: "#" },
    { name: "MoH Diagnistic Reports", link: "#" }
] as Page);
const items = ref<ReportData[]>([]);
const yearSelected = ref({ name: 'select year' });
const years = ref<DropdownItem[]>([]);
const cookie = useCookie('token');
const loading = ref(false);
const router = useRouter();
const route = useRoute();
const { $toast } = useNuxtApp();
const facility = useFacilityStore();
const reportData = ref<any[]>([]);

const showEnumeration = (
    test: any,
    month: string,
    count: number,
    associated_ids: string
): void => {
    if (count !== 0 && associated_ids !== "") {
        const getFullMonth: string = moment(month).format("MMMM-YYYY");
        router.push(
            `/reports/${associated_ids}?origin=moh&type=haematology-report&dateRange=${getFullMonth}&test=${test} - ${getFullMonth}&department=haematology&count=${count}`
        );
    } else {
        $toast.warning("No data found for this month");
    }
};

const getReportIndicators = async (): Promise<void> => {
    loading.value = true;
    const request: Request = {
        route: `${endpoints.reportIndicators}?department=haematology`,
        method: 'GET',
        token: `${cookie.value}`
    };
    const { data: responseData, error }: Response = await fetchRequest(request)
    if (responseData.value) {
        items.value = responseData.value.map((value: string) => ({
            indicator: value,
            jan: { count: '!', associated_ids: "" },
            feb: { count: '!', associated_ids: "" },
            mar: { count: '!', associated_ids: "" },
            totalQ1: '!',
            apr: { count: '!', associated_ids: "" },
            may: { count: '!', associated_ids: "" },
            june: { count: '!', associated_ids: "" },
            totalQ2: '!',
            jul: { count: '!', associated_ids: "" },
            aug: { count: '!', associated_ids: "" },
            sept: { count: '!', associated_ids: "" },
            totalQ3: '!',
            oct: { count: '!', associated_ids: "" },
            nov: { count: '!', associated_ids: "" },
            dec: { count: '!', associated_ids: "" },
            totalQ4: '!',
            total: '!',
        }));
        loading.value = false;
    }
    if (error.value) {
        console.error(error.value);
        loading.value = false;
        $toast.error(REPORT_INDICATORS_LOAD_FAILURE);
    }
}

const getReportData = async (): Promise<void> => {
    if (yearSelected.value.name === 'select year') {
        $toast.warning('Please select a year');
    } else {
        generateReport();
    }
}

const getRequiredDateParam = (): string => {
    let t: string = "";
    if (yearSelected.value.name !== 'select year') {
        t = `&year=${yearSelected.value.name}`
    }
    if (route.query.period) {
        const paddedDate = `1/${String(route.query.period)}`;
        t = `&year=${moment(paddedDate).format('YYYY')}`;
        yearSelected.value = { name: moment(paddedDate).format('YYYY') }
    }
    return t
}

const generateReport = async (): Promise<void> => {
    loading.value = true
    const request: Request = {
        route: `${endpoints.mohReport}haematology?report_id=${route.query.report_id}${getRequiredDateParam()}`,
        method: 'GET',
        token: `${cookie.value}`
    }
    const { data: responseData, error, pending }: Response = await fetchRequest(request)
    loading.value = pending
    if (responseData.value) {
        let indicatorsData: { indicator: string; jan: any; feb: any; mar: any; totalQ1: any; apr: any; may: any; june: any; totalQ2: any; jul: any; aug: any; sept: any; totalQ3: any; oct: any; nov: any; dec: any; totalQ4: any; total: any }[] = [];
        items.value.map((values) => {
            let janData = responseData.value.january[values.indicator];
            let febData = responseData.value.february[values.indicator];
            let marData = responseData.value.march[values.indicator];
            let aprData = responseData.value.april[values.indicator];
            let mayData = responseData.value.may[values.indicator];
            let junData = responseData.value.june[values.indicator];
            let julyData = responseData.value.july[values.indicator];
            let augData = responseData.value.august[values.indicator];
            let septData = responseData.value.september[values.indicator];
            let octData = responseData.value.october[values.indicator];
            let novData = responseData.value.november[values.indicator];
            let decData = responseData.value.december[values.indicator];
            let totalQ1 = janData.count + febData.count + marData.count;
            let totalQ2 = aprData.count + mayData.count + junData.count;
            let totalQ3 = julyData.count + augData.count + septData.count;
            let totalQ4 = octData.count + novData.count + decData.count;
            indicatorsData.push({
                indicator: values.indicator,
                jan: janData,
                feb: febData,
                mar: marData,
                totalQ1: totalQ1,
                apr: aprData,
                may: mayData,
                june: junData,
                totalQ2: totalQ2,
                jul: julyData,
                aug: augData,
                sept: septData,
                totalQ3: totalQ3,
                oct: octData,
                nov: novData,
                dec: decData,
                totalQ4: totalQ4,
                total: totalQ1 + totalQ2 + totalQ3 + totalQ4
            });
            reportData.value.push({
                "Laboratory Service": values.indicator,
                "January": janData.count,
                "February": febData.count,
                "March": marData.count,
                "Total Q1": totalQ1,
                "April": aprData.count,
                "May": mayData.count,
                "June": junData.count,
                "Total Q2": totalQ2,
                "July": julyData.count,
                "August": augData.count,
                "September": septData.count,
                "Total Q3": totalQ3,
                "October": octData.count,
                "November": novData.count,
                "December": decData.count,
                "Total Q4": totalQ4,
                "Total": totalQ1 + totalQ2 + totalQ3 + totalQ4
            });
        });
        items.value = [];
        items.value.push(...indicatorsData);
        loading.value = false;
        updateReportId(responseData.value.report_id);
        $toast.success('Report data generated successfully!');
    }
    if (error.value) {
        loading.value = false;
        console.error(error.value);
        $toast.error(ERROR_MESSAGE);
    }
}

const updateReportId = (reportId: string): void => {
    const currentRoute = router.currentRoute.value;
    const newQuery = {
        ...currentRoute.query,
        report_id: reportId,
    };
    router.replace({ query: newQuery }).catch((err: any) => {
        console.error("Failed to replace route:", err);
    });
};

const getYears = (): void => {
    const currentYear = new Date().getFullYear();
    years.value = Array.from({ length: currentYear - 1999 }, (_, i) => ({
        name: (currentYear - i).toString(),
        id: currentYear - i
    }));
}

watch(
    yearSelected,
    () => {
        if (yearSelected) {
            const currentRoute = router.currentRoute.value;
            const newQuery = {
                ...currentRoute.query,
                "period": moment(yearSelected.value.name).format("M/yyyy"),
                "report_id": ""
            };
            router.replace({ query: newQuery }).catch((err: any) => {
                console.error("Failed to replace route:", err);
            });
        }
    },
    { deep: true }
);

const validFields = computed((): boolean => route.query.period !== undefined && route.query.period !== "");

onMounted(() => {
    if (validFields.value) {
        generateReport();
    }
});

onMounted(() => {
    getYears();
    getReportIndicators();
})
</script>

<style></style>
