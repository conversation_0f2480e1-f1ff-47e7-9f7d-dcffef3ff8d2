<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center py-5">
      <div class="flex items-center mr-2">
        <div
          class="w-12 h-12 bg-gray-100 flex items-center justify-center rounded-full"
        >
          <img
            src="@/assets/icons/report.png"
            alt="report-icon"
            class="w-8 h-8"
          />
        </div>
        <div
          class="w-12 h-12 bg-gray-100 flex items-center justify-center rounded-full -ml-3"
        >
          <img
            src="@/assets/images/whonet.png"
            alt="report-icon"
            class="w-8 h-8"
          />
        </div>
      </div>
      <h3 class="text-2xl font-semibold uppercase">WHONET Report</h3>
    </div>

    <div class="w-full flex items-center justify-between">
      <div class="bg-white flex space-x-5 py-5 flex-wrap">
        <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
          <FunnelIcon class="w-5 h-5 mr-2" />
          Filter By Date Range
          <div class="w-72 ml-2">
            <datepicker
              required
              position="left"
              placeholder="select start & end date"
              :range="true"
              format="dd/MM/yyyy"
              @cleared="clearDateRange"
              input-class-name="datepicker"
              v-model="dateRange"
              :max-date="new Date()"
            />
          </div>
        </div>
        <div>
          <CoreActionButton
            color="primary"
            text="Generate Report"
            :icon="ArrowPathIcon"
            :click="() => generateReport()"
            :loading="loading"
          />
        </div>
      </div>

      <div>
        <CoreExportButton
          text="Export Excel"
          @click="() => {isExportDialogOpen = true;}"
        />

        <TransitionRoot appear :show="isExportDialogOpen" as="template">
          <Dialog as="div" @close="isExportDialogOpen = false" class="relative z-10">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0"
              enter-to="opacity-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100"
              leave-to="opacity-0"
            >
              <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
              <div class="flex min-h-full items-center justify-center p-4 text-center">
                <TransitionChild
                  as="template"
                  enter="duration-300 ease-out"
                  enter-from="opacity-0 scale-95"
                  enter-to="opacity-100 scale-100"
                  leave="duration-200 ease-in"
                  leave-from="opacity-100 scale-100"
                  leave-to="opacity-0 scale-95"
                >
                  <DialogPanel class="w-full max-w-md transform overflow-hidden rounded bg-white p-6 text-left align-middle shadow-xl transition-all">
                    <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                      Export Report To Excel
                    </DialogTitle>
                    <div class="mt-4">
                      <p class="text-sm text-gray-500">
                        Select results format to export.
                      </p>
                      <FormKit
                        type="radio"
                        name="export_format"
                        v-model="selectedExportFormat"
                        :options="exportFormats"
                      />
                    </div>

                    <div class="mt-6 flex justify-end space-x-3">
                      <button
                        type="button"
                        class="inline-flex justify-center rounded border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-200"
                        @click="isExportDialogOpen = false"
                      >
                        Cancel
                      </button>
                      <excel
                        class="btn btn-default"
                        :header="[]"
                        :data="exportData"
                        worksheet="report-work-sheet"
                        :name="`whonet_report_${moment(startDate).format('DD_MM_yyyy')}_to_${moment(endDate).format('DD_MM_yyyy')}.xls`"
                      >
                        <button
                          type="button"
                          class="inline-flex justify-center rounded border border-transparent bg-sky-50 px-4 py-2 text-sm font-medium text-sky-600 hover:bg-sky-200"
                          @click="isExportDialogOpen = false"
                        >
                          Export Excel
                        </button>
                      </excel>
                    </div>
                  </DialogPanel>
                </TransitionChild>
              </div>
            </div>
          </Dialog>
        </TransitionRoot>
      </div>
    </div>

    <div class="border rounded print-container mt-5">
      <div
        class="w-full rounded-tr rounded-tl border-b px-5 py-5 flex items-center justify-between"
      >
        <div class="flex flex-col space-y-2">
          <img
            src="@/assets/images/logo.png"
            alt="app-logo"
            class="w-24 h-24 object-cover"
          />
          <h3 class="text-xl font-semibold">WHONET REPORT</h3>
        </div>
        <ReportsAddress />
      </div>

      <div class="mt-3 px-5 border-b">
        <h3 class="font-medium mb-2">
          Tests Performed Period:
          <span class="font-normal">
            {{ startDate ? moment(startDate).format(DATE_FORMAT) : "-" }}
            -
            {{ endDate ? moment(endDate).format(DATE_FORMAT) : "-" }}
          </span>
        </h3>
      </div>
      <div class="max-w-full overflow-x-auto">
        <div class="p-4">
          <table class="w-full border border-dotted rounded overflow-x-auto">
            <thead class="bg-gray-100">
              <tr class="border-b">
                <th
                  v-for="(header, index) in getHeaders(reportData)"
                  :key="index"
                  class="px-2 py-2 text-center border-r text-lg uppercase"
                >
                  {{ formatHeaderDisplay(header) }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(row, rowIndex) in reportData"
                :key="rowIndex"
                class="divide-gray-200"
              >
                <td
                  v-for="(header, headerIndex) in getHeaders(reportData)"
                  :key="`${rowIndex}-${headerIndex}`"
                  class="capitalize px-2 py-2 text-center border-b border-r"
                >
                  {{ row[header as any] === "" ? "—" : row[header as any] }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <ReportsLoader
        :condition="loading"
        :cancelReportGeneration="() => cancelRequest()"
      />
      <div
        class="flex flex-col space-y-3 items-center justify-center py-10"
        v-if="reportData.length == 0 && !loading"
      >
        <img
          src="@/assets/images/page.png"
          class="w-20 h-20 object-cover"
          alt="page-icon"
        />
        <p>
          Please generate report data to preview the
          <span class="font-medium">{{ `whonet` }}</span>
          report.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Package from "@/package.json";
import { endpoints } from "@/services/endpoints";
import { useFacilityStore } from "@/store/facility";
import type { Page } from "@/types";
import moment from "moment";
import { ArrowPathIcon, FunnelIcon } from "@heroicons/vue/24/solid";
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';

definePageMeta({
  layout: "dashboard",
  middleware: ["auth"],
});

useHead({
  title: `${Package.name} - Daily Log Reports`,
});

const exportFormats = ref<Array<{ label: string; value: string }>>([
  { label: "Show zone numbers (e.g. '1,2,3,4...')", value: "number" },
  { label: "Show interpretation (e.g. 'Resistant (R), Susceptible (S), Intermediate (I)')", value: "interpretation" },
]);
const selectedExportFormat = ref("number");
const facility = useFacilityStore();
const pages = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Reports",
    link: "#",
  },
]);
const { $toast } = useNuxtApp();
const { loading, executeCancellableRequest, cancelRequest } =
  useCancellableRequest();
const isExportDialogOpen = ref(false);
const dateRange = ref<Array<string>>(["", ""]);
const reportData = ref<Array<Record<string, any>>>([]);
const startDate = computed(() => {
  return dateRange.value[0]
    ? moment(dateRange.value[0]).format("YYYY-MM-DD")
    : "";
});
const endDate = computed(() => {
  return dateRange.value[1]
    ? moment(dateRange.value[1]).format("YYYY-MM-DD")
    : "";
});

const clearDateRange = () => {
  dateRange.value = ["", ""];
};

const getHeaders = (data: any[]) => {
  const allowedHeaders = [
    "laboratory",
    "first_name",
    "last_name",
    "gender",
    "dob",
    "ward_or_location",
    "accession_number",
    "specimen_type",
    "requested_by",
  ];

  return allowedHeaders.filter(
    (header) => data.length > 0 && data.some((row) => header in row)
  );
};

const formatHeaderDisplay = (header: string) => {
  const displayNames: Record<string, string> = {
    WARD_OR_LOCATION: "WARD OR LOCATION",
    SPECIMEN_TYPE: "SPECIMEN TYPE",
    REQUESTED_BY: "REQUESTED BY",
    ACCESSION_NUMBER: "ACCESSION NUMBER",
    FIRST_NAME: "FIRST NAME",
    LAST_NAME: "LAST NAME",
  };

  return displayNames[header] || header.replaceAll("_", " ");
};

const exportData = computed(() => {
  const allKeys = new Set<string>();
  reportData.value.forEach((row) => {
    Object.keys(row).forEach((key) => allKeys.add(key));
  });

  return reportData.value.map((row) => {
    const newRow: Record<string, string | number> = {};
    Array.from(allKeys).forEach((key) => {
      const formattedKey = key.toUpperCase().replace(/[_-]/g, " ");

      // Handle antibiotic results differently
      if (typeof row[key] === 'string' && row[key] && row[key].startsWith("{")) {
        try {
          const result = JSON.parse(row[key]);
          if (selectedExportFormat.value === "number") {
            newRow[formattedKey] = result.zone || "";
          } else {
            newRow[formattedKey] = result.interpretation || "";
          }
        } catch (e) {
          newRow[formattedKey] = row[key] || "";
        }
      } else {
        // For non-antibiotic fields, keep the original value
        newRow[formattedKey] = row[key] || "";
      }
    });
    return newRow;
  });
});

const generateReport = async (): Promise<void> => {
  loading.value = true;
  if(!startDate.value || !endDate.value) {
    $toast.warn("Please select a date range");
    loading.value = false;
    return;
  }
  reportData.value = [];
  let queryParams = `start_date=${startDate.value}&end_date=${endDate.value}`;
  const { data, error } = await executeCancellableRequest({
    route: `${endpoints.whonet}?${queryParams}`,
    method: "GET",
  });
  if (data.value) {
    reportData.value = data.value.data;
    loading.value = false;
    $toast.success("Report generated successfully");
  }
  if (error.value) {
    console.error(error.value);
    $toast.error("An error occurred while generating report");
    loading.value = false;
  }
};

onMounted(() => {
  loading.value = false;
});
</script>

<style></style>
