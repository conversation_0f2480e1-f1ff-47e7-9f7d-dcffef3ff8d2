<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <h3 class="text-2xl font-semibold uppercase">Patient reports</h3>
    </div>

    <div class="w-full flex items-center justify-between">
      <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
        <FunnelIcon class="w-5 h-5 mr-2" />
        Filter By Date Range
        <div class="w-72 ml-2">
          <datepicker
            required
            position="left"
            placeholder="select start & end date"
            :range="true"
            input-class-name="datepicker"
            format="dd/MM/yyyy"
            v-model="selectedDateRange"
            @update:model-value="dateClicked"
          />
        </div>
      </div>
      <div class="flex justify-end px-2 py-2 mb-2">
        <CoreSearchBar :search="search" @update="updateSearch" />
      </div>
    </div>

    <CoreDatatable
      :headers="headers"
      :data="filteredPatients"
      :loading="loading"
      :searchField="searchField"
      :searchValue="searchValue"
      :serverItemsLength="serverItemsLength"
      :serverOptions="serverOptions"
      @update="updatePatients"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <CoreActionButton
            :click="() => viewReport(item)"
            color="primary"
            text="View Report"
            :icon="viewIcon"
          />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script lang="ts">
import {
  ArrowTopRightOnSquareIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import type { Header, ServerOptions } from "vue3-easy-data-table";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Page, Patient, Request, Response } from "@/types";
import Package from "@/package.json";

export default {
  setup() {
    definePageMeta({
      layout: "dashboard",
      middleware: ['reports']
    });
    useHead({
      title: `${Package.name.toUpperCase()} - Patient Report`,
    });
  },
  components: { MagnifyingGlassIcon, FunnelIcon },
  data() {
    return {
      headers: [
        { text: "patient id", value: "client_id", sortable: true },
        { text: "name", value: "name", sortable: true },
        { text: "sex", value: "sex", sortable: true },
        { text: "date of birth", value: "date_of_birth", sortable: true },
        { text: "actions", value: "actions" },
      ] as Array<Header>,
      patients: new Array<Patient>(),
      pages: [
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Reports",
          link: "#",
        },
      ] as Page,
      search: "" as string,
      loading: false as boolean,
      viewIcon: ArrowTopRightOnSquareIcon as Object,
      cookie: useCookie("token") as any,
      selectedDateRange: ["", ""],
      serverItemsLength: <number>0,
      serverOptions: <ServerOptions>{
        page: 1,
        rowsPerPage: 25,
        sortBy: "name",
      },
      searchField: "name" as string,
      searchValue: "" as string,
    };
  },
  created() {
    this.init();
  },
  computed: {
    filteredPatients() {
      return this.patients.map((patient) => ({
        ...patient,
        name: `${patient.first_name} ${
          patient.middle_name ? patient.middle_name : ""
        } ${patient.last_name}`
          .split(" ")
          .map(
            (namePart) => namePart.charAt(0).toUpperCase() + namePart.slice(1)
          )
          .join(" "),
        date_of_birth: moment(patient.date_of_birth).format(DATE_FORMAT_NO_TIME),
      }));
    },
  },
  methods: {
    updateSearch(value: string): void {
      this.searchValue = value;
      this.search = value;
      this.updatePatients(true);
    },
    updatePatients(value: any): void {
      if (typeof value === "object") {
        this.serverOptions = value;
      }
      this.init();
    },
    async init(): Promise<void> {
      this.loading = true;
      const { page, rowsPerPage } = this.serverOptions;
      let startDate =
        this.selectedDateRange !== null &&
        this.selectedDateRange[0].toString() != ""
          ? moment(this.selectedDateRange[0].toString()).format("YYYY-MM-DD")
          : "";
      let endDate =
        this.selectedDateRange !== null &&
        this.selectedDateRange[1].toString() != ""
          ? moment(this.selectedDateRange[1].toString()).format("YYYY-MM-DD")
          : "";
      const request: Request = {
        route: `${endpoints.clients}?page=${page}&per_page=${rowsPerPage}&search=${this.search}&from=${startDate}&to=${endDate}`,
        method: "GET",
        token: `${this.cookie}`,
      };

      let { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;

      if (data.value) {
        this.patients = data.value.clients;
        this.serverItemsLength = data.value.meta.total_count;
      }

      if (error.value) {
        console.error(error.value.data);
        useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
      }
    },
    dateClicked() {
      this.init();
    },
    async viewReport(patient: any): Promise<void> {
      this.$router.push(`/reports/daily/patient-report/${patient.client_id}`);
    },
  },
};
</script>

<style></style>
