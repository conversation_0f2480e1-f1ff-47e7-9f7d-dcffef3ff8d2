<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center py-5">
      <img src="@/assets/icons/medical-record.png" alt="report-icon" class="w-8 h-8 mr-2" />
      <h3 class="text-2xl font-semibold uppercase" style="font-size: 24px; line-height: 2rem">
        patient report for
        <span class="text-sky-500 text-2xl" style="font-size: 24px; line-height: 2rem">"{{
          `${person.first_name} ${person.middle_name ? person.middle_name : ""
          } ${person.last_name}`
        }}"</span>
      </h3>
    </div>

    <div class="mt-2 space-y-3">
      <div class="w-full flex items-center justify-between">
        <div class="flex items-center space-x-5">
          <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
            <FunnelIcon class="w-5 h-5 mr-2" />
            Filter By Date Range
            <div class="w-72 ml-2">
              <datepicker required position="left" placeholder="select start & end date" :range="true"
                format="dd/MM/yyyy" input-class-name="datepicker" v-model="dateRange" />
            </div>
          </div>

          <div class="flex items-center space-x-2">
            <input type="checkbox" v-model="showPendingTests" />
            <label>Include pending tests</label>
          </div>

          <div class="flex items-center space-x-2">
            <input type="checkbox" v-model="printAllTests" />
            <label>Print all tests for this patient</label>
          </div>

          <CoreActionButton :loading="loading" color="primary" text="Generate Report" :icon="refreshIcon"
            :click="() => init()" />
        </div>

        <div>
          <CorePrinter :print-small-label="shouldPrintSmallLabel(reportData)" :id="patientId" :orderId="orderId"
            v-bind="printAllTests ? { encounter_id: reportData[0].encounter_id } : {}"
            :tests="getSmallLabelPrintTests(reportData)" :orderTests="reportData[0].tests" @update="updateReport"
            v-if="reportData.length > 0" ref="corePrinter" />
        </div>
      </div>

      <div v-if="reportData.length > 0" class="border rounded print-container mt-10">
        <div class="rounded-tr rounded-tl border-b px-5 py-5">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <img src="@/assets/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
              <h3 class="text-2xl font-semibold" style="font-size: 24px; line-height: 2rem">
                PATIENT REPORT
              </h3>
            </div>
            <div>
              <barcode :value="reportData[0].accession_number" :display-value="false" :height="50" />
            </div>
            <div class="bg-gray-50 px-4 py-2 rounded border border-dotted">
              <address class="font-normal">
                <span class="flex items-center not-italic text-xl font-semibold border-b mb-2 border-dotted"
                  style="font-size: 20px; line-height: 2rem">
                  {{ facility.details.name }}
                </span>
                <span class="flex items-center not-italic text-gray-600">
                  {{ facility.details.address }}
                </span>
                <span class="flex items-center not-italic text-gray-600">
                  {{ facility.details.phone }}
                </span>
              </address>
            </div>
          </div>

          <div class="flex items-center justify-between mt-5">
            <div>
              <span class="font-medium">Report Date:</span>
              {{ moment(new Date()).format(DATE_FORMAT_TIME) }}
            </div>
            <div>
              <span class="font-medium">No. Printed:</span>
              {{ getPrintCount(reportData) }}
            </div>
            <div>
              <span class="font-medium">Date Sample Collected:</span>
              {{
                reportData.length > 0 &&
                moment(reportData[0].sample_collection_time).format(DATE_FORMAT_TIME)
              }}
            </div>
          </div>

          <table class="w-full mt-2">
            <tbody>
              <tr>
                <td class="border px-2 py-2 font-bold">Patient Name</td>
                <td class="border px-2 py-2">
                  {{
                    `${person.first_name} ${person.middle_name ? person.middle_name : ""
                    } ${person.last_name}`
                  }}
                </td>
                <td class="border-t border-l px-2 py-2 font-bold">Sex: </td>
                <td class="border-t px-2 py-2">
                  {{ person.sex === "F" ? "Female" : "Male" }}
                </td>
                <td class="border-t border-l px-2 py-2 font-bold">Age: </td>
                <td class="border-t px-2 py-2 border-r">
                  {{ calculateAgeFullFormat(person.date_of_birth) }}
                </td>
              </tr>
              <tr>
                <td class="border px-2 py-2 font-bold">Patient ID</td>
                <td class="border px-2 py-2">{{ person.id }}</td>
                <td class="border px-2 py-2 font-bold" colspan="2">Address</td>
                <td class="border px-2 py-2" colspan="2">
                  {{ getPhysicalAddress }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div v-for="(report, index) in reportData" :key="index">
          <div class="bg-gray-100 border-b flex items-center justify-between px-2 py-2 font-medium">
            <div class="flex items-center">
              Accession No: {{ report.accession_number }}
            </div>
            <div>
              Requested By: {{ report.requested_by }} ({{
                report.requesting_ward
              }})
            </div>
          </div>

          <table class="border-collapse border-slate-500 w-full">
            <tbody>
              <tr>
                <td class="border-b border-r px-2 py-2 font-bold">
                  Specimen Type
                </td>
                <td class="border-b border-r px-2 py-2">
                  {{ report.specimen }}
                </td>
                <td class="border-b border-r px-2 py-2 font-bold">
                  Date Registered
                </td>
                <td class="border-b px-2 py-2">
                  {{ moment(report.created_date).format(DATE_FORMAT_TIME) }}
                </td>
              </tr>
              <tr>
                <td class="border-b border-r px-2 py-2 font-bold">
                  Test Type(s)
                </td>
                <td class="border-b border-r px-2 py-2">
                  {{ getTestTypes(report.test_types) }}
                </td>
                <td class="border-b border-r px-2 py-2 font-bold">
                  Lab Sections
                </td>
                <td class="border-b px-2 py-2">
                  {{ getDepartments(report.test_types) }}
                </td>
              </tr>
              <tr>
                <td class="border-b border-r px-2 py-2 font-bold">
                  Specimen Status
                </td>
                <td class="border-b border-r px-2 py-2">
                  {{ getOrderStatus(report.order_status) }}
                </td>
                <td class="border-b border-r px-2 py-2 font-bold">
                  Received By
                </td>
                <td class="border-b px-2 py-2">
                  {{ getOrderStatusInitiatorName(report.order_status_trail) }}
                </td>
              </tr>
            </tbody>
          </table>

          <table class="border-collapse w-full">
            <thead>
              <tr class="bg-gray-100 text-black">
                <th class="px-2 py-2 text-lg font-semibold">Results</th>
                <th :colspan="2" class="px-2 py-2 text-lg font-semibold">
                  Tests Authorized({{ report.tests_verified }})
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th class="border px-2 py-2 font-bold">Test Types</th>
                <th class="border px-2 py-2 font-bold">Results</th>
                <th class="border px-2 py-2 font-bold">Audit details</th>
              </tr>
              <template v-for="test in showPendingTests
                ? report.tests
                : getCompletedTests(report.tests)" :key="test.id">
                <tr>
                  <td class="border px-2 py-2 text-left">
                    {{ test.test_type_name }}
                  </td>
                  <td class="p-0 border-b" style="padding: 0 !important">
                    <table class="w-full border-collapse" style="
                        border-collapse: collapse !important;
                        margin: 0 !important;
                        padding: 0 !important;
                      ">
                      <thead>
                        <tr class="border-b border-t bg-gray-100">
                          <th class="border-r border-b px-2 py-2 font-bold">
                            Measure
                          </th>
                          <th class="border-r border-b px-2 py-2 font-bold">
                            Result
                          </th>
                          <th class="border-r border-b px-2 py-2 font-bold">
                            Units
                          </th>
                          <th class="px-2 py-2 border-b font-bold">Range</th>
                        </tr>
                      </thead>
                      <tbody class="border-b">
                        <template v-for="(indicator, t) in test.indicators" :key="t">
                          <tr class="border-b">
                            <td class="border-r px-2 py-2">
                              {{ indicator.name }}
                            </td>
                            <td class="px-2 py-2 flex items-center border-r">
                              <p v-html="showResult(
                                indicator.result?.value ?? '',
                                test?.suscept_test_result
                              )"></p>
                              <span v-if="!indicator.result?.value">Not done</span>
                            </td>
                            <td class="px-2">{{ indicator?.unit }}</td>
                            <td class="border-t border-l px-2 py-2 font-bold" v-if="
                              shouldDisplayRange(
                                test.indicators,
                                person,
                                indicator
                              ) !== undefined &&
                              shouldDisplayRange(
                                test.indicators,
                                person,
                                indicator
                              ) !== null
                            ">
                              ({{
                                shouldDisplayRange(
                                  test.indicators,
                                  person,
                                  indicator
                                ).lower_range
                              }}
                              -
                              {{
                                shouldDisplayRange(
                                  test.indicators,
                                  person,
                                  indicator
                                ).upper_range
                              }})
                            </td>
                            <td class="border px-2 py-2" v-else></td>
                          </tr>
                        </template>
                      </tbody>
                    </table>
                  </td>

                  <td class="border px-2 py-2">
                    <div class="test-status">
                      <p class="font-bold">Test Status</p>
                      <p class="px-2">{{ getTestStatusName(test.status) }}</p>
                      <p class="px-2">
                        By:
                        {{
                          getTestStatusInitiatorName(
                            test.status_trail,
                            test.status
                          )["name"]
                        }}
                      </p>
                      <p class="px-2">
                        On:
                        {{
                          moment(
                            getTestStatusInitiatorName(
                              test.status_trail,
                              test.status
                            )["created_at"]
                          ).format(DATE_FORMAT_TIME)
                        }}
                      </p>
                    </div>
                    <div v-if="
                      getTestStatusInitiatorName(
                        test.status_trail,
                        'completed'
                      )['name']
                    ">
                      <p class="font-bold">Performed By</p>
                      <p class="px-2">
                        {{
                          getTestStatusInitiatorName(
                            test.status_trail,
                            "completed"
                          )["name"]
                        }}
                      </p>
                      <p class="px-2 mb-2">
                        On:
                        {{
                          moment(
                            getTestStatusInitiatorName(
                              test.status_trail,
                              "completed"
                            )["created_at"]
                          ).format(DATE_FORMAT_TIME)
                        }}
                      </p>
                      <div v-if="getMachineName(test.indicators)" class="mb-2">
                        <h3 class="font-bold">Laboratory Machine Used</h3>
                        <p>{{ getMachineName(test.indicators) }}</p>
                      </div>
                    </div>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>

          <div>
            <div class="bg-gray-100 px-4 py-2 border-b">
              <h3 class="text-lg font-semibold">Test Remarks</h3>
            </div>
            <template v-if="hasAnyRemarks(report.tests)">
              <div class="px-4 py-2 border-b"
                v-for="test in showPendingTests ? report.tests : getCompletedTests(report.tests)" :key="test.id">
                <div v-if="getRemarks(test)" class="mb-3">
                  <p class="font-medium">{{ test.test_type_name }}:</p>
                  <p class="ml-4" v-html="getRemarks(test)"></p>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="px-4 py-2 border-b">
                <p class="text-gray-600">No remarks available for these tests.</p>
              </div>
            </template>
          </div>

          <div class="m-2" v-show="checkSusceptibilityResults(report)">
            <h3 class="text-lg font-semibold uppercase mb-3 mt-3">
              Suscpeptibility Test Results
            </h3>
            <div>
              <div class="grid grid-cols-2 gap-4" v-for="test in report.tests" :key="test.id">
                <div v-for="(result, index) in test.suscept_test_result" :key="index"
                  v-if="test.test_type_name.toLowerCase().includes('culture')">
                  <div class="border rounded-tr rounded-tl px-2 py-2 font-medium text-lg">
                    {{ result.name }}
                  </div>
                  <table class="w-full">
                    <thead>
                      <tr class="border-b px-2 py-2 text-left border-r border-l">
                        <th class="px-4 py-2 border-r">Drug</th>
                        <th class="px-4 py-2 border-r">Zone (mm)</th>
                        <th class="px-4 py-2">Intrepretation</th>
                      </tr>
                    </thead>
                    <tbody classs="border-l border-r">
                      <tr v-for="drug in getFilteredDrugs(result.drugs)" :key="drug.drug_id">
                        <th class="text-left border-l border-r px-4 py-2 border-b font-normal">
                          {{ drug.name }}
                        </th>
                        <th class="text-left border-l border-r px-4 py-2 border-b font-normal">
                          {{ drug.zone }}
                        </th>
                        <th class="text-left border-l border-r px-4 py-2 border-b font-normal">
                          {{ drug.interpretation }}
                        </th>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div v-show="loading" class="mx-auto justify-center flex flex-col items-center space-y-3 py-20">
          <CoreLoader />
          <p class="text-base">
            Generating report, please wait<span class="animate-ping">...</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { endpoints } from "@/services/endpoints";
import {
  PrinterIcon,
  QrCodeIcon,
  UserIcon,
  ArrowPathIcon,
  FunnelIcon,
} from "@heroicons/vue/24/solid/index.js";
import type {
  Page,
  Patient,
  Request,
  Order,
  Response,
  Indicator,
  IndicatorRange,
  Test,
  StatusTrail,
} from "@/types";
import fetchRequest from "@/services/fetch";
import moment from "moment";
import { useFacilityStore } from "@/store/facility";
import Package from "@/package.json";

export default {
  name: "PatientReport",
  components: {
    UserIcon,
    QrCodeIcon,
    FunnelIcon,
  },
  setup() {
    definePageMeta({
      layout: "dashboard",
      middleware: ['reports']
    });
    useHead({
      title: `${Package.name} - Patient Report`,
    });
  },
  data() {
    return {
      dateRange: new Array("", ""),
      pages: [
        { name: "Home", link: "/home" },
        { name: "Reports", link: "#" },
        { name: "Daily Reports", link: "#" },
        { name: "Patient Report", link: "/reports/daily/patient-report" },
      ] as Page,
      location: {} as any,
      reportData: new Array<any>(),
      person_identifiers: [] as Array<any>,
      physicalAddress: "" as string,
      person: {} as Patient,
      printIcon: PrinterIcon as Object,
      refreshIcon: ArrowPathIcon as Object,
      cookie: useCookie("token"),
      moment: moment,
      facility: useFacilityStore(),
      hasSusceptivibility: false as boolean,
      authorizedTestCount: 0 as number,
      showPendingTests: false as boolean,
      loading: false as boolean,
      printAllTests: true as boolean,
    };
  },
  computed: {
    router() {
      return this.$router;
    },
    patientId(): string {
      return String(this.router.currentRoute.value.params.patientId);
    },
    orderId(): string {
      return String(this.$route.query.order_id);
    },
    getPhysicalAddress() {
      if (this.person_identifiers.length > 0) {
        this.person_identifiers.forEach((item) => {
          if (
            item.identifier_type === "current_traditional_authority" ||
            item.identifier_type === "physical_address"
          ) {
            this.physicalAddress = item.value;
          }
        });
      }
      return this.physicalAddress;
    },
  },
  async created() {
    this.init();
  },
  watch: {
    printAllTests() {
      // Automatically reload data when printAllTests checkbox is toggled
      this.init();
    }
  },
  methods: {
    async init(): Promise<void> {
      this.loading = true;
      const orderId = this.$route.query.order_id?.toString();
      const encounterId = this.$route.query.encounter_id?.toString();
      const startDate =
        this.dateRange !== null &&
          this.dateRange.length > 0 &&
          moment(this.dateRange[0]).isValid()
          ? moment(this.dateRange[0]).format("YYYY-MM-DD")
          : "";

      const endDate =
        this.dateRange !== null &&
          this.dateRange.length > 1 &&
          moment(this.dateRange[1]).isValid()
          ? moment(this.dateRange[1]).format("YYYY-MM-DD")
          : "";

      this.person_identifiers = new Array<any>();
      if (orderId !== null && orderId !== undefined) {
        // Conditionally add encounter_id parameter based on printAllTests
        const baseRoute = `${endpoints.tests}/${this.patientId}/report?order_id=${orderId}&from=${startDate}&to=${endDate}`;
        const route = this.printAllTests ? `${baseRoute}&encounter_id=${encounterId}` : baseRoute;

        const request: Request = {
          route: route,
          method: "GET",
          token: `${this.cookie}`,
        };
        const { data, error, pending }: Response = await fetchRequest(request);
        this.loading = pending;
        if (data.value) {
          this.loading = false;
          this.person = data.value.client.person;
          this.person_identifiers = data.value.client.client_identifiers;
          let formattedData = data.value.orders.map((order: any) => {
            let count = 0;
            if (order.tests !== undefined) {
              count += order.tests.filter(
                (test: { status: string }) =>
                  test.status.toLowerCase() === "verified"
              ).length;
            }
            return {
              ...order,
              tests_verified: count,
            };
          });
          this.reportData = formattedData;
          this.showPrinterDialog();
        }
        if (error.value) {
          this.loading = false;
          console.error(error.value);
        }
      } else {
        // Conditionally add encounter_id parameter based on printAllTests
        const baseRoute = `${endpoints.tests}/${this.patientId}/report?from=${startDate}&to=${endDate}`;
        const route = this.printAllTests ? `${baseRoute}&encounter_id=${encounterId}` : baseRoute;

        const request: Request = {
          route: route,
          method: "GET",
          token: `${this.cookie}`,
        };
        const { data, error, pending }: Response = await fetchRequest(request);
        this.loading = pending;
        if (data.value) {
          this.person = data.value.client.person;
          this.person_identifiers = data.value.client.client_identifiers;
          let formattedData = data.value.orders.map((order: any) => {
            let count = 0;
            if (order.tests !== undefined) {
              count += order.tests.filter(
                (test: { status: string }) =>
                  test.status.toLowerCase() === "verified"
              ).length;
            }
            return {
              ...order,
              tests_verified: count,
            };
          });
          this.reportData = formattedData;
          this.loading = false;
          this.showPrinterDialog();
        }
        if (error.value) {
          console.error(error.value);
          this.loading = false;
        }
      }
    },
    getOrderStatusInitiatorName(data: Array<Order>): string {
      const specimenStatus = ["specimen-accepted", "specimen-rejected"];
      const result = data.find((item) =>
        specimenStatus.includes(item.status.name)
      );
      return result
        ? `${result.initiator.first_name} ${result.initiator.last_name}`
        : "";
    },
    getOrderStatus(status: string): string {
      return status !== null ? capitalizeStr(status.split("-").join(" ")) : "";
    },
    getTestStatusInitiatorName(data: Array<Order>, status: string) {
      const result = data.find((item) => status === item.status.name);
      return result
        ? {
          name: `${result.initiator.first_name} ${result.initiator.last_name}`,
          created_at: result.created_date,
        }
        : {
          name: "",
          created_at: "",
        };
    },
    getTestTypes(data: Array<Order>) {
      return data.map((item) => item.name).join(", ");
    },
    getDepartments(data: Array<Order>): string {
      const uniqueDepartments = [
        ...new Set(data.map((item) => item.department)),
      ];
      return uniqueDepartments.join(", ");
    },
    getAuthorizedTestCount(data: Array<Order>): number {
      return data.reduce((count, order) => count + order.tests_verified, 0);
    },
    showIndicatorRanges(data: Array<Order>) {
      return data.some(
        (indicator) => indicator.test_indicator_type === "numeric"
      );
    },
    getTestStatusName(status: string) {
      if (status == "verified") {
        status = "Authorized";
      } else {
        status = status
          .split("-")
          .map((word) => this.capitalizeStr(word))
          .join(" ");
      }
      return status;
    },
    capitalizeStr(str: string) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    updateReport(value: boolean): void {
      if (value) {
        this.init();
      }
    },
    checkSusceptibilityResults(report: any): boolean {
      return report.tests.some(
        (test: any) => test.suscept_test_result.length !== 0
      );
    },
    getFilteredDrugs(drugs: Array<any>): Array<any> {
      return drugs.filter((drug) => drug.zone !== null);
    },
    showResult(result: any, susceptibilityResults: Array<any>): string {
      return susceptibilityResults.length > 0 && result.toLowerCase() === 'growth'
        ? `Growth of ${[...new Set(susceptibilityResults)]
          .map((result) => result.name)
          .join(", ")}`
        : result;
    },
    shouldPrintSmallLabel(orders: Array<Order>): boolean {
      return orders.some((order) =>
        order.test_types.some((test) => test.print_device)
      );
    },
    getSmallLabelPrintTests(orders: Array<Order>): Array<string> {
      const returnValue: string[] = [];
      const printTestTypes: Set<string> = new Set();
      orders.forEach((order) => {
        order.test_types.forEach((test) => {
          if (test.print_device) {
            printTestTypes.add(test.name);
          }
        });
      });
      orders.forEach((order) => {
        order.tests.forEach((test) => {
          if (printTestTypes.has(test.test_type_name)) {
            returnValue.push(String(test.id));
          }
        });
      });
      return returnValue;
    },
    getPrintCount(reportData: Array<any>): number {
      return reportData.reduce(
        (count, report) => count + report.print_count,
        0
      );
    },
    getRequiredRange(patient: Patient, ranges: any): IndicatorRange {
      const patientSex = patient.sex == "M" ? "Male" : "Female";
      const age = Math.ceil(
        Number(
          calculateAge(patient.date_of_birth) == 0
            ? 1
            : Number(calculateAge(patient.date_of_birth))
        )
      );
      return ranges.filter(
        (range: any) =>
          (range.sex === patientSex &&
            age > Number(range.min_age) &&
            age < Number(range.max_age)) ||
          (range.sex === "Both" &&
            age > Number(range.min_age) &&
            age < Number(range.max_age))
      )[0];
    },
    shouldDisplayRange(
      indicators: Array<Order>,
      patient: Patient,
      indicator: Indicator
    ): IndicatorRange {
      let result = null as unknown as IndicatorRange;
      if (this.showIndicatorRanges(indicators)) {
        result = this.getRequiredRange(patient, indicator.indicator_ranges);
      }
      return result;
    },
    getMachineName(indicators: Array<Order>): string {
      return indicators[0].result.machine_name;
    },
    getCompletedTests(tests: Array<Test>): Array<Test> {
      const validStatuses = new Set(["completed", "verified", "test-rejected"]);
      return tests.filter((item: Test) =>
        validStatuses.has(item.status.toLowerCase())
      );
    },
    showPrinterDialog() {
      this.$nextTick(() => {
        if (this.$route.query.printer?.toString() == "true") {
          (this.$refs.corePrinter as any).init();
        }
      });
    },
    getRemarks(test: Test): string {
      const rejectedReason = test?.status_trail.find((trail: StatusTrail) => trail.status.name.toLowerCase() === "test-rejected");
      let reasonDescription: string = "N/A";
      if (rejectedReason) {
        reasonDescription = `<b>Rejected Reason:</b> ${rejectedReason?.status_reason?.description}`;
      }
      const remarks: string = test?.result_remarks?.value;
      if (remarks && reasonDescription !== "N/A") {
        return `${remarks}<br>${reasonDescription}`;
      }
      return remarks || reasonDescription;
    },
    hasAnyRemarks(tests: Test[]) {
      return tests.some(test => {
        const remarks = this.getRemarks(test);
        return remarks && remarks !== "N/A";
      });
    }
  },
};
</script>
