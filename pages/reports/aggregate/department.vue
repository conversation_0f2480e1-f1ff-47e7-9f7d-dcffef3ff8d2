<template>
  <div class="px-5 py-5">
    <CoreDownloadingDialog ref="downloadModalRef" />
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center py-5">
      <img src="@/assets/icons/report.png" alt="report-icon" class="w-8 h-8 mr-2" />
      <h3 class="text-2xl font-semibold uppercase">{{ title }}</h3>
    </div>

    <div class="w-full flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
          <FunnelIcon class="w-5 h-5 mr-2" />
          Filter By Date Range
          <div class="w-72 ml-2">
            <datepicker @cleared="isCleared" required position="left" placeholder="select start & end date"
              :range="true" input-class-name="datepicker" format="dd/MM/yyyy" v-model="dateRange" :maxDate="new Date()"/>
          </div>
        </div>
        <div class="w-44">
          <CoreDropdown :items="departments" v-model="selectedDepartment" />
        </div>
        <div>
          <CoreActionButton color="primary" text="Generate Report" :icon="ArrowPathIcon" :click="() => generateReport()"
            :loading="loading" />
        </div>
      </div>
      <div class="flex items-center space-x-3">
        <CorePrinterReport :printSmallLabel="false" />
        <excel class="btn btn-default" ref="excelRef" :header="header" :data="currentData" worksheet="report-work-sheet"
          :name="fileName">
          <button ref="excelTriggerRef"></button>
        </excel>
        <CoreExportButton @click="exportAllData" text="Export Excel" />
      </div>
    </div>

    <div class="border rounded print-container mt-5">
      <div class="w-full rounded-tr rounded-tl border-b px-5 py-5 flex items-center justify-between">
        <div class="flex flex-col space-y-2">
          <img src="@/assets/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
          <h3 class="text-xl font-semibold">LABORATORY DEPARTMENT REPORT</h3>
        </div>
        <ReportsAddress />
      </div>

      <div class="mt-3 px-5 border-b">
        <h3 class="font-medium mb-2">
          Tests Performed Period:
          <span class="font-normal">
            {{
              dateRange[0].toString() != ""
                ? moment(dateRange[0].toString()).format(DATE_FORMAT)
                : ""
            }}
            -
            {{
              dateRange[1].toString() != ""
                ? moment(dateRange[1].toString()).format(DATE_FORMAT)
                : ""
            }}
          </span>
        </h3>
      </div>

      <div v-if="reportData.wards.length > 0 && reportData.data.length > 0" class="overflow-x-auto"
        id="print-container">
        <table class="w-full border border-dotted rounded overflow-x-auto">
          <thead class="w-full">
            <tr>
              <th class="px-2 py-2 text-center border-r">TESTS</th>
              <th class="px-2 py-2 text-center border-r border-b bg-gray-100" :colspan="reportData.wards.length">
                WARDS
              </th>
              <th class="px-2 py-2 text-center">TOTAL</th>
            </tr>
            <tr class="border-b">
              <th class="border-r"></th>
              <th class="px-2 py-2 text-center border-r" v-for="(ward, index) in reportData.wards" :key="index">
                {{ ward }}
              </th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(data, index) in reportData.data" :key="index">
              <th class="capitalize px-2 py-2 text-center bg-gray-50 border-b border-t">
                {{ Object.keys(data)[0].toString().toUpperCase() }}
              </th>
              <th class="capitalize px-2 py-2 text-center bg-gray-50 border-b border-t"
                v-for="(ward, wardIndex) in reportData.wards" :key="wardIndex"></th>
              <th class="capitalize px-2 py-2 text-center bg-gray-50 border-b border-t"></th>
              <tr class="border-t border-b" v-for="(report, reportIndex) in data[Object.keys(data)[0]]"
                :key="reportIndex">
                <td class="capitalize font-medium px-2 py-2 text-center border-r bg-gray-50">
                  {{ report.test_type }}
                </td>
                <td @click="
                  showEnumeration(
                    report.test_type,
                    ward,
                    report.ward[ward] ? report.ward[ward].total : 0,
                    report.ward[ward] ? report.ward[ward].associated_ids : ''
                  )
                  " v-for="(ward, wardIndex) in reportData.wards" :key="wardIndex"
                  class="px-4 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ report.ward[ward] ? report.ward[ward].total : 0 }}
                </td>
                <td class="px-2 py-2 text-center font-medium">
                  {{ calculateRowTotal(report) }}
                </td>
              </tr>
            </template>
          </tbody>
        </table>

        <table v-if="reportData.blood_bank_products.length > 0"
          class="w-full border border-dotted rounded overflow-x-auto mt-10">
          <thead>
            <tr>
              <th class="border-b px-2 py-2 uppercase" :rowspan="4">
                Blood Products
              </th>
              <th class="border-b px-2 py-2"></th>
              <th class="border-b px-2 py-2 bg-gray-100" :colspan="reportData.wards.length">
                Wards
              </th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(data, index) in reportData.blood_bank_products" :key="index">
              <tr class="border-t">
                <td class="capitalize px-2 py-2 text-center bg-gray-50 border-r">
                  {{ Object.keys(data)[0] }}
                </td>
                <td class="px-2 py-2 border-r border-b"></td>
                <th :colspan="3" class="px-2 py-2 text-center border-r border-b"
                  v-for="(ward, index) in reportData.wards" :key="index">
                  {{ ward }}
                </th>
              </tr>

              <tr>
                <td class="px-2 py-2 bg-gray-50 border-r">&nbsp;</td>
                <td class="px-2 py-2 border-b border-r">Age-Ranges</td>
                <template v-for="(i, k) in reportData.wards" :key="k">
                  <td class="px-2 py-2 border-b border-r">0-5</td>
                  <td class="px-2 py-2 border-b border-r">6-14</td>
                  <td class="border-r px-2 py-2 border-b">15-120</td>
                </template>
              </tr>
              <tr>
                <td class="px-2 py-2 bg-gray-50 border-r">&nbsp;</td>
                <td class="px-2 py-2 border-b border-r">Female</td>
                <template v-for="ward in reportData.wards" :key="ward">
                  <td
                    class="px-4 py-2 border-b border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                    @click="
                      showEnumeration(
                        `${Object.keys(data)[0]} for females, `,
                        ward,
                        getDataValue(data, 'female', ward, '0-5'),
                        getAssociatedIds(data, 'female', ward, '0-5')
                      )
                      ">
                    {{ getDataValue(data, "female", ward, "0-5") }}
                  </td>
                  <td
                    class="px-2 py-2 border-b border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                    @click="
                      showEnumeration(
                        `${Object.keys(data)[0]} for females, `,
                        ward,
                        getDataValue(data, 'female', ward, '6-14'),
                        getAssociatedIds(data, 'female', ward, '6-14')
                      )
                      ">
                    {{ getDataValue(data, "female", ward, "6-14") }}
                  </td>
                  <td
                    class="px-2 py-2 border-b border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                    @click="
                      showEnumeration(
                        `${Object.keys(data)[0]} for females, `,
                        ward,
                        getDataValue(data, 'female', ward, '15-120'),
                        getAssociatedIds(data, 'female', ward, '15-120')
                      )
                      ">
                    {{ getDataValue(data, "female", ward, "15-120") }}
                  </td>
                </template>
              </tr>
              <tr>
                <td class="px-2 py-2 bg-gray-50 border-r">&nbsp;</td>
                <td class="px-2 py-2 border-b border-r">Male</td>
                <template v-for="ward in reportData.wards" :key="ward">
                  <td
                    class="px-2 py-2 border-b border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                    @click="
                      showEnumeration(
                        `${Object.keys(data)[0]} for males`,
                        ward,
                        getDataValue(data, 'male', ward, '0-5'),
                        getAssociatedIds(data, 'male', ward, '0-5')
                      )
                      ">
                    {{ getDataValue(data, "male", ward, "0-5") }}
                  </td>
                  <td
                    class="px-2 py-2 border-b border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                    @click="
                      showEnumeration(
                        `${Object.keys(data)[0]} for males`,
                        ward,
                        getDataValue(data, 'male', ward, '6-14'),
                        getAssociatedIds(data, 'male', ward, '6-14')
                      )
                      ">
                    {{ getDataValue(data, "male", ward, "6-14") }}
                  </td>
                  <td
                    class="px-2 py-2 border-b border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                    @click="
                      showEnumeration(
                        `${Object.keys(data)[0]} for males`,
                        ward,
                        getDataValue(data, 'male', ward, '15-120'),
                        getAssociatedIds(data, 'male', ward, '15-120')
                      )
                      ">
                    {{ getDataValue(data, "male", ward, "15-120") }}
                  </td>
                </template>
              </tr>
            </template>
          </tbody>
        </table>

        <div class="w-full py-2 mt-2.5 border-b" v-if="reportData.critical_values.length > 0">
          <h3 class="text-xl font-semibold uppercase text-center">
            CRITICAL VALUES
          </h3>
        </div>

        <table class="w-full border border-dotted rounded overflow-x-auto" v-if="reportData.critical_values.length > 0">
          <thead>
            <tr>
              <th class="px-2 py-2 text-center border-r">Measure</th>
              <th class="px-2 py-2 text-center border-r border-b bg-gray-100" :colspan="reportData.wards.length">
                Wards
              </th>
              <th class="px-2 py-2 text-center font-bold uppercase">Total</th>
            </tr>
            <tr class="border-b">
              <th class="border-r"></th>
              <th class="px-2 py-2 text-center border-r" v-for="(ward, index) in reportData.wards" :key="index">
                {{ ward }}
              </th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(data, index) in reportData.critical_values" :key="index">
              <th class="px-2 py-2 text-center uppercase bg-gray-50 border-b border-t">
                {{ Object.keys(data)[0] }}
              </th>
              <th class="capitalize px-2 py-2 text-center bg-gray-50 border-b border-t"
                v-for="(ward, wardIndex) in reportData.wards" :key="wardIndex"></th>
              <th class="capitalize px-2 py-2 text-center bg-gray-50 border-b border-t"></th>
              <tr class="border-t border-b" v-for="(report, reportIndex) in data[Object.keys(data)[0]]"
                :key="reportIndex">
                <td class="capitalize px-2 py-2 text-center border-r">
                  {{ report.critical_value_level }}
                </td>

                <td v-for="(ward, wardIndex) in reportData.wards" :key="wardIndex" @click="
                  showEnumeration(
                    `${Object.keys(data)[0]} - Critical value (${report.critical_value_level
                    })`,
                    ward,
                    report.ward[ward] ? report.ward[ward].total : 0,
                    report.ward[ward] ? report.ward[ward].associated_ids : ''
                  )
                  "
                  class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ report.ward[ward] ? report.ward[ward].total : 0 }}
                </td>
                <td class="px-2 py-2 text-center font-bold">
                  {{ calculateRowTotal(report) }}
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
      <ReportsLoader :condition="loading" :cancelReportGeneration="() => cancelRequest()" />
      <div class="flex flex-col space-y-3 items-center justify-center py-10" v-if="
        reportData.wards.length == 0 &&
        reportData.data.length == 0 &&
        !loading
      ">
        <img src="@/assets/images/page.png" class="w-20 h-20 object-cover" alt="page-icon" />
        <p>
          Please generate report data to preview the
          <span class="font-medium">{{ reportName }}</span>
          report.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Department, Page, Request, Response } from "@/types";
import moment from "moment";
import { ArrowPathIcon, FunnelIcon } from "@heroicons/vue/24/solid/index.js";
import Package from "@/package.json";
import { ExportToExcel } from "vue-doc-exporter";
import { useFacilityStore } from "@/store/facility";
import type DownloadingDIalog from "@/components/core/DownloadingDIalog.vue";

definePageMeta({
  layout: "dashboard",
  middleware: ["reports"],
});
useHead({
  title: `${Package.name.toUpperCase()} - Department Report`,
});

const title = "Department Report";
const facility = useFacilityStore();
const departments = ref<Array<Department>>(new Array<Department>());
const dateRange = ref<Array<any>>(new Array("", ""));
const isCleared = (): void => {
  dateRange.value = new Array("", "");
};
const selectedDepartment = ref<Department>({ name: "select department" });
const cookie = useCookie("token");
const router = useRouter();
const route = useRoute();
const { $toast } = useNuxtApp();
const { loading, executeCancellableRequest, cancelRequest } = useCancellableRequest();
const excelRef = ref<InstanceType<any> | null>(null);
const currentData = ref<Record<string, any>[]>([]);
const fileName = ref('');
const header = ref<string[]>([]);
const excelTriggerRef = ref();
const downloadModalRef = ref<InstanceType<any> | null>(null)
const reportData = ref<Record<string, Array<any>>>({ wards: [], data: [] });
const pages = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Reports",
    link: "#",
  },
  {
    name: "Aggregate Reports",
    link: "#",
  },
]);

const reportName = computed((): string => {
  const { id, name } = selectedDepartment.value;
  return id === 0 || id === undefined ? "" : name;
});

const startDate = computed(() => {
  return dateRange.value[0]
    ? moment(dateRange.value[0]).format("YYYY-MM-DD")
    : "";
});
const endDate = computed(() => {
  return dateRange.value[1]
    ? moment(dateRange.value[1]).format("YYYY-MM-DD")
    : "";
});

const exportData = computed((): Object[] => {
  const flattenedArray: Array<Record<string, any>> = [];
  reportData.value.data.length > 0 && reportData.value.data.forEach((entry: Record<string, any>) => {
    Object.keys(entry).forEach((month) => {
      entry[month].forEach((testEntry: { ward: { [s: string]: { total: number } }; test_type: any; }) => {

        const flattenedObject: Record<string, any> = {
          "TEST": testEntry.test_type.toString().toUpperCase(),
          "MONTH": capitalizeStr(month)
        };

        reportData.value.wards.forEach((ward: string) => {
          flattenedObject[ward.toUpperCase()] = 0;
        });

        Object.keys(testEntry.ward).forEach((ward) => {
          flattenedObject[ward.toUpperCase()] = testEntry.ward[ward].total;
        });

        const total = Object.values(testEntry.ward).reduce((sum, ward) => sum + ward.total, 0);
        flattenedObject.total = total;

        flattenedArray.push(flattenedObject);
      });
    });
  });

  return flattenedArray;
});

const exportCriticalValuesData = computed((): Array<Record<string, any>> => {
  const flattenedArray: Array<Record<string, any>> = [];

  reportData.value.critical_values.forEach((valueEntry: any) => {
    Object.keys(valueEntry).forEach((key) => {
      const criticalValueEntries = valueEntry[key];

      criticalValueEntries.forEach((entry: { critical_value_level: string; ward: Record<string, { total: number }> }) => {
        const flattenedObject: Record<string, any> = {
          "TEST": key,
          "CRITICAL VALUE LEVEL": entry.critical_value_level
        };

        reportData.value.wards.forEach((ward: string) => {
          flattenedObject[ward.toUpperCase()] = 0;
        });

        Object.keys(entry.ward).forEach((ward) => {
          flattenedObject[ward.toUpperCase()] = entry.ward[ward].total;
        });

        flattenedObject.TOTAL = Object.values(entry.ward).reduce((sum, ward) => sum + ward.total, 0);
        flattenedArray.push(flattenedObject);
      });
    });
  });

  return flattenedArray;
});

const exportBloodProductsData = computed((): Object[] => {
  const flattenedArray: Array<Record<string, any>> = [];
  reportData.value.blood_bank_products.length > 0 && reportData.value.blood_bank_products.forEach((entry: Record<string, any>) => {
    Object.keys(entry).forEach((product) => {
      entry[product].forEach((productEntry: { gender: string; ward: any[]; }) => {
        productEntry.ward.forEach((wardEntry: Record<string, any>) => {
          Object.keys(wardEntry).forEach((wardName) => {
            const wardDetails = wardEntry[wardName];
            Object.keys(wardDetails).forEach((ageRange) => {
              const ageDetails = wardDetails[ageRange];
              const flattenedObject: Record<string, any> = {
                "PRODUCT": product,
                "GENDER": capitalizeStr(productEntry.gender),
                "AGE RANGE": ageRange,
                "WARD ": wardName.toUpperCase(),
                "TOTAL COUNT": ageDetails.total,
              };
              flattenedArray.push(flattenedObject);
            });
          });
        });
      });
    });
  });
  return flattenedArray;
});

const exportAllData = async (): Promise<void> => {
  downloadModalRef.value?.openModal();
  try {
    const datasets: Array<Array<Record<string, any>>> = [
      exportData.value,
      exportBloodProductsData.value,
      exportCriticalValuesData.value
    ];
    const sheetNames: string[] = [
      'Department Report',
      'Department - Blood Bank Products Report',
      'Department - Hematology Critical Values Report'
    ];

    for (let index = 0; index < datasets.length; index++) {
      const data = datasets[index];
      const sheetName = sheetNames[index].toLowerCase().replace(/ /g, "_");
      currentData.value = data;
      fileName.value = `${sheetName}_${moment(startDate.value).format('DD_MM_yyyy')}_to_${moment(endDate.value).format('DD_MM_yyyy')}.xls`;
      header.value = [
        `LABORATORY DEPARTMENT REPORT`,
        sheetName,
        `PERIOD FROM ${moment(startDate.value).format('DD-MM-yyyy')} TO ${moment(endDate.value).format('DD-MM-yyyy')}`,
        facility.details.name,
        facility.details.address,
        facility.details.phone
      ];
      await nextTick();
      excelTriggerRef.value?.click()
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    $toast.success(REPORTS_DOWNLOAD_SUCCESS);
  } catch (e: any) {
    console.error("error dowloading reports: ",e)
    $toast.error(REPORTS_DOWNLOAD_ERROR);
  }
  downloadModalRef.value?.closeModal();
};

async function generateReport(): Promise<void> {
  if (!validate()) {
    loading.value = true;
    reportData.value = { wards: [], data: [] };
    let queryParams = `from=${startDate.value}&to=${endDate.value
      }&department=${checkStatus(selectedDepartment.value.name)}&report_id=${route.query.report_id
      }`;

    const request: Request = {
      route: `${endpoints.aggregateReports}/department?${queryParams}`,
      method: "GET",
      token: `${cookie.value}`,
    };

    const { data, error, pending }: Response = await executeCancellableRequest(request);
    loading.value = pending;

    if (data.value) {
      loading.value = false;
      reportData.value = data.value;
      updateReportId(data.value.report_id);
      useNuxtApp().$toast.success("Report generated successfully!");
    }

    if (error.value) {
      loading.value = false;
      console.error(error.value);
    }
  } else {
    useNuxtApp().$toast.warning("Please select a department!");
  }
}

const updateReportId = (reportId: string) => {
  const currentRoute = router.currentRoute.value;
  const newQuery = {
    ...currentRoute.query,
    report_id: reportId,
  };
  router.replace({ query: newQuery }).catch((err: any) => {
    console.error("Failed to replace route:", err);
  });
};

function checkStatus(status: string): string {
  return status === "select department" ||
    status === "-- select test type --" ||
    status === "-- select test status --"
    ? ""
    : status;
}

function validate(): Boolean {
  return selectedDepartment.value.name == "select department";
}

const showEnumeration = (
  test: any,
  ward: string,
  count: number,
  associated_ids: string
): void => {
  if (count !== 0 && associated_ids !== "") {
    router.push(
      `/reports/${associated_ids}?origin=aggregate&type=department-report&from=${startDate.value}&to=${endDate.value}&test=${test} - ${ward}&department=${selectedDepartment.value.name}&count=${count}`
    );
  } else {
    $toast.warning("No data found for this month");
  }
};

function calculateRowTotal(report: { ward: { [x: string]: any } }): number {
  const total = reportData.value.wards.reduce(
    (acc: any, ward: string | number) => {
      return acc + (report.ward[ward] ? report.ward[ward].total : 0);
    },
    0
  );

  return total;
}

function getDataValue(
  data: { [key: string]: any[] },
  gender: string,
  ward: string,
  ageRange: string
): any {
  const bloodBankProductCopy = JSON.parse(
    JSON.stringify(data[Object.keys(data)[0]])
  );

  const genderData = bloodBankProductCopy.find(
    (item: { gender: string }) => item.gender === gender
  );
  if (!genderData) {
    return 0;
  }

  const wardData = genderData.ward.find(
    (item: {}) => Object.keys(item)[0] === ward.toLowerCase()
  );
  if (!wardData) {
    return 0;
  }

  const ageRangeData = wardData[ward.toLowerCase()][ageRange];
  if (ageRangeData === undefined) {
    return 0;
  }

  return ageRangeData.total;
}

function getAssociatedIds(
  data: { [key: string]: any[] },
  gender: string,
  ward: string,
  ageRange: string
): any {
  const bloodBankProductCopy = JSON.parse(
    JSON.stringify(data[Object.keys(data)[0]])
  );

  const genderData = bloodBankProductCopy.find(
    (item: { gender: string }) => item.gender === gender
  );
  if (!genderData) {
    return 0;
  }

  const wardData = genderData.ward.find(
    (item: {}) => Object.keys(item)[0] === ward.toLowerCase()
  );
  if (!wardData) {
    return 0;
  }

  const ageRangeData = wardData[ward.toLowerCase()][ageRange];
  if (ageRangeData === undefined) {
    return 0;
  }

  return ageRangeData.associated_ids;
}

watch(
  dateRange,
  () => {
    const startDate = dateRange.value[0]
      ? moment(dateRange.value[0]).format("YYYY-MM-DD")
      : "";
    const endDate = dateRange.value[1]
      ? moment(dateRange.value[1]).format("YYYY-MM-DD")
      : "";
    if (startDate || endDate) {
      const currentRoute = router.currentRoute.value;
      const newQuery = {
        ...currentRoute.query,
        ...(startDate && { from: startDate }),
        ...(endDate && { to: endDate }),
      };
      router.replace({ query: newQuery }).catch((err: any) => {
        console.error("Failed to replace route:", err);
      });
    }
  },
  { deep: true }
);

watch(selectedDepartment, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    const currentRoute = router.currentRoute.value;
    const newQuery = {
      ...currentRoute.query,
      department: newValue.name || undefined,
      report_id: "",
    };
    router.replace({ query: newQuery }).catch((err) => {
      console.error("Failed to replace route:", err);
    });
  }
});

const validFields = computed((): boolean => {
  if (
    route.query.to !== undefined &&
    route.query.from !== undefined &&
    route.query.department !== undefined
  ) {
    dateRange.value = [route.query.from, route.query.to];
    selectedDepartment.value.name = `${route.query.department}`;
    return (
      route.query.to !== "" &&
      route.query.from !== "" &&
      route.query.department !== ""
    );
  }
  return false;
});

onMounted(() => {
  if (validFields.value) {
    generateReport();
  }else{
    loading.value = false;
  }

  departments.value = useNuxtApp().$metadata.departments;
});
</script>
