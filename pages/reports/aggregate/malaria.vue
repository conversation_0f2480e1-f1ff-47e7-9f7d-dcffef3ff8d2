<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />
    <div class="flex items-center py-5">
      <img src="@/assets/icons/report.png" alt="report-icon" class="w-8 h-8 mr-2" />
      <h3 class="text-2xl font-semibold uppercase">{{ title }}</h3>
    </div>
    <div class="w-full flex items-center justify-between">
      <FormKit type="form" submit-label="Update" @submit="generateReport" :actions="false" #default="{ value }"
        id="submitForm">
        <div class="w-full flex items-center space-x-3">
          <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
            <FunnelIcon class="w-5 h-5 mr-2" />
            Filter By Date Range
            <div class="w-72 ml-2">
              <datepicker @cleared="isCleared" format="dd/MM/yyyy" required position="left"
                placeholder="select start & end date" :range="true" input-class-name="datepicker" v-model="dateRange" :maxDate="new Date()"/>
            </div>
          </div>
          <div>
            <CoreActionButton type="submit" :loading="loading" color="primary" text="Generate Report"
              :icon="refreshIcon" :click="() => { }" />
          </div>
        </div>
      </FormKit>
      <div class="flex items-center space-x-3">
        <ExportToExcel element="print-container" :filename="`malaria_report_from_${moment(startDate).format(
          'DD_MM_yyyy'
        )}_to_${moment(endDate).format(
          'DD_MM_yyyy'
        )}_for_${facility.details.name.toLowerCase().split(' ').join('_')}`">
          <CoreExportButton text="Export Excel" />
        </ExportToExcel>
      </div>
    </div>

    <div class="border rounded mt-10">
      <div class="rounded-tr rounded-tl px-5 py-5 flex items-center justify-between">
        <div class="flex flex-col space-y-2">
          <img src="@/assets/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
          <h3 class="text-xl font-semibold">MALARIA REPORT</h3>
        </div>
        <ReportsAddress />
      </div>

      <div class="border-t" id="print-container" v-if="wardData.length > 0 && !loading">
        <div class="mt-3">
          <h4 class="font-medium mb-2 px-5">
            Tests for Period:
            <span class="text-normal font-normal">
              {{
                dateRange[0].toString() != ""
                  ? moment(dateRange[0].toString()).format("DD/MM/YYYY")
                  : ""
              }}
              -
              {{
                dateRange[1].toString() != ""
                  ? moment(dateRange[1].toString()).format("DD/MM/YYYY")
                  : ""
              }}
            </span>
          </h4>
        </div>

        <table class="w-full overflow-x-auto">
          <thead class="w-full bg-gray-50 border">
            <tr>
              <th colspan="2" class="border-r py-2 px-4"></th>
              <th colspan="2" class="border-r py-2 px-4 border-b">MRDT</th>
              <th colspan="2" class="border-b">MICROSCOPY</th>
            </tr>
            <tr>
              <th class="py-2 px-4">WARDS</th>
              <th class="border-r py-2 px-4">RESULTS</th>
              <th class="border-r py-2 px-4">Over 5 years</th>
              <th class="border-r py-2 px-4">Under 5 years</th>
              <th class="border-r py-2 px-4">Over 5 years</th>
              <th>Under 5 years</th>
            </tr>
          </thead>
          <tbody class="border">
            <template v-for="(report, index) in wardData" :key="'ward-' + index + '-pos'">
              <tr>
                <td rowspan="3" class="py-2 border-r border-b bg-gray-50 font-medium">
                  {{ report.ward }}
                </td>
                <td class="border-b bg-gray-50 border-dotted border-r px-2">Positive</td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Positive Over 5 years old', report.ward, report.mrdt_pos_over5?.count || 0, report.mrdt_pos_over5?.associated_ids || '')">
                  {{ report.mrdt_pos_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Positive Under 5 years old', report.ward, report.mrdt_pos_under5?.count || 0, report.mrdt_pos_under5?.associated_ids || '')">
                  {{ report.mrdt_pos_under5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Positive Over 5 years old', report.ward, report.micro_pos_over5?.count || 0, report.micro_pos_over5?.associated_ids || '')">
                  {{ report.micro_pos_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Positive Under 5 years old', report.ward, report.micro_pos_under5?.count || 0, report.micro_pos_under5?.associated_ids || '')">
                  {{ report.micro_pos_under5?.count || 0 }}
                </td>
              </tr>
              <tr>
                <td class="border-b bg-gray-50 border-dotted border-r px-2">Negative</td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Negative Over 5 years old', report.ward, report.mrdt_neg_over5?.count || 0, report.mrdt_neg_over5?.associated_ids || '')">
                  {{ report.mrdt_neg_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Negative Under 5 years old', report.ward, report.mrdt_neg_under5?.count || 0, report.mrdt_neg_under5?.associated_ids || '')">
                  {{ report.mrdt_neg_under5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Negative Over 5 years old', report.ward, report.micro_neg_over5?.count || 0, report.micro_neg_over5?.associated_ids || '')">
                  {{ report.micro_neg_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Negative Under 5 years old', report.ward, report.micro_neg_under5?.count || 0, report.micro_neg_under5?.associated_ids || '')">
                  {{ report.micro_neg_under5?.count || 0 }}
                </td>
              </tr>
              <tr>
                <td class="border-b bg-gray-50 border-dotted border-r px-2">Invalid</td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Invalid Over 5 years old', report.ward, report.mrdt_inv_over5?.count || 0, report.mrdt_inv_over5?.associated_ids || '')">
                  {{ report.mrdt_inv_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Invalid Under 5 years old', report.ward, report.mrdt_inv_under5?.count || 0, report.mrdt_inv_under5?.associated_ids || '')">
                  {{ report.mrdt_inv_under5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Invalid Over 5 years old', report.ward, report.micro_inv_over5?.count || 0, report.micro_inv_over5?.associated_ids || '')">
                  {{ report.micro_inv_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Invalid Under 5 years old', report.ward, report.micro_inv_under5?.count || 0, report.micro_inv_under5?.associated_ids || '')">
                  {{ report.micro_inv_under5?.count || 0 }}
                </td>
              </tr>
            </template>

            <template v-for="(report, index) in genderData" :key="'gender-' + index">
              <tr>
                <td rowspan="3" class="py-2 border-r font-medium bg-gray-50 border-b">
                  {{ report.gender === 'M' ? 'MALE' : 'FEMALE' }}
                </td>
                <td class="border-b bg-gray-50 border-dotted border-r px-2">Positive</td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Positive Over 5 years old', report.ward, report.mrdt_pos_over5?.count || 0, report.mrdt_pos_over5?.associated_ids || '')">
                  {{ report.mrdt_pos_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Positive Under 5 years old', report.ward, report.mrdt_pos_under5?.count || 0, report.mrdt_pos_under5?.associated_ids || '')">
                  {{ report.mrdt_pos_under5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Positive Over 5 years old', report.ward, report.micro_pos_over5?.count || 0, report.micro_pos_over5?.associated_ids || '')">
                  {{ report.micro_pos_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Positive Under 5 years old', report.ward, report.micro_pos_under5?.count || 0, report.micro_pos_under5?.associated_ids || '')">
                  {{ report.micro_pos_under5?.count || 0 }}
                </td>
              </tr>
              <tr>
                <td class="border-b bg-gray-50 border-dotted border-r px-2">Negative</td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Negative Over 5 years old', report.ward, report.mrdt_neg_over5?.count || 0, report.mrdt_neg_over5?.associated_ids || '')">
                  {{ report.mrdt_neg_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Negative Under 5 years old', report.ward, report.mrdt_neg_under5?.count || 0, report.mrdt_neg_under5?.associated_ids || '')">
                  {{ report.mrdt_neg_under5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Negative Over 5 years old', report.ward, report.micro_neg_over5?.count || 0, report.micro_neg_over5?.associated_ids || '')">
                  {{ report.micro_neg_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Negative Under 5 years old', report.ward, report.micro_neg_under5?.count || 0, report.micro_neg_under5?.associated_ids || '')">
                  {{ report.micro_neg_under5?.count || 0 }}
                </td>
              </tr>
              <tr>
                <td class="border-b bg-gray-50 border-dotted border-r px-2">Invalid</td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Invalid Over 5 years old', report.ward, report.mrdt_inv_over5?.count || 0, report.mrdt_inv_over5?.associated_ids || '')">
                  {{ report.mrdt_inv_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Invalid Under 5 years old', report.ward, report.mrdt_inv_under5?.count || 0, report.mrdt_inv_under5?.associated_ids || '')">
                  {{ report.mrdt_inv_under5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Invalid Over 5 years old', report.ward, report.micro_inv_over5?.count || 0, report.micro_inv_over5?.associated_ids || '')">
                  {{ report.micro_inv_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Invalid Under 5 years old', report.ward, report.micro_inv_under5?.count || 0, report.micro_inv_under5?.associated_ids || '')">
                  {{ report.micro_inv_under5?.count || 0 }}
                </td>
              </tr>
            </template>


            <template v-for="(report, index) in encounterTypeData" :key="'encounter-' + index">
              <tr>
                <td rowspan="3" class="py-2 border-r font-medium bg-gray-50 border-b">
                  {{ report.encounter_type }}
                </td>
                <td class="border-b bg-gray-50 border-dotted border-r px-2">Positive</td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Positive Over 5 years old', report.ward, report.mrdt_pos_over5?.count || 0, report.mrdt_pos_over5?.associated_ids || '')">
                  {{ report.mrdt_pos_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Positive Under 5 years old', report.ward, report.mrdt_pos_under5?.count || 0, report.mrdt_pos_under5?.associated_ids || '')">
                  {{ report.mrdt_pos_under5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Positive Over 5 years old', report.ward, report.micro_pos_over5?.count || 0, report.micro_pos_over5?.associated_ids || '')">
                  {{ report.micro_pos_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Positive Under 5 years old', report.ward, report.micro_pos_under5?.count || 0, report.micro_pos_under5?.associated_ids || '')">
                  {{ report.micro_pos_under5?.count || 0 }}
                </td>
              </tr>
              <tr>
                <td class="border-b bg-gray-50 border-dotted border-r px-2">Negative</td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Negative Over 5 years old', report.ward, report.mrdt_neg_over5?.count || 0, report.mrdt_neg_over5?.associated_ids || '')">
                  {{ report.mrdt_neg_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Negative Under 5 years old', report.ward, report.mrdt_neg_under5?.count || 0, report.mrdt_neg_under5?.associated_ids || '')">
                  {{ report.mrdt_neg_under5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Negative Over 5 years old', report.ward, report.micro_neg_over5?.count || 0, report.micro_neg_over5?.associated_ids || '')">
                  {{ report.micro_neg_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Negative Under 5 years old', report.ward, report.micro_neg_under5?.count || 0, report.micro_neg_under5?.associated_ids || '')">
                  {{ report.micro_neg_under5?.count || 0 }}
                </td>
              </tr>
              <tr>
                <td class="border-b bg-gray-50 border-dotted border-r px-2">Invalid</td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Invalid Over 5 years old', report.ward, report.mrdt_inv_over5?.count || 0, report.mrdt_inv_over5?.associated_ids || '')">
                  {{ report.mrdt_inv_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Invalid Under 5 years old', report.ward, report.mrdt_inv_under5?.count || 0, report.mrdt_inv_under5?.associated_ids || '')">
                  {{ report.mrdt_inv_under5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Invalid Over 5 years old', report.ward, report.micro_inv_over5?.count || 0, report.micro_inv_over5?.associated_ids || '')">
                  {{ report.micro_inv_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Invalid Under 5 years old', report.ward, report.micro_inv_under5?.count || 0, report.micro_inv_under5?.associated_ids || '')">
                  {{ report.micro_inv_under5?.count || 0 }}
                </td>
              </tr>
            </template>

            <template v-for="(report, index) in pegnantFemaleData" :key="'preg-' + index">
              <tr>
                <td rowspan="3" class="py-2 border-r uppercase font-medium px-2 bg-gray-50">
                  {{ report.indicator }}
                </td>
                <td class="border-b bg-gray-50 border-dotted border-r px-2">Positive</td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Positive Over 5 years old', report.ward, report.mrdt_pos_over5?.count || 0, report.mrdt_pos_over5?.associated_ids || '')">
                  {{ report.mrdt_pos_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Positive Under 5 years old', report.ward, report.mrdt_pos_under5?.count || 0, report.mrdt_pos_under5?.associated_ids || '')">
                  {{ report.mrdt_pos_under5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Positive Over 5 years old', report.ward, report.micro_pos_over5?.count || 0, report.micro_pos_over5?.associated_ids || '')">
                  {{ report.micro_pos_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Positive Under 5 years old', report.ward, report.micro_pos_under5?.count || 0, report.micro_pos_under5?.associated_ids || '')">
                  {{ report.micro_pos_under5?.count || 0 }}
                </td>
              </tr>
              <tr>
                <td class="border-b bg-gray-50 border-dotted border-r px-2">Negative</td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Negative Over 5 years old', report.ward, report.mrdt_neg_over5?.count || 0, report.mrdt_neg_over5?.associated_ids || '')">
                  {{ report.mrdt_neg_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Negative Under 5 years old', report.ward, report.mrdt_neg_under5?.count || 0, report.mrdt_neg_under5?.associated_ids || '')">
                  {{ report.mrdt_neg_under5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Negative Over 5 years old', report.ward, report.micro_neg_over5?.count || 0, report.micro_neg_over5?.associated_ids || '')">
                  {{ report.micro_neg_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Negative Under 5 years old', report.ward, report.micro_neg_under5?.count || 0, report.micro_neg_under5?.associated_ids || '')">
                  {{ report.micro_neg_under5?.count || 0 }}
                </td>
              </tr>
              <tr>
                <td class="border-b bg-gray-50 border-dotted border-r px-2">Invalid</td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Invalid Over 5 years old', report.ward, report.mrdt_inv_over5?.count || 0, report.mrdt_inv_over5?.associated_ids || '')">
                  {{ report.mrdt_inv_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('MRDT Invalid Under 5 years old', report.ward, report.mrdt_inv_under5?.count || 0, report.mrdt_inv_under5?.associated_ids || '')">
                  {{ report.mrdt_inv_under5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Invalid Over 5 years old', report.ward, report.micro_inv_over5?.count || 0, report.micro_inv_over5?.associated_ids || '')">
                  {{ report.micro_inv_over5?.count || 0 }}
                </td>
                <td class="px-2 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150 border-r border-b border-dotted"
                  @click="showEnumeration('Microscopy Invalid Under 5 years old', report.ward, report.micro_inv_under5?.count || 0, report.micro_inv_under5?.associated_ids || '')">
                  {{ report.micro_inv_under5?.count || 0 }}
                </td>
              </tr>
            </template>

          </tbody>
        </table>

        <div>
          <table>
            <thead>
              <tr></tr>
              <tr>
                <th class="text-xl font-semibold py-5 px-5">SUMMARY</th>
              </tr>
              <tr></tr>
            </thead>
          </table>
          <table class="w-full">
            
            <thead class="w-full bg-gray-50 border-t">
              <tr class="border-b">
                <th class="px-10 py-2 text-left uppercase border-r"></th>
                <th class="px-4 py-2 text-left uppercase border-r">
                  TOTAL TESTED
                </th>
                <th class="px-4 py-2 text-left uppercase border-r">
                  TOTAL POSITIVE
                </th>
                <th class="px-4 py-2 text-left uppercase border-r">
                  TOTAL NEGATIVE
                </th>
                <th class="px-4 py-2 text-left uppercase border-r">MALE</th>
                <th class="px-4 py-2 text-left uppercase border-r">FEMALE</th>
                <th class="px-4 py-2 text-left uppercase border-r">
                  FEMALE PREGNANT
                </th>
                <th class="px-4 py-2 text-left uppercase">IN PATENTS</th>
              </tr>
            </thead>
            <tbody>
              <tr class="border-b border-r">
                <td class="px-2 py-2 border-r border-l font-medium uppercase bg-gray-50">
                  Microscopy Over 5 years
                </td>
                <td @click="
                  showEnumeration(
                    `Total Tested Microscopy Over 5 years old`,
                    '',
                    summaryData?.total_tested.micro_over_5
                      ? summaryData?.total_tested.micro_over_5.count
                      : 0,
                    summaryData?.total_tested.micro_over_5
                      ? summaryData?.total_tested.micro_over_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_tested.micro_over_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total Microscopy Positive Over 5 years old`,
                    '',
                    summaryData?.total_positive.micro_over_5
                      ? summaryData?.total_positive.micro_over_5.count
                      : 0,
                    summaryData?.total_positive.micro_over_5
                      ? summaryData?.total_positive.micro_over_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_positive.micro_over_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total Microscopy Negative Over 5 years old`,
                    '',
                    summaryData?.total_negative.micro_over_5
                      ? summaryData?.total_negative.micro_over_5.count
                      : 0,
                    summaryData?.total_negative.micro_over_5
                      ? summaryData?.total_negative.micro_over_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_negative.micro_over_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total Tested Male Microscopy Over 5 years old`,
                    '',
                    summaryData?.total_male.micro_over_5
                      ? summaryData?.total_male.micro_over_5.count
                      : 0,
                    summaryData?.total_male.micro_over_5
                      ? summaryData?.total_male.micro_over_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_male.micro_over_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total Tested Female Microscopy Over 5 years old`,
                    '',
                    summaryData?.total_female.micro_over_5
                      ? summaryData?.total_female.micro_over_5.count
                      : 0,
                    summaryData?.total_female.micro_over_5
                      ? summaryData?.total_female.micro_over_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_female.micro_over_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total Tested Microscopy in Pregnant Females Over 5 years old`,
                    '',
                    summaryData?.total_female_preg.micro_over_5
                      ? summaryData?.total_female_preg.micro_over_5.count
                      : 0,
                    summaryData?.total_female_preg.micro_over_5
                      ? summaryData?.total_female_preg.micro_over_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_female_preg.micro_over_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total In Patient Microscopy Over 5 years old`,
                    '',
                    summaryData?.total_in_patient.micro_over_5
                      ? summaryData?.total_in_patient.micro_over_5.count
                      : 0,
                    summaryData?.total_in_patient.micro_over_5
                      ? summaryData?.total_in_patient.micro_over_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_in_patient.micro_over_5.count }}
                </td>
              </tr>
              <tr class="border-b border-r">
                <td class="px-2 py-2 border-r border-l font-medium uppercase bg-gray-50">
                  Microscopy Under 5 years
                </td>
                <td @click="
                  showEnumeration(
                    `Total In Patient Microscopy Under 5 years old`,
                    '',
                    summaryData?.total_tested.micro_under_5
                      ? summaryData?.total_tested.micro_under_5.count
                      : 0,
                    summaryData?.total_tested.micro_under_5
                      ? summaryData?.total_tested.micro_under_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_tested.micro_under_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total Microscopy Positive Under 5 years old`,
                    '',
                    summaryData?.total_positive.micro_under_5
                      ? summaryData?.total_positive.micro_under_5.count
                      : 0,
                    summaryData?.total_positive.micro_under_5
                      ? summaryData?.total_positive.micro_under_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_positive.micro_under_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total Negative Microscopy Under 5 years old`,
                    '',
                    summaryData?.total_negative.micro_under_5
                      ? summaryData?.total_negative.micro_under_5.count
                      : 0,
                    summaryData?.total_negative.micro_under_5
                      ? summaryData?.total_negative.micro_under_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_negative.micro_under_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total Microscopy in Males Under 5 years old`,
                    '',
                    summaryData?.total_male.micro_under_5
                      ? summaryData?.total_male.micro_under_5.count
                      : 0,
                    summaryData?.total_male.micro_under_5
                      ? summaryData?.total_male.micro_under_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_male.micro_under_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total Microscopy in Female Under 5 years old`,
                    '',
                    summaryData?.total_female.micro_under_5
                      ? summaryData?.total_female.micro_under_5.count
                      : 0,
                    summaryData?.total_female.micro_under_5
                      ? summaryData?.total_female.micro_under_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_female.micro_under_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total Microscopy in Pregnant Females Under 5 years old`,
                    '',
                    summaryData?.total_female_preg.micro_under_5
                      ? summaryData?.total_female_preg.micro_under_5.count
                      : 0,
                    summaryData?.total_female_preg.micro_under_5
                      ? summaryData?.total_female_preg.micro_under_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_female_preg.micro_under_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total In Patient Microscopy Under 5 years old`,
                    '',
                    summaryData?.total_in_patient.micro_under_5
                      ? summaryData?.total_in_patient.micro_under_5.count
                      : 0,
                    summaryData?.total_in_patient.micro_under_5
                      ? summaryData?.total_in_patient.micro_under_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_in_patient.micro_under_5.count }}
                </td>
              </tr>
              <tr class="border-b border-r">
                <td class="px-2 py-2 border-r border-l font-medium uppercase bg-gray-50">
                  MRDT Over 5 years
                </td>
                <td @click="
                  showEnumeration(
                    `Total Tested MRDT Over 5 years old`,
                    '',
                    summaryData?.total_tested.mrdt_over_5
                      ? summaryData?.total_tested.mrdt_over_5.count
                      : 0,
                    summaryData?.total_tested.mrdt_over_5
                      ? summaryData?.total_tested.mrdt_over_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_tested.mrdt_over_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total MRDT Positive Over 5 years old`,
                    '',
                    summaryData?.total_positive.mrdt_over_5
                      ? summaryData?.total_positive.mrdt_over_5.count
                      : 0,
                    summaryData?.total_positive.mrdt_over_5
                      ? summaryData?.total_positive.mrdt_over_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_positive.mrdt_over_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total MRDT Negative Over 5 years old`,
                    '',
                    summaryData?.total_negative.mrdt_over_5
                      ? summaryData?.total_negative.mrdt_over_5.count
                      : 0,
                    summaryData?.total_negative.mrdt_over_5
                      ? summaryData?.total_negative.mrdt_over_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_negative.mrdt_over_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total MRDT Males Over 5 years old`,
                    '',
                    summaryData?.total_male.mrdt_over_5
                      ? summaryData?.total_male.mrdt_over_5.count
                      : 0,
                    summaryData?.total_male.mrdt_over_5
                      ? summaryData?.total_male.mrdt_over_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_male.mrdt_over_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total MRDT Females Over 5 years old`,
                    '',
                    summaryData?.total_female.mrdt_over_5
                      ? summaryData?.total_female.mrdt_over_5.count
                      : 0,
                    summaryData?.total_female.mrdt_over_5
                      ? summaryData?.total_female.mrdt_over_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_female.mrdt_over_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total MRDT in Pregnant Females Over 5 years old`,
                    '',
                    summaryData?.total_female_preg.mrdt_over_5
                      ? summaryData?.total_female_preg.mrdt_over_5.count
                      : 0,
                    summaryData?.total_female_preg.mrdt_over_5
                      ? summaryData?.total_female_preg.mrdt_over_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_female_preg.mrdt_over_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total In Patient MRDT Over 5 years old`,
                    '',
                    summaryData?.total_in_patient.mrdt_over_5
                      ? summaryData?.total_in_patient.mrdt_over_5.count
                      : 0,
                    summaryData?.total_in_patient.mrdt_over_5
                      ? summaryData?.total_in_patient.mrdt_over_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_in_patient.mrdt_over_5.count }}
                </td>
              </tr>
              <tr class="border-b border-r">
                <td class="px-2 py-2 border-r border-l font-medium uppercase bg-gray-50">
                  MRDT Under 5 years
                </td>
                <td @click="
                  showEnumeration(
                    `Total Tested MRDT Under 5 years old`,
                    '',
                    summaryData?.total_tested.mrdt_under_5
                      ? summaryData?.total_tested.mrdt_under_5.count
                      : 0,
                    summaryData?.total_tested.mrdt_under_5
                      ? summaryData?.total_tested.mrdt_under_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_tested.mrdt_under_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total MRDT Positive Under 5 years old`,
                    '',
                    summaryData?.total_positive.mrdt_under_5
                      ? summaryData?.total_positive.mrdt_under_5.count
                      : 0,
                    summaryData?.total_positive.mrdt_under_5
                      ? summaryData?.total_positive.mrdt_under_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_positive.mrdt_under_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total MRDT Negative Under 5 years old`,
                    '',
                    summaryData?.total_negative.mrdt_under_5
                      ? summaryData?.total_negative.mrdt_under_5.count
                      : 0,
                    summaryData?.total_negative.mrdt_under_5
                      ? summaryData?.total_negative.mrdt_under_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_negative.mrdt_under_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total MRDT Males Under 5 years old`,
                    '',
                    summaryData?.total_male.mrdt_under_5
                      ? summaryData?.total_male.mrdt_under_5.count
                      : 0,
                    summaryData?.total_male.mrdt_under_5
                      ? summaryData?.total_male.mrdt_under_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_male.mrdt_under_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total MRDT Females Under 5 years old`,
                    '',
                    summaryData?.total_female.mrdt_under_5
                      ? summaryData?.total_female.mrdt_under_5.count
                      : 0,
                    summaryData?.total_female.mrdt_under_5
                      ? summaryData?.total_female.mrdt_under_5.associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_female.mrdt_under_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total MRDT In Pregnant Females Under 5 years old`,
                    '',
                    summaryData?.total_female_preg.mrdt_under_5
                      ? summaryData?.total_female_preg.mrdt_under_5.count
                      : 0,
                    summaryData?.total_female_preg.mrdt_under_5
                      ? summaryData?.total_female_preg.mrdt_under_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_female_preg.mrdt_under_5.count }}
                </td>
                <td @click="
                  showEnumeration(
                    `Total In Patient MRDT Under 5 years old`,
                    '',
                    summaryData?.total_in_patient.mrdt_under_5
                      ? summaryData?.total_in_patient.mrdt_under_5.count
                      : 0,
                    summaryData?.total_in_patient.mrdt_under_5
                      ? summaryData?.total_in_patient.mrdt_under_5
                        .associated_ids
                      : ''
                  )
                  "
                  class="px-2 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                  {{ summaryData?.total_in_patient.mrdt_under_5.count }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div v-if="wardData.length == 0 && !loading"
        class="w-full flex flex-col items-center justify-center space-y-2 py-10">
        <img src="@/assets/images/page.png" alt="page-icon" class="object-cover w-20 h-20" />
        <p class="text-base">Data not found, please generate report</p>
      </div>
      <ReportsLoader :condition="loading" :cancelReportGeneration="() => cancelRequest()" />
    </div>

  </div>
</template>

<script setup lang="ts">
import { endpoints } from "@/services/endpoints";
import type {
  Department,
  MReportSummary,
  Page,
  Request,
  Response,
} from "@/types";
import moment from "moment";
import {
  ArrowPathIcon as refreshIcon,
  FunnelIcon,
} from "@heroicons/vue/24/solid/index.js";
import { useFacilityStore } from "@/store/facility";
import { ExportToExcel } from "vue-doc-exporter";
import Package from "@/package.json";

definePageMeta({
  layout: "dashboard",
  middleware: ["reports"],
});
useHead({
  title: `${Package.name.toUpperCase()} - Malaria Report`,
});

const title = ref<string>("Malaria Report");
const facility = useFacilityStore();
const pages = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Reports",
    link: "#",
  },
  {
    name: "Aggregate Reports",
    link: "#",
  },
]);
const selectedDepartment = ref<Department>({ name: "select department" });
const cookie = useCookie("token");
const router = useRouter();
const route = useRoute();
const { $toast } = useNuxtApp();
const dateRange = ref<string[]>(new Array("", ""));
const { loading, executeCancellableRequest, cancelRequest } = useCancellableRequest();

const isCleared = (): void => {
  dateRange.value = new Array("", "");
};

const startDate = computed((): string => {
  return dateRange.value[0]
    ? moment(dateRange.value[0]).format("YYYY-MM-DD")
    : "";
});
const endDate = computed((): string => {
  return dateRange.value[1]
    ? moment(dateRange.value[1]).format("YYYY-MM-DD")
    : "";
});
const wardData = ref<any[]>([]);
const genderData = ref<any[]>([]);
const encounterTypeData = ref<any[]>([]);
const pegnantFemaleData = ref<any[]>([]);
const summaryData = ref<MReportSummary>({
  total_tested: {
    micro_over_5: { count: 0, associated_ids: "" },
    micro_under_5: { count: 0, associated_ids: "" },
    mrdt_over_5: { count: 0, associated_ids: "" },
    mrdt_under_5: { count: 0, associated_ids: "" },
  },
  total_positive: {
    micro_over_5: { count: 0, associated_ids: "" },
    micro_under_5: { count: 0, associated_ids: "" },
    mrdt_over_5: { count: 0, associated_ids: "" },
    mrdt_under_5: { count: 0, associated_ids: "" },
  },
  total_negative: {
    micro_over_5: { count: 0, associated_ids: "" },
    micro_under_5: { count: 0, associated_ids: "" },
    mrdt_over_5: { count: 0, associated_ids: "" },
    mrdt_under_5: { count: 0, associated_ids: "" },
  },
  total_male: {
    micro_over_5: { count: 0, associated_ids: "" },
    micro_under_5: { count: 0, associated_ids: "" },
    mrdt_over_5: { count: 0, associated_ids: "" },
    mrdt_under_5: { count: 0, associated_ids: "" },
  },
  total_female: {
    micro_over_5: { count: 0, associated_ids: "" },
    micro_under_5: { count: 0, associated_ids: "" },
    mrdt_over_5: { count: 0, associated_ids: "" },
    mrdt_under_5: { count: 0, associated_ids: "" },
  },
  total_in_patient: {
    micro_over_5: { count: 0, associated_ids: "" },
    micro_under_5: { count: 0, associated_ids: "" },
    mrdt_over_5: { count: 0, associated_ids: "" },
    mrdt_under_5: { count: 0, associated_ids: "" },
  },
  total_out_patient: {
    micro_over_5: { count: 0, associated_ids: "" },
    micro_under_5: { count: 0, associated_ids: "" },
    mrdt_over_5: { count: 0, associated_ids: "" },
    mrdt_under_5: { count: 0, associated_ids: "" },
  },
  total_referal: {
    micro_over_5: { count: 0, associated_ids: "" },
    micro_under_5: { count: 0, associated_ids: "" },
    mrdt_over_5: { count: 0, associated_ids: "" },
    mrdt_under_5: { count: 0, associated_ids: "" },
  },
  total_female_preg: {
    micro_over_5: { count: 0, associated_ids: "" },
    micro_under_5: { count: 0, associated_ids: "" },
    mrdt_over_5: { count: 0, associated_ids: "" },
    mrdt_under_5: { count: 0, associated_ids: "" },
  },
});

const checkStatus = (status: string): String => {
  return status == SELECT_DEPARTMENT ? "" : status;
};

const showEnumeration = (
  test: any,
  ward: string,
  count: number,
  associated_ids: string
): void => {
  if (count !== 0 && associated_ids !== "") {
    router.push(
      `/reports/${associated_ids}?origin=aggregate&type=department-report&from=${startDate.value}&to=${endDate.value}&test=${test} - ${ward}&department=${selectedDepartment.value.name}&count=${count}`
    );
  } else {
    $toast.warning("No data found for this month");
  }
};

async function generateReport(): Promise<void> {
  loading.value = true;
  let startDate =
    dateRange.value[0].toString() != ""
      ? moment(dateRange.value[0].toString()).format("YYYY-MM-DD")
      : "";
  let endDate =
    dateRange.value[1].toString() != ""
      ? moment(dateRange.value[1].toString()).format("YYYY-MM-DD")
      : "";
  let queryParams = `from=${startDate}&to=${endDate}&department=${checkStatus(
    selectedDepartment.value.name
  )}&report_id=${route.query.report_id}`;
  const request: Request = {
    route: `${endpoints.aggregateReports}/malaria_report?${queryParams}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error, pending }: Response = await executeCancellableRequest(request);
  loading.value = pending;
  if (data.value) {
    loading.value = false;
    wardData.value = data.value.data.by_ward;
    genderData.value = data.value.data.by_gender;
    encounterTypeData.value = data.value.data.by_encounter_type;
    pegnantFemaleData.value = data.value.data.by_female_preg;
    summaryData.value = data.value.summary;
    updateReportId(data.value.report_id);
    data.value.data.by_ward.length > 0
      ? useNuxtApp().$toast.success("Report data generated successfully!")
      : useNuxtApp().$toast.warning(
        `No data found in period ${startDate} - ${endDate}`
      );
  }
  if (error.value) {
    loading.value = false;
    console.error(error.value);
    useNuxtApp().$toast.error(error.value);
  }
}

const updateReportId = (id: string): void => {
  const currentRoute = router.currentRoute.value;
  const newQuery = {
    ...currentRoute.query,
    report_id: id,
  };
  router.replace({ query: newQuery }).catch((err: any) => {
    console.error("Failed to replace route:", err);
  });
};

watch(
  dateRange,
  () => {
    const startDate = dateRange.value[0]
      ? moment(dateRange.value[0]).format("YYYY-MM-DD")
      : "";
    const endDate = dateRange.value[1]
      ? moment(dateRange.value[1]).format("YYYY-MM-DD")
      : "";
    if (startDate || endDate) {
      const currentRoute = router.currentRoute.value;
      const newQuery = {
        ...currentRoute.query,
        ...(startDate && { from: startDate }),
        ...(endDate && { to: endDate }),
      };
      router.replace({ query: newQuery }).catch((err: any) => {
        console.error("Failed to replace route:", err);
      });
    }
  },
  { deep: true }
);

const validFields = computed((): boolean => {
  if (route.query.to !== undefined && route.query.from !== undefined) {
    dateRange.value = [String(route.query.from), String(route.query.to)];
    return route.query.to !== "" && route.query.from !== "";
  }
  return false;
});

onMounted(() => {
  if (validFields.value) {
    generateReport();
  } else {
    loading.value = false;
  }
});
</script>

<style></style>
