<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center py-5">
      <img src="@/assets/icons/report.png" alt="report-icon" class="w-8 h-8 mr-2" />
      <h3 class="text-2xl font-semibold uppercase">{{ title }}</h3>
    </div>

    <div>
      <div class="font-medium bg-gray-100">
        <button v-for="(tab, index) in tabs" :key="index" @click="onTabClick(index)" :class="activeTab == index
          ? 'inline-block px-2 py-2 text-white bg-sky-500'
          : 'inline-block px-2 py-2 border-r hover:text-sky-500 transition duration-150'
          ">
          {{ tab }}
        </button>
      </div>
      <div class="mt-4">
        <component :is="components[activeTab]" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Page } from "@/types";
import Package from "@/package.json";
import GeneralCounts from "@/components/culture-sensitivity/general-counts.vue";
import WardBasedCounts from "@/components/culture-sensitivity/wards-counts.vue";
import OrganismsBasedCounts from "@/components/culture-sensitivity/organisms-counts.vue";
import OrganismsInWardsCounts from "@/components/culture-sensitivity/organisms-wards-counts.vue";
import AntibioticSusceptibilityTest from "@/components/culture-sensitivity/ast.vue";
import { useCultureRoutesStore } from "@/store/culture";

definePageMeta({
  layout: "dashboard",
  middleware: ["reports"],
});
useHead({
  title: `${Package.name.toUpperCase()} - Culture & Sensitivity Report`,
});

const cultureStore = useCultureRoutesStore();
const router = useRouter();
const route = useRoute();
const title = ref<string>("Culture & Sensitivity Report");
const tabs = ref<string[]>(
  new Array(
    "General Counts",
    "Ward Based Counts",
    "Organisms Based Count",
    "Organisms in Wards Count",
    "Antibiotic Susceptibility Test (AST)"
  )
);

const components = [
  GeneralCounts,
  WardBasedCounts,
  OrganismsBasedCounts,
  OrganismsInWardsCounts,
  AntibioticSusceptibilityTest
]
const activeTab = ref<number>(Number(route.query["current-tab"]) || 0);
const routerQuery = useRouterQuery();
const pages = ref<Page>(
  new Array(
    {
      name: "Home",
      link: "/home",
    },
    {
      name: "Reports",
      link: "#",
    },
    {
      name: "Aggregate Reports",
      link: "#",
    }
  )
);

const updateRouteWithTabAndReport = (tabIndex: number) => {
  const reportId = cultureStore.routes.find((route) => route.index === tabIndex)?.reportId || '';
  if (reportId !== "" || reportId !== undefined) {
    routerQuery.replaceOneQuery("current-tab", { "current-tab": tabIndex });
  }
};

onMounted(() => {
  const currentTab = Number(router.currentRoute.value.query['current-tab']) || 0;
  activeTab.value = currentTab;
  updateRouteWithTabAndReport(currentTab);
});

const onTabClick = (index: number): void => {
  activeTab.value = index;
  updateRouteWithTabAndReport(index);
};
</script>
