<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center py-5">
      <img src="@/assets/icons/report.png" alt="report-icon" class="w-8 h-8 mr-2" />
      <h3 class="text-2xl font-semibold uppercase">{{ title }}</h3>
    </div>

    <div class="w-full flex items-center justify-between mb-3">
      <FormKit type="form" submit-label="Update" @submit="validator() && generateReport()" :actions="false"
        #default="{ value }" id="submitForm">
        <div class="flex items-center space-x-3">
          <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
            <FunnelIcon class="w-5 h-5 mr-2" />
            Filter By Date Range
            <div class="w-72 ml-2">
              <datepicker @cleared="isCleared" format="dd/MM/yyyy" required position="left"
                placeholder="select start & end date" :range="true" input-class-name="datepicker" v-model="dateRange" :maxDate="new Date()"/>
            </div>
          </div>
          <div class="w-44">
            <CoreDropdown :items="departments" v-model="selectedDepartment" />
          </div>
          <CoreActionButton type="submit" color="primary" text="Generate Report" :icon="refreshIcon" :click="() => { }"
            :loading="loading" />
        </div>
      </FormKit>
      <div class="flex items-center space-x-3">
        <CorePrinterReport />
        <div>
          <excel class="btn btn-default" :header="[
            `LABORATORY STATISTICS REPORT `,
            `PERIOD FROM ${moment(startDate).format(
              'DD-MM-yyyy'
            )} TO ${moment(endDate).format('DD-MM-yyyy')}`,
            facility.details.name,
            facility.details.address,
            facility.details.phone,
          ]" :data="exportData" worksheet="report-work-sheet" :name="`lab-statistics-report_${moment(startDate).format(
              'DD_MM_yyyy'
            )}_to_${moment(endDate).format('DD_MM_yyyy')}.xls`">
            <CoreExportButton text="Export Excel" />
          </excel>
        </div>
      </div>
    </div>

    <div class="border rounded print-container" id="print-container">
      <div class="rounded-tr rounded-tl border-b px-5 py-5 flex items-center justify-between">
        <div class="flex flex-col space-y-2">
          <img src="@/assets/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
          <h3 class="text-xl font-semibold">LABORATORY STATISTICS REPORT</h3>
        </div>
        <ReportsAddress />
      </div>

      <div class="mt-3 px-5">
        <h3 class="font-medium mb-2">
          Tests Performed Period:
          <span class="font-normal">
            {{ startDate != "" ? moment(startDate).format(DATE_FORMAT) : "" }} -
            {{ endDate != "" ? moment(endDate).format(DATE_FORMAT) : "" }}
          </span>
        </h3>
      </div>
      <div class="overflow-x-auto bg-white border-t">
        <table class="w-full overflow-x-auto" v-if="filteredData.length > 0 && !loading">
          <thead class="uppercase bg-gray-100">
            <tr class="text-left">
              <th class="px-4 py-2">Tests</th>
              <th v-for="month in months" :key="month" class="px-4 py-2">
                {{ month }}
              </th>
              <th class="px-4 py-2">Total</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="department in filteredData" :key="department.department">
              <tr style="width: 100% !important" class="w-full bg-sky-50 border-t border-b border-dotted">
                <td class="px-4 py-2 font-bold">{{ department.department }}</td>
                <td v-for="month in months" :key="month" class="px-4 py-2"></td>
                <td class="px-4 py-2"></td>
              </tr>
              <template v-for="(test, index) in department.tests" :key="test.name">
                <tr :class="{
                  'bg-gray-50': shouldApplyGrayBackground(index),
                  'border-b border-dotted': true,
                }">
                  <td class="px-4 py-2">{{ test.name }}</td>
                  <td @click="
                    showEnumeration(
                      test,
                      month,
                      getTestResultByMonth(test, month)
                    )
                    " v-for="month in months" :key="month"
                    class="px-4 py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                    {{ getTestResultByMonth(test, month) }}
                  </td>
                  <td class="px-4 py-2">{{ getTotalTestResults(test) }}</td>
                </tr>
              </template>
            </template>
          </tbody>
        </table>
        <ReportsLoader :condition="loading" :cancelReportGeneration="() => cancelRequest()" />
        <div v-if="reportData.length == 0 && !loading"
          class="w-full flex flex-col items-center justify-center space-y-2 py-10">
          <img src="@/assets/images/page.png" alt="page-icon" class="object-cover w-20 h-20" />
          <p>Data not found, please generate report</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowPathIcon as refreshIcon,
  FunnelIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import { endpoints } from "@/services/endpoints";
import { useFacilityStore } from "@/store/facility";
import type { Department, Page, Request, Response } from "@/types";
import Package from "@/package.json";

definePageMeta({
  layout: "dashboard",
  middleware: ["reports"],
});
useHead({
  title: `${Package.name.toUpperCase()} - Lab Statistics Report`,
});

const title: string = "Lab Statistics Report";
const facility = useFacilityStore();
const cookie = useCookie("token");
const pages: Page = [
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Reports",
    link: "#",
  },
  {
    name: "Aggregate Reports",
    link: "#",
  },
];
const departments: Ref<Array<Department>> = ref<Array<Department>>([]);
const selectedDepartment: Ref<Record<string, string>> = ref<
  Record<string, string>
>({ name: "select department" });
const dateRange = ref<Array<any>>(["", ""]);
const reportData = ref<Array<any>>([]);
const months = ref<Array<any>>([]);
const filteredData = ref<Array<any>>([]);
const exportData = ref<Array<any>>([]);
const router = useRouter();
const route = useRoute();
const { $toast, $metadata } = useNuxtApp();
const { loading, executeCancellableRequest, cancelRequest } = useCancellableRequest();
const isCleared = (): void => {
  dateRange.value = new Array("", "");
};

function checkStatus(status: string): string {
  if (
    status === "select department" ||
    status === "-- select test type --" ||
    status === "-- select test status --"
  ) {
    return "";
  }
  return status;
}
function generateMonths(from: string, to: string): void {
  const startDate = new Date(from);
  const endDate = new Date(to);
  const mts = new Array();
  let currentDate = startDate;
  while (currentDate <= endDate) {
    const month = currentDate.toLocaleString("default", { month: "long" });
    mts.push(month);
    currentDate.setMonth(currentDate.getMonth() + 1);
  }
  months.value = mts;
}

const validator = (): boolean => {
  const { value } = selectedDepartment;
  if (value.name === "select department")
    return !useNuxtApp().$toast.warning("Please select a department");
  return true;
};

const startDate = computed(() => {
  return dateRange.value[0]
    ? moment(dateRange.value[0]).format("YYYY-MM-DD")
    : "";
});
const endDate = computed(() => {
  return dateRange.value[1]
    ? moment(dateRange.value[1]).format("YYYY-MM-DD")
    : "";
});

async function generateReport(): Promise<void> {
  loading.value = true;
  let queryParams = `from=${startDate.value}&to=${endDate.value
    }&department=${checkStatus(selectedDepartment.value.name)}&report_id=${route.query.report_id}`;
  const request: Request = {
    route: `${endpoints.aggregateReports}/lab_statistics?${queryParams}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error, pending }: Response = await executeCancellableRequest(request);
  loading.value = pending;
  if (data.value) {
    generateMonths(data.value.from, data.value.to);
    reportData.value = data.value.data;
    filteredData.value = filterData(data.value.data);
    let expData: {
      [key: string]: number;
    }[] = [];
    filteredData.value.forEach(
      (department: { department: string; tests: any[] }) => {
        const departmentObj: {
          [key: string]: any;
        } = {};
        departmentObj["Tests".toUpperCase()] =
          department.department.toUpperCase();
        months.value.forEach((month: string) => {
          departmentObj[month.toUpperCase()] = "";
        });
        departmentObj["Total".toUpperCase()] = "";
        expData.push(departmentObj);
        department.tests.forEach((test: any) => {
          const testObj: {
            [key: string]: number;
          } = {};
          testObj["Tests".toUpperCase()] = test.name;
          months.value.forEach((month: string) => {
            testObj[month.toUpperCase()] = getTestResultByMonth(test, month);
          });
          testObj["Total".toUpperCase()] = getTotalTestResults(test);
          expData.push(testObj);
        });
      }
    );
    exportData.value = expData;
    loading.value = false;
    updateReportId(data.value.report_id);
    data.value.data.length > 0
      ? useNuxtApp().$toast.success("Report data generated successfully")
      : useNuxtApp().$toast.warning(
        `No data found for period ${startDate.value} - ${endDate.value}`
      );
  }
  if (error.value) {
    loading.value = false;
    console.error("error: ", error.value);
    useNuxtApp().$toast.error(ERROR_MESSAGE);
  }
}

const updateReportId = (reportId: string) => {
  const currentRoute = router.currentRoute.value;
  const newQuery = {
    ...currentRoute.query,
    report_id: reportId,
  };
  router.replace({ query: newQuery }).catch((err: any) => {
    console.error("Failed to replace route:", err);
  });
};

function filterData(reportData: Array<any>): Array<any> {
  return reportData.map((item: any) => {
    const department = Object.keys(item)[0];
    const tests: any = Object.entries(item[department]).map(
      ([testName, results]) => {
        const testResults: any = Object.entries(results as any).map(
          ([month, count]) => ({
            [month]: count,
          })
        );
        return { name: testName, results: testResults };
      }
    );
    return { department, tests };
  });
}

function getTestResultByMonth(test: any, month: string): number {
  let result = 0;
  test.results.forEach((testResult: any) => {
    let value = testResult[month] !== undefined ? testResult[month].total : 0;
    result += value;
  });
  return result;
}

function getTotalTestResults(test: any): number {
  let total = 0;
  test.results.forEach((testResult: any) => {
    Object.values(testResult).forEach((item: any) => {
      total += item.total;
    });
  });
  return total;
}

const showEnumeration = (test: any, month: string, count: number): void => {
  const testResult = test.results.find(
    (testResult: any) => testResult[month] !== undefined
  );
  if (testResult) {
    router.push(
      `/reports/${testResult[month].associated_ids}?origin=aggregate&type=lab-statistics-report&from=${startDate.value}&to=${endDate.value}&test=${test.name}&department=${selectedDepartment.value.name}&count=${count}&page=1&rowsPerPage=10`
    );
  } else {
    $toast.warning("No data found for this month");
  }
};

function shouldApplyGrayBackground(index: number): Boolean {
  return index % 2 === 0;
}

watch(
  dateRange,
  () => {
    const startDate = dateRange.value[0]
      ? moment(dateRange.value[0]).format("YYYY-MM-DD")
      : "";
    const endDate = dateRange.value[1]
      ? moment(dateRange.value[1]).format("YYYY-MM-DD")
      : "";
    if (startDate || endDate) {
      const currentRoute = router.currentRoute.value;
      const newQuery = {
        ...currentRoute.query,
        ...(startDate && { from: startDate }),
        ...(endDate && { to: endDate }),
      };
      router.replace({ query: newQuery }).catch((err: any) => {
        console.error("Failed to replace route:", err);
      });
    }
  },
  { deep: true }
);

watch(selectedDepartment, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    const currentRoute = router.currentRoute.value;
    const newQuery = {
      ...currentRoute.query,
      department: newValue.name || undefined,
      report_id: ''
    };
    router.replace({ query: newQuery }).catch((err) => {
      console.error("Failed to replace route:", err);
    });
  }
});

const validFields = computed(() => {
  if (
    route.query.to !== undefined &&
    route.query.from !== undefined &&
    route.query.department !== undefined
  ) {
    dateRange.value = [route.query.from, route.query.to];
    selectedDepartment.value.name = `${route.query.department}`;
    return (
      route.query.to !== "" &&
      route.query.from !== "" &&
      route.query.department !== ""
    );
  }
  return false;
});

onMounted(() => {
  if (validFields.value) {
    generateReport();
  } else {
    loading.value = false;
  }
  departments.value = $metadata.departments;
  const allDepartmentExists = departments.value.some(
    (department: Department) => department.name == "All"
  );
  if (!allDepartmentExists) {
    departments.value.push({ id: 0, name: "All" });
  }
  departments.value.sort((a: Department, b: Department) => {
    const nameA = a.name.toUpperCase();
    const nameB = b.name.toUpperCase();
    if (nameA < nameB) {
      return -1;
    }
    if (nameA > nameB) {
      return 1;
    }
    return 0;
  });
});
</script>

<style></style>
