<template>
    <div class="px-5 py-5">

        <CoreBreadcrumb :pages="pages" />

        <div class="flex items-center py-5">
            <img src="@/assets/icons/report.png" alt="report-icon" class="w-8 h-8 mr-2" />
            <h3 class="text-2xl font-semibold uppercase">
                {{ title }}
            </h3>
        </div>

        <div class="w-full flex items-center justify-between mb-5">

            <FormKit type="form" submit-label="Update" @submit="generateReport" :actions="false" #default="{ value }"
                id="submitForm">
                <div class="w-full flex items-center space-x-3">
                    <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
                        <FunnelIcon class="w-5 h-5 mr-2" />
                        Filter By Date Range
                        <div class="w-72 ml-2">
                            <datepicker required position="left" placeholder="select start & end date" :range="true"
                                input-class-name="datepicker" v-model="dateRange" format="dd/MM/yyyy" :maxDate="new Date()"/>
                        </div>
                    </div>
                    <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
                        <Bars3CenterLeftIcon class="w-5 h-5 mr-2 my-2.5" />
                        Report Type
                        <div class="w-48 ml-2">
                            <CoreDropdown class="bg-white" :items="reportTypes" v-model="reportTypeSelected" />
                        </div>
                    </div>

                    <div class="flex justify-end py-2">
                        <div class="bg-gray-100 rounded flex items-center text-zinc-500">
                            <div class="w-48">
                                <CoreDropdown class="bg-white rounded" :isSearchable="true" :items="filteredUsers"
                                    v-model="selectedUser" @update="update" />
                            </div>
                            <button type="button" @click="selectedUser = { name: 'Select and search for user...' }"
                                v-if="selectedUser.name != null && selectedUser.name !== 'Select and search for user...'">
                                <XMarkIcon class="w-5 h-5 mx-2 my-2.5" />
                            </button>
                        </div>
                    </div>
                    <div class="w-48">
                        <CoreActionButton type="submit" color="primary" text="Generate Report" :icon="refreshIcon"
                            :click="(() => { })" :loading="loading" />
                    </div>
                </div>
            </FormKit>

            <excel class="btn btn-default"
                :header="[`USER STATISTICS ${reportTypeSelected.name.toUpperCase()} REPORT`, `PERIOD FROM ${moment(startDate).format(DATE_FORMAT)} TO ${moment(endDate).format(DATE_FORMAT)}`, facility.details.name, facility.details.address, facility.details.phone]"
                :data="exportData" worksheet="report-work-sheet"
                :name="`user_statistics_report_${reportTypeSelected.name.toLowerCase().split(' ').join('_')}_${moment(startDate).format('DD_MM_yyyy')}_to_${moment(endDate).format('DD_MM_yyyy')}.xls`">
                <CoreExportButton text="Export Excel" />
            </excel>
        </div>

        <div class="border rounded print-container mt-5">
            <div class="w-full rounded-tr rounded-tl border-b px-5 py-5 flex items-center justify-between">
                <div class="flex flex-col space-y-2">
                    <img src="@/assets/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
                    <h3 class="text-xl font-semibold">USER STATISTICS REPORT</h3>
                </div>
                <ReportsAddress />
            </div>

            <div class="mt-3 px-5">
                <h3 class="font-medium mb-2">
                    Tests Performed Period:
                    <span class="font-normal">
                        {{
                            dateRange.length > 0 && dateRange[0].toString() != ""
                                ? moment(dateRange[0].toString()).format(DATE_FORMAT)
                        : ""
                        }}
                        -
                        {{
                            dateRange.length > 0 && dateRange[1].toString() != ""
                                ? moment(dateRange[1].toString()).format(DATE_FORMAT)
                        : ""
                        }}
                    </span>
                </h3>
            </div>

            <div id="print-container" v-if="statistics.length > 0">
                <CoreDatatable :headers="headers" :data="filteredStatistics" :serverItemsLength="serverItemsLength"
                    :serverOptions="serverOptions" :loading="loading" :search-field="dataSeachValue"
                    :search-value="search" @update="updateReport">
                    <template v-slot:actions="{ item }">
                        <td>
                            {{ reportTypeSelected.name.toLowerCase() == 'patients registry' ? `${item.first_name}
                            ${item.middle_name} ${item.last_name}` : `${item.client.first_name}
                            ${item.client.middle_name}
                            ${item.client.last_name}`
                            }}
                        </td>
                    </template>
                    <template v-slot:item-tests_received="{ item }">
                        <td class="hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                            @click="showEnumeration('Tests Received', selectedUser.name, item.tests_received.count, item.tests_received.associated_ids)">
                            {{ item.tests_received.count }}
                        </td>
                    </template>
                    <template v-slot:item-specimen_collected="{ item }">
                        <td class="hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                            @click="showEnumeration('Tests Received', selectedUser.name, item.specimen_collected.count, item.specimen_collected.associated_ids)">
                            {{ item.specimen_collected.count }}
                        </td>
                    </template>
                    <template v-slot:item-specimen_rejected="{ item }">
                        <td class="hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                            @click="showEnumeration('Tests Received', selectedUser.name, item.specimen_rejected.count, item.specimen_rejected.associated_ids)">
                            {{ item.specimen_rejected.count }}
                        </td>
                    </template>
                    <template v-slot:item-tests_completed="{ item }">
                        <td class="hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                            @click="showEnumeration('Tests Received', selectedUser.name, item.tests_completed.count, item.tests_completed.associated_ids)">
                            {{ item.tests_completed.count }}
                        </td>
                    </template>
                    <template v-slot:item-tests_authorized="{ item }">
                        <td class="hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                            @click="showEnumeration('Tests Received', selectedUser.name, item.tests_authorized.count, item.tests_authorized.associated_ids)">
                            {{ item.tests_received.count }}
                        </td>
                    </template>
                </CoreDatatable>
            </div>
            <ReportsLoader :condition="loading" :cancelReportGeneration="() => cancelRequest()" />
            <div v-if="statistics.length == 0 && !loading"
                class="w-full flex flex-col items-center justify-center space-y-2 py-10">
                <img src="@/assets/images/page.png" alt="page-icon" class="object-cover w-20 h-20" />
                <p>Data not found, please generate report</p>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>

import { ArrowPathIcon, Bars3CenterLeftIcon, FunnelIcon, XMarkIcon } from '@heroicons/vue/24/solid/index.js';
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import type { Page, Request, Response } from '@/types';
import moment from 'moment';
import type { ServerOptions } from 'vue3-easy-data-table';
import Package from '@/package.json'
import type { ReportTypes } from '@/types/report';
import { useFacilityStore } from '@/store/facility';

definePageMeta({
    layout: 'dashboard',
    middleware: ['reports']
});
useHead({
    title: `${Package.name.toUpperCase()} - User Statistics Report`
});

const { loading, executeCancellableRequest, cancelRequest } = useCancellableRequest();
const search = ref<string>("");
const searchValue = ref<string>("");
const refreshIcon = ArrowPathIcon as Object
const statistics = ref(new Array<any>())
const cookie = useCookie('token');
const serverOptions = ref<ServerOptions>({
    page: 1,
    rowsPerPage: 20,
    sortBy: "name",
})
const serverItemsLength = ref<number>(0)
const dateRange = ref<Array<Date>>([])
const title = ref<string>("User Statistics Report");
const users = ref<Array<any>>([]);
const facility = useFacilityStore();
const router = useRouter();
const route = useRoute();
const reportTypeSelected = ref<Record<string, string>>({ name: route.query["report-type"] as string || "select report type" });
const { $toast } = useNuxtApp();
const selectedUser = ref<Record<string, string>>({
    name: 'Select and search for user...'
})
const pages = ref<Page>([
    {
        name: "Home",
        link: "/home"
    },
    {
        name: "Reports",
        link: "#"
    },
    {
        name: "Aggregate Reports",
        link: "#"
    }
])
const reportTypes: Ref<ReportTypes> = ref<ReportTypes>(
    [
        { name: "Summary" },
        { name: "Specimen Registry" },
        { name: 'Patients Registry' },
        { name: "Tests Registry" },
        { name: "Tests Performed" }
    ]
);

const filteredStatistics = computed(() => {
    return statistics.value.map((item: any) => ({
        ...item,
        created_date: moment(item.created_date).format('DD/MMM/YYYY'),
        age: calculateAge(item.date_of_birth)
    }));
});

const exportData = computed(() => {
    interface ReportType {
        name: "Summary" | "Patient Registry" | 'Tests Registry' | 'Specimen Registry' | 'Tests Performed'
    }
    const report = reportTypeSelected.value as unknown as ReportType;

    const generateSummary = () => {
        return (filteredStatistics.value.length > 0 && report.name == 'Summary') ? filteredStatistics.value.map((
            statistic: {
                user: string,
                tests_completed: { count: number, associated_ids: string },
                tests_received: { count: number, associated_ids: string },
                specimen_collected: { count: number, associated_ids: string },
                specimen_rejected: { count: number, associated_ids: string },
                tests_authorized: { count: number, associated_ids: string }
            }) => ({
                "USER": statistic.user,
                "TESTS COMPLETED": statistic.tests_completed.count,
                "TESTS RECEIVED": statistic.tests_received.count,
                "SPECIMEN COLLECTED": statistic.specimen_collected.count,
                "SPECIMEN REJECTED": statistic.specimen_rejected.count,
                "TESTS AUTHORIZED": statistic.tests_authorized.count
            })) : [];
    };

    const generatePatientsRegistry = () => {
        return (filteredStatistics.value.length > 0 && report.name == 'Patient Registry') ? filteredStatistics.value.map((statistic) => ({
            "PATIENT NO": statistic.id,
            "PATIENT NAME": `${statistic.client.first_name} ${statistic.client.last_name}`,
            "AGE": statistic.age,
            "SEX": statistic.sex
        })) : [];
    };

    const generateTestsRegistry = () => {
        return (filteredStatistics.value.length > 0 && report.name == 'Tests Registry') ? filteredStatistics.value.map((statistic) => ({
            "TEST TYPE": statistic.test_type_name,
            "PATIENT NUMBER": statistic.id,
            "PATIENT NAME": `${statistic.client.first_name} ${statistic.client.last_name}`,
            "SPECIMEN": statistic.specimen_type,
            "DATE REGISTERED": statistic.created_date
        })) : [];
    };

    const generateSpecimenRegistry = () => {
        return (filteredStatistics.value.length > 0 && report.name == 'Specimen Registry') ? filteredStatistics.value.map((statistic) => ({
            "SPECIMEN NUMBER": statistic.id,
            "PATIENT NAME": statistic.patient_name,
            "PATIENT NUMBER": statistic.patient_no,
            "DATE REGISTERED": statistic.created_date
        })) : [];
    };

    const generateTestsPerformed = () => {
        return (filteredStatistics.value.length > 0 && report.name == 'Tests Performed') ? filteredStatistics.value.map((statistic) => ({
            "TEST TYPE": statistic.test_type_name,
            "PATIENT NUMBER": statistic.id,
            "PATIENT NAME": statistic.patient_name,
            "SPECIMEN": statistic.specimen_type,
            "DATE REGISTERED": statistic.created_date
        })) : [];
    };

    const mapping = {
        "Summary": generateSummary(),
        "Patient Registry": generatePatientsRegistry(),
        "Tests Registry": generateTestsRegistry(),
        "Specimen Registry": generateSpecimenRegistry(),
        "Tests Performed": generateTestsPerformed()
    };

    return mapping[report.name];
});


const filteredUsers = computed(() => {
    return users.value.map((user: any) => ({
        id: user.id,
        name: `${user.first_name} ${user.last_name}`
    }))
})

const dataSeachValue = computed(() => {
    const valueMappings: { [key: string]: string } = {
        'tests performed': 'test_type',
        'patients registry': 'actions',
        'tests registry': 'test_type_name',
        'specimen registry': 'specimen',
        'summary': 'user',
    };
    const reportType = reportTypeSelected.value.name.toLowerCase();
    return valueMappings[reportType] || '';
})

const headers = computed(() => {
    const mappings: { [key: string]: Array<Record<string, string>> } = {
        'summary': new Array(
            { text: 'Name', value: 'user' },
            { text: 'Tests Received', value: 'tests_received' },
            { text: 'Specimen Collected', value: 'specimen_collected' },
            { text: 'Specimen Rejected', value: 'specimen_rejected' },
            { text: 'Tests Performed', value: 'tests_completed' },
            { text: 'Tests Authorized', value: 'tests_authorized' }
        ),
        'specimen registry': new Array(
            { text: 'Specimen Number', value: 'id' },
            { text: 'Name', value: 'specimen' },
            { text: 'Patient Number', value: 'patient_no' },
            { text: 'Patient Name', value: 'patient_name' },
            { text: 'Date Registered', value: 'created_date' },
        ),
        'tests registry': new Array(
            { text: 'Test Type', value: 'test_type_name' },
            { text: 'Patient Number', value: 'client.id' },
            { text: 'Patient Name', value: 'actions' },
            { text: 'Specimen', value: 'specimen_type' },
            { text: 'Date Registered', value: 'created_date' }
        ),
        'patients registry': new Array(
            { text: 'Patient Number', value: 'id' },
            { text: 'Name', value: 'actions' },
            { text: 'Gender', value: 'sex' },
            { text: 'Age', value: 'age' },
            { text: 'Date Registered', value: 'created_date' },
        ),
        'tests performed': new Array(
            { text: 'Test Id', value: 'test_id' },
            { text: 'Test Type', value: 'test_type' },
            { text: 'Patient No', value: 'patient_no' },
            { text: 'Patient Name', value: 'patient_name' },
            { text: 'Accession Number', value: 'accession_number' },
            { text: 'Date Registered', value: 'created_date' }
        )
    };
    const reportType = reportTypeSelected.value.name.toLowerCase();
    return mappings[reportType] || new Array<any>();
});

const startDate = computed(() => {
    return dateRange.value[0] ? moment(dateRange.value[0]).format('YYYY-MM-DD') : '';
});
const endDate = computed(() => {
    return dateRange.value[1] ? moment(dateRange.value[1]).format('YYYY-MM-DD') : '';
});

async function generateReport(): Promise<void> {
    if (checkReportType(reportTypeSelected.value) == '') useNuxtApp().$toast.warning("Please select a report type!")
    else {
        loading.value = true;
        const { page, rowsPerPage } = serverOptions.value
        let queryParams = `from=${startDate.value}&to=${endDate.value}&report_type=${checkReportType(reportTypeSelected.value).toLocaleLowerCase()}&page=${page}&limit=${rowsPerPage}&user=${selectedUser.value.id}&report_id=${route.query.report_id || ''}`
        const request: Request = {
            route: `${endpoints.aggregateReports}user_statistics?${queryParams}`,
            method: 'GET',
            token: `${cookie.value}`
        }
        const { data, error, pending }: Response = await executeCancellableRequest(request);
        loading.value = pending;
        if (data.value) {
            loading.value = false;
            statistics.value = data.value.data.tests
            serverItemsLength.value = data.value.data.metadata.total_count;
            updateReportId(data.value.data.report_id);
            data.value.data.tests.length > 0 ?
                (useNuxtApp().$toast.success('Report data generated successfully'))
                :
                (useNuxtApp().$toast.warning(`No data found in period ${startDate.value} - ${endDate.value}`))
        }
        if (error.value) {
            loading.value = false;
            console.error(error.value)
            useNuxtApp().$toast.error(ERROR_MESSAGE)
        }
    }
}

const updateReportId = (reportId: string) => {
    const currentRoute = router.currentRoute.value;
    const newQuery = {
        ...currentRoute.query,
        report_id: reportId,
    };
    router.replace({ query: newQuery }).catch((err: any) => {
        console.error("Failed to replace route:", err);
    });
};

async function getUsers(): Promise<void> {
    const { data, error }: Response = await fetchRequest(
        {
            route: `${endpoints.users}?page=${1}&per_page=${2000}`,
            method: 'GET',
            token: `${cookie.value}`
        }
    );
    if (data.value) {
        users.value = data.value.data
        const allUserExist = filteredUsers.value.some(
            (user: any) => user.name == "All"
        );
        if (!allUserExist) {
            filteredUsers.value.unshift({ id: 0, name: "All" });
        }
    }
    if (error.value) {
        console.error(error.value);
    }
}

const showEnumeration = (
    test: any,
    user: string,
    count: number,
    associated_ids: string
): void => {
    if (count !== 0 && associated_ids !== "") {
        router.push(
            `/reports/${associated_ids}?origin=aggregate&type=user-statistics-report&from=${startDate.value}&to=${endDate.value}&test=${test} - ${user}&department=&count=${count}`
        );
    } else {
        $toast.warning("No data found!");
    }
};

function checkReportType(report: Record<string, string>): string {
    return report.name == 'select report type' ? '' : report.name
}

function updateReport(value: any): void {
    if (typeof value === "object") {
        serverOptions.value = value;
    }
}

function update(value: string): void {
    search.value = value;
    searchValue.value = value;
    generateReport();
}

watch(
    () => serverOptions.value,
    ((o, n) => {
        o != n && (generateReport())
    })
)

watch(
    () => reportTypeSelected.value,
    ((t, d) => {
        if (t !== d) {
            statistics.value = [];
            const currentRoute = router.currentRoute.value;
            const newQuery = {
                ...currentRoute.query,
                "report-type": t.name,
                report_id: ""
            };
            router.replace({ query: newQuery }).catch((err: any) => {
                console.error("Failed to replace route:", err);
            });
        }
    })
);

watch(
    () => selectedUser.value,
    ((t, d) => {
        if (t !== d) {
            statistics.value = [];
            const currentRoute = router.currentRoute.value;
            const newQuery = {
                ...currentRoute.query,
                "user": t.name,
                report_id: ""
            };
            router.replace({ query: newQuery }).catch((err: any) => {
                console.error("Failed to replace route:", err);
            });
        }
    })
);

watch(
    dateRange,
    (n, o) => {
        if (n !== o && n.length > 0) {
            const startDate = dateRange.value[0]
                ? moment(dateRange.value[0]).format("YYYY-MM-DD")
                : "";
            const endDate = dateRange.value[1]
                ? moment(dateRange.value[1]).format("YYYY-MM-DD")
                : "";
            if (startDate || endDate) {
                const currentRoute = router.currentRoute.value;
                const newQuery = {
                    ...currentRoute.query,
                    ...(startDate && { from: startDate }),
                    ...(endDate && { to: endDate }),
                    report_id: ""
                };
                router.replace({ query: newQuery }).catch((err: any) => {
                    console.error("Failed to replace route:", err);
                });
            }
        }
    },
    { deep: true }
);

const validFields = computed((): boolean => {
    if (
        route.query.to !== undefined &&
        route.query.from !== undefined &&
        route.query.user !== undefined
    ) {
        dateRange.value = [new Date(String(route.query.from)), new Date(String(route.query.to))];
        selectedUser.value.name = `${route.query.user}`;
        return (
            route.query.to !== "" &&
            route.query.from !== "" &&
            route.query.user !== ""
        );
    }
    return false;
});

onMounted(() => {
    if (validFields.value) {
        generateReport();
    }else{
        loading.value = false;
    }
});

onMounted(async () => {
    await getUsers();
})
</script>

<style scoped lang="scss">
@keyframes ping {

    75%,
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

.animate-ping {
    animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
</style>
