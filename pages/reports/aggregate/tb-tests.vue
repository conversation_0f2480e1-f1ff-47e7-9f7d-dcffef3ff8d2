<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center py-5">
      <img
        src="@/assets/icons/report.png"
        alt="report-icon"
        class="w-8 h-8 mr-2"
      />
      <h3 class="text-2xl font-semibold uppercase">{{ title }}</h3>
    </div>

    <div class="flex justify-between items-center">
      <form
        @submit.prevent="generateReport()"
        class="w-full flex items-center space-x-3"
      >
        <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
          <FunnelIcon class="w-5 h-5 mr-2" />
          Filter By Date Range
          <div class="w-72 ml-2">
            <datepicker
              @cleared="isCleared"
              required
              position="left"
              placeholder="select start & end date"
              :range="true"
              input-class-name="datepicker"
              v-model="dateRange"
              format="dd/MM/yyyy"
              :maxDate="new Date()"
            />
          </div>
        </div>
        <div class="w-48">
          <CoreActionButton
            type="submit"
            color="primary"
            text="Generate Report"
            :icon="refreshIcon"
            :click="() => {}"
            :loading="loading"
          />
        </div>
      </form>
      <div class="flex items-center space-x-3">
        <CorePrinterReport :printSmallLabel="false" />
        <excel
          class="btn btn-default"
          :header="[
            `TB TESTS LABORATORY REPORT`,
            `PERIOD FROM ${moment(startDate).format(
              'DD-MM-yyyy'
            )} TO ${moment(endDate).format('DD-MM-yyyy')}`,
            facility.details.name,
            facility.details.address,
            facility.details.phone,
          ]"
          :data="exportData"
          worksheet="report-work-sheet"
          :name="`tb_tests_report_from_${startDate}_to_${endDate}.xls`"
        >
          <CoreExportButton text="Export Excel" />
        </excel>
      </div>
    </div>

    <div class="border rounded mt-10" id="print-container">
      <div class="flex items-center justify-between px-5 py-5 border-b">
        <div class="flex flex-col space-y-2">
          <img
            src="@/assets/images/logo.png"
            alt="app-logo"
            class="w-24 h-24 object-cover"
          />
          <h3 class="text-xl font-semibold">TB TESTS REPORT</h3>
        </div>
        <ReportsAddress />
      </div>

      <div class="m-3">
        <h3 class="text-lg font-semibold mb-2">
          Tests Performed Period:
          <span class="text-normal font-normal">
            {{ startDate != "" ? moment(startDate).format("DD/MM/YYYY") : "" }}
            - {{ endDate != "" ? moment(endDate).format("DD/MM/YYYY") : "" }}
          </span>
        </h3>
      </div>

      <div class="overflow-x-auto" v-if="!loading">
        <table class="w-full rounded overflow-x-auto border-t border-b">
          <template v-for="(value, key) in reportData.data" v-bind:key="key">
            <thead class="w-full border-b">
              <tr>
                <th
                  :colspan="reportData.months.length + 1"
                  class="uppercase text-center py-2 border-b"
                >
                  {{ key }}
                </th>
              </tr>
              <tr class="bg-gray-50">
                <th class="px-2 py-2 border-r uppercase">Result</th>
                <th
                  class="px-2 py-2 border-r"
                  v-for="(month, index) in reportData.months"
                  :key="index"
                >
                  {{ month.toString().toUpperCase() }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                class="border-b"
                v-for="(data, dataIndex) in value"
                :key="dataIndex"
              >
                <td class="px-2 py-2 border-r capitalize">{{ data.result }}</td>
                <td
                  v-for="month in reportData.months"
                  :key="month"
                  class="px-4 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                  @click="
                    showEnumeration(
                      `${key} - ${data.result}`,
                      month,
                      data.month[month] ? data.month[month].count : 0,
                      data.month[month] ? data.month[month].associated_ids : ''
                    )
                  "
                >
                  {{ data.month[month] ? data.month[month].count : 0 }}
                </td>
              </tr>
              <tr class="bg-gray-50 border-b">
                <td class="px-2 py-2 border-r font-semibold uppercase">
                  Total Examined
                </td>
                <td
                  v-for="month in sortMonths(reportData.months)"
                  :key="month"
                  class="px-2 py-2 border-r"
                >
                  {{ calculateTotal(month, value) }}
                </td>
              </tr>
              <tr
                class="border-t bg-gray-100 border-b"
                v-if="key.toString().toLowerCase() == 'smear microscopy result'"
              >
                <td class="px-2 py-2 border-r font-semibold uppercase">
                  Pick Up Rate
                </td>
                <template
                  v-for="(month, index) in reportData.months"
                  :key="index"
                >
                  <td class="px-2 py-2 border-r">
                    {{ calculatePercentage(month, value) }}%
                  </td>
                </template>
              </tr>
            </tbody>
          </template>
        </table>
      </div>

      <div
        v-show="loading"
        class="w-full items-center flex flex-col space-y-2 my-10"
      >
        <CoreLoader />
        <p>
          Generating report, please wait<span class="animate-pulse">...</span>
        </p>
      </div>

      <div
        v-if="reportData.data.length == 0 && !loading"
        class="w-full flex flex-col items-center justify-center space-y-2 py-10"
      >
        <img
          src="@/assets/images/page.png"
          alt="page-icon"
          class="object-cover w-20 h-20"
        />
        <p>Data not found, please generate report</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowPathIcon as refreshIcon,
  FunnelIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import { useFacilityStore } from "@/store/facility";
import type { Month, Page, Request, Response, TBREPORTDATA } from "@/types";
import Package from "@/package.json";

definePageMeta({
  layout: "dashboard",
  middleware: ["reports"],
});
useHead({
  title: `${Package.name.toUpperCase()} - TB Tests Reports`,
});

const title = "TB Report";
const pages = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Reports",
    link: "#",
  },
  {
    name: "Aggregate Reports",
    link: "#",
  },
]);
const dateRange = ref<Array<any>>([]);
const loading = ref<boolean>(false);
const reportData = ref<Record<any, any>>({
  months: new Array<any>(),
  data: new Array<any>(),
});
const facility = useFacilityStore();
const cookie = useCookie("token");
const router = useRouter();
const route = useRoute();
const { $toast } = useNuxtApp();
const isCleared = (): void => {
  dateRange.value = new Array("", "");
};

const calculateTotal = (month: string | number, report: any): number => {
  let total = 0;
  report.forEach((data: any) => {
    total += data.month[month] ? data.month[month].count : 0;
  });
  return total;
};

const sortMonths = (months: string[]): string[] => {
  return months.sort((a: string, b: string) => {
    const monthsOrder: string[] = [];
    const date = new Date();
    for (let i = 0; i < 12; i++) {
      date.setMonth(i);
      monthsOrder.push(date.toLocaleString("default", { month: "long" }));
    }
    return monthsOrder.indexOf(a) - monthsOrder.indexOf(b);
  });
};

interface ReportResult {
  result: string;
  month: Record<Month, { count: number }>;
}

const exportData = computed(() => {
  const rtValue: TBREPORTDATA[] = [];
  Object.keys(reportData.value.data).length > 0 &&
    Object.entries(reportData.value.data).forEach(
      ([key, value]: [string, any]) => {
        value.forEach((reportResult: ReportResult) => {
          const resultEntry: TBREPORTDATA = {
            TEST: key,
            RESULT: reportResult.result,
            ...sortMonths(Object.keys(reportResult.month)).reduce(
              (acc, month) => {
                const monthValue = reportResult.month[month as Month]
                  ? reportResult.month[month as Month].count
                  : 0;
                acc[month.toUpperCase()] =
                  monthValue !== undefined && monthValue !== null
                    ? monthValue
                    : 0;
                return acc;
              },
              {} as Record<string, number>
            ),
          };
          rtValue.push(resultEntry);
        });
      }
    );
  return rtValue;
});

const calculatePercentage = (month: string | number, report: any): number => {
  let percentile = 1;
  let positives = 1;
  if (Object.keys(report)[0].toString().toLowerCase().includes("+")) {
    report[Object.keys(report)[0]].forEach((data: any) => {
      positives += data.month[month] ? data.month[month] : 0;
    });
  }
  let total = 0;
  report.forEach((data: any) => {
    total += data.month[month] ? data.month[month].count : 0;
  });
  if (total > 0) {
    percentile = (positives / total) * 100;
  } else {
    percentile = 0;
  }
  return Math.floor(percentile);
};

const startDate = computed(() => {
  return dateRange.value[0]
    ? moment(dateRange.value[0]).format("YYYY-MM-DD")
    : "";
});

const endDate = computed(() => {
  return dateRange.value[1]
    ? moment(dateRange.value[1]).format("YYYY-MM-DD")
    : "";
});

async function generateReport(): Promise<void> {
  loading.value = true;
  let queryParams = `from=${startDate.value}&to=${endDate.value}&report_id=${route.query.report_id}`;
  const request: Request = {
    route: `${endpoints.aggregateReports}tb_tests?${queryParams}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;
  if (data.value) {
    loading.value = false;
    reportData.value = data.value;
    updateReportId(data.value.report_id);
    Object.keys(reportData.value.data).length > 0
      ? useNuxtApp().$toast.success("Report data generated successfully")
      : useNuxtApp().$toast.warning(
          `No data found in period ${startDate.value} - ${endDate.value}`
        );
  }
  if (error.value) {
    loading.value = false;
    console.error(error.value);
    useNuxtApp().$toast.success(ERROR_MESSAGE);
  }
}

const updateReportId = (reportId: string): void => {
  const currentRoute = router.currentRoute.value;
  const newQuery = {
    ...currentRoute.query,
    report_id: reportId,
  };
  router.replace({ query: newQuery }).catch((err: any) => {
    console.error("Failed to replace route:", err);
  });
};

const showEnumeration = (
  test: any,
  ward: string,
  count: number,
  associated_ids: string
): void => {
  if (count !== 0 && associated_ids !== "") {
    router.push(
      `/reports/${associated_ids}?origin=aggregate&type=tb-tests-report&from=${startDate.value}&to=${endDate.value}&test=${test} - ${ward}&department=&count=${count}`
    );
  } else {
    $toast.warning("No data found for this month");
  }
};

watch(
  dateRange,
  () => {
    const startDate = dateRange.value[0]
      ? moment(dateRange.value[0]).format("YYYY-MM-DD")
      : "";
    const endDate = dateRange.value[1]
      ? moment(dateRange.value[1]).format("YYYY-MM-DD")
      : "";
    if (startDate || endDate) {
      const currentRoute = router.currentRoute.value;
      const newQuery = {
        ...currentRoute.query,
        report_id: "",
        ...(startDate && { from: startDate }),
        ...(endDate && { to: endDate }),
      };
      router.replace({ query: newQuery }).catch((err: any) => {
        console.error("Failed to replace route:", err);
      });
    }
  },
  { deep: true }
);

const validFields = computed(() => {
  if (route.query.to !== undefined && route.query.from !== undefined) {
    dateRange.value = [route.query.from, route.query.to];
    return route.query.to !== "" && route.query.from !== "";
  }
  return false;
});

onMounted(() => {
  if (validFields.value) {
    generateReport();
  }
});
</script>

<style scoped></style>
