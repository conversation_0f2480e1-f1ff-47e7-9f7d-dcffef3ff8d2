<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center py-5">
      <img src="@/assets/icons/report.png" alt="report-icon" class="w-8 h-8 mr-2" />
      <h3 class="text-2xl font-semibold uppercase">{{ title }}</h3>
    </div>

    <div class="w-full flex items-center justify-between mb-5">
      <FormKit type="form" submit-label="Update" @submit="generateReport" :actions="false" #default="{ value }"
        id="submitForm">
        <div class="w-full flex items-center space-x-3">
          <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
            <FunnelIcon class="w-5 h-5 mr-2" />
            Filter By Date Range
            <div class="w-72 ml-2">
              <datepicker @cleared="isCleared" required position="left" placeholder="select start & end date"
                :range="true" format="dd/MM/yyyy" input-class-name="datepicker" v-model="dateRange" :maxDate="new Date()"/>
            </div>
          </div>
          <div class="w-40">
            <CoreDropdown :items="departments" v-model="selectedDepartment" />
          </div>
          <div>
            <CoreActionButton :loading="loading" color="primary" type="submit" text="Generate Report"
              :icon="refreshIcon" :click="() => { }" />
          </div>
        </div>
      </FormKit>
      <div class="w-48 flex items-end justify-end">
        <ExportToExcel element="print-container" :filename="`infection_report_from_${moment(startDate).format(
          'DD_MM_YYYY'
        )}_to_${moment(endDate).format(
          'DD_MM_YYYY'
        )}_for_${facility.details.name.toLowerCase().split(' ').join('_')}`">
          <CoreExportButton text="Export Excel" />
        </ExportToExcel>
      </div>
    </div>

    <div>
      <div class="rounded border">
        <div class="rounded-tr rounded-tl border-b px-5 py-5 flex items-center justify-between">
          <div class="flex flex-col space-y-2">
            <img src="@/assets/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
            <h3 class="text-xl font-semibold uppercase">INFECTION REPORT</h3>
          </div>
          <ReportsAddress />
        </div>
        <div v-if="reportData.length > 0 && !loading">
          <div class="hidden">
            <h3>INFECTION REPORT</h3>
            <h3>{{ facility.details.name }}</h3>
            <p>{{ facility.details.address }}</p>
            <p>{{ facility.details.phone }}</p>
          </div>
          <div class="text-lg font-semibold py-2 px-2">
            Tests Performed Period:
            <span class="text-normal font-normal">
              {{
                startDate != "" ? moment(startDate).format("DD/MM/YYYY") : ""
              }}
              - {{ endDate != "" ? moment(endDate).format("DD/MM/YYYY") : "" }}
            </span>
          </div>
          <div id="print-container">
            <div v-html="infectionReportTable(reportData)" @click="handleTableClick"></div>
            <div>
              <h3 class="px-2 py-2 text-xl font-semibold uppercase">SUMMARY</h3>
              <table class="w-full border-t">
                <thead>
                  <tr class="border-b border-t bg-gray-100">
                    <th class="px-2 py-2 border-r text-left">TESTS</th>
                    <th class="px-2 py-2 text-left">TOTAL</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(data, index) in summaryData" :key="data.name"
                    :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'">
                    <td class="px-2 py-2 border-r border-b">{{ data.name }}</td>
                    <td @click="
                      showEnumeration(
                        data.name,
                        selectedDepartment.name,
                        data?.total ? data.total : 0,
                        data?.associated_ids ? data.associated_ids : ''
                      )
                      " class="px-4 py-2 border-r hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150">
                      {{ data.total }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div v-if="reportData.length == 0 && !loading"
          class="w-full flex flex-col items-center justify-center space-y-2 py-10">
          <img src="@/assets/images/page.png" alt="page-icon" class="object-cover w-20 h-20" />
          <p class="text-base">Data not found, please generate report</p>
        </div>
        <ReportsLoader :condition="loading" :cancelReportGeneration="() => cancelRequest()" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowPathIcon as refreshIcon,
  FunnelIcon,
} from "@heroicons/vue/24/solid/index.js";
import type {
  Department,
  InfectionReportDefinition,
  Page,
  Request,
  Response,
  InfectionReportSummary
} from "@/types";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import moment from "moment";
import { ExportToExcel } from "vue-doc-exporter";
import Package from "@/package.json";
import { useFacilityStore } from "@/store/facility";

definePageMeta({
  layout: "dashboard",
  middleware: ["reports"],
});
useHead({
  title: `${Package.name.toUpperCase()} - Infection Report`,
});

const title: Ref<string> = ref<string>("Infection Report");
const pages: Ref<Page> = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Reports",
    link: "#",
  },
  {
    name: "Aggregate Reports",
    link: "#",
  },
]);
const departments = ref<Array<Department>>([]);
const selectedDepartment = ref<Department>({
  id: 0,
  name: "select department",
});
const cookie = useCookie("token");
const { loading, executeCancellableRequest, cancelRequest } = useCancellableRequest();
const router = useRouter();
const route = useRoute();
const { $toast } = useNuxtApp();
const facility = useFacilityStore();
const reportData = ref<Array<any>>([]);
const summaryData = ref<InfectionReportSummary[]>([]);
const tableGenerated = ref<boolean>(false);
const dateRange: Ref<Array<any>> = ref<Array<any>>(new Array("", ""));
const startDate = computed(() => {
  return dateRange.value[0]
    ? moment(dateRange.value[0]).format("YYYY-MM-DD")
    : "";
});
const endDate = computed(() => {
  return dateRange.value[1]
    ? moment(dateRange.value[1]).format("YYYY-MM-DD")
    : "";
});

const isCleared = (): void => {
  dateRange.value = new Array("", "");
};

const generateTableHeader = (): string => {
  return '<thead class="w-full h-full border-b"><tr class="w-full h-full bg-gray-50"><th rowspan="2" class="px-2 py-2 border-r">Test</th><th rowspan="2" class="border-r">Measure</th><th rowspan="2" class="px-2 py-2 border-r">Results</th><th rowspan="2" class="px-2 py-2 border-r">Sex</th><th colspan="3" scope="colgroup" class="border-r border-b px-2 py-2">Age Range</th><th rowspan="2" class="px-2 py-2 border-r">M/F Total</th><th rowspan="2" class="px-2 py-2">Total</th></tr><tr><th scope="col" class="border-l px-2 py-2 border-r bg-gray-50">[0-5]</th><th scope="col" class="border-r px-2 py-2 bg-gray-50">[5-14]</th><th scope="col" class="border-r px-2 py-2 bg-gray-50">[14-120]</th></tr></thead>';
};

const safeReplace = (
  original: string,
  search: string,
  replacement: string
): string => {
  const index = original.indexOf(search);
  if (index !== -1) {
    return (
      original.slice(0, index) +
      replacement +
      original.slice(index + search.length)
    );
  }
  return original;
};

const showEnumeration = (
  test: any,
  ward: string,
  count: number,
  associated_ids: string
): void => {
  if (count !== 0 && associated_ids !== "") {
    router.push(
      `/reports/${associated_ids}?origin=aggregate&type=infection-report&from=${startDate.value}&to=${endDate.value}&test=${test} - ${ward}&department=${selectedDepartment.value.name}&count=${count}`
    );
  } else {
    $toast.warning("No data found for this month");
  }
};

const generateTableBody = (data: any): string => {
  tableGenerated.value = false;
  let tBody: string = ``;
  let currentMeasure: string = "";
  let currentTest: string = "";
  let currentResult: string = "";
  let testCount: number = 0;
  let measureCount: number = 0;
  let resultCount: number = 0;
  let resultTotal: number = 0;
  let testTotal: number = 0;
  const flattenedData = data.reduce(
    (flattened: any[], test: { measures: any[]; test_type: string }) => {
      const flattenedMeasures = test.measures.flatMap(
        (measure: { results: any[]; name: any }) => {
          const results = measure.results.flatMap(
            (result: { [x: string]: any }[]) => {
              const resultKey = Object.keys(result[0])[0];
              const genderResults = result[0][resultKey];
              return Object.entries(genderResults).map(([gender, values]) => {
                const genderValues = values as { [key: string]: number | null };
                return {
                  test_type: test.test_type,
                  measure: measure.name,
                  result: resultKey,
                  gender: gender === "F" ? "Female" : "Male",
                  ...genderValues,
                };
              });
            }
          );
          return results;
        }
      );
      flattened.push(...flattenedMeasures);
      return flattened;
    },
    new Array()
  );
  flattenedData.forEach(
    (rowData: {
      test_type: string;
      measure: string;
      result: string;
      gender: string;
      L_E_5: { count: number; associated_ids: string };
      G_5_L_E_14: { count: number; associated_ids: string };
      G_14: { count: number; associated_ids: string };
    }) => {
      measureCount++;
      resultCount++;
      testCount++;
      if (currentTest === rowData.test_type) {
        tBody += `<tr>`;
        if (currentMeasure !== rowData.measure) {
          currentMeasure = rowData.measure;
          currentResult = rowData.result;
          tBody = safeReplace(tBody, "NEW_MEASURE", measureCount.toString());
          tBody = tBody.replace("NEW_RESULT", resultCount.toString());
          tBody = safeReplace(tBody, "RESULT_TOTAL", resultTotal.toString());
          measureCount = 0;
          resultCount = 0;
          resultTotal = 0;
          tBody +=
            `<td class='px-2 py-2 border-r border-b' rowspan='NEW_MEASURE'>` +
            rowData.measure +
            "</td>";
          tBody +=
            `<td class='border-r border-b px-2 py-2' rowspan='NEW_RESULT'>` +
            rowData.result +
            "</td>";
        } else {
          if (currentResult !== rowData.result) {
            tBody = tBody.replace(/NEW_RESULT/g, resultCount.toString());
            tBody = safeReplace(tBody, "RESULT_TOTAL", resultTotal.toString());
            currentResult = rowData.result;
            resultCount = 0;
            resultTotal = 0;
            tBody +=
              `<td class='border-r border-b px-2 py-2' rowspan='NEW_RESULT'>` +
              rowData.result +
              `</td>`;
          }
        }
      } else {
        currentTest = rowData.test_type;
        currentMeasure = rowData.measure;
        currentResult = rowData.result;
        tBody = tBody.replace(/NEW_TEST/g, testCount.toString());
        tBody = safeReplace(tBody, "NEW_MEASURE", measureCount.toString());
        tBody = tBody.replace(/NEW_RESULT/g, resultCount.toString());
        tBody = safeReplace(tBody, "RESULT_TOTAL", resultTotal.toString());
        tBody = safeReplace(tBody, "TEST_TOTAL", testTotal.toString());
        measureCount = 0;
        resultCount = 0;
        resultTotal = 0;
        testCount = 0;
        testTotal = 0;
        tBody += `<tr class='tests'>`;
        tBody +=
          `<td class='border-b border-r px-2 py-2' rowspan='NEW_TEST'>` +
          rowData.test_type +
          `</td>`;
        tBody +=
          `<td class='border-r border-b px-2 py-2' rowspan='NEW_MEASURE'>` +
          rowData.measure +
          `</td>`;
        tBody +=
          `<td class='border-r border-b px-2 py-2' rowspan='NEW_RESULT'>` +
          rowData.result +
          `</td>`;
      }
      tBody +=
        `<td class='border-r border-b px-2 py-2'>` + rowData.gender + `</td>`;
      const L_E_5 = getDefintion("L_E_5");
      tBody += `
      <td
        class="px-2 py-2 border-r border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
        data-clickable="true"
        id="tdRef"
        data-test="${rowData.test_type}"
        data-ward="${L_E_5}"
        data-count="${rowData.L_E_5 ? rowData.L_E_5.count : 0}"
        data-ids="${rowData.L_E_5 ? rowData.L_E_5.associated_ids : ""}">
        ${rowData.L_E_5 ? rowData.L_E_5.count.toString() : "0"}
      </td>`;

      const G_5_L_E_14 = getDefintion("L_E_5");
      tBody +=
        `<td class="px-2 py-2 border-r border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
        data-clickable="true"
        id="tdRef"
        data-test="${rowData.test_type}"
        data-ward="${G_5_L_E_14}"
        data-count="${rowData.L_E_5 ? rowData.L_E_5.count : 0}"
        data-ids="${rowData.L_E_5 ? rowData.L_E_5.associated_ids : ""}">` +
        (rowData.G_5_L_E_14 ? rowData.G_5_L_E_14.count : 0).toString() +
        `</td>`;

      const G_14 = getDefintion("L_E_5");
      tBody +=
        `<td class="px-2 py-2 border-r border-b hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
        data-clickable="true"
        id="tdRef"
        data-test="${rowData.test_type}"
        data-ward="${G_14}"
        data-count="${rowData.G_14 ? rowData.G_14.count : 0}"
        data-ids="${rowData.G_14 ? rowData.G_14.associated_ids : ""}">` +
        (rowData.G_14 ? rowData.G_14.count : 0).toString() +
        `</td>`;


      tBody +=
        `<td class='border-r border-b px-2 py-2'>` +
        (
          (rowData.L_E_5 ? rowData.L_E_5.count : 0) +
          (rowData.G_5_L_E_14 ? rowData.G_5_L_E_14.count : 0) +
          (rowData.G_14 ? rowData.G_14.count : 0)
        ).toString() +
        `</td>`;

      resultTotal +=
        (rowData.L_E_5 ? rowData.L_E_5.count : 0) +
        (rowData.G_5_L_E_14 ? rowData.G_5_L_E_14.count : 0) +
        (rowData.G_14 ? rowData.G_14.count : 0);

      if (currentResult === rowData.result && resultCount == 0) {
        tBody += `<td class='border-r border-b px-2 py-2' rowspan='NEW_RESULT'>RESULT_TOTAL</td>`;
      }

      if (measureCount == 0) {
        testTotal =
          (rowData.L_E_5 ? rowData.L_E_5.count : 0) +
          (rowData.G_5_L_E_14 ? rowData.G_5_L_E_14.count : 0) +
          (rowData.G_14 ? rowData.G_14.count : 0);
      }
    }
  );
  tBody = safeReplace(tBody, "NEW_TEST", (++testCount).toString());
  tBody = safeReplace(tBody, "NEW_MEASURE", (++measureCount).toString());
  tBody = safeReplace(tBody, "NEW_RESULT", (++resultCount).toString());
  tBody = safeReplace(tBody, "RESULT_TOTAL", resultTotal.toString());
  tBody = safeReplace(tBody, "TEST_TOTAL", testTotal.toString());
  tBody += `</tr>`;

  tableGenerated.value = true;
  return tBody;
};

const getDefintion = (key: InfectionReportDefinition) => {
  const definition = {
    L_E_5: "Less than or equal to 5 years old",
    G_5_L_E_14:
      "Greater than 5 years old and less than or equal to 14 years old",
    G_14: "Greater than 14 years old",
  };
  return definition[key];
};

/**
 * @method generateTableHeader generates table header
 * @returns string
 */
const infectionReportTable = (reportData: any): string => {
  const tHeader: string = generateTableHeader();
  const tBody: string =
    reportData.length > 0 ? generateTableBody(reportData) : "<tr></tr>";
  return `<table class="w-full rounded border-t">${tHeader}${tBody}</table>`;
};
/**
 * @method loadDepartments gets list of departments
 * @retuns promise @type void
 */
async function loadDepartments(): Promise<void> {
  const { data, error }: Response = await fetchRequest({
    route: `${endpoints.departments}`,
    method: "GET",
    token: `${cookie.value}`,
  });
  if (data.value) {
    departments.value = data.value;
    if (!departments.value.some((department) => department.id === 0))
      departments.value.unshift({ id: 0, name: "All" });
  }
  if (error.value) {
    console.error(error.value);
  }
}
/**
 * @method generateReport generates report
 * @retuns promise @type void
 */
async function generateReport(): Promise<void> {
  if (validate()) $toast.warning("Please select a department");
  else {
    loading.value = true;
    let startDate =
      dateRange.value[0].toString() != ""
        ? moment(dateRange.value[0].toString()).format("YYYY-MM-DD")
        : "";
    let endDate =
      dateRange.value[1].toString() != ""
        ? moment(dateRange.value[1].toString()).format("YYYY-MM-DD")
        : "";
    const request: Request = {
      route: `${endpoints.aggregateReports}infection?from=${startDate}&to=${endDate}&department=${selectedDepartment.value.name}&report_id=${route.query.report_id}`,
      method: "GET",
      token: `${cookie.value}`,
    };
    const { data, error, pending }: Response = await executeCancellableRequest(request);
    loading.value = pending;
    if (data.value) {
      loading.value = false;
      reportData.value = data.value.data;
      summaryData.value = data.value.summary;
      updateReportId(data.value.report_id);
      $toast.success("Report data generated successfully!");
    }
    if (error.value) {
      console.error(error.value);
      loading.value = false;
      $toast.error(ERROR_MESSAGE);
    }
  }
}

const handleTableClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (target.tagName === "TD" && target.id === "tdRef") {
    const { test, ward, count, ids } = target.dataset;
    if (count && ids) {
      const countNum = parseInt(count, 10);
      if (countNum !== 0 && ids !== "") {
        router.push(
          `/reports/${ids}?origin=aggregate&type=infection-report&from=${startDate.value}&to=${endDate.value}&test=${test} - ${ward}&department=${selectedDepartment.value.name}&count=${countNum}`
        );
      } else {
        $toast.warning("No data found for this month");
      }
    } else {
      $toast.warning("No data found for this month");
    }
  }
};

const updateReportId = (reportId: string) => {
  const currentRoute = router.currentRoute.value;
  const newQuery = {
    ...currentRoute.query,
    report_id: reportId,
  };
  router.replace({ query: newQuery }).catch((err: any) => {
    console.error("Failed to replace route:", err);
  });
};

/**
 * @method validate validates form
 * @retuns promise @type void
 */
const validate = (): Boolean => {
  return selectedDepartment.value.name == SELECT_DEPARTMENT;
};

watch(
  dateRange,
  () => {
    const startDate = dateRange.value[0]
      ? moment(dateRange.value[0]).format("YYYY-MM-DD")
      : "";
    const endDate = dateRange.value[1]
      ? moment(dateRange.value[1]).format("YYYY-MM-DD")
      : "";
    if (startDate || endDate) {
      const currentRoute = router.currentRoute.value;
      const newQuery = {
        ...currentRoute.query,
        ...(startDate && { from: startDate }),
        ...(endDate && { to: endDate }),
      };
      router.replace({ query: newQuery }).catch((err: any) => {
        console.error("Failed to replace route:", err);
      });
    }
  },
  { deep: true }
);

watch(selectedDepartment, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    const currentRoute = router.currentRoute.value;
    const newQuery = {
      ...currentRoute.query,
      department: newValue.name || undefined,
      report_id: "",
    };
    router.replace({ query: newQuery }).catch((err) => {
      console.error("Failed to replace route:", err);
    });
  }
});

const validFields = computed(() => {
  if (
    route.query.to !== undefined &&
    route.query.from !== undefined &&
    route.query.department !== undefined
  ) {
    dateRange.value = [route.query.from, route.query.to];
    selectedDepartment.value.name = `${route.query.department}`;
    return (
      route.query.to !== "" &&
      route.query.from !== "" &&
      route.query.department !== ""
    );
  }
  return false;
});

onMounted(() => {
  if (validFields.value) {
    generateReport();
  } else {
    loading.value = false;
  }
});

loadDepartments();
</script>
