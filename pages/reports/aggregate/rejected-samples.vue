<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center py-5">
      <img
        src="@/assets/icons/report.png"
        alt="report-icon"
        class="w-8 h-8 mr-2"
      />
      <h3 class="text-2xl font-semibold uppercase">{{ title }}</h3>
    </div>

    <div class="w-full flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
          <FunnelIcon class="w-5 h-5 mr-2" />
          Filter By Date Range
          <div class="w-72 ml-2">
            <datepicker
              @cleared="isCleared"
              required
              position="left"
              placeholder="select start & end date"
              :range="true"
              input-class-name="datepicker"
              v-model="dateRange"
              format="dd/MM/yyyy"
              :maxDate="new Date()"
            />
          </div>
        </div>
        <div class="w-44">
          <CoreDropdown :items="departments" v-model="selectedDepartment" />
        </div>
        <CoreActionButton
          color="primary"
          text="Generate Report"
          :icon="refreshIcon"
          :click="() => generateReport()"
          :loading="loading"
        />
      </div>
      <div class="flex items-center space-x-3">
        <CorePrinterReport :printSmallLabel="false" />
        <excel
          class="btn btn-default"
          :header="[
            `REJECTED ${selectedDepartment.name.toUpperCase()} SAMPLES REPORT`,
            `PERIOD FROM ${moment(startDate).format(DATE_FORMAT)} TO ${moment(
              endDate
            ).format(DATE_FORMAT)}`,
            facility.details.name,
            facility.details.address,
            facility.details.phone,
          ]"
          :data="exportData"
          worksheet="report-work-sheet"
          :name="`rejected_${selectedDepartment.name
            .toLowerCase()
            .split(' ')
            .join('_')}_report_${moment(startDate).format(
            'DD_MM_yyyy'
          )}_to_${moment(endDate).format('DD_MM_yyyy')}.xls`"
        >
          <CoreExportButton text="Export Excel" />
        </excel>
      </div>
    </div>

    <div class="border rounded mt-5">
      <div class="flex items-center justify-between px-5 py-5 border-b">
        <div class="flex flex-col space-y-2">
          <img
            src="@/assets/images/logo.png"
            alt="app-logo"
            class="w-24 h-24 object-cover"
          />
          <h3 class="text-xl font-semibold">REJECTED SAMPLES REPORT</h3>
        </div>
        <ReportsAddress />
      </div>

      <div class="mt-3 px-5">
        <h3 class="text-lg font-semibold mb-2">
          Tests Performed Period:
          <span class="text-normal font-normal">
            {{ startDate != "" ? moment(startDate).format(DATE_FORMAT) : "" }} -
            {{ endDate != "" ? moment(endDate).format(DATE_FORMAT) : "" }}
          </span>
        </h3>
      </div>

      <div
        class="print-container"
        id="print-container"
        v-if="reportData.results.length > 0 && !loading"
      >
        <table class="w-full border border-dotted rounded overflow-x-auto">
          <template v-for="(report, index) in processedReportData" :key="index">
            <div v-if="report.name" class="w-full px-2 py-2 bg-gray-100 text-sky-500 uppercase text-lg font-semibold">{{ report.name }}</div>
            <thead class="bg-gray-50 border-b border-dotted rounded-t">
              <tr>
                <th
                  class="border-r border-b border-dotted px-2 py-2 font-semibold"
                >
                  TESTS
                </th>
                <th
                  v-for="ward in reportData.wards"
                  :key="ward"
                  class="border-r border-b border-dotted px-2 py-2 uppercase font-semibold"
                >
                  {{ ward }}
                </th>
                <th class="border-r border-t border-b border-dotted px-2 py-2">
                  TOTAL
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="testType in report.testTypes" :key="testType.name">
                <td class="border-r border-b border-dotted px-2 py-2">
                  {{ testType.name }}
                </td>

                <td
                  v-for="ward in reportData.wards"
                  :key="ward"
                  @click="
                    showEnumeration(
                      testType.name,
                      ward,
                      testType.wards[ward] ? testType.wards[ward].count : 0,
                      testType.wards[ward]
                        ? testType.wards[ward].associated_ids
                        : ''
                    )
                  "
                  class="px-4 border-r border-b border-dotted py-2 hover:font-medium cursor-pointer hover:text-sky-500 hover:underline transition duration-150"
                >
                  {{ testType.wards[ward] ? testType.wards[ward].count : 0 }}
                </td>
                <td
                  class="border-r border-t border-b border-dotted px-2 py-2 font-bold"
                >
                  {{ getTotalCountForTestType(report.name, testType.name) }}
                </td>
              </tr>
            </tbody>
          </template>
        </table>
      </div>
      <ReportsLoader
        :condition="loading"
        :cancelReportGeneration="() => cancelRequest()"
      />
      <div
        class="flex flex-col space-y-3 items-center justify-center py-10"
        v-if="
          reportData.wards.length == 0 &&
          reportData.results.length == 0 &&
          !loading
        "
      >
        <img
          src="@/assets/images/page.png"
          class="w-20 h-20 object-cover"
          alt="page-icon"
        />
        <p class="text-base">
          Please generate report data to preview the report
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { endpoints } from "@/services/endpoints";
import type {
  Department,
  DropdownItem,
  Page,
  Request,
  Response,
} from "@/types";
import moment from "moment";
import {
  ArrowPathIcon as refreshIcon,
  FunnelIcon,
} from "@heroicons/vue/24/solid/index.js";
import { useFacilityStore } from "@/store/facility";
import Package from "@/package.json";

definePageMeta({
  layout: "dashboard",
  middleware: ["reports"],
});

useHead({
  title: `${Package.name.toUpperCase()} - Rejected Samples Report`,
});

const title = "Rejected Samples Report";
const pages = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Reports",
    link: "#",
  },
  {
    name: "Aggregate Reports",
    link: "#",
  },
]);
const departments = ref<Array<Department>>([]);
const cookie = useCookie("token");
const facility = useFacilityStore();
const router = useRouter();
const route = useRoute();
const { loading, executeCancellableRequest, cancelRequest } =
  useCancellableRequest();
const dateRange = ref<Array<any>>(["", ""]);
const { $toast, $metadata } = useNuxtApp();
const selectedDepartment = ref<DropdownItem>({
  name: route.query.department ? String(route.query.department) : SELECT_DEPARTMENT,
  id: route.query.department_id ? Number(route.query.department_id) : undefined
});

const isCleared = (): void => {
  dateRange.value = new Array("", "");
};

interface ReportData {
  value: any;
  wards: any;
  results: Array<any>;
}

const reportData = ref<ReportData>({
  results: [],
  value: undefined,
  wards: [],
});

const processedReportData = computed(() => {
  return reportData.value.results.map((report: any) => {
    const testTypeMap: {
      [key: string]: {
        [key: string]: { count: number; associated_ids: string };
      };
    } = {};

    report.test_types.forEach((testType: any) => {
      const { name, ward, count, associated_ids } = testType;

      if (!testTypeMap[name]) {
        testTypeMap[name] = {};
      }

      if (!testTypeMap[name][ward]) {
        testTypeMap[name][ward] = { count: 0, associated_ids: "" };
      }

      testTypeMap[name][ward].count += count;
      testTypeMap[name][ward].associated_ids = associated_ids;
    });

    const testTypes = Object.keys(testTypeMap).map((name) => ({
      name,
      wards: testTypeMap[name],
    }));

    return {
      name: report.name,
      testTypes,
    };
  });
});

const exportData = computed((): any[] => {
  interface FlattenedResult {
    TESTS: string;
    TOTAL: number;
    [ward: string]: number | string;
  }
  const flattenedResults: FlattenedResult[] = [];
  reportData.value.results.forEach((result) => {
    const testTypeMap: { [testType: string]: { [ward: string]: number } } = {};
    const totalCounts: { [testType: string]: number } = {};
    result.test_types.forEach(
      (test: { name: string; ward: string; count: number }) => {
        const { name: testType, ward, count } = test;

        if (!testTypeMap[testType]) {
          testTypeMap[testType] = {};
          totalCounts[testType] = 0;
        }

        testTypeMap[testType][ward] = count || 0;
        totalCounts[testType] += count;
      }
    );

    for (const testType in testTypeMap) {
      const wardCounts = testTypeMap[testType];
      const flattenedResult: any = {
        TESTS: testType,
      };

      reportData.value.wards.forEach((ward: any) => {
        flattenedResult[ward] =
          wardCounts[ward] !== undefined ? wardCounts[ward] : 0;
      }); 
      flattenedResult["REASON"] = result.name;
      flattenedResult["TOTAL"] = totalCounts[testType];
      flattenedResults.push(flattenedResult);
    }
  });
  return flattenedResults;
});

const getTotalCountForTestType = (
  reportName: string,
  testTypeName: string
): number => {
  const report = processedReportData.value.find(
    (report) => report.name === reportName
  );
  const testType = report?.testTypes.find(
    (testType) => testType.name === testTypeName
  );
  if (!testType) return 0;
  return Object.values(testType.wards).reduce((sum, ward) => {
    return sum + ward.count;
  }, 0);
};

const startDate = computed(() => {
  return dateRange.value[0]
    ? moment(dateRange.value[0]).format("YYYY-MM-DD")
    : "";
});
const endDate = computed(() => {
  return dateRange.value[1]
    ? moment(dateRange.value[1]).format("YYYY-MM-DD")
    : "";
});

async function generateReport(): Promise<void> {
  if (validate()) $toast.warning("Please select a department");
  else {
    loading.value = true;
    const request: Request = {
      route: `${endpoints.aggregateReports}rejected?from=${
        startDate.value
      }&to=${endDate.value}&department=${
        selectedDepartment.value.id == 0 ? "" : selectedDepartment.value.id
      }&report_id=${route.query.report_id}`,
      method: "GET",
      token: `${cookie.value}`,
    };
    const { data, error, pending }: Response = await executeCancellableRequest(
      request
    );
    loading.value = pending;
    if (data.value) {
      console.log(data.value);
      loading.value = false;
      reportData.value = data.value;
      updateReportId(data.value.report_id);
      data.value.results.length > 0
        ? $toast.success("Report generated successfully!")
        : $toast.warning("Data not found within the specified period");
    }
    if (error.value) {
      console.error(error.value);
      loading.value = false;
      $toast.error(ERROR_MESSAGE);
    }
  }
}

const validate = (): Boolean => {
  return selectedDepartment.value.name == SELECT_DEPARTMENT;
};

const updateReportId = (reportId: string) => {
  const currentRoute = router.currentRoute.value;
  const newQuery = {
    ...currentRoute.query,
    report_id: reportId,
  };
  router.replace({ query: newQuery }).catch((err: any) => {
    console.error("Failed to replace route:", err);
  });
};

const showEnumeration = (
  test: any,
  ward: string,
  count: number,
  associated_ids: string
): void => {
  if (count !== 0 && associated_ids !== "") {
    const departmentName = selectedDepartment.value.name;
    const departmentId = selectedDepartment.value.id;
    
    router.push(
      `/reports/${associated_ids}?origin=aggregate&type=rejected-samples-report&from=${startDate.value}&to=${endDate.value}&test=${test} - ${ward}&department=${departmentName}&department_id=${departmentId}&count=${count}`
    );
  } else {
    $toast.warning("No data found for this month");
  }
};

watch(
  dateRange,
  () => {
    const startDate = dateRange.value[0]
      ? moment(dateRange.value[0]).format("YYYY-MM-DD")
      : "";
    const endDate = dateRange.value[1]
      ? moment(dateRange.value[1]).format("YYYY-MM-DD")
      : "";
    if (startDate || endDate) {
      const currentRoute = router.currentRoute.value;
      const newQuery = {
        ...currentRoute.query,
        ...(startDate && { from: startDate }),
        ...(endDate && { to: endDate }),
        ...(selectedDepartment.value.name !== SELECT_DEPARTMENT && { 
          department: selectedDepartment.value.name,
          department_id: selectedDepartment.value.id
        }),
        ...(route.query.report_id && { report_id: "" }),
      };
      router.replace({ query: newQuery }).catch((err: any) => {
        console.error("Failed to replace route:", err);
      });
    }
  },
  { deep: true }
);

watch(selectedDepartment, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    const currentRoute = router.currentRoute.value;
    const newQuery = {
      ...currentRoute.query,
      department: newValue.name || "",
      department_id: newValue.id,
      report_id: "",
    };
    router.replace({ query: newQuery }).catch((err) => {
      console.error("Failed to replace route:", err);
    });
  }
});

const updateDepartmentFromRoute = () => {
  if (route.query.department) {
    const dept = departments.value.find(
      (d) => d.name === route.query.department
    );
    
    if (dept) {
      selectedDepartment.value = {
        name: dept.name,
        id: dept.id
      };
    } else if (route.query.department_id) {
      selectedDepartment.value = {
        name: String(route.query.department),
        id: Number(route.query.department_id)
      };
    }
  }
};

const validFields = computed(() => {
  if (
    route.query.to !== undefined &&
    route.query.from !== undefined &&
    route.query.department !== undefined
  ) {
    dateRange.value = [route.query.from, route.query.to];
    updateDepartmentFromRoute();
    return (
      route.query.to !== "" &&
      route.query.from !== "" &&
      route.query.department !== ""
    );
  }
  return false;
});

onMounted(() => {
  departments.value = $metadata.departments;
  const allDepartmentExists = departments.value.some(
    (department: Department) => department.name == "All"
  );
  if (!allDepartmentExists) {
    departments.value.push({ id: 0, name: "All" });
  }
  departments.value.sort((a: Department, b: Department) => {
    const nameA = a.name.toUpperCase();
    const nameB = b.name.toUpperCase();
    if (nameA < nameB) {
      return -1;
    }
    if (nameA > nameB) {
      return 1;
    }
    return 0;
  });
  
  updateDepartmentFromRoute();
  
  if (validFields.value) {
    generateReport();
  } else {
    loading.value = false;
  }
});
</script>
