<template>
    <div class="py-5 px-5">

        <CoreBreadcrumb :pages="pages"/>

        <div class="flex items-center justify-between py-5">
        
            <h3 class="text-2xl font-semibold">{{ page }}</h3>
    
        </div>

        <div class="flex justify-between w-full py-2 mb-2">
            <div class="flex items-center space-x-3">
                <div class="w-96">
                    <datepicker placeholder="Select start & end date to filter worksheets" input-classes="border rounded px-2 py-1.5 block focus:outline-none focus:ring-2 focus:ring-sky-500 transition duration-150 focus:border-none" range as-single v-model="dateRange"/> 
                </div>
            </div>
            <CoreSearchBar :search="search"/>
        </div>

        <CoreDatatable :headers="headers" :data="worksheets">
        
            <template v-slot:actions="{ item }">
    
                <div class="py-2 flex items-center space-x-2">
        
                    <CoreActionButton :click="(() => {})" @click="handleClick(item.worksheet_id)" color="primary" text="View" :icon="viewIcon"/>

                    <WorksheetsPrintResults :data="item"/>
            
                </div>
            
            </template>
            
        </CoreDatatable>

    </div>
</template>

<script lang="ts">

import { ArrowTopRightOnSquareIcon, MagnifyingGlassIcon } from '@heroicons/vue/24/solid/index.js';


definePageMeta({
    layout: 'dashboard'
})

export default {
    data() {
        return {
            dateRange: new Array(),
            page: "Worksheets" as string,
            search: "" as string,
            worksheets: [
                {
                    id: 1,
                    worksheet_id: 1001,
                    device: 'Blood analyzer',
                    status: 'completed',
                    total_tests: 50,
                    created_at: '2022-01-01T09:00:00Z',
                    started_at: '2022-01-01T10:00:00Z',
                    completed_at: '2022-01-01T12:00:00Z',
                    verified_at: '2022-01-01T13:00:00Z'
                },
                {
                    id: 2,
                    worksheet_id: 1001,
                    device: 'Urine analyzer',
                    status: 'verified',
                    total_tests: 25,
                    created_at: '2022-01-02T09:00:00Z',
                    started_at: '2022-01-02T10:00:00Z',
                    completed_at: '2022-01-02T11:30:00Z',
                    verified_at: '2022-01-02T12:00:00Z'
                },
                {
                    id: 3,
                    worksheet_id: 1002,
                    device: 'Microscope',
                    status: 'started',
                    total_tests: 100,
                    created_at: '2022-01-03T09:00:00Z',
                    started_at: '2022-01-03T10:30:00Z',
                    completed_at: '2022-01-03T10:30:00Z',
                    verified_at: '2022-01-03T10:30:00Z'
                },
                {
                    id: 4,
                    worksheet_id: 1002,
                    device: 'Blood analyzer',
                    status: 'created',
                    total_tests: 50,
                    created_at: '2022-01-04T09:00:00Z',
                    started_at: '2022-01-03T10:30:00Z',
                    completed_at: '2022-01-03T10:30:00Z',
                    verified_at: '2022-01-03T10:30:00Z'
                },
                {
                    id: 5,
                    worksheet_id: 1003,
                    device: 'Urine analyzer',
                    status: 'completed',
                    total_tests: 25,
                    created_at: '2022-01-05T09:00:00Z',
                    started_at: '2022-01-05T10:00:00Z',
                    completed_at: '2022-01-05T11:00:00Z',
                    verified_at: '2022-01-05T12:00:00Z'
                },
                {
                    id: 6,
                    worksheet_id: 1003,
                    device: 'Microscope',
                    status: 'verified',
                    total_tests: 100,
                    created_at: '2022-01-06T09:00:00Z',
                    started_at: '2022-01-06T10:30:00Z',
                    completed_at: '2022-01-06T12:00:00Z',
                    verified_at: '2022-01-06T13:00:00Z'
                },
                {
                    id: 7,
                    worksheet_id: 1004,
                    device: 'Blood analyzer',
                    status: 'started',
                    total_tests: 50,
                    created_at: '2022-01-07T09:00:00Z',
                    started_at: '2022-01-07T10:00:00Z',
                    completed_at: '2022-01-03T10:30:00Z',
                    verified_at: '2022-01-03T10:30:00Z'
                }],
            pages: [
                {
                    name: "Home",
                    link: "/home"
                }
            ] as Array<{
                name: String;
                link: String;
            }>,
            viewIcon: ArrowTopRightOnSquareIcon
        };
    },
    components: { MagnifyingGlassIcon },
    setup(){
        const headers = [
            { text: "ID", value: "id", sortable: true },
            { text: "Worksheet No", value: "worksheet_id", sortable: true },
            { text: "Device", value: "device" },
            { text: "Status", value: "status" },
            { text: "Total Tests", value: "total_tests" },
            { text: "Created", value: "created_at" },
            { text: "Started", value: "started_at" },
            { text: "Completed", value: "completed_at" },
            { text: "Verified", value: "verified_at" },
            { text: "Actions", value: "actions" }
        ];

        return { headers }
    },
    methods: {
        handleClick(id: any){
            this.$router.push(`/worksheets/${id}`)
        }
    }
}
</script>

<style>

</style>