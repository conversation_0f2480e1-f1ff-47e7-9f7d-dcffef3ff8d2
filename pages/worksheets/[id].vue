<template>
    <div class="px-5 py-5">

        <CoreBreadcrumb :pages="pages"/>

            <div class="flex items-center justify-between py-5">
            
            <h3 class="text-2xl font-semibold">{{ page }}</h3>

        </div>

        <div class="flex justify-between w-full py-2 mb-2">
            <div></div>
            <CoreSearchBar :search="search"/>
        </div>

        <div class="mt-2 space-y-3">

        <CoreDatatable :headers="headers" :data="worksheets">

            <template v-slot:actions="{ item }">

                <div class="py-2 flex items-center space-x-2">

                    <CoreActionButton color="error" :text="`Re-run`" :icon="rerunIcon" />
            
                </div>
            
            </template>
            
        </CoreDatatable>

        </div>

        <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 rounded bg-gray-50">
            <CoreActionButton :icon="confirmIcon" text="Verify worksheet" color="success"/>
            <CoreActionButton :icon="printIcon" text="Print results" color="success"/>
        </div>

    </div>
</template>

<script lang="ts">

import { ArrowPathIcon, CheckCircleIcon, PrinterIcon } from '@heroicons/vue/24/solid/index.js';


definePageMeta({
    layout: 'dashboard',
    key: route => route.fullPath
})


export default{
    data(){

        return{
            pages: [
                {
                    name: "Home",
                    link: "/home"
                },
                {
                    name: "Worksheets",
                    link: "/worksheets"
                }
            ] as Array<{
                name: String;
                link: String;
            }>,
            search: "" as string,
            page: `Tests for worksheet no:: ${this.$route.params.id}` as string,
            confirmIcon: CheckCircleIcon,
            printIcon: PrinterIcon,
            rerunIcon: ArrowPathIcon,
            worksheets: [
                {
                    "tracking_no": "TN001",
                    "patient_no": "P001",
                    "random_id": "RID001",
                    "test_type": "PCR",
                    "specimen": "Nasopharyngeal swab",
                    "date_created": "2022-01-15",
                    "facility": "Hospital A",
                    "status": "Completed",
                    "result": "Positive"
                },
                {
                    "tracking_no": "TN002",
                    "patient_no": "P002",
                    "random_id": "RID002",
                    "test_type": "Antigen",
                    "specimen": "Saliva",
                    "date_created": "2022-01-16",
                    "facility": "Clinic B",
                    "status": "Completed",
                    "result": "Negative"
                },
                {
                    "tracking_no": "TN003",
                    "patient_no": "P003",
                    "random_id": "RID003",
                    "test_type": "PCR",
                    "specimen": "Nasopharyngeal swab",
                    "date_created": "2022-01-17",
                    "facility": "Laboratory C",
                    "status": "In Progress",
                    "result": null
                }
            ]
        }
    },
    setup(){
        const headers = [
            { text: "Tracking Number", value: "tracking_no", sortable: true },
            { text: "Patient Name", value: "patient_no", sortable: true },
            { text: "ARV Number", value: "random_id" },
            { text: "Test", value: "test_type" },
            { text: "Specimen", value: "specimen" },
            { text: "Date Created", value: "date_created" },
            { text: "Facility", value: "facility" },
            { text: "Status", value: "status" },
            { text: "Result", value: "result" },
            { text: "Actions", value: "actions" }
        ];

        return { headers }
    }
}
</script>