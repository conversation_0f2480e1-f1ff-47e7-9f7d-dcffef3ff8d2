<template>
  <div class="px-5 py-5">

    <CoreBreadcrumb :pages="pages"/>

    <div class="flex justify-between w-full px-2 py-2 mb-2 mt-3">
      <div class="flex items-center space-x-3">
        <h3 class="text-2xl font-semibold">New Early Infant Diagnosis Entry</h3>
      </div>
      <EidRemoteOrder/>
    </div>

    <CoreStepper :steps="4">
      <template v-slot="{ step }">
            <div v-if="step === 1">
                <div class="rounded border">
                    <div class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-medium text-md">
                        Section 1: Health Facility Information
                    </div>
                    <div>
                        <div class="w-full flex items-center px-5 space-x-3 py-5">
                            <div class="w-1/2 flex flex-col space-y-2">
                                <label class="font-medium">District</label>
                                <CoreDropdown :items="districts" :model-value="selectedDistrict"/>
                            </div>
                            <div class="w-1/2 flex flex-col space-y-2">
                                <label class="font-medium">Facility</label>
                                <CoreDropdown :items="facilities" :model-value="selectedFacility"/>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="rounded border mt-5">
                    <div class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-medium text-md">
                        Section 2: Patient Information
                    </div>
                    <div class="space-y-3 pb-10">
                        <div class="w-full flex items-center px-5 space-x-3 mt-3">
                            <div class="w-1/2 flex flex-col space-y-2">
                                <label class="font-medium">Patient Surname</label>
                                <input v-model="surname" type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                            </div>
                            <div class="w-1/2 flex flex-col space-y-2">
                                <label class="font-medium">Patient First Name</label>
                                <input v-model="firstname" type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                            </div>
                        </div>
                        <div class="w-full flex items-center px-5 space-x-3">
                            <div class="w-1/2 flex flex-col space-y-2 mb-4">
                                <label class="font-medium">Patient ID</label>
                                <div id="otp" class="flex flex-row text-center mb-3">
                                <input
                                    class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="first" maxlength="1"
                                    v-for="(item, index) in patientID"
                                    :key="index"
                                    v-model="patientID[index]"
                                    @input="((event: InputEvent) => handlePatientIdInput(event as InputEvent, index))"
                                    :tabindex="index + 1"
                                    :ref="'input' + index"
                                    :onfocus="(event: any) => { event.target.select() }"
                                />
                                </div>
                            </div>
                            <div class="w-1/2 flex flex-col space-y-2">
                                <label class="font-medium">Date of Birth</label>
                                <div class="w-full">
                                    <datepicker range :placeholder="new Date().toLocaleDateString()" input-classes="border rounded px-2 py-1.5 block focus:outline-none focus:ring-2 focus:ring-sky-500 transition duration-150 focus:border-none" as-single :shortcuts="false" v-model="dateOfBirth" :formatter="DATE_FORMATter"/>
                                </div>
                            </div>
                        </div>
                        <div class="w-full flex items-center px-5 space-x-3 mt-3">
                            <div class="w-1/2 flex flex-col space-y-2">
                                <label class="font-medium">Gender</label>
                                <CoreDropdown :items="gender" :model-value="genderSelected"/>
                            </div>
                            <div class="w-1/2 flex flex-col space-y-2">
                                <label class="font-medium">Patient/Gurdian Phone</label>
                                <CorePhonePicker :phone="gurdianPhone"/>
                            </div>
                        </div>
                        <div class="w-full flex items-center px-5 space-x-3 mt-3">
                            <div class="w-1/2 flex flex-col space-y-2">
                                <label class="font-medium">Date Sample Drawn</label>
                                <div class="w-full">
                                    <datepicker range :placeholder="new Date().toLocaleDateString()" input-classes="border rounded px-2 py-1.5 block focus:outline-none focus:ring-2 focus:ring-sky-500 transition duration-150 focus:border-none" as-single :shortcuts="false" v-model="dateSampleDrawn" :formatter="DATE_FORMATter"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
          </div>

          <!-- step 2: section 3 and 4-->
          <div v-if="step === 2">
              <div class="rounded border">
                  <div class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-medium text-md">
                      Section 3: Reason For Test
                  </div>
                  <div class="px-2">
                      <div class="px-2 py-2">
                          <label for="radio-group">Select reason:</label>
                          <div id="radio-group" class="mt-2 flex flex-col space-y-2">
                              <label class="flex items-center">
                                  <input type="radio" v-model="reasonForTest" value="routine" class="mr-2">
                                  Routine
                              </label>
                              <label class="flex items-center">
                                  <input type="radio" v-model="reasonForTest" value="targeted" class="mr-2">
                                  Targeted
                              </label>
                              <label class="flex items-center">
                                  <input type="radio" v-model="reasonForTest" value="follow up after highVL" class="mr-2">
                                  Follow up after highVL
                              </label>
                              <label class="flex items-center">
                                  <input type="radio" v-model="reasonForTest" value="repeat" class="mr-2">
                                  Repeat
                              </label>
                          </div>
                      </div>
                  </div>
              </div>

              <div class="rounded border mt-5">
                  <div class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-medium text-md">
                      Section 4: Patient & Sample Details
                  </div>
                  <div class="px-2">
                      <div class="px-2 py-2">
                          <label class="font-medium">ART Initiation Date</label>
                          <div class="w-72">
                              <datepicker range
                                :placeholder="new Date().toDateString()"
                                input-class-name="border border-gray-50 rounded px-2 py-1.5 block focus:outline-none transition duration-150"
                                as-single
                                :shortcuts="true"
                                v-model="artInitiationDate"
                                :text-input="true"
                                :year-range="dateRange"
                                :max-date="new Date()"
                                :ignore-time-validation="true"
                                :teleport="true"
                                :enable-time-picker="false"
                                :formatter="DATE_FORMATter"
                            />
                          </div>
                      </div>
                      <div class="px-2 py-2">
                          <label class="font-medium">Sample Type:</label>
                          <div id="radio-group" class="mt-2 flex flex-col space-y-2">
                              <label class="flex items-center">
                                  <input type="radio" v-model="sampleType" value="dbs" class="mr-2">
                                  DBS (using Capillary Tube)
                              </label>
                              <label class="flex items-center">
                                  <input type="radio" v-model="sampleType" value="plasma" class="mr-2">
                                  Plasma
                              </label>
                          </div>
                      </div>
                      <div class="px-2 py-2">
                          <label for="radio-group" class="mb-3 mt-2 font-medium">Current ART Regimen:</label>
                          <div class="grid grid-cols-7 w-1/2 mt-2">
                              <div v-for="regimen in regimens.one" class="col-span-1 bg-purple-200 px-4 py-4 border-t border-b border-l border-purple-100">
                                  <label class="flex items-center">
                                      <input type="radio" v-model="selectedRegimen" :value="regimen.value" class="mr-2">
                                      {{ regimen.value }}
                                  </label>
                              </div>
                              <div v-for="regimen in regimens.two" class="col-span-1 bg-yellow-200 px-4 py-4 border-t border-b border-l border-yellow-100">
                                  <label class="flex items-center">
                                      <input type="radio" v-model="selectedRegimen" :value="regimen.value" class="mr-2">
                                      {{ regimen.value }}
                                  </label>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>

          <!-- step 2: section 3 and 4-->
          <div v-if="step === 3">
              <div class="rounded border mt-5">
                  <div class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-medium text-md">
                      Section 5: Details of Person Collecting Sample
                  </div>

                  <div class="py-5 px-5">
                      <div class="w-full flex items-center space-x-3 mt-3">
                          <div class="w-1/2 flex flex-col space-y-2">
                              <label class="font-medium">Surname</label>
                              <input v-model="collectorSurname" type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                          </div>
                          <div class="w-1/2 flex flex-col space-y-2">
                              <label class="font-medium">First Name</label>
                              <input v-model="collectorFirstname" type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                          </div>
                      </div>
                      <div class="w-full flex items-center space-x-3 mt-3">
                          <div class="w-1/2 flex flex-col space-y-2">
                              <label class="font-medium">Phone Number</label>
                              <CorePhonePicker :phone="collectorPhone"/>
                          </div>
                          <div class="w-1/2 flex flex-col space-y-2">
                              <label class="font-medium">HTC Provider ID</label>
                              <div id="htc_provider_id" class="flex flex-row text-center mb-3">
                                <input
                                  class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="first" maxlength="1"
                                  v-for="(item, index) in htcProviderId"
                                  :key="index"
                                  v-model="htcProviderId[index]"
                                  @input="((event: InputEvent) => handleHTCProviderIdInput(event as InputEvent, index))"
                                  :tabindex="index + 1"
                                  :ref="'input' + index"
                                  :onfocus="(event: any) => { event.target.select() }"
                                />
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>

          <!-- step 2: section 3 and 4-->
          <div v-if="step === 4">
              <div class="rounded border mt-5">
                  <div class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-semibold text-md">
                      Section 6: Confirmation
                  </div>

                  <div class="px-5 py-5">
                    <div class="bg-orange-50 text-orange-400 font-medium flex items-center px-2 py-2 rounded">
                        <InformationCircleIcon class="w-5 h-5 mr-3"/> Please make sure you have entered the correct information as they appear on the EID &amp; Viral Load Requisition Form
                    </div>
                    <div class="mb-2">
                        <div class="px-2 py-2 bg-gray-50 mt-5 font-semibold">
                            Health Facility Information
                        </div>
                        <div class="w-full grid grid-cols-2">
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">District -:-</p>
                                <p class="text-gray-800">{{ selectedDistrict.name }}</p>
                            </div>
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">Facility -:-</p>
                                <p class="text-gray-800">{{ selectedFacility.name }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-2">
                        <div class="px-2 py-2 bg-gray-50 mt-5 font-semibold">
                            Patient Information
                        </div>
                        <div class="w-full grid grid-cols-3 gap-4 pt-2">
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">Patient Surname -:-</p>
                                <p class="text-gray-800">{{ surname }}</p>
                            </div>
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">Patient first name -:-</p>
                                <p class="text-gray-800">{{ firstname }}</p>
                            </div>
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">Patient ID -:-</p>
                                <p class="text-gray-800">{{ patientID.filter(item => item !== "").join("") }}</p>
                            </div>
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">Date Of Birth -:-</p>
                                <p class="text-gray-800">{{ dateOfBirth }}</p>
                            </div>
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">Gender -:-</p>
                                <p class="text-gray-800">{{ genderSelected.name }}</p>
                            </div>
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">Patient/Guardian phone -:-</p>
                                <p class="text-gray-800">{{ gurdianPhone }}</p>
                            </div>
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">Date Sample Drawn -:-</p>
                                <p class="text-gray-800">{{ dateSampleDrawn }}</p>
                            </div>

                        </div>
                    </div>

                    <div class="mb-2">
                        <div class="px-2 py-2 bg-gray-50 mt-5 font-semibold">
                            Reason For Test
                        </div>
                        <div class="w-full grid grid-cols-2">
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">Select Reason -:-</p>
                                <p class="text-gray-800">{{ reasonForTest }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-2">
                        <div class="px-2 py-2 bg-gray-50 mt-5 font-semibold">
                            Patient & Sample Details
                        </div>
                        <div class="w-full grid grid-cols-2 mt-2 gap-4">
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">ART Initiation Date -:-</p>
                                <p class="text-gray-800">{{ artInitiationDate }}</p>
                            </div>
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">Sample type -:-</p>
                                <p class="text-gray-800">{{ sampleType }}</p>
                            </div>
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">Current ART Regimen -:-</p>
                                <p class="text-gray-800">{{ selectedRegimen }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-2">
                        <div class="px-2 py-2 bg-gray-50 mt-5 font-semibold">
                            Details of Person Collecting Sample
                        </div>
                        <div class="w-full grid grid-cols-2 mt-2 gap-4">
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">Surname -:-</p>
                                <p class="text-gray-800">{{ collectorSurname }}</p>
                            </div>
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">First name -:-</p>
                                <p class="text-gray-800">{{ collectorFirstname }}</p>
                            </div>
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">Phone number -:-</p>
                                <p class="text-gray-800">{{ collectorPhone }}</p>
                            </div>
                            <div class="flex items-center space-x-3 mt-2 col-span-1">
                                <p class="text-base font-medium">HTC Provider ID -:-</p>
                                <p class="text-gray-800">{{ htcProviderId.filter(item => item !== "").join("") }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center space-x-3 justify-end mt-5">
                        <CoreActionButton :icon="clearIcon" :text="'Clear form'"/>
                        <CoreActionButton :icon="rejectIcon" :text="'Reject sample'" color="error"/>
                        <CoreActionButton :icon="saveIcon" :text="'Accept sample'" color="success"/>
                    </div>
                  </div>
              </div>
          </div>
      </template>

  </CoreStepper>
  </div>
</template>

<script lang="ts">

import { ArrowDownTrayIcon, BackspaceIcon, ChevronLeftIcon, ChevronRightIcon, InformationCircleIcon, QrCodeIcon, XMarkIcon } from '@heroicons/vue/24/solid/index.js';
import { Page } from '@/types';

definePageMeta({
  layout: 'dashboard'
})

export default {

  data() {
    return {
      leftIcon: ChevronLeftIcon,
      rightIcon: ChevronRightIcon,
      saveIcon: ArrowDownTrayIcon,
      clearIcon: BackspaceIcon,
      rejectIcon: XMarkIcon,
      dateFrom: new Array(),
      artInitiationDate: "",
      surname: "",
      firstname: "",
      dateOfBirth: "",
      search: "",
      dateSampleDrawn: "",
      genderSelected: { name: "Male" },
      gender: [
        {
          name: "Male"
        },
        {
          name: "Female Non-Preg./ Bf."
        },
        {
          name: "Female Pregnant"
        },
        {
          name: "Female Breastfeeding"
        }
      ],
      patientID: new Array(12).fill(''),
      htcProviderId: new Array(10).fill(''),
      gurdianPhone: "",
      selectedDistrict: { name: "Lilongwe" },
      districts: [
          { name: 'Blantyre' },
          { name: 'Lilongwe' },
          { name: 'Mzuzu' },
          { name: 'Zomba' },
          { name: 'Mchinji' },
          { name: 'Dedza' },
          { name: 'Nkhotakota' },
          { name: 'Nsanje' },
          { name: 'Salima' },
          { name: 'Karonga' }
      ],
      selectedFacility: { name: "Queen Elizabeth Central Hospital" },
      facilities: [
          { name: 'Kamuzu Central Hospital', city: 'Lilongwe' },
          { name: 'Queen Elizabeth Central Hospital', city: 'Blantyre' },
          { name: 'Mzuzu Central Hospital', city: 'Mzuzu' },
          { name: 'Zomba Central Hospital', city: 'Zomba' },
          { name: 'Dedza District Hospital', city: 'Dedza' },
          { name: 'Nkhotakota District Hospital', city: 'Nkhotakota' },
          { name: 'Mulanje District Hospital', city: 'Mulanje' },
          { name: 'Balaka District Hospital', city: 'Balaka' },
          { name: 'Salima District Hospital', city: 'Salima' },
          { name: 'Machinga District Hospital', city: 'Machinga' }
      ],
      reasonForTest: "",
      pages: [
        {
          name: "Home",
          link: "/home"
        },
        {
          name: "Sample Entry",
          link: "#"
        }
      ] as Page,
      regimens: {
          one: [
              {
                  value: "0P"
              },
              {
                  value: "2P"
              },
              {
                  value: "4P"
              },
              {
                  value: "9P"
              },
              {
                  value: "11P"
              },
              {
                  value: "14P"
              },
              {
                  value: "15P"
              },
              {
                  value: "16P"
              },
          ],
          two: [
              {
                  value: "0A"
              },
              {
                  value: "2A"
              },
              {
                  value: "4A"
              },
              {
                  value: "5A"
              },
              {
                  value: "6A"
              },
              {
                  value: "7A"
              },
              {
                  value: "8A"
              },
              {
                  value: "9A"
              },
              {
                  value: "10A"
              },
              {
                  value: "11A"
              },
              {
                  value: "12A"
              },
              {
                  value: "13A"
              },
              {
                  value: "14A"
              },
              {
                  value: "15A"
              },
              {
                  value: "NS"
              },
          ]
      },
      selectedRegimen: "",
      sampleType: "",
      collectorSurname: "",
      collectorFirstname: "",
      collectorPhone: ""
    };
  },

  components: { QrCodeIcon, InformationCircleIcon },

  methods: {

    handlePatientIdInput(event: InputEvent, index: number): void {

      if (event.target instanceof HTMLInputElement && event.target.value.length === 1 && index < this.patientID.length - 1) {
        const nextInput = this.$refs['input' + (index + 1)] as HTMLInputElement[];
        if (nextInput.length > 0) {
          nextInput[0].focus();
        }
      }

    },

    handleHTCProviderIdInput(event: InputEvent, index: number): void {

      if (event.target instanceof HTMLInputElement && event.target.value.length === 1 && index < this.htcProviderId.length - 1) {
        const nextInput = this.$refs['input' + (index + 1)] as HTMLInputElement[];
        if (nextInput.length > 0) {
          nextInput[0].focus();
        }
      }

    },

  }
}
</script>

<style>

</style>
