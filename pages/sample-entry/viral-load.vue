<template>
    <div class="px-5 py-5">

        <CoreBreadcrumb :pages="pages"/>

        <div class="flex justify-between w-full px-2 py-2 mb-2 mt-3">
            <div class="flex items-center space-x-3">
                <h3 class="text-2xl font-semibold">New viral load entry</h3>
            </div>
            <div class="flex items-center border rounded">
                <div class="border-r px-2 p-2 bg-gray-50">
                    <QrCodeIcon class="w-5 h-5"/>
                </div>
                <input type="text" id="email-address-icon" class="px-2 block focus:border-none outline-none transition duration-150 text-sm" placeholder="Scan barcode">
            </div>
        </div>

        <CoreStepper :steps="4">
            <template v-slot="{ step }">
                <div v-if="step === 1">
                    <div class="rounded border">
                        <div class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-medium text-md">
                            Section 1: Health Facility Information
                        </div>
                        <div>
                            <div class="w-full flex items-center px-5 space-x-3 py-5">
                                <div class="w-1/2 flex flex-col space-y-2">
                                    <label class="font-medium">District</label>
                                    <CoreDropdown :items="districts" :model-value="selectedDistrict"/>
                                </div>
                                <div class="w-1/2 flex flex-col space-y-2">
                                    <label class="font-medium">Facility</label>
                                    <CoreDropdown :items="facilities" :model-value="selectedFacility"/>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="rounded border mt-5">
                        <div class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-medium text-md">
                            Section 2: Patient Information
                        </div>
                        <div class="space-y-3 pb-10">
                            <div class="w-full flex items-center px-5 space-x-3 mt-3">
                                <div class="w-1/2 flex flex-col space-y-2">
                                    <label class="font-medium">Patient Surname</label>
                                    <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                                </div>
                                <div class="w-1/2 flex flex-col space-y-2">
                                    <label class="font-medium">Patient First Name</label>
                                    <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                                </div>
                            </div>
                            <div class="w-full flex items-center px-5 space-x-3">
                                <div class="w-1/2 flex flex-col space-y-2 mb-4">
                                    <label class="font-medium">Patient ID</label>
                                    <div id="otp" class="flex flex-row text-center mb-3">
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded" type="text" id="first" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded" type="text" id="second" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded" type="text" id="third" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded" type="text" id="fourth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded" type="text" id="fifth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded" type="text" id="sixth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded" type="text" id="sixth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded" type="text" id="sixth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded" type="text" id="sixth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded" type="text" id="sixth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded" type="text" id="sixth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded" type="text" id="sixth" maxlength="1" />
                                    </div>
                                </div>
                                <div class="w-1/2 flex flex-col space-y-2">
                                    <label class="font-medium">Date of Birth</label>
                                    <div class="w-full">
                                        <datepicker
                                            :placeholder="new Date().toDateString()"
                                            input-class-name="border border-gray-50 rounded px-2 py-1.5 block focus:outline-none transition duration-150"
                                            as-single
                                            :shortcuts="true"
                                            v-model="dateFrom"
                                            :text-input="true"
                                            :year-range="dateRange"
                                            :max-date="new Date()"
                                            :ignore-time-validation="true"
                                            :teleport="true"
                                            :enable-time-picker="false"
                                            :formatter="DATE_FORMATter"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="w-full flex items-center px-5 space-x-3 mt-3">
                                <div class="w-1/2 flex flex-col space-y-2">
                                    <label class="font-medium">Gender</label>
                                    <CoreDropdown :items="gender" :model-value="genderSelected"/>
                                </div>
                                <div class="w-1/2 flex flex-col space-y-2">
                                    <label class="font-medium">Patient/Gurdian Phone</label>
                                    <CorePhonePicker :phone="gurdianPhone"/>
                                </div>
                            </div>
                            <div class="w-full flex items-center px-5 space-x-3 mt-3">
                                <div class="w-1/2 flex flex-col space-y-2">
                                    <label class="font-medium">Date Sample Drawn</label>
                                    <div class="w-full">
                                        <datepicker
                                            :placeholder="new Date().toDateString()"
                                            input-class-name="border border-gray-50 rounded px-2 py-1.5 block focus:outline-none transition duration-150"
                                            as-single
                                            :shortcuts="true"
                                            v-model="dateFrom"
                                            :text-input="true"
                                            :year-range="dateRange"
                                            :max-date="new Date()"
                                            :ignore-time-validation="true"
                                            :teleport="true"
                                            :enable-time-picker="false"
                                            :formatter="DATE_FORMATter"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- step 2: section 3 and 4-->
                <div v-if="step === 2">
                    <div class="rounded border">
                        <div class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-medium text-md">
                            Section 3: Reason For Test
                        </div>
                        <div class="px-2">
                            <div class="px-2 py-2">
                                <label for="radio-group">Select reason:</label>
                                <div id="radio-group" class="mt-2 flex flex-col space-y-2">
                                    <label>
                                        <input type="radio" v-model="reasonForTest" value="routine">
                                        Routine
                                    </label>
                                    <label>
                                        <input type="radio" v-model="reasonForTest" value="targeted">
                                        Targeted
                                    </label>
                                    <label>
                                        <input type="radio" v-model="reasonForTest" value="follow up after highVL">
                                        Follow up after highVL
                                    </label>
                                    <label>
                                        <input type="radio" v-model="reasonForTest" value="repeat">
                                        Repeat
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="rounded border mt-5">
                        <div class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-medium text-md">
                            Section 4: Patient & Sample Details
                        </div>
                        <div class="px-2">
                            <div class="px-2 py-2">
                                <label class="font-medium">ART Initiation Date</label>
                                <div class="w-72">
                                    <datepicker
                                        :placeholder="new Date().toDateString()"
                                        input-class-name="border border-gray-50 rounded px-2 py-1.5 block focus:outline-none transition duration-150"
                                        as-single
                                        :shortcuts="true"
                                        v-model="dateFrom"
                                        :text-input="true"
                                        :year-range="dateRange"
                                        :max-date="new Date()"
                                        :ignore-time-validation="true"
                                        :teleport="true"
                                        :enable-time-picker="false"
                                        :formatter="DATE_FORMATter"
                                    />
                                </div>
                            </div>
                            <div class="px-2 py-2">
                                <label class="font-medium">Sample Type:</label>
                                <div id="radio-group" class="mt-2 flex flex-col space-y-2">
                                    <label>
                                        <input type="radio" v-model="sampleType" value="dbs">
                                        DBS (using Capillary Tube)
                                    </label>
                                    <label>
                                        <input type="radio" v-model="sampleType" value="plasma">
                                        Plasma
                                    </label>
                                </div>
                            </div>
                            <div class="px-2 py-2">
                                <label for="radio-group" class="mb-3 mt-2 font-medium">Current ART Regimen:</label>
                                <div class="grid grid-cols-7 w-1/2 mt-2">
                                    <div v-for="regimen in regimens.one" class="col-span-1 bg-purple-200 px-4 py-4 border-t border-b border-l border-purple-100">
                                        <label class="flex items-center">
                                            <input type="radio" v-model="selectedRegimen" :value="regimen.value" class="mr-2">
                                            {{ regimen.value }}
                                        </label>
                                    </div>
                                    <div v-for="regimen in regimens.two" class="col-span-1 bg-yellow-200 px-4 py-4 border-t border-b border-l border-yellow-100">
                                        <label class="flex items-center">
                                            <input type="radio" v-model="selectedRegimen" :value="regimen.value" class="mr-2">
                                            {{ regimen.value }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- step 2: section 3 and 4-->
                <div v-if="step === 3">
                    <div class="rounded border mt-5">
                        <div class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-medium text-md">
                            Section 5: Details of Person Collecting Sample
                        </div>

                        <div class="py-5 px-5">
                            <div class="w-full flex items-center space-x-3 mt-3">
                                <div class="w-1/2 flex flex-col space-y-2">
                                    <label class="font-medium">Surname</label>
                                    <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                                </div>
                                <div class="w-1/2 flex flex-col space-y-2">
                                    <label class="font-medium">First Name</label>
                                    <input type="text" class="w-full border rounded px-1.5 py-1.5 focus:outline-none focus:ring-2 focus:border-none focus:ring-sky-500 transition duration-150"/>
                                </div>
                            </div>
                            <div class="w-full flex items-center space-x-3 mt-3">
                                <div class="w-1/2 flex flex-col space-y-2">
                                    <label class="font-medium">Phone Number</label>
                                    <CorePhonePicker :phone="gurdianPhone"/>
                                </div>
                                <div class="w-1/2 flex flex-col space-y-2">
                                    <label class="font-medium">HTC Provider ID</label>
                                    <div id="htc_provider_id" class="flex flex-row text-center mb-3">
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="first" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="second" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="third" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="fourth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="fifth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="sixth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="sixth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="sixth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="sixth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="sixth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="sixth" maxlength="1" />
                                        <input class="mr-2 border h-10 w-10 text-center form-control rounded focus:ring-2 focus:outline-none focus:ring-sky-500 focus:border-none" type="text" id="sixth" maxlength="1" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- step 2: section 3 and 4-->
                <div v-if="step === 4">
                    <div class="rounded border mt-5">
                        <div class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-medium text-md">
                            Section 6: Confirmation
                        </div>

                        <div class="px-5 py-5">
                            <div class="bg-orange-100 text-orange-500 font-medium flex items-center px-2 py-2 rounded">
                                <InformationCircleIcon class="w-5 h-5 mr-3"/> Please make sure you have entered the correct information as they appear on the EID &amp; Viral Load Requisition Form
                            </div>

                            <div>
                                <div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>

        </CoreStepper>


    </div>
</template>

<script lang="ts">

import { MagnifyingGlassIcon, QrCodeIcon, ChevronLeftIcon, ChevronRightIcon, InformationCircleIcon } from '@heroicons/vue/24/solid/index.js';
import { Page } from '@/types';

definePageMeta({
    layout: 'dashboard'
})

export default {
    data() {
        return {
            leftIcon: ChevronLeftIcon,
            rightIcon: ChevronRightIcon,
            dateFrom: new Array(),
            search: "",
            formatter: ({
              date: 'DD MMM YYYY',
              month: 'MMM'
            }),
            genderSelected: { name: "Male" },
            gender: [
                {
                    name: "Male"
                },
                {
                    name: "Female Non-Preg./ Bf."
                },
                {
                    name: "Female Pregnant"
                },
                {
                    name: "Female Breastfeeding"
                }
            ],
            gurdianPhone: "",
            selectedDistrict: { name: "Lilongwe" },
            districts: [
                { name: 'Blantyre' },
                { name: 'Lilongwe' },
                { name: 'Mzuzu' },
                { name: 'Zomba' },
                { name: 'Mchinji' },
                { name: 'Dedza' },
                { name: 'Nkhotakota' },
                { name: 'Nsanje' },
                { name: 'Salima' },
                { name: 'Karonga' }
            ],
            selectedFacility: { name: "Queen Elizabeth Central Hospital" },
            facilities: [
                { name: 'Kamuzu Central Hospital', city: 'Lilongwe' },
                { name: 'Queen Elizabeth Central Hospital', city: 'Blantyre' },
                { name: 'Mzuzu Central Hospital', city: 'Mzuzu' },
                { name: 'Zomba Central Hospital', city: 'Zomba' },
                { name: 'Dedza District Hospital', city: 'Dedza' },
                { name: 'Nkhotakota District Hospital', city: 'Nkhotakota' },
                { name: 'Mulanje District Hospital', city: 'Mulanje' },
                { name: 'Balaka District Hospital', city: 'Balaka' },
                { name: 'Salima District Hospital', city: 'Salima' },
                { name: 'Machinga District Hospital', city: 'Machinga' }
            ],
            reasonForTest: "",
            pages: [
                {
                    name: "Home",
                    link: "/home"
                },
                {
                    name: "Sample Entry",
                    link: "#"
                }
            ] as Page,
            regimens: {
                one: [
                    {
                        value: "0P"
                    },
                    {
                        value: "2P"
                    },
                    {
                        value: "4P"
                    },
                    {
                        value: "9P"
                    },
                    {
                        value: "11P"
                    },
                    {
                        value: "14P"
                    },
                    {
                        value: "15P"
                    },
                    {
                        value: "16P"
                    },
                ],
                two: [
                    {
                        value: "0A"
                    },
                    {
                        value: "2A"
                    },
                    {
                        value: "4A"
                    },
                    {
                        value: "5A"
                    },
                    {
                        value: "6A"
                    },
                    {
                        value: "7A"
                    },
                    {
                        value: "8A"
                    },
                    {
                        value: "9A"
                    },
                    {
                        value: "10A"
                    },
                    {
                        value: "11A"
                    },
                    {
                        value: "12A"
                    },
                    {
                        value: "13A"
                    },
                    {
                        value: "14A"
                    },
                    {
                        value: "15A"
                    },
                    {
                        value: "NS"
                    },
                ]
            },
            selectedRegimen: "",
            sampleType: ""
        };
    },
    components: { MagnifyingGlassIcon, QrCodeIcon, InformationCircleIcon }
}
</script>

<style scoped>
.datepicker .selected-date {
  background-color: rgb(0, 157, 255) !important;
}
</style>
