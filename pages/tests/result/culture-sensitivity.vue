<template>
  <div class="px-5 py-5">
    <div>
      <CoreBreadcrumb :pages="pages" />

      <div class="flex items-center justify-between py-5">
        <h3 class="text-2xl font-semibold">{{ header }}</h3>
      </div>

      <div
        class="w-full justify-center items-center py-20 mx-auto flex flex-col space-y-2"
        v-if="fetching"
      >
        <CoreLoader />
        <p class="ont-medium text-sky-500">Loading, please wait...</p>
      </div>

      <div v-if="!fetching">
        <div class="grid grid-cols-3 gap-4 py-5">
          <div class="rounded border">
            <div class="px-4 py-2 bg-gray-50 border-b rounded-t">
              <h3 class="font-semibold text-gray-700 uppercase">Patient</h3>
            </div>
            <div class="w-full py-2">
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Patient Number</h3>
                <p>{{ unwrappedDetails?.id }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Name</h3>

                <p>
                  {{
                    `${unwrappedDetails?.client?.first_name} ${unwrappedDetails?.client?.middle_name} ${unwrappedDetails?.client?.last_name}`
                  }}
                </p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Sex</h3>
                <p>{{ details?.client?.sex }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Age</h3>
                <p>{{ calculateAge(details?.client?.date_of_birth || '') }}</p>
              </div>
            </div>
          </div>
          <div class="rounded border">
            <div class="px-4 py-2 bg-gray-50 border-b rounded-t">
              <h3 class="font-semibold text-gray-700 uppercase">Specimen</h3>
            </div>
            <div class="w-full py-2">
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Specimen Type</h3>
                <p>{{ details?.specimen_type }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Tracking Number</h3>
                <p>{{ details?.tracking_number }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Accession Number</h3>
                <p>{{ details?.accession_number }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Status</h3>
                <p>
                  {{
                    details?.order_status
                      ? details.order_status
                          .split("-")
                          .join(" ")
                          .charAt(0)
                          .toUpperCase() +
                        details.order_status.split("-").join(" ").slice(1)
                      : ''
                  }}
                </p>
              </div>
            </div>
          </div>
          <div class="rounded border max-h-72 overflow-y-auto">
            <div class="px-4 py-2 bg-gray-50 border-b rounded-t">
              <h3 class="font-semibold text-gray-700 uppercase">Test</h3>
            </div>
            <div class="w-full py-2">
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Name</h3>
                <p>{{ testNameDisplay === 'full_name' ? details?.test_type_name : details?.test_type_preferred_name }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Date Registered</h3>
                <p>{{ details?.created_date ? moment(details.created_date).format(DATE_FORMAT) : '' }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Receipt Date</h3>
                <p>{{ details?.updated_date ? moment(details.updated_date).format(DATE_FORMAT) : '' }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Test Status</h3>
                <p>
                  {{
                    details?.status
                      ? details.status.charAt(0).toUpperCase() + details.status.slice(1)
                      : ''
                  }}
                </p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Ward/Location</h3>
                <p>{{ details?.requesting_ward }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Physician</h3>
                <p>
                  {{
                    details?.requested_by
                      ? details.requested_by.charAt(0).toUpperCase() +
                        details.requested_by.slice(1)
                      : ''
                  }}
                </p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted bg-gray-50"
              >
                <h3 class="font-medium">Request Origin</h3>
                <p>{{ details?.request_origin }}</p>
              </div>
              <div
                class="w-full flex justify-between px-5 py-2.5 border-b border-dotted"
              >
                <h3 class="font-medium">Registered By</h3>
                <p>{{ details?.registered_by }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-5 gap-4">
          <div class="col-span-2 rounded flex flex-col space-y-2.5 border">
            <div class="bg-gray-50 px-4 py-2 rounded-t border-b">
              <h3 class="font-semibold text-gray-700 uppercase">
                Observations
              </h3>
            </div>
            <FormKit
              type="form"
              submit-label="Update"
              @submit="saveResults"
              :actions="false"
            >
              <div
                class="px-5 py-2 space-y-2"
                v-for="(detail, index) in details?.indicators"
                :key="index"
              >
                <label class="font-medium">{{ detail.name }}</label>

                <CultureSensitivityOrganisms
                  :result="detail.result"
                  @update="updateResult"
                  @apply="loadOrganisms"
                  :data="detail.indicator_ranges"
                />

                <div>
                  <p class="text-base font-medium mb-2">Remarks</p>
                  <RichTextEditor
                    theme="snow"
                    class="editor"
                    v-model:content="remark"
                    contentType="html"
                  ></RichTextEditor>
                </div>

                <CoreActionButton
                  :loading="saving"
                  :click="() => {}"
                  type="submit"
                  :icon="saveIcon"
                  text="Save changes"
                  color="success"
                />
              </div>
            </FormKit>
          </div>
          <div class="col-span-3 rounded flex flex-col space-y-2.5 border">
            <div class="bg-gray-50 px-4 py-2 rounded-t border-b">
              <h3 class="font-semibold text-gray-700 uppercase">
                Culture worksheet
              </h3>
            </div>
            <div class="px-5 py-2 space-y-2">
              <div>
                <h3 class="mb-2 mt-2 font-medium">Observations & Work-Up</h3>
                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="submitObservation"
                  :actions="false"
                  #default=""
                >
                  <table class="w-full text-left border border-dotted">
                    <thead class="bg-gray-50 border-b border-dotted">
                      <tr>
                        <th
                          class="px-2 py-3 font-semibold text-left border-r border-dotted"
                        >
                          Date
                        </th>
                        <th
                          class="px-2 py-3 font-semibold text-left border-r border-dotted"
                        >
                          Lab Tech
                        </th>
                        <th
                          class="px-2 py-3 font-semibold text-left border-r border-dotted"
                        >
                          Remarks
                        </th>
                        <th class="px-2 py-3 font-semibold text-left">
                          Action
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        class="border-b border-dotted"
                        v-for="(
                          observations, index
                        ) in details?.culture_observation"
                        :key="index"
                      >
                        <td class="px-2 py-3 border-r border-dotted">
                          {{ moment(observations.observation_date).fromNow() }}
                        </td>
                        <td class="px-2 py-3 border-r border-dotted">
                          {{
                            `${observations.user.first_name} ${observations.user.middle_name}
                                                ${observations.user.last_name}`
                          }}
                        </td>
                        <td class="px-2 py-3 border-r border-dotted">
                          <FormKit
                            type="textarea"
                            label=""
                            name="Remarks"
                            :value="observations.description"
                            v-if="observations.description === ''"
                            validation="required"
                          />
                          <p v-if="observations.description != ''">
                            {{ observations.description }}
                          </p>
                        </td>
                        <td class="px-2 py-3">
                          <CoreActionButton
                            type="submit"
                            text="Save"
                            color="success"
                            v-if="observations.description === ''"
                            :icon="saveIcon"
                            :loading="loading"
                            :click="() => {}"
                          />
                        </td>
                      </tr>

                      <tr class="border-b border-dotted">
                        <td class="px-2 py-3 border-r border-dotted">
                          {{ moment().fromNow() }}
                        </td>
                        <td class="px-2 py-3 border-r border-dotted">
                          {{
                            `${authStore.user.first_name} ${authStore.user.middle_name} ${authStore.user.last_name}`
                          }}
                        </td>
                        <td class="px-2 py-3 border-r border-dotted">
                          <FormKit
                            type="textarea"
                            label=""
                            name="Remarks"
                            v-model="remarks"
                            validation="required"
                          />
                        </td>
                        <td class="px-2 py-3">
                          <CoreActionButton
                            type="submit"
                            text="Save"
                            color="success"
                            :icon="saveIcon"
                            :loading="loading"
                            :click="() => {}"
                          />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </FormKit>
              </div>
            </div>
          </div>
        </div>

        <div class="rounded border mt-5">
          <div class="bg-gray-50 px-4 py-2 rounded-t border-b">
            <h3 class="font-semibold text-gray-700 uppercase">
              Susceptibility Test Results
            </h3>
          </div>
          <div class="px-5 py-5">
            <div
              v-for="(result, index) in suspceptibilityResult"
              v-bind:key="result.name"
              class="mt-2"
            >
              <div class="flex items-center mb-3 space-x-20">
                <h3 class="font-semibold text-lg">{{ result.name }}</h3>

                <div class="flex items-center space-x-3">
                  <CultureSensitivityDrug
                    :index="index"
                    @update="getUpdatedDrugs"
                  />

                  <CoreActionButton
                    text="Save"
                    color="success"
                    :icon="updateIcon"
                    :loading="updating"
                    :click="() => updateResults(result)"
                  />
                  <CoreActionButton
                    :loading="deleting"
                    :icon="trashIcon"
                    color="error"
                    text="Delete"
                    :click="
                      () => {
                        deleteSusceptivityResult(result.id, index);
                      }
                    "
                  />
                </div>
              </div>
              <table class="text-left border border-dotted">
                <thead class="bg-gray-50 border-b border-dotted">
                  <tr>
                    <th
                      class="px-2 py-3 font-semibold text-left border-r border-dotted"
                    >
                      Drug
                    </th>
                    <th
                      class="px-2 py-3 font-semibold text-left border-r border-dotted"
                    >
                      Zone Diameter(mm)
                    </th>
                    <th
                      class="px-2 py-3 font-semibold text-left border-r border-dotted"
                    >
                      Interpretation (I,S,R)
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    class="border-b border-dotted"
                    v-for="drug in result.drugs"
                    v-bind:key="drug.name"
                  >
                    <td class="px-2 py-3 border-r border-dotted">
                      {{ drug.name }}
                    </td>
                    <td class="px-2 py-3">
                      <CoreDropdown :items="diameters()" v-model="drug.zone" />
                    </td>
                    <td class="px-2 py-3">
                      <CoreDropdown
                        :items="INTERPRETATIONS"
                        v-model="drug.interpretation"
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  XMarkIcon,
  ArrowUpTrayIcon,
  TrashIcon,
  CheckIcon,
  PlusIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import { useAuthStore } from "@/store/auth";
import { usePreference } from "@/composables/usePreference";
import type { Drug, Page, Request, Test, Response } from "@/types";
import Package from "@/package.json";

useHead({
  title: `${Package.name.toUpperCase()} - Enter Culture & Sensitivity Test Results`,
});

definePageMeta({
  layout: "dashboard",
});

const { value: testNameDisplay } = usePreference<string>("test_name_display", "preferred_name");

const cookie = useCookie("token");
const authStore = useAuthStore();
const header = ref<string>("Enter Culture & Sensitivity Results");
const loading = ref<boolean>(false);
const pages = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Tests",
    link: "/tests",
  },
]);
const remarks = ref<string>("");
const remark = ref<string>("");
const saveIcon = CheckIcon;
const updateIcon = ArrowUpTrayIcon;
const trashIcon = TrashIcon;
const details = ref<Test | null>(null);
const organisms = ref<Array<{ id: number; name: string; drugs: Array<Drug> }>>([]);
const suspceptibilityResult = ref<Array<any>>([]);
const updating = ref<boolean>(false);
const saving = ref<boolean>(false);
const deleting = ref<boolean>(false);
const results = ref<any>({});
const testId = ref<string>("");
const accessionNo = ref<string>("");
const fetching = ref<boolean>(false);
const route = useRoute();
const router = useRouter();

onMounted(() => {
  accessionNo.value = `${route.query.accession_number}`;
  testId.value = `${route.query.test_id}`;
  init();
});
const unwrappedDetails = computed(() => details.value);

const init = async (): Promise<void> => {
  fetching.value = true;
  const request: Request = {
    route: `${endpoints.tests}/${testId.value}`,
    method: "GET",
    token: `${cookie.value}`,
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  fetching.value = pending;

  if (data.value) {
    details.value = data.value;
    fetching.value = false;
    suspceptibilityResult.value = data.value.suscept_test_result.map(
      (test: any) => ({
        id: test.organism_id,
        name: test.name,
        drugs: test.drugs.map((drug: any) => ({
          name: drug.name,
          drug_id: drug.drug_id,
          zone: { name: drug.zone === null ? "--select result--" : drug.zone },
          interpretation: {
            name:
              drug.interpretation === "" || drug.interpretation === null
                ? "--select result--"
                : drug.interpretation,
          },
        })),
      })
    );
    remark.value =
      data.value.result_remarks?.value ?? data.value.result_remarks?.value;
  }

  if (error.value) {
    fetching.value = false;
    console.error("error fetcing test details: ", error.value);
  }
};
const loadOrganisms = async (
  value: Array<{ id: number; name: string; drugs: Array<any> }>
): Promise<void> => {
  value.forEach(async (element) => {
    const request: Request = {
      route: `${endpoints.organisms}/${element.id}`,
      method: "GET",
      token: `${cookie.value}`,
    };
    const { data, error }: Response = await fetchRequest(request);
    if (data.value) {
      organisms.value.push(data.value);

      let dataHolder: {
        id: number | null;
        name: string | null;
        drugs: Drug[];
      } = {
        id: null,
        name: null,
        drugs: [],
      };

      organisms.value.forEach((organism) => {
        dataHolder.id = organism.id;
        dataHolder.name = organism.name;

        const organismDrugs = organism.drugs.map((drug) => ({
          name: drug.name,
          drug_id: drug.id,
          zone: { name: "--select result--" },
          interpretation: { name: "--select result--" },
        }));
        dataHolder.drugs = dataHolder.drugs.concat(organismDrugs);
      });
      suspceptibilityResult.value.push(dataHolder);
    }

    if (error.value) {
      console.error(error.value);
    }
  });
};

const diameters = (): Array<{ name: string }> => {
  let diametersArray = new Array<{ name: string }>();
  for (let i = 0; i <= 200; i++) {
    diametersArray.push({ name: `${i}` });
  }
  diametersArray.unshift({name: '--select result--'});
  return diametersArray;
};
const submitObservation = async (): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: endpoints.cultureObservations,
    method: "POST",
    token: `${cookie.value}`,
    body: {
      test_id: testId.value,
      description: remarks.value,
    },
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;

  if (data.value) {
    remarks.value = "";
    loading.value = false;
    init();
    suspceptibilityResult.value = [];
  }

  if (error.value) {
    loading.value = false;
    console.error(error.value);
  }
};

/**
 * @method updateResults load results to backend (new and update data)
 * @returns promise @type void
 * @param details The details of the organism to update
 */
const updateResults = async (details: any): Promise<void> => {
  updating.value = true;
  const filteredResults = suspceptibilityResult.value.filter(
    (organism) => details.name.toLowerCase() === organism.name.toLowerCase()
  );

  filteredResults.map(async (organism) => {
    let filteredDrugs: any[] = [];
    organism.drugs.map((drug: any) => {
      filteredDrugs.push({
        drug_id: drug.drug_id,
        zone: drug.zone.name !== "--select result--" ? drug.zone.name : "",
        interpretation:
          drug.interpretation.name !== "--select result--"
            ? drug.interpretation.name
            : "",
      });
    });
    const request: Request = {
      route: endpoints.drugSusceptibility,
      method: "POST",
      token: `${cookie.value}`,
      body: {
        test_id: testId.value,
        organism_id: organism.id,
        drugs: filteredDrugs.filter(
          (drug: Drug) =>
            String(drug.zone) !== "" && String(drug.interpretation) !== ""
        ),
        status: "completed",
      },
    };

    const { data, error, pending }: Response = await fetchRequest(request);
    updating.value = pending;
    if (data.value) {
      updating.value = false;
      useNuxtApp().$toast.success(
        "Suspceptibility test results updated successfully!"
      );
    }
    if (error.value) {
      console.error(error.value);
      updating.value = false;
      useNuxtApp().$toast.error(ERROR_MESSAGE);
    }
  });
  updating.value = false;
};


const deleteSusceptivityResult = async (
  organismId: string,
  index: number
): Promise<void> => {
  deleting.value = true;
  const request: Request = {
    route: `${endpoints.drugSusceptibility}/delete`,
    method: "PUT",
    token: `${cookie.value}`,
    body: {
      test_id: details.value?.id || 0,
      organism_id: organismId,
    },
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  deleting.value = pending;

  if (data.value) {
    deleting.value = false;
    suspceptibilityResult.value.splice(index, 1);
    useNuxtApp().$toast.success(
      "Suspceptibility test results deleted successfully!"
    );
  }

  if (error.value) {
    console.error(error.value);
    deleting.value = false;
    useNuxtApp().$toast.success("An error has occurred, please try again!");
  }
};


const updateResult = (value: Object): void => {
  results.value = value;
};

const saveResults = async (): Promise<void> => {
  saving.value = true;
  const request: Request = {
    route: `${endpoints.updateResults}`,
    method: "POST",
    token: `${cookie.value}`,
    body: {
      test_id: details.value?.id || 0,
      test_indicators: results.value?.value == null ? [] : [{
        indicator: results.value?.test_indicator_id,
        value: results.value?.value,
      }],
      remarks: remark.value,
    },
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  saving.value = pending;
  if (data.value) {
    saving.value = false;
    useNuxtApp().$toast.success("Test results updated successfully");
    router.back();
  }

  if (error.value) {
    saving.value = false;
    useNuxtApp().$toast.error(ERROR_MESSAGE);
    console.error(error.value);
  }
};

const getUpdatedDrugs = (value: any): void => {
  suspceptibilityResult.value.map((item, index) => {
    if (value.index === index) {
      value.drugs.map((drug: { name: any; id: any }) => {
        item.drugs.push({
          name: drug.name,
          drug_id: drug.id,
          zone: { name: "--select result--" },
          interpretation: { name: "--select result--" },
        });
      });
    }
  });
};
</script>

<style></style>
