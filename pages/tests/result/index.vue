<template>
  <div class="p-5">
    <CoreBreadcrumb :pages="pages" />
    <div class="flex items-center justify-between py-5">
      <h3 class="text-2xl font-semibold">{{ header }}</h3>
    </div>

    <div class="grid grid-cols-5 gap-4">
      <div class="flex flex-col space-y-4 col-span-2 order-2">
        <div class="rounded border">
          <div class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-semibold text-lg">
            Patient Details
          </div>
          <div>
            <div class="w-full flex items-center" style="padding-bottom: 20px">
              <div class="w-full space-y-2.5 py-5">
                <div
                  class="w-full py-2 px-4 bg-gray-50 border-t border-b border-dotted flex justify-between items-center">
                  <p class="font-medium">Patient No</p>
                  <p>{{ patientNo }}</p>
                </div>
                <div class="w-full py-2 px-4 bg-white-100 flex justify-between items-center">
                  <p class="font-medium">Name</p>
                  <p>{{ name }}</p>
                </div>
                <div
                  class="w-full py-2 px-4 bg-gray-50 border-t border-b border-dotted flex justify-between items-center">
                  <p class="font-medium">Age</p>
                  <p>{{ age }}</p>
                </div>
                <div class="w-full py-2 px-4 bg-white-100 flex justify-between items-center">
                  <p class="font-medium">Sex</p>
                  <p>{{ sex_ }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="rounded border">
          <div class="bg-gray-50 px-2 py-2 border-b rounded-tl-lg rounded-tr-lg font-semibold text-lg">
            Specimen Details
          </div>
          <div>
            <div class="w-full flex items-center">
              <div class="w-full space-y-2.5 py-5">
                <div
                  class="w-full py-2 px-4 bg-gray-50 border-t border-b border-dotted flex justify-between items-center">
                  <p class="font-medium">Specimen Type</p>
                  <p>{{ specimenType }}</p>
                </div>
                <div class="w-full py-2 px-4 flex justify-between items-center">
                  <p class="font-medium">Tracking Number</p>
                  <p>{{ trackingNumber }}</p>
                </div>
                <div
                  class="w-full py-2 px-4 bg-gray-50 border-t border-b border-dotted flex justify-between items-center">
                  <p class="font-medium">Accession Number</p>
                  <p>{{ accessionNumber }}</p>
                </div>
                <div class="w-full py-2 px-4 flex justify-between items-center">
                  <p class="font-medium">Status</p>
                  <p>{{ testStatus }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="rounded border">
          <div class="bg-gray-50 px-2 py-2 border-b rounded-tl-lg rounded-tr-lg font-semibold text-lg">
            Test Details
          </div>
          <div class="w-full flex items-center max-h-60 overflow-y-auto">
            <div class="w-full py-5">
              <div class="w-full flex items-center pb-0 pt-72">
                <div class="w-full space-y-2 py-5">
                  <div
                    class="w-full py-2 px-4 bg-gray-50 border-t border-b border-dotted flex justify-between items-center">
                    <p class="font-medium">Test Type</p>
                    <p>{{ testType }}</p>
                  </div>
                  <div class="w-full py-2 px-4 bg-white-100 flex justify-between items-center">
                    <p class="font-medium">Requesting Ward/Location</p>
                    <p>{{ requestingWard }}</p>
                  </div>
                  <div
                    class="w-full py-2 px-4 bg-gray-50 border-t border-b border-dotted flex justify-between items-center">
                    <p class="font-medium">Date Registered</p>
                    <p>{{ moment(dateRegistered).format(DATE_FORMAT) }}</p>
                  </div>
                  <div class="w-full py-2 px-4 bg-white-100 flex justify-between items-center">
                    <p class="font-medium">Receipt Date</p>
                    <p>{{ moment(receiptDate).format(DATE_FORMAT) }}</p>
                  </div>
                  <div
                    class="w-full py-2 px-4 bg-gray-50 border-t border-b border-dotted flex justify-between items-center">
                    <p class="font-medium">Test Status</p>
                    <p>{{ testStatus }}</p>
                  </div>
                  <div class="w-full py-2 px-4 bg-white-100 flex justify-between items-center">
                    <p class="font-medium">Requesting Physician</p>
                    <p>{{ requestingPhysician }}</p>
                  </div>
                  <div
                    class="w-full py-2 px-4 bg-gray-50 border-t border-b border-dotted flex justify-between items-center">
                    <p class="font-medium">Request Origin</p>
                    <p>{{ requestOrigin }}</p>
                  </div>
                  <div class="w-full py-2 px-4 bg-white-100 flex justify-between items-center">
                    <p class="font-medium">Registered By</p>
                    <p>{{ registeredBy }}</p>
                  </div>
                  <div
                    class="w-full py-2 px-4 bg-gray-50 border-t border-b border-dotted flex justify-between items-center">
                    <p class="font-medium">Performed By</p>
                    <p>{{ performedBy }}</p>
                  </div>
                  <div class="w-full py-2 px-4 bg-white-100 flex justify-between items-center">
                    <p class="font-medium">Turn around time</p>
                    <p>
                      {{
                        `${turnAroundTime.value} ${Number(turnAroundTime.value) == 1
                          ? turnAroundTime.unit.toLowerCase().slice(0, -1)
                          : turnAroundTime.unit
                        }`
                      }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="rounded border order-1 col-span-3">
        <div
          class="bg-gray-50 px-2 py-2 border-b rounded-tr rounded-tl font-medium text-md gap-5 flex justify-between items-center">
          <div class="w-full flex items-center gap-5 flex-row">
            <CoreActionButton text="Fetch results" color="warning" :icon="refreshIcon" :loading="fetching"
              :disabled="resultsPresent" :click="() => fetchResults()" v-show="machineOriented" />

            <CoreActionButton text="Print Accession Number" color="primary" :icon="printIcon"
              :click="() => printMachine()" />

            <CoreActionButton text="Authorize" color="success" v-show="shouldDisplayButton({
              status: testStatus,
              completed_by: completedBy,
            })
              " :icon="approveIcon" :click="() => authorise()" :loading="authorizing" />
          </div>

          <div class="mr-2">
            <div class="flex space-x-2 items-center bg-gray-200 rounded">
              <div class="flex items-center bg-gray-200 rounded">
                <button @click="onToggleGrid(true)" :class="{
                  'bg-sky-500 text-white': toggleGrid,
                  'text-gray-500': !toggleGrid,
                }" class="rounded p-2">
                  <ViewColumnsIcon class="w-5 h-5" />
                </button>
                <button @click="onToggleGrid(false)" :class="{
                  'bg-sky-500 text-white': !toggleGrid,
                  'text-gray-500': toggleGrid,
                }" class="rounded p-2">
                  <ListBulletIcon class="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <div v-show="!loading" class="p-5">
          <div :class="{
            'w-full grid grid-cols-3 gap-4': toggleGrid,
            'flex flex-col gap-4': !toggleGrid,
          }" v-if="measures.length !== 0">
            <div :class="{
              'col-span-3 grid grid-cols-3 gap-3': toggleGrid,
              'flex flex-col': !toggleGrid,
            }" v-if="hasDropdownMeasures">
              <div v-for="(measure, index) in dropDownMeasures" :key="index">
                <div>
                  <p class="font-medium" :id="`measure-mapping-id:${measure.id}`">
                    {{ measure.name }}
                  </p>
                  <CoreDropdown :items="measure.ranges" v-model="measure.value" />
                </div>
              </div>
            </div>

            <div :class="{
              'col-span-3 grid grid-cols-3 gap-3': toggleGrid,
              'flex flex-col': !toggleGrid,
            }" v-if="hasTextInputMeasures">
              <div v-for="(measure, index) in textInputMeasures" :key="index">
                <div v-if="
                  measure.type === 'numeric' || measure.type === 'free_text'
                ">
                  <div class="relative flex items-center space-x-1.5"
                    v-if="!measure.originalName?.toLowerCase().includes('date')">
                    <FormKit :type="measure.originalName?.toLowerCase().includes('comment')
                      ? 'textarea'
                      : 'text'
                      " :label="measure.name" v-model.lazy="measure.value.name" :help="`${measureReference(measure.ranges)} ${measure.unit
                        }`" :delay="1000" :id="`measure-mapping-id:${measure.id}`"
                      @input="((e: any) => validateInput(measure, e))" />
                    <div class="absolute top-10 right-2">
                      <CoreLoader v-if="measure.originalName?.toLowerCase() == 'pack no.' && isValidatingInput"
                        :height="20" :width="20" />
                    </div>
                  </div>

                  <div v-else :id="`measure-mapping-id:${measure.id}`">
                    <p class="text-base font-medium mb-2">{{ measure.name }}</p>
                    <datepicker :label="measure.name" input-class-name="datepicker" placeholder="dd/MM/yyyy"
                      v-model="measure.value.name" text-input format="dd/MM/yyyy" :min-date="new Date()" />
                  </div>
                </div>
              </div>
            </div>

            <div :class="{
              'col-span-3': toggleGrid,
              'flex flex-col': !toggleGrid,
            }" v-if="hasRichTextMeasures">
              <div v-for="(measure, index) in richTextEditorMeasures" :key="index">
                <div :id="`measure-mapping-id:${measure.id}`">
                  <p class="font-medium mb-2">{{ measure.name }}</p>
                  <RichTextEditor theme="snow" class="editor" v-model:content="measure.value.name" contentType="html">
                  </RichTextEditor>
                </div>
              </div>
            </div>
          </div>

          <div class="w-full mt-3 relative">
            <p class="font-medium mb-2">Remarks</p>
            <RichTextEditor theme="snow" class="editor" v-model:content="remarks" contentType="html"></RichTextEditor>
          </div>

          <CoreActionButton :loading="updating" text="Update Test Results" color="success" :icon="arrowDownIcon"
            :click="() => updateTestResults()" class="mt-5" v-if="testStatus.toLowerCase() !== 'verified'" />
        </div>

        <ShimmerResults :loading="loading" :isGrid="toggleGrid" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  PrinterIcon,
  HandThumbUpIcon,
  ArrowPathIcon,
  ArrowDownTrayIcon,
  ViewColumnsIcon,
  ListBulletIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import { calculateAge } from "@/utils/functions";
import PrinterService from "@/services/printer_service";
import type { Response, Request, Indicator, Preference } from "@/types";
import { useAuthStore } from "@/store/auth";
import moment from "moment";
import Package from "@/package.json";

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: `${Package.name.toUpperCase()} - Enter Test Results`,
});

const route = useRoute();
const router = useRouter();
const accessionNo = ref(route.query.accession_number as string);
const testId = ref(route.query.test_id as string);
const cookie = useCookie("token");
const nuxtApp = useNuxtApp();
const authStore = useAuthStore();
const { $toast } = useNuxtApp();
const details = ref({} as any);
const header = ref("Enter Test Results");
const patientNo = ref<string>("");
const name = ref<string>("");
const age = ref<string>("");
const sex_ = ref<string>("");
const specimenType = ref<string>("");
const trackingNumber = ref<string>("");
const accessionNumber = ref<string>("");
const testType = ref<string>("");
const requestingWard = ref<string>("");
const dateRegistered = ref<string>("");
const receiptDate = ref<string>("");
const testStatus = ref<string>("");
const requestingPhysician = ref<string>("");
const requestOrigin = ref<string>("");
const registeredBy = ref<string>("");
const performedBy = ref<string>("");
const turnAroundTime = ref({ value: "", unit: "" });
const measures = ref<any[]>([]);
const loading = ref<boolean>(false);
const fetching = ref<boolean>(false);
const updating = ref<boolean>(false);
const isValidatingInput = ref<boolean>(false);
const authorizing = ref<boolean>(false);
const resultsPresent = ref<boolean>(true);
const remarks = ref<string>("");
const machineOriented = ref<boolean>(false);
const completedBy = ref({} as any);
const machineName = ref<string>("");
const statuses = ref({ id: 0, initiator: { id: 0 } });
const refreshIcon = ArrowPathIcon;
const printIcon = PrinterIcon;
const approveIcon = HandThumbUpIcon;
const arrowDownIcon = ArrowDownTrayIcon;

const pages = ref([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Tests",
    link: "/tests",
  },
]);

const { value: testNameDisplay } = usePreference("test_name_display", "preferred_name");

const hasGridLayout = computed((): boolean => {
  const preference = authStore.user.preferences.find(
    (preference: Preference) => preference.name === "grid_or_list_enter_result_view"
  );
  return (preference?.value ?? "grid") === "grid";
});
const toggleGrid = ref<boolean>(hasGridLayout.value);

const textInputMeasures = computed(() => {
  return measures.value.filter(
    (measure: { type: string }) =>
      measure.type === "numeric" || measure.type === "free_text"
  );
});

const dropDownMeasures = computed(() => {
  const filteredMeasures = measures.value.filter(
    (measure: { type: string }) =>
      measure.type === "auto_complete" || measure.type === "alpha_numeric"
  );
  filteredMeasures.forEach((measure: { ranges: { name: string }[] }) => {
    measure.ranges.unshift({ name: "-- select result --" });
  });
  return filteredMeasures;
});

const richTextEditorMeasures = computed(() => {
  return measures.value.filter(
    (measure: { type: string }) => measure.type === "rich_text"
  );
});

const hasTextInputMeasures = computed(() => {
  return (
    measures.value.filter(
      (measure: { type: string }) =>
        measure.type === "numeric" || measure.type === "free_text"
    ).length > 0
  );
});

const hasDropdownMeasures = computed(() => {
  return (
    measures.value.filter(
      (measure: { type: string }) =>
        measure.type === "alpha_numeric" || measure.type === "auto_complete"
    ).length > 0
  );
});

const hasRichTextMeasures = computed(() => {
  return (
    measures.value.filter(
      (measure: { type: string }) => measure.type === "rich_text"
    ).length > 0
  );
});

const onToggleGrid = (value: boolean): void => {
  toggleGrid.value = value;
};

/**
 * @method resultsAvailable check if test results are available
 * @param accessionNumber
 * @param testTypeId
 * @return promise
 */
async function resultsAvailable(accessionNumber: string, testTypeId: number): Promise<void> {
  const request: Request = {
    route: `${endpoints.resultsAvailable}?accession_number=${accessionNumber}&test_type_id=${testTypeId}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error }: Response = await fetchRequest(request);
  if (data.value) {
    resultsPresent.value = data.value.result_available ? false : true;
  }
  if (error.value) {
    console.error(error.value);
  }
}

/**
 * @method fetchResults get results json files via accession numbers
 * @param null
 * @returns promise
 */
async function fetchResults(): Promise<void> {
  fetching.value = true;
  const request: Request = {
    route: `${endpoints.fetchResults}?accession_number=${accessionNo.value}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  fetching.value = pending;
  if (data.value) {
    measures.value.forEach((measure: { id: any; value: { name: string } }) => {
      data.value.forEach(
        (item: { indicator_id: any; value: string; machine_name: string , tracking_number: string}) => {
          if (String(measure.id) === String(item.indicator_id) && item.tracking_number === accessionNo.value) {
            measure.value.name = item.value;
            machineName.value = item.machine_name;
          }
        }
      );
    });
    fetching.value = false;
  }
  if (error.value) {
    console.error(error.value);
    fetching.value = false;
  }
}

/**
 * @method loadTestMesures
 * @param array @type indicator
 * @returns promise
 */
function loadTestMesures(array: Array<Indicator>): void {
  measures.value = [];
  if (array) {
    array.forEach((element: any) => {
      measures.value.push({
        name: String(testNameDisplay.value) == "preferred_name" ? (element.preferred_name ?? element.name) : element.name,
        originalName: element.name,
        id: element.id,
        machine_name: element.result?.machine_name,
        value:
          element.test_indicator_type === "free_text" ||
            element.test_indicator_type === "numeric"
            ? {
              name: element.name.toLowerCase().includes("date")
                ? moment(element.result?.value, "DD/MMM/YYYY").format(
                  "DD/MMM/YYYY"
                )
                : element.result?.value,
            }
            : element.result?.value != null
              ? {
                name: element.name.toLowerCase().includes("date")
                  ? moment(element.result?.value, "DD/MMM/YYYY").format(
                    "DD/MMM/YYYY"
                  )
                  : element.result?.value,
              }
              : element.test_indicator_type == "rich_text"
                ? { name: "" }
                : { name: "-- select result --" },
        type: element.test_indicator_type,
        unit: element.unit,
        ranges: toIndicatorRanges(element.indicator_ranges, element.id),
      });
    });
  }
}

/**
 * @method toIndicatorRanges
 * @returns array @type Object
 */
function toIndicatorRanges(array: Array<Indicator>, IndicatorId: string): any {
  let rangesArray: {
    id: string;
    name: string;
    lowerRange: string;
    upperRange: string;
  }[] = [];
  if (array) {
    array.forEach((element: any) => {
      rangesArray.push({
        id: IndicatorId,
        name: element.value,
        lowerRange: element.lower_range,
        upperRange: element.upper_range,
      });
    });
  }
  return rangesArray;
}

/**
 * @method measuresToIndicators
 * @param null
 * @returns array @type Object
 */
function measuresToIndicators(): Array<Object> {
  let indicators = new Array<{
    indicator: string;
    value: string;
    machine_name: any;
  }>();
  measures.value.forEach((element: any) => {
    indicators.push({
      indicator: element.id,
      value:
        element.type == "free_text"
          ? element.value.name !== undefined
            ? element.name.toLowerCase().includes("date")
              ? moment(element.value.name)
                .format("DD/MMM/YYYY")
                .toLowerCase() !== "invalid date"
                ? moment(element.value.name).format("DD/MMM/YYYY")
                : ""
              : element.value.name
            : null
          : element.value !== undefined
            ? element?.name?.toLowerCase().includes("date")
              ? moment(element.value).format("DD/MMM/YYYY").toLowerCase() !==
                "invalid date"
                ? moment(element.value).format("DD/MMM/YYYY")
                : ""
              : element.value
            : null,
      machine_name:
        element.value !== null || undefined
          ? machineName.value === ""
            ? element.machine_name
            : machineName.value
          : null,
    });
  });
  return indicators;
}

/**
 * @method updateTestResults updating test results
 * @param null
 * @returns promise @type void
 */
async function updateTestResults(): Promise<void> {
  updating.value = true;
  let indicatorValues = measuresToIndicators().map((indicator: any) => {
    if (typeof indicator.value === "object" && indicator.value !== null) {
      indicator.value =
        indicator.value.name == "-- select result --"
          ? null
          : indicator.value.name;
    }
    return indicator;
  });
  const request: Request = {
    route: `${endpoints.updateResults}`,
    method: "POST",
    token: `${cookie.value}`,
    body: {
      test_id: testId.value,
      test_indicators: indicatorValues,
      remarks: remarks.value,
    },
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  updating.value = pending;
  if (data.value) {
    updating.value = false;
    nuxtApp.$toast.success("Test results updated successfully");
    router.back();
  }
  if (error.value) {
    updating.value = false;
    console.error(error.value);
  }
}

async function authorise(): Promise<void> {
  authorizing.value = true;
  const request: Request = {
    route: `${endpoints.testStatus}/${testId.value}/verified`,
    method: "PUT",
    token: `${cookie.value}`,
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  authorizing.value = pending;
  if (data.value) {
    authorizing.value = false;
    nuxtApp.$toast.success("Test status authorized successfully!");
    router.push("/tests");
  }
  if (error.value) {
    console.error(error.value);
    authorizing.value = false;
  }
}

function isCompletedByCurrentUserOrSuperAdmin(item: {
  status?: string;
  completed_by?: any;
}): boolean {
  const currentUser = authStore.user;
  const completedBy = item.completed_by;
  if (completedBy) {
    if (completedBy.id !== currentUser.id) {
      return true;
    } else if (completedBy.is_super_admin === true) {
      return true;
    }
  }
  return false;
}

/**
 * @method shouldDisplayButton
 * @param item
 * @param status
 * @returns boolean
 */
function shouldDisplayButton(item: {
  status: string;
  completed_by?: {};
}): boolean {
  const lowerCaseStatus = item.status.toLowerCase();
  if (lowerCaseStatus === "completed") {
    const isCompletedByCurrentUserOrAdmin =
      isCompletedByCurrentUserOrSuperAdmin(item);
    return isCompletedByCurrentUserOrAdmin;
  }
  return false;
}

/**
 * @method printMachine invokes hilabelprinter service on lbl file download
 * @param nullable
 * @returns promise
 */
async function printMachine(): Promise<void> {
  const { alertConfirmation } = useAlert();
  if (
    await alertConfirmation({
      message: "Do you want to print specimen label?",
    })
  ) {
    await PrinterService.printSpecimenLabel(accessionNo.value);
  }
}

function measureReference(indicatorRanges: Array<any>): string {
  if (indicatorRanges.length === 0) {
    return "";
  }
  const range = indicatorRanges[0];
  if (range.lowerRange == null) {
    return "";
  }
  return `(Ref ${range.lowerRange} - ${range.upperRange})`;
}

/**
 * @method init load test details
 * @param null
 * @returns promise
 */
async function init(): Promise<any> {
  loading.value = true;
  const request: Request = {
    route: `${endpoints.tests}/${testId.value}`,
    method: "GET",
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;
  if (data.value) {
    loading.value = false;
    name.value =
      data.value.client.first_name + " " + data.value.client?.last_name;
    patientNo.value = data.value.id;
    sex_.value = data.value.client?.sex;
    age.value = calculateAge(data.value.client.date_of_birth) + "";
    trackingNumber.value = data.value.tracking_number;
    accessionNumber.value = data.value.accession_number;
    testStatus.value = data.value.status;
    specimenType.value = String(testNameDisplay.value) == "preferred_name" ? data.value.specimen_type_preferred_name : data.value.specimen_type;
    requestingPhysician.value = data.value.requested_by;
    testType.value = String(testNameDisplay.value) == "preferred_name" ? data.value.test_type_preferred_name : data.value.test_type_name;
    requestingWard.value = data.value.requesting_ward;
    dateRegistered.value = data.value.created_date;
    receiptDate.value = data.value.created_date;
    requestOrigin.value = data.value.request_origin;
    turnAroundTime.value = data.value.expected_turn_around_time;
    remarks.value = data.value.result_remarks
      ? data.value.result_remarks.value
      : "";
    loadTestMesures(data.value.indicators);
    data.value.status_trail.map((trail: any) => {
      if (trail.status.name.toLowerCase() === "completed") {
        statuses.value = trail;
        performedBy.value = `${trail.initiator.first_name} ${trail.initiator.last_name}`;
      }
    });
    data.value.status_trail.map((status: any) => {
      if (status.status.name.toLowerCase() === "pending") {
        registeredBy.value = `${status.initiator.first_name} ${status.initiator.last_name}`;
      }
    });
    resultsAvailable(data.value.accession_number, data.value.test_type_id);
    machineOriented.value = data.value.is_machine_oriented;
    completedBy.value = data.value.completed_by;
    details.value = data.value;
  }
  if (error.value) {
    console.error(error.value);
    loading.value = false;
  }
}

const validateInput = async (
  measure: { name: string; originalName: string; value: string },
  event: string
): Promise<void> => {
  const alert = useAlert();
  if (measure.originalName?.toLowerCase() !== "pack no." || event === '') return;
  isValidatingInput.value = true;
  const request: Request = {
    route: `${endpoints.processCrossMatch}/pack_number_exist?pack_number=${event.replaceAll('$', '')}`,
    method: "GET",
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  isValidatingInput.value = pending;
  if (data.value) {
    isValidatingInput.value = false;
    if (data.value.pack_number_exist) {
      $toast.warning("Pack number already exists");
      const continueCurrentPack = await alert.alertConfirmation({
        message: `Pack number already exists, you wish to continue? A suffix ${data.value.next_suffix} will be logged`,
      });
      if (continueCurrentPack) {
        $toast.warning("A suffix will be logged");
      } else {
        measure.value = "";
      }
    }
  }

  if (error.value) {
    console.error(error.value);
    $toast.error(ERROR_MESSAGE);
    isValidatingInput.value = false;
  }
};

onMounted(() => {
  accessionNo.value = `${route.query.accession_number}`;
  testId.value = `${route.query.test_id}`;
  init();
});
</script>

<style scoped></style>
