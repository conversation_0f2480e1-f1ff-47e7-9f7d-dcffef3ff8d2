<template>
  <div>
    <div class="py-5 px-5">
      <CoreBreadcrumb :pages="pages" />
      <div class="mt-5">
        <div class="flex items-center space-x-2">
          <img
            src="@/assets/icons/tests.png"
            class="w-auto h-6"
            alt="flash-icon"
          />
          <h3 class="text-xl font-semibold uppercase">
            New order for
            <span>
              <nuxt-link
                class="text-sky-500 text-xl font-semibold"
                :to="`/patients?search=${patientName
                  .toLowerCase()
                  .replace(' ', '+')}`"
              >
                “{{ patientName }}”
              </nuxt-link>
            </span>
          </h3>
        </div>
        <div class="border rounded mt-5">
          <div
            class="bg-gray-50 flex items-center space-x-3 px-2 py-2 rounded-tr rounded-tl border-b"
          >
            <img
              src="@/assets/icons/fever.svg"
              class="h-8 w-auto"
              alt="fever-icon"
            />
            <h3 class="font-semibold text-lg">Patient Details</h3>
          </div>

          <div class="w-full py-5 px-5">
            <div class="flex items-center space-x-20">
              <div class="flex items-center space-x-3">
                <h3 class="font-medium">Patient Number:</h3>
                <p class="mt-1 text-gray-600">{{ patientNumber }}</p>
              </div>
              <div class="flex items-center space-x-3">
                <h3 class="font-medium">Age</h3>
                <p class="mt-1 text-gray-600">{{ patientAge }} years old</p>
              </div>
              <div class="flex items-center space-x-3">
                <h3 class="font-medium">Sex:</h3>
                <p class="mt-1 text-gray-600">{{ patientSex }}</p>
              </div>
            </div>
          </div>
        </div>

        <FormKit
          type="form"
          submit-label="Update"
          @submit="validator() && saveChanges()"
          :actions="false"
          #default="{ value }"
          id="submitForm"
        >
          <div class="border mt-5 rounded">
            <div
              class="bg-gray-50 flex items-center space-x-3 px-2 py-2 rounded-tr rounded-tl border-b"
            >
              <h3 class="font-semibold text-lg">Order Details</h3>
            </div>
            <div class="px-5 py-5">
              <div class="w-full mb-5">
                <div class="flex flex-col space-y-2">
                  <FormKit
                    label="Requesting Physician"
                    validation="required"
                    v-model="physician"
                  />
                </div>
              </div>
              <div class="w-full flex items-center space-x-10 mb-5">
                <div class="w-1/2 flex flex-col space-y-2">
                  <label class="font-medium">Visit Type</label>
                  <CoreDropdown
                    :items="visitTypes"
                    v-model="visitTypeSelected"
                    :class="
                      visitTypeSelected.name == 'select visit type' &&
                      'text-gray-600'
                    "
                  />
                </div>
                <div class="w-1/2 flex flex-col space-y-2">
                  <label class="font-medium">{{ requestingTitle }}</label>
                  <CoreDropdown
                    isSearchable
                    :items="wards"
                    v-model="wardSelected"
                    :class="
                      wardSelected.name == 'select ward' && 'text-gray-600'
                    "
                  />
                </div>
              </div>
              <div class="w-full flex items-center space-x-10 mb-5">
                <div class="w-1/2 flex flex-col space-y-2">
                  <label class="font-medium">Specimen Type</label>
                  <CoreDropdown
                    :items="specimens"
                    v-model="specimenSelected"
                    :class="
                      specimenSelected.name == 'select specimen' &&
                      'text-gray-600'
                    "
                  />
                </div>
                <div class="w-1/2 flex flex-col space-y-2">
                  <label class="font-medium">Tests</label>
                  <multi-select
                    :required="true"
                    style="--ms-max-height: none !important"
                    v-model="testsSelected"
                    :options="tests[specimenSelected.id || 0]"
                    placeholder="select tests"
                    mode="tags"
                    clear
                    searchable
                    class="focus:ring-none fcus:border-none focus:outline-none multiselect-green"
                  />
                </div>
              </div>
              <div class="w-full my-6">
                <input
                  type="checkbox"
                  placeholder="Name of physician"
                  class="mr-3 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-none transition duration-150"
                  v-model="isBDE"
                />
                <label class="font-medium">Back Data Entry</label>
              </div>
              <div class="w-full mb-5 flex flex-col space-y-2" v-if="isBDE">
                <label class="font-medium">Sample collection date</label>
                <datepicker
                  required
                  position="left"
                  placeholder="select date"
                  :range="false"
                  input-class-name="datepicker"
                  v-model="sampleCollectionDate"
                />
              </div>

              <div class="w-full">
                <CoreActionButton
                  type="submit"
                  text="Place order"
                  color="primary"
                  :icon="CheckIcon"
                  :click="() => {}"
                />
              </div>
            </div>
          </div>
        </FormKit>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { useAuthStore } from "@/store/auth";
import { useAlert } from "@/utils/useAlert";
import Api from "@/services/api";
import { OrderService } from "@/services/order_service";
import PrinterService from "@/services/printer_service";
import { isEmpty, calculateAge } from "@/utils/functions";
import fetchRequest from "@/services/fetch";
import { endpoints } from "@/services/endpoints";
import Package from "@/package.json";

import {
  CheckIcon,
} from "@heroicons/vue/24/solid/index.js";

import type {
  Client,
  DropdownItem,
  Page,
  Request,
  Response,
  Test,
  Specimen,
  VisitType,
} from "@/types";

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: `${Package.name.toUpperCase()} - New Test`,
});

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const alert = useAlert();
const token = useCookie("token");

const patient = ref<Client>({
  date_of_birth: "",
  first_name: "",
  middle_name: "",
  last_name: "",
  client_id: 0,
  id: 0,
  sex: "",
  birth_date_estimated: false,
});

const pages = ref<Page>([
  { name: "Home", link: "/home" },
  { name: "Tests", link: "/tests" },
]);

const visitTypes = ref<VisitType[]>([]);
const specimens = ref<Specimen[]>([]);
const wards = ref<DropdownItem[]>([]);
const tests = ref<Test[]>([]);
const visitTypeSelected = ref<DropdownItem>({
  name: "select visit type",
  id: 0,
});
const specimenSelected = ref<DropdownItem>({ name: "select specimen", id: 0 });
const wardSelected = ref<DropdownItem>({ name: "select ward", id: 0 });
const testsSelected = ref<any[]>([]);
const physician = ref("");
const sampleCollectionDate = ref("");
const requestingTitle = ref("Requesting Ward");
const isBDE = ref(false);

const patientAge = computed(() => calculateAge(patient.value.date_of_birth));
const patientSex = computed(() =>
  patient.value.sex.match(/f/i) ? "Female" : "Male"
);
const patientName = computed(
  () =>
    `${patient.value.first_name} ${patient.value.middle_name} ${patient.value.last_name}`
);
const patientNumber = computed(() => patient.value.client_id);
const currentDepartment = computed(() =>
  authStore.user.departments.find((item) => item.name === authStore.department)
);

function getPatient(client_id: string | string[]) {
  const request: Request = {
    route: `${endpoints.clients}/${client_id}`,
    method: "GET",
    token: `${token.value}`,
  };
  fetchRequest(request).then(({ data, error }: Response) => {
    if (data.value) {
      patient.value = data.value;
    }
    if (error.value) {
      console.error(error.value);
    }
  });
}

function getIdFromName(
  locations: DropdownItem[],
  name: string
): number | null | undefined {
  const location = locations.find((dept) => dept.name === name);
  return location ? location.id : null;
}

async function saveChanges() {
  const orderService = new OrderService(patient.value.client_id);
  orderService.createEncounter(
    Number(visitTypeSelected.value.id),
    Number(wardSelected.value.id)
  );
  orderService.buildOrder(physician.value, sampleCollectionDate.value);
  orderService.setTests(Number(specimenSelected.value.id), testsSelected.value);
  const lab_location_id = getIdFromName(
    authStore.locations,
    authStore.selectedLocation
  );
  const order = await orderService.createOrder(Number(lab_location_id));
  if (!isEmpty(order)) {
    useNuxtApp().$toast.success(
      `Order with accession number ${order.accession_number} has been created successfully!`
    );
    if (
      await alert.alertConfirmation({
        message: "Do you want to print specimen label?",
      })
    ) {
      await PrinterService.printSpecimenLabel(order.accession_number);
    }
  }
  router.push("/tests");
}

function validator(): boolean {
  if (Number(visitTypeSelected.value.id) == 0) {
    useNuxtApp().$toast.warning("Please select visit type and try again!");
    return false;
  }
  if (Number(specimenSelected.value.id) == 0) {
    useNuxtApp().$toast.warning("Please select specimen and try again!");
    return false;
  }
  if (Number(wardSelected.value.id) == 0) {
    useNuxtApp().$toast.warning("Please select ward and try again!");
    return false;
  }
  if (testsSelected.value.length == 0) {
    useNuxtApp().$toast.warning("Please select tests and try again!");
    return false;
  }
  return true;
}

watch(visitTypeSelected, async (visitType) => {
  if (visitType.name.toLowerCase() == "referral") {
    requestingTitle.value = "Requesting Facility";
    wardSelected.value = { name: "select facility", id: 0 };
  } else {
    requestingTitle.value = "Requesting Ward";
  }
  wards.value = [];
  if (isEmpty(wards.value[Number(visitType.id)])) {
    wards.value = await Api.getJson(
      "encounter_type_facility_section_mappings/facility_sections",
      {
        encounter_type_id: Number(visitType.id),
      }
    );
  }
});

watch(specimenSelected, async (specimen) => {
  if (isEmpty(tests.value[Number(specimen.id)])) {
    tests.value[Number(specimen.id)] =
      (await Api.getJson("specimen/test_types", {
        specimen_id: Number(specimen.id),
        department_id: currentDepartment.value.id,
        sex: patientSex.value
      })) || [];
  }
});

watch(
  () => authStore,
  async (newAuthStore) => {
    for (const key in tests.value) {
      if (tests.value.hasOwnProperty(key)) {
        delete tests.value[key];
      }
    }
    if (!isEmpty(specimenSelected.value)) {
      tests.value[Number(specimenSelected.value.id)] =
        (await Api.getJson("specimen/test_types", {
          specimen_id: Number(specimenSelected.value.id),
          department_id: newAuthStore.user.departments.find(
            (item: { name: any }) => item.name === newAuthStore.department
          ).id,
          sex: patientSex.value
        })) || [];

    }
    Api.getJson("specimen", {
      department_id: currentDepartment.value.id,
    }).then((data) => (specimens.value = data
      .filter((item: any) => item.preferred_name !== null)
      .map((item: any) => { return { id: item.id, name: item.preferred_name }})));
  },
  { deep: true }
);

onMounted(async () => {
  const res = await Api.getJson("encounter_types");
  visitTypes.value = res.data;

  const specimenData = await Api.getJson("specimen", {
    department_id: currentDepartment.value.id,
  });
  specimens.value = specimenData
    .filter((item: any) => item.preferred_name !== null)
    .map((item: any) => { return { id: item.id, name: item.preferred_name }});

  getPatient(route.query.patient_id as string);
});
</script>
