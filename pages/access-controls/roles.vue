<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <h3 class="text-2xl font-semibold">{{ header }}</h3>
      <RolesAddDialog />
    </div>

    <div class="flex items-center justify-between py-5">
      <div></div>
      <div class="flex items-center space-x-3">
        <CoreSearchBar v-model:search="search" />
      </div>
    </div>

    <CoreDatatable
      :headers="headers"
      :data="filteredRoles"
      :loading="loading"
      :search-value="searchValue"
      :search-field="'name'"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <RolesViewDialog :data="item" @update="init" />
          <RolesEditDialog :data="item" @update="init" />
          <RolesDeleteDialog :data="item" @update="init" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { endpoints } from "@/services/endpoints";
import type { Header, Page, Request, Response } from "@/types";
import fetchRequest from "@/services/fetch";
import Package from "@/package.json";

definePageMeta({
  layout: "dashboard",
  middleware: ["user-accounts"],
});

useHead({
  title: `${Package.name.toUpperCase()} - Roles`,
});

const header = ref("Roles");
const pages = ref([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Access Controls",
    link: "#",
  },
] as Page);

const roles = ref<any[]>([]);
const cookie = useCookie("token");
const headers = ref([
  { text: "id", value: "id", sortable: true },
  { text: "name", value: "name", sortable: true },
  { text: "actions", value: "actions" },
] as Header);

const searchValue = ref<string>("");
const search = ref<string>("");
const loading = ref<boolean>(false);

const updateSearch = (value: string): void => {
  searchValue.value = value;
  search.value = value;
};

const init = async (): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: endpoints.roles,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;

  if (data.value) {
    roles.value = data.value;
    loading.value = false;
  }

  if (error.value) {
    console.error(error.value);
    loading.value = false;
  }
};

watch(search, (value) => {
  updateSearch(value);
});

const filteredRoles = computed(() => {
  return roles.value.map((role) => ({
    id: role.id,
    name: role.name,
  }));
});

onMounted(() => {
  init();
});
</script>

<style scoped></style>
