<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <h3 class="text-2xl font-semibold">{{ header }}</h3>
      <UserAccountsAddDialog
        @update="fetchUserAccounts"
        v-if="usePermissions().can.manage('users')"
      />
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar v-model:search="search" />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="filteredUsers"
      :loading="loading"
      :searchField="'username'"
      :searchValue="searchValue"
      :serverItemsLength="serverItemsLength"
      :serverOptions="serverOptions"
      @update="updateUsers"
      v-if="usePermissions().can.manage('users')"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <UserAccountsViewDialog
            :data="item"
            v-if="usePermissions().can.manage('users')"
          />
          <UserAccountsEditDialog
            :data="item"
            @update="fetchUserAccounts"
            v-if="usePermissions().can.manage('users')"
          />
          <UserAccountsDisableDialog
            :data="item"
            @update="fetchUserAccounts"
            v-if="usePermissions().can.manage('users')"
          />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script setup lang="ts">
/* __placeholder__ */
import type { ServerOptions } from "vue3-easy-data-table";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { AccountUser, Request, Response } from "@/types";
import Package from "@/package.json";
import { useAccountsFields } from "@/components/user-accounts/fields";

definePageMeta({
  layout: "dashboard",
  middleware: ["user-accounts"]
});
useHead({
  title: `${Package.name.toUpperCase()} - User Accounts`,
});

const header = ref<string>("List of users");
const fields = useAccountsFields();
const headers = fields.HEADERS.value;
const users = ref([]);
const cookie = useCookie("token");
const loading = ref<boolean>(false);
const search = ref<string>("");
const searchValue = ref<string>("");
const pages = fields.PAGES.value;
const serverItemsLength = ref<number>(0);
const serverOptions = ref<ServerOptions>({
  page: 1,
  rowsPerPage: 25,
  sortBy: "name",
});

const filteredUsers = computed(() => {
  return users.value.map((user: AccountUser) => ({
    id: user.id,
    is_active: user.is_active,
    username: user.username.charAt(0).toUpperCase() + user.username.slice(1),
    fullname: `${user.first_name} ${user.last_name}`,
    sex: user.sex,
  }));
});

/**
 * @method fetchUserAccounts - fetch user accounts from the api
 * @returns {Promise<void>}
 */
async function fetchUserAccounts(): Promise<void> {
  loading.value = true;
  const { page, rowsPerPage } = serverOptions.value;
  const request: Request = {
    route: `${endpoints.users}?page=${page}&per_page=${rowsPerPage}&search=${search.value}`,
    method: "GET",
    token: `${cookie.value}`,
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;

  if (data.value) {
    users.value = data.value.data;
    loading.value = false;
    serverItemsLength.value = data.value.meta.total_count;
  }

  if (error.value) {
    console.error(error.value);
    loading.value = false;
  }
}
const updateUsers = (value: any): void => {
  if (typeof value === "object") {
    serverOptions.value = value;
  }
};

watch(search, () => {
  fetchUserAccounts();
});

watch(serverOptions, () => {
  fetchUserAccounts();
});

onMounted(() => {
  fetchUserAccounts();
});
</script>

<style scoped></style>
