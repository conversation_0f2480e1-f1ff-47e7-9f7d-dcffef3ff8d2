<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <h3 class="text-2xl font-semibold">Permissions</h3>
    </div>

    <div class="flex items-center space-x-3 justify-between">
      <CoreSearchBar v-model:search="search" />
      <CoreActionButton
        :loading="loading"
        :click="update"
        text="Save changes"
        color="success"
        :icon="saveIcon"
      />
    </div>

    <div class="w-full mt-5 rounded">
      <div
        v-show="loading"
        class="flex flex-col items-center mx-auto justify-center py-20"
      >
        <CoreLoader :loading="loading" />
        <p class="text-base mt-2">
          Loading permissions, please wait<span class="animate-pulse">...</span>
        </p>
      </div>

      <div class="overflow-x-auto mb-20 rounded" v-show="!loading">
        <table class="table-auto w-full border-collapse border rounded">
          <thead class="border-b">
            <tr class="w-full bg-gray-50">
              <th
                class="px-20 py-2 text-left uppercase font-semibold border sticky left-0"
              >
                Permissions
              </th>
              <th
                class="px-4 py-2 text-left uppercase font-semibold"
                v-for="role in roles"
                :key="role.id"
              >
                {{ role.name }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(privilege, index) in filteredPrivileges" :key="index">
              <td class="border-r sticky left-0 bg-white">
                <div class="">
                  <div
                    class="flex items-center justify-between border-b py-2 px-4 hover:bg-gray-100 transition duration-150 hover:font-medium"
                  >
                    <p>{{ privilege.display_name }}</p>
                  </div>
                </div>
              </td>
              <td
                class="border px-4 py-2"
                v-for="(role, index) in roles"
                :key="index"
              >
                <input
                  type="checkbox"
                  :checked="role.privileges.filter((item: any) => item.name == privilege.name).length > 0"
                  class="mr-2 leading-tight"
                  :disabled="disableCheckBox(role.name)"
                  @change="(event) => updatePermissions(event, role, privilege)"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { ArrowDownTrayIcon } from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import type { Request, Response } from "@/types";
import fetchRequest from "@/services/fetch";
import Package from "@/package.json";

definePageMeta({
  layout: "dashboard",
  middleware: ["user-accounts"],
});

useHead({
  title: `${Package.name.toUpperCase()} - Permissions`,
});

const saveIcon = ArrowDownTrayIcon;
const pages = ref([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Access Controls",
    link: "#",
  },
]);
const { $toast } = useNuxtApp();
const search = ref<string>("");
const privileges = ref<any[]>([]);
const roles = ref<any[]>([]);
const cookie = useCookie("token");
const loading = ref(false);

const filteredPrivileges = computed(() => {
  if (!search.value) {
    return privileges.value;
  }

  const searchLower = search.value.toLowerCase();

  return privileges.value.filter((privilege) =>
    privilege.display_name.toLowerCase().includes(searchLower)
  );
});

const init = async (): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: endpoints.privileges,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, pending, error }: Response = await fetchRequest(request);

  if (data.value) {
    privileges.value = data.value;
    loading.value = false;
  }

  if (error.value) {
    console.error(error.value);
    loading.value = false;
  }
};

const loadRoles = async (): Promise<void> => {
  const request: Request = {
    route: endpoints.roles,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error }: Response = await fetchRequest(request);
  if (data.value) {
    roles.value = data.value;
  }
  if (error.value) {
    console.error(error.value);
  }
};

const update = async (): Promise<any> => {
  loading.value = true;
  const request: Request = {
    route: `${endpoints.roles}/update_permissions`,
    method: "PUT",
    token: `${cookie.value}`,
    body: { role_privileges: roles.value },
  };
  const { data, pending, error }: Response = await fetchRequest(request);
  loading.value = pending;

  if (data.value) {
    loading.value = false;
    $toast.success("Permissions updated successfully!");
    await init();
    await loadRoles();
  }

  if (error.value) {
    loading.value = false;
    console.error(error.value);
  }
};

const disableCheckBox = (role: String): boolean => {
  const roles = new Set(["superadmin", "superuser"]);
  return roles.has(role.toLowerCase());
};

const updatePermissions = (
  event: Event,
  role: { privileges: { id: number; name: string }[] },
  privilege: { id: number; name: string }
): void => {
  const { checked } = event.target as HTMLInputElement;
  role.privileges = checked
    ? [...role.privileges, privilege]
    : role.privileges.filter(({ id }) => id !== privilege.id);
};

watch(search, (value) => {
  init();
});

onMounted(() => {
  init();
  loadRoles();
});
</script>

<style></style>
