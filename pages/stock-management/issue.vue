<template>
    <div class="px-5 py-5">
        <CoreBreadcrumb :pages="pages" />
        <div class="flex items-center justify-between py-5">
            <h3 class="text-2xl font-semibold">{{ header }}</h3>
        </div>
        <div class="flex items-center justify-end py-5">
            <CoreSearchBar v-model:search="search" @update="init"/>
        </div>
        <div>
            <CoreDatatable :loading="loading" :headers="headers" :data="filteredData" search-field="movement_to"
                :searchValue="search" :serverItemsLength="serverItemsLength" :serverOptions="serverOptions" @update="init">
                <template v-slot:actions="{ item }">
                    <div class="py-2 flex items-center space-x-2">
                        <StockIssueViewDialog :data="item" />
                        <CoreActionButton :click="(() => approveStockIssue(item.id))" text="Approve" color="success"
                            :icon="addIcon" v-if="item.stock_status.toLowerCase() == 'pending'" />
                        <StockIssueRejectDialog :data="item" @update="init"
                            v-if="item.stock_status.toLowerCase() == 'pending'" />
                    </div>
                </template>
            </CoreDatatable>
        </div>
    </div>
</template>

<script lang="ts">

import type { Page, Header, Response } from '@/types'
import StockModule from '@/repository/modules/stock';
import moment from 'moment';
import { DocumentCheckIcon, XMarkIcon } from '@heroicons/vue/24/solid/index.js';
import type { ServerOptions } from 'vue3-easy-data-table';
import Package from '@/package.json';

export default {
    setup() {
        definePageMeta({
            layout: 'dashboard',
        });
        useHead({
            title: `${Package.name.toUpperCase()} - Issues Stock`
        })
    },
    data() {
        return {
            header: 'Stock Issue' as string,
            pages: [
                {
                    name: 'Home',
                    link: '/home',
                },
                {
                    name: 'Stock Management',
                    link: '#',
                }
            ] as Page,
            search: '' as string,
            headers: [
                { text: "destination", value: "movement_to", sortable: true },
                { text: "status", value: "stock_status" },
                { text: "transaction items", value: "stock_transactions.length" },
                { text: "movement date", value: "movement_date" },
                { text: "actions", value: "actions" }
            ] as Header,
            data: new Array<any>(),
            cookie: useCookie('token'),
            loading: false as boolean,
            addIcon: DocumentCheckIcon,
            rejectIcon: XMarkIcon,
            serverItemsLength: 0 as number,
            serverOptions: <ServerOptions>{
                page: 1,
                rowsPerPage: 25,
                sortBy: "name",
            },
        }
    },
    created() {
        this.init(this.search);
    },
    computed: {
        filteredData() {
            return this.data.map((item) => ({
                ...item,
                movement_date: moment(item.movement_date).format(DATE_FORMAT),
            }))
        }
    },
    methods: {
        async init(search: string): Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const { page, rowsPerPage } = this.serverOptions;
            const params = `search=${search}&page=${page}&per_page=${rowsPerPage}`
            const { data, error, pending }: Response = await stockModule.getStockIssues(`${this.cookie}`, params)
            this.loading = pending;
            if (data.value) {
                console.log(data.value)
                this.loading = false;
                this.data = data.value.data;
                this.serverItemsLength = data.value.meta.total_count;
            }
            if (error.value) {
                console.error(error.value)
                this.loading = false;
            }
        },
        async approveStockIssue(issueId: string): Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const { data, error, pending }: Response = await stockModule.approveStockIssues(`${this.cookie}`, issueId)
            this.loading = pending;
            if (data.value) {
                console.log(data.value)
                this.loading = false;
                useNuxtApp().$toast.success('Stock issue approved successfully')
                this.init(this.search);
            }
            if (error.value) {
                console.error(error.value)
                useNuxtApp().$toast.error(ERROR_MESSAGE)
                this.loading = false;
            }
        }
    }
}
</script>
<style>
</style>
