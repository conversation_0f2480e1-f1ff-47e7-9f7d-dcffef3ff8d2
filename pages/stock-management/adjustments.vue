<template>
    <div class="px-5 py-5">
        <CoreBreadcrumb :pages="pages" />
        <div class="flex items-center justify-between py-5">

            <h3 class="text-2xl font-semibold">Stock Adjustments</h3>

            <div class="flex items-center space-x-3">
                <StockAdjustmentsAddDialog @update="init" />
            </div>

        </div>

        <div class="flex items-center justify-end py-5">
            <CoreSearchBar v-model:search="search"/>
        </div>

        <div>
            <CoreDatatable :loading="loading" :headers="headers" :data="filteredAdjustments" :search-value="search"
                search-field="name" :serverItemsLength="serverItemsLength" :serverOptions="serverOptions"
                @update="init">
                <template v-slot:actions="{ item }">
                    <div class="py-2 flex items-center space-x-2">
                        <StockAdjustmentsViewDialog :data="item" />
                        <StockAdjustmentsDeleteDialog :data="item" @update="init" />
                    </div>
                </template>
            </CoreDatatable>
        </div>
    </div>
</template>

<script lang="ts">

import type { Page, Response } from '@/types'
import StockModule from '@/repository/modules/stock'
import type { ServerOptions } from 'vue3-easy-data-table'
import moment from 'moment'
import Package from '@/package.json';

export default {
    setup() {
        useHead({
            title: `${Package.name.toUpperCase()} - Stock Adjustments`
        })
        definePageMeta({
            layout: 'dashboard'
        })
    },
    data() {
        return {
            pages: [
                {
                    name: 'Home',
                    link: '/home',
                },
                {
                    name: 'Stock Management',
                    link: '#',
                }
            ] as Page,
            header: 'Stock Adjustments' as string,
            search: '' as string,
            loading: false as boolean,
            headers: [
                { text: "item", value: "name", sortable: true },
                { text: "lot", value: "lot" },
                { text: "batch", value: "batch" },
                { text: "adjustment", value: "transacted_quantity" },
                { text: "reason", value: "reason" },
                { text: "date created", value: "transaction_date" },
                { text: "actions", value: "actions" }
            ],
            cookie: useCookie('token'),
            adjustments: new Array<any>(),
            serverItemsLength: 0 as number,
            serverOptions: <ServerOptions>{
                page: 1,
                rowsPerPage: 25,
                sortBy: "name",
            },
        }
    },
    computed: {
        filteredAdjustments(){
            return this.adjustments.map((adjustment) => ({
                ...adjustment,
                transaction_date: moment(adjustment.transaction_date).format(DATE_FORMAT)
            }))
        }
    },
    created(){
        this.init();
    },
    methods: {
        async init(): Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const { page, rowsPerPage } = this.serverOptions;
            const params = `search=${this.search}&page=${page}&per_page=${rowsPerPage}&transaction_type=adjust stock`
            const { data, pending, error }: Response = await stockModule.getStockTransactions(`${this.cookie}`, params);
            this.loading = pending;
            if(data.value){
                this.adjustments = data.value.data;
                this.loading = false;
                this.serverItemsLength = data.value.meta.total_count;
            }
            if(error.value){
                console.error(error.value)
                this.loading = false;
            }
        }
    },
    watch: {
        search(value: string){
            this.init();
        }
    }
}
</script>
<style>
</style>
