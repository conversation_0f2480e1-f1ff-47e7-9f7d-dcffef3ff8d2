<template>
    <div class="px-5 py-5">
        <CoreBreadcrumb :pages="pages" />
        <div class="flex items-center justify-between py-5">
            <h3 class="text-2xl font-semibold">{{ header }}</h3>
        </div>
        <div class="flex items-center justify-end py-5">
            <CoreSearchBar v-model:search="search" />
        </div>
        <div>
            <CoreDatatable :headers="headers" :data="filteredStocks" :loading="loading" search-field="name" :search-value="search"
                :serverItemsLength="serverItemsLength" v-model:serverOptions="serverOptions" @update="init">
                <template v-slot:actions="{ item }">
                    <div class="py-2 flex items-center space-x-2">
                        <StockViewDialog :data="item" />
                        <CoreActionButton v-if="can.manage('stock_management')" :click="(() => { $router.push('/stock-management/stock-items') })" text="Edit"
                            color="success" :icon="editIcon" />
                        <CoreActionButton v-if="can.manage('stock_management')" :click="(() => { $router.push('/stock-management/stock-items') })" text="Delete"
                            color="error" :icon="deleteIcon" />
                    </div>
                </template>
            </CoreDatatable>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Header, Page, Response } from '@/types';
import { PencilSquareIcon, TrashIcon } from '@heroicons/vue/24/solid/index.js';
import StockModule from '@/repository/modules/stock';
import type { ServerOptions } from 'vue3-easy-data-table';
import moment from 'moment';
import Package from '@/package.json';

definePageMeta({
    layout: 'dashboard',
    middleware: ['stock-management']
});

useHead({
    title: `${Package.name.toUpperCase()} - Stock`
});


const { can } = usePermissions();
const header = ref<string>('Stock');
const deleteIcon = TrashIcon;
const editIcon = PencilSquareIcon;
const pages = ref<Page>([
    {
        name: 'Home',
        link: '/home',
    },
    {
        name: 'Stock Management',
        link: '#',
    }
]);

const search = ref<string>('');
const headers = ref<Header>([
    { text: "name", value: "stock_item.name", sortable: true },
    {
        text: "description", value: "stock_item.description",
        sortable: false
    },
    { text: "quantity", value: "quantity", sortable: false },
    { text: "date modified", value: "updated_date", sortable: false },
    { text: "actions", value: "actions", sortable: false }
]);

const loading = ref<boolean>(false);
const serverItemsLength = ref<number>(0);
const stocks = ref<any[]>([]);
const serverOptions = ref<ServerOptions>({
    page: 1,
    rowsPerPage: 25,
    sortBy: "name",
});

const filteredStocks = computed(() => {
    return stocks.value.map((stock: { updated_date: string }) => ({
        ...stock,
        updated_date: moment(stock.updated_date).format(DATE_FORMAT)
    }));
});

const init = async (options: ServerOptions = { page: 1, rowsPerPage: 25 }): Promise<void> => {
    loading.value = true;
    const stockModule = new StockModule();
    const { page, rowsPerPage } = options;
    let params = `?page=${page}&per_page=${rowsPerPage}&search=${search.value}`;
    const { data, error, pending }: Response = await stockModule.getStock(``, params);
    loading.value = pending;
    if (data.value) {
        loading.value = false;
        stocks.value = data.value.data;
        serverItemsLength.value = data.value.meta.total_count;
    }
    if (error.value) {
        loading.value = false;
        console.error(error.value);
    }
};

onMounted(() => {
    init();
});

watch(search, () => {
    init();
});
</script>

<style>
</style>
