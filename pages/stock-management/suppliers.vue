<template>
    <div class="px-5 py-5">

        <CoreBreadcrumb :pages="pages" />

        <div class="flex items-center justify-between py-5">

            <h3 class="text-2xl font-semibold">{{ header }}</h3>

            <div class="flex items-center space-x-3">
                <StockSupplierAddDialog @update="init" />
            </div>

        </div>

        <div class="flex items-center justify-end py-5">
            <CoreSearchBar :search="search" />
        </div>

        <div>
            <CoreDatatable :headers="headers" :data="suppliers" :loading="loading">
                <template v-slot:actions="{ item }">
                    <div class="py-2 flex items-center space-x-2">
                        <StockSupplierViewDialog :data="item" />
                        <StockSupplierEditDialog :data="item" @update="init" />
                        <StockSupplierDeleteDialog :data="item" @update="init" />
                    </div>
                </template>
            </CoreDatatable>
        </div>

    </div>
</template>

<script lang="ts">

import moment from 'moment';
import StockModule from '@/repository/modules/stock';
import type { Header, Page } from '@/types';
import Package from '@/package.json';

export default {
    setup() {
        useHead({
            title: `${Package.name.toUpperCase()} - Stock Suppliers`
        });
        definePageMeta({
            layout: 'dashboard'
        });
    },
    data() {
        return {
            header: 'Stock Suppliers' as string,
            search: '' as string,
            pages: [
                {
                    name: 'Home',
                    link: '/home',
                },
                {
                    name: 'Stock Management',
                    link: '#',
                }
            ] as Page,
            headers: [
                { text: "name", value: "name", sortable: true },
                { text: "address", value: "address" },
                { text: "actions", value: "actions" }
            ] as Header,
            suppliers: new Array<any>(),
            loading: false as boolean,
            cookie: useCookie('token')
        }
    },
    created() {
        this.init();
    },
    methods: {
        async init(): Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const { data, error, pending } = await stockModule.getStockSupplier(`${this.cookie}`)
            this.loading = pending;
            if (data.value) {
                this.suppliers = data.value.filter((category: { created_date: string }) => ({
                    ...category,
                    created_date: moment(category.created_date).format(DATE_FORMAT)
                }))
                this.loading = false;
            }
            if (error.value) {
                console.error(error.value)
                this.loading = false;
            }
        }
    }
}
</script>

<style>
</style>
