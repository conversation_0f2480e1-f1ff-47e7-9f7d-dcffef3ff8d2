<template>
    <div class="px-5 py-5">
        <CoreBreadcrumb :pages="pages" />
        <div class="flex items-center justify-between py-5">
            <h3 class="text-2xl font-semibold">{{ header }}</h3>
            <div class="flex items-center space-x-3">
                <StockCategoryAddDialog @update="init"/>
            </div>
        </div>
        <div class="flex items-center justify-end py-5">
            <CoreSearchBar v-model:search="search"/>
        </div>
        <div>
            <CoreDatatable :headers="headers" :data="categories" :search-field="'name'" :search-value="search">
                <template v-slot:actions="{ item }">
                    <div class="py-2 flex items-center space-x-2">
                        <StockCategoryViewDialog :data="item"/>
                        <StockCategoryEditDialog :data="item" @update="init"/>
                        <StockCategoryDeleteDialog :data="item" @update="init"/>
                    </div>
                </template>
            </CoreDatatable>
        </div>
    </div>
</template>

<script setup lang="ts">
import StockModule from '@/repository/modules/stock'
import type { Header, Page } from '@/types'
import moment from 'moment'
import Package from '@/package.json'

definePageMeta({
    layout: 'dashboard',
    middleware: ['stock-management']
});

useHead({
    title: `${Package.name.toUpperCase()} - Stock Categories`
});

const header = ref<string>('Stock Category');
const search = ref<string>('');
const loading = ref<boolean>(false);
const cookie = useCookie('token');
const categories = ref<any[]>([])
const pages = ref<Page>([
    {
        name: 'Home',
        link: '/home',
    },
    {
        name: 'Stock Management',
        link: '#',
    }
]);

const headers = ref<Header>([
    { text: "name", value: "name", sortable: true },
    { text: "date created", value: "created_date", sortable: true },
    { text: "actions", value: "actions", sortable: false }
]);

const init = async (): Promise<void> => {
    loading.value = true
    const stockModule = new StockModule()
    const { data, error, pending } = await stockModule.getStockCategory(`${cookie.value}`)
    loading.value = pending

    if (data.value) {
        categories.value = data.value.map((category: { created_date: string }) => ({
            ...category,
            created_date: moment(category.created_date).format(DATE_FORMAT)
        }))
        loading.value = false
    }

    if (error.value) {
        console.error(error.value)
        loading.value = false
    }
}

onMounted(() => {
    init()
})
</script>

<style>

</style>
