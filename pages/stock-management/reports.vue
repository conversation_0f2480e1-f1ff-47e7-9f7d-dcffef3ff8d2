<template>
    <div class="px-5 py-5">

        <CoreBreadcrumb :pages="pages" />

        <div class="flex items-center justify-between py-5">

            <h3 class="text-2xl font-semibold">{{ header }}</h3>

        </div>

        <div>
            <div class="flex items-center space-x-2 bg-gray-50">
                <button @click="tab = 0"
                    :class="tab == 0 ? `bg-sky-500 text-white py-2 px-4` : `font-medium px-4 text-gray-600 hover:text-sky-500 transition duration-150`">
                    General Stock
                </button>
                <button @click="tab = 1"
                    :class="tab == 1 ? `bg-sky-500 text-white py-2 px-4` : `font-medium px-4 text-gray-600 hover:text-sky-500 transition duration-150`">
                    Stock Movements
                </button>
            </div>
            <div v-if="tab == 0" class="py-5">
                <div>
                    <div class="w-full flex items-center justify-between py-5">
                        <div class="flex items-center space-x-2">
                            <CoreActionButton :loading="generating" :click="(() => { generateStockReport() })"
                                text="Generate Report" color="primary" :icon="generateIcon" />
                        </div>
                        <div class="flex items-center space-x-2">
                            <excel class="btn btn-default"
                                :header="[`GENERAL STOCK LABORATORY REPORT`, facility.details.name, facility.details.address, facility.details.phone]"
                                :data="generalStockExportData" worksheet="report-work-sheet"
                                :name="`general_stock_laboratory.xls`">
                                <CoreExportButton color="success" :click="(() => { })" :icon="exportIcon"
                                    text="Export" />
                            </excel>
                        </div>
                    </div>
                    <div id="print-container1">
                        <table class="w-full border rounded px-10 py-5">
                            <tbody>
                                <tr class="flex items-center justify-between px-5">
                                    <div class="flex flex-col space-y-2">
                                        <img src="@/assets/images/logo.png" alt="app-logo"
                                            class="w-24 h-24 object-cover" />
                                        <h3 class="text-xl font-semibold">GENERAL STOCK REPORT</h3>
                                    </div>
                                    <td class="py-5">
                                        <ReportsAddress />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <table class="w-full">
                            <thead class="w-full">
                                <tr class="border-b border-r border-l bg-gray-50">
                                    <th class="px-2 py-2 text-left border-r uppercase">Stock Item</th>
                                    <th class="px-2 py-2 text-left border-r uppercase">Location</th>
                                    <th class="px-2 py-2 text-left border-r uppercase">Batch</th>
                                    <th class="px-2 py-2 text-left border-r uppercase">Lot</th>
                                    <th class="px-2 py-2 text-left border-r uppercase">Quantity</th>
                                    <th class="px-2 py-2 text-left uppercase">Remarks</th>
                                </tr>
                            </thead>
                            <tbody class="w-full">
                                <tr class="w-full border-b" v-for="stock in generalStockData" :key="stock.id">
                                    <td class="px-2 py-2 text-left border-r border-l">{{ stock.name }}</td>
                                    <td class="px-2 py-2 text-left border-r border-l">{{ stock.stock_location }}</td>
                                    <td class="px-2 py-2 text-left border-r border-l">{{ stock.batch }}</td>
                                    <td class="px-2 py-2 text-left border-r border-l text-green-500">#{{ stock.lot }}
                                    </td>
                                    <td class="px-2 py-2 text-left border-r border-l">{{
                                        stock.consolidated_available_balance }}
                                    </td>
                                    <td class="px-2 py-2 text-left border-r border-l">
                                        <div class="flex items-center space-x-1" :class="getStockClass(stock?.remarks)">
                                            <component :is="getStockIcon(stock?.remarks)" class="w-5 h-5" />
                                            <p>{{ getStockText(stock?.remarks) }}</p>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                            
                        </table>
                    </div>
                </div>
            </div>
            <div v-if="tab == 1" class="px-3 py-3">
                <div>
                    <div class="flex items-center justify-between py-5">
                        <div class="flex items-center space-x-2">
                            <div class="w-72">
                                <datepicker position="left" input-class-name="datepicker" range placeholder="start date & end date"
                                v-model="dateRange" format="dd/MM/yyyy" :maxDate="new Date()"/>
                            </div>
                            <CoreDropdown class="w-44" :items="transactionTypes" v-model="selectedType" />
                            <CoreActionButton text="Generate" :click="(() => { generateStockReport() })" color="success"
                                :icon="generateIcon" />
                        </div>
                        <div>
                            <excel class="btn btn-default"
                                :header="[`STOCK MOVEMENT LABORATORY REPORT`, facility.details.name, facility.details.address, facility.details.phone]"
                                :data="stockMovementExportData" worksheet="report-work-sheet"
                                :name="`stock_movement_laboratory.xls`">
                                <CoreExportButton color="success" :click="(() => { })" :icon="exportIcon"
                                    text="Export" />
                            </excel>
                        </div>
                    </div>

                    <div class="border rounded mt-10" id="print-container">
                        <table class="w-full rounded-t border-b px-10 py-5">
                            <tbody>
                                <tr class="flex items-center justify-between px-5">
                                    <div class="flex flex-col space-y-2">
                                        <img src="@/assets/images/logo.png" alt="app-logo"
                                            class="w-24 h-24 object-cover" />
                                        <h3 class="text-xl font-semibold">STOCK MOVEMENT REPORT</h3>
                                    </div>
                                    <td class="py-5">
                                        <ReportsAddress />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div>
                            <div class="px-3 py-4">
                                <h4 class="font-medium">Movement period:
                                    <span class="text-normal font-normal">
                                        {{ dateRange !== null && dateRange.length > 0
                                            ? moment(dateRange[0]).format(DATE_FORMAT)
                                            : "" }} -
                                        {{ dateRange !== null && dateRange.length > 1
                                            ? moment(dateRange[1]).format(DATE_FORMAT)
                                            : "" }}
                                    </span>
                                </h4>
                            </div>
                            <div v-show="generating" class="flex itmes-center mx-auto justify-center py-20">
                                <CoreLoader :loading="generating" />
                            </div>
                            <table class="w-full" v-if="reportData.length > 0 && !generating">
                                <thead class="w-full">
                                    <tr class="border-t border-b bg-gray-50">
                                        <th class="px-2 py-2 text-left border-r uppercase">Stock Item</th>
                                        <th class="px-2 py-2 text-left border-r uppercase">Type</th>
                                        <th class="px-2 py-2 text-left border-r uppercase">Stock Before</th>
                                        <th class="px-2 py-2 text-left border-r uppercase">Adjustment</th>
                                        <th class="px-2 py-2 text-left border-r uppercase">Stock After</th>
                                        <th class="px-2 py-2 text-left uppercase">Date</th>
                                    </tr>
                                </thead>
                                <tbody class="w-full">
                                    <tr class="w-full" v-for="report in reportData" :key="report.Lot">
                                        <td class="px-2 py-2 text-left border-r">{{ report.name }}</td>
                                        <td class="px-2 py-2 text-left border-r border-l">{{ report.transaction_type }}
                                        </td>
                                        <td class="px-2 py-2 text-left border-r border-l">{{
                                            report.overall_stock_balance_before_transaction }}</td>
                                        <td class="px-2 py-2 text-left border-r border-l">{{ report.transacted_quantity
                                            }}
                                        </td>
                                        <td class="px-2 py-2 text-left border-r border-l">{{
                                            report.overall_stock_balance_after_transaction }}</td>
                                        <td class="px-2 py-2 text-left border-l">{{
                                            moment(report.transaction_date).format(DATE_FORMAT) }}</td>
                                    </tr>
                                </tbody>
                            </table>

                        </div>
                    </div>
                    <div v-if="reportData.length == 0 && !generating"
                        class="flex flex-col space-y-3 items-center justify-center">
                        <img src="@/assets/icons/stock_out.svg" class="w-20 h-20" alt="no-stock-svg"/>
                        <p>Please generate report data to preview the report</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">

import type { Page, Response, StockTransactionType } from '@/types';
import { ArchiveBoxXMarkIcon, ArrowPathIcon, CheckCircleIcon, DocumentTextIcon, ExclamationCircleIcon, ExclamationTriangleIcon } from '@heroicons/vue/24/solid/index.js';
import { useFacilityStore } from '@/store/facility';
import StockModule from '@/repository/modules/stock';
import moment from 'moment';
import { ExportToExcel, ExportToWord } from 'vue-doc-exporter';
import Package from '@/package.json'

export default {
    setup() {
        definePageMeta({
            layout: 'dashboard'
        });
        useHead({
            title: `${Package.name.toUpperCase()} - Stock Reports`
        });
    },
    components: { ExportToExcel, ExportToWord, ExclamationCircleIcon, CheckCircleIcon, ExclamationTriangleIcon, ArchiveBoxXMarkIcon },
    data() {
        return {
            moment: moment,
            header: 'Stock Reports' as string,
            facility: useFacilityStore(),
            tab: 0 as number,
            generating: false as boolean,
            dateRange: <Array<any>>[],
            pages: [
                {
                    name: 'Home',
                    link: '/home',
                },
                {
                    name: 'Stock Management',
                    link: '#',
                }
            ] as Page,
            reportData: new Array<any>(),
            generateIcon: ArrowPathIcon,
            exportIcon: DocumentTextIcon,
            cookie: useCookie('token'),
            transactionTypes: new Array<StockTransactionType>(),
            selectedType: <StockTransactionType>{ name: '-- select type --' }
        };
    },
    created() {
        this.init();
    },
    computed: {
        generalStockExportData() {
            return this.reportData.map((data) => ({
                "STOCK ITEM": data.name,
                "DESCRIPTION": data.description,
                "BALANCE": data.consolidated_available_balance,
                "MINIMUM ORDER LEVEL": data.minimum_order_level,
                "LOCATION": data.stock_location,
                "CATEGORY": data.stock_category,
                "STRENGTH": data.strength,
                "BATCH": data.batch,
                "LOT NUMBER": data.lot,
                "TRANSACTION TYPE": data.transaction_type,
                "EXPIRY DATE": moment(data.expiry_date).format(DATE_FORMAT),
                "RECEIVER": data.receiving_from,
                "SENDER": data.sending_to,
                "REMAKRS": this.checkStock(data)
            }))
        },
        stockMovementExportData() {
            return this.reportData.map((data) => ({
                "Stock Item": data.name,
                "TYPE": data.transaction_type,
                "STOCK BEFORE": data.overall_stock_balance_before_transaction,
                "ADJUSTMENT": data.transacted_quantity,
                "STOCK AFTER": data.overall_stock_balance_after_transaction,
                "DATE": moment(data.transaction_date).format(DATE_FORMAT),
            }))
        },
        generalStockData() {
            return this.reportData.map((data) => ({
                ...data,
                remarks: this.checkStock(data)
            }))
        },
        getStockClass() {
            return (remarks: string) => {
                const lowerRemarks = (remarks || '').toLowerCase();
                if (lowerRemarks === 'not expired') {
                    return 'text-green-600';
                } else if (lowerRemarks === 'expired') {
                    return 'text-red-600';
                } else if (lowerRemarks === 'near expiry') {
                    return 'text-yellow-500';
                } else if (lowerRemarks === 'out of stock') {
                    return 'text-gray-600';
                }
                return '';
            }
        },
        getStockIcon() {
            return (remarks: string) => {
                const lowerRemarks = (remarks || '').toLowerCase();
                if (lowerRemarks === 'not expired') {
                    return 'CheckCircleIcon';
                } else if (lowerRemarks === 'expired' || lowerRemarks === 'near expiry') {
                    return 'ExclamationTriangleIcon';
                } else if (lowerRemarks === 'out of stock') {
                    return 'ArchiveBoxXMarkIcon';
                }
                return 'div';
            }
        },
        getStockText() {
            return (remarks: string) => {
                const lowerRemarks = (remarks || '').toLowerCase();
                if (lowerRemarks === 'not expired') {
                    return '';
                } else if (lowerRemarks === 'expired') {
                    return 'Expired';
                } else if (lowerRemarks === 'near expiry') {
                    return 'Near Expiry';
                } else if (lowerRemarks === 'out of stock') {
                    return 'Out of Stock';
                }
                return '';
            }
        }
    },
    methods: {
        checkStock(stock: any): any {
            let status = this.checkExpiryStatus(stock);
            if (status == "Not Expired") {
                if (stock.after_transaction_remaining_balance > 0) {
                    if (stock.after_transaction_remaining_balance < stock.minimum_order_level) {
                        return 'Low Stock'
                    }
                } else {
                    return 'Out of Stock'
                }
            } else {
                return status
            }
        },
        checkExpiryStatus(stockData: any): string {
            const currentDate = new Date();
            currentDate.setHours(0, 0, 0, 0);
            const expiryDate = new Date(stockData.expiry_date);
            expiryDate.setHours(0, 0, 0, 0);
            if (expiryDate < currentDate) {
                return 'Expired';
            }
            const twoWeeksLater = new Date();
            twoWeeksLater.setDate(currentDate.getDate() + 14);
            if (expiryDate <= twoWeeksLater) {
                return 'Near Expiry';
            }
            return 'Not Expired';
        },
        async init(): Promise<void> {
            const stockModule = new StockModule();
            const { data, error, pending }: Response = await stockModule.getStockTransactionTypes(`${this.cookie}`);
            if (data.value) {
                this.transactionTypes = data.value;
            }
            if (error.value) {
                console.error(error.value);
            }
        },
        async generateStockReport(): Promise<void> {
            this.generating = true;
            const startDate = this.dateRange !== null && this.dateRange.length > 0
                ? moment(this.dateRange[0]).format(DATE_FORMAT)
                : "";
            const endDate = this.dateRange !== null && this.dateRange.length > 1
                ? moment(this.dateRange[1]).format(DATE_FORMAT)
                : "";
            const type = this.selectedType.name == '-- select type --' ? '' : this.selectedType.name;
            const stockModule = new StockModule();
            const { data, error, pending }: Response = await stockModule.generateStockMovementReport(`${this.cookie}`, type, startDate, endDate);
            this.generating = pending;
            if (data.value) {
                this.reportData = data.value.data;
                this.generating = false;
            }
            if (error.value) {
                console.error(error.value);
                this.generating = false;
            }
        }
    }
}
</script>

<style></style>
