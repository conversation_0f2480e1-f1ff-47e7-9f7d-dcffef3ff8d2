<template>
    <div class="px-5 py-5">

        <CoreBreadcrumb :pages="pages" />

        <div class="flex items-center justify-between py-5">

            <h3 class="text-2xl font-semibold">{{ header }}</h3>

            <div class="flex items-center space-x-3">
                <StockLocationsAddDialog @update="init"/>
            </div>

        </div>

        <div class="flex items-center justify-end py-5">
            <CoreSearchBar :search.sync="search"/>
        </div>

        <div>
            <CoreDatatable :loading="loading" :headers="headers" :data="locations" :search-value="search" search-field="name">
                <template v-slot:actions="{ item }">
                    <div class="py-2 flex items-center space-x-2">
                        <StockLocationsViewDialog :data="item"/>
                        <StockLocationsEditDialog :data="item" @update="init"/>
                        <StockLocationsDeleteDialog :data="item" @update="init"/>
                    </div>
                </template>
            </CoreDatatable>
        </div>

    </div>
</template>

<script lang="ts">

import { PlusIcon, ArrowTopRightOnSquareIcon, TrashIcon, PencilSquareIcon } from '@heroicons/vue/24/solid/index.js';
import moment from 'moment';
import StockModule from '@/repository/modules/stock';
import type { Page, Header, StockLocation } from '@/types';
import Package from '@/package.json';

export default {
    setup() {
        definePageMeta({
            layout: 'dashboard'
        });
        useHead({
            title: `${Package.name.toUpperCase()} - Stock Locations`
        });
    },
    data() {
        return {
            header: 'Stock Locations' as string,
            addIcon: PlusIcon,
            viewIcon: ArrowTopRightOnSquareIcon,
            deleteIcon: TrashIcon,
            editIcon: PencilSquareIcon,
            search: '' as string,
            loading: false as boolean,
            cookie: useCookie('token'),
            pages: [
                {
                    name: 'Home',
                    link: '/home',
                },
                {
                    name: 'Stock Management',
                    link: '#',
                }
            ] as Page,
            headers: [
                { text: "name", value: "name", sortable: true },
                { text: "description", value: "description" },
                { text: "actions", value: "actions" }
            ] as Header,
            locations: new Array<StockLocation>()
        }
    },
    created(){
        this.init();
    },
    methods: {
        async init() : Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const { data, error, pending } = await stockModule.getStockLocation(`${this.cookie}`)
            this.loading = pending;
            if(data.value){
                this.locations = data.value.filter((item: { created_date: string }) => ({
                        ...item,
                        created_date: moment(item.created_date).format(DATE_FORMAT)
                    }))
                this.loading = false;
            }
            if(error.value){
                console.error(error.value)
                this.loading = false;
            }
        }
    },
    watch: {
        search(newValue){
            console.log(newValue)
        }
    }
}
</script>
<style lang="">

</style>
