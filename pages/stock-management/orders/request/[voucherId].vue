<template>
    <div class="px-5 py-5">
        <CoreBreadcrumb :pages="pages" />
        <div class="py-5 flex items-center justify-between">
            <h3 class="text-2xl font-semibold">{{ header }}</h3>

        </div>
        <div class="px-4 py-2 bg-gray-50 flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <TicketIcon class="h-5 w-5" />
                <p>Voucher Number: <strong>{{ $route.params.voucherId }}</strong></p>
            </div>
            <div class="flex items-center space-x-3 justify-end">
                <CoreActionButton :loading="verifying" :icon="allIcon" text="Verify all" color="success" :click="verifyStockOrder"/>
                <StockOrdersRejectDialog :stockId="`${$route.query.order_id}`" :orderId="`${$route.params.voucherId}`"/>
            </div>
        </div>

        <div>
            <div v-show="!loading" class="flex flex-col space-y-3" v-for="(requisition, index) in requisitions" :key="index">
                <div class="grid grid-cols-4 gap-4 mt-5" v-if="requisition.requisition_status.toLowerCase() !== 'rejected'">
                    <div class="flex flex-col space-y-2">
                        <FormKit label="Stock item" :disabled="true" v-model="requisition.item.name"/>
                    </div>
                    <FormKit label="Quantity requested" type="number" validation="required"
                        v-model="requisition.quantity_requested" />
                    <div class="mt-8 flex space-x-2.5 items-center" v-if="requisition.requisition_status.toLowerCase() !== 'requested'">
                        <CoreActionButton :key="requisition.id" :icon="checkIcon" text="Verify" color="success" :click="(() => { verifyStockRequisition(requisition) })" />
                        <StockOrdersRejectRequisition @update="init" :data="requisition" :orderId="`${$route.params.voucherId}`"/>
                    </div>
                </div>
            </div>
            <div v-show="loading">
                <div class="flex items-center space-x-3 mb-3" v-for="i in 5" :key="i">
                    <div class="w-1/4 bg-gray-100 rounded animate-pulse h-10 mt"></div>
                    <div class="w-1/4 bg-gray-100 rounded animate-pulse h-10"></div>
                    <div class="w-1/4 bg-gray-100 rounded animate-pulse h-10"></div>
                    <div class="w-1/4 bg-gray-100 rounded animate-pulse h-10"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">

import type { Page, RequisitionItem, Response } from "@/types"
import { DocumentCheckIcon, NoSymbolIcon, TicketIcon, TrashIcon, CheckIcon } from "@heroicons/vue/24/solid/index.js";
import StockModule from "@/repository/modules/stock";
import Package from '@/package.json'

definePageMeta({
    layout: 'dashboard'
})

export default {
    components: { TicketIcon },
    data() {
        return {
            header: 'Verify Stock Order' as string,
            checkIcon: CheckIcon,
            allIcon: DocumentCheckIcon,
            rejectIcon: NoSymbolIcon,
            pages: [
                {
                    name: 'Home',
                    link: '/home',
                },
                {
                    name: 'Stock Orders',
                    link: '/stock-management/orders',
                }
            ] as Page,
            cookie: useCookie('token'),
            deleteicon: TrashIcon,
            loading: false as boolean,
            verifying: false as boolean,
            requisitions: new Array<any>(),
            voidReason: '' as string,
        };
    },
    created(){
        this.init();
    },
    methods: {
        async init () : Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const { data, error, pending } : Response = await stockModule.getStockOrder(`${this.cookie}`, `${this.$route.query.order_id}`);
            this.loading = pending;
            if(data.value){
                this.requisitions = data.value.stock_requisitions
                this.loading = false;
            }
            if(error.value){
                console.error(error.value)
                this.loading = false;
            }
        },
        async verifyStockRequisition(requisition: RequisitionItem) : Promise<void>{
            this.loading = true;
            const stockModule = new StockModule();
            const params = {
                stock_order_id: `${this.$route.query.order_id}`,
                stock_requisition_id: requisition.id
            }
            const { data, error, pending } : Response = await stockModule.verifyStockOrderRequisition(`${this.cookie}`, params);
            this.loading = pending;
            if(data.value){
                useNuxtApp().$toast.success('Stock order requisition verified successfully!');
                this.init();
                this.loading = false;
            }
            if(error.value){
                console.error(error.value)
                this.loading = false;
            }
        },
        async verifyStockOrder() : Promise<void>{
            this.verifying = true;
            const stockModule = new StockModule();
            const params = {
                stock_order_id: `${this.$route.query.order_id}`,
                stock_requisition_ids: this.requisitions.map((requisition) => requisition.id)
            }
            const { data, error, pending } : Response = await stockModule.verifyStockOrder(`${this.cookie}`, params);
            this.verifying = pending;
            if(data.value){
                useNuxtApp().$toast.success('Stock order verified successfully!');
                this.$router.push('/stock-management/orders')
                this.verifying = false;
            }
            if(error.value){
                console.error(error.value)
                this.verifying = false;
            }
        },
        deleteStockItem(index: number): void {
            if (index >= 0 && index < this.requisitions.length) {
                this.requisitions.splice(index, 1);
            }
        },
    }

}
</script>
<style>
</style>
