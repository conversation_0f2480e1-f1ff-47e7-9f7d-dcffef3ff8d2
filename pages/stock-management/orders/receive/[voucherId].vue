<template>
    <div class="px-5 py-5">

        <CoreBreadcrumb :pages="pages" />

        <div class="py-5 flex items-center justify-between">
            <h3 class="text-2xl font-semibold">{{ header }}</h3>
            <CoreActionButton text="Receive Order" color="success" :icon="checkIcon" :click="(() => receiveOrder())" />
        </div>

        <div>
            <div class="py-2" v-show="!loading">
                <div class="flex items-center space-x-3">
                    <img src="~assets/icons/pharmacy_alt.svg" class="w-8 h-8" />
                    <h3 class="text-xl font-semibold">Pharmacy</h3>
                </div>
                <div class="border rounded mt-3">
                    <div class="px-4 py-2 bg-gray-50 font-medium rounded-t border-b">
                        Issuer
                    </div>
                    <div class="px-5 py-5">
                        <FormKit type="form" submit-label="Update"
                            @submit="hasIssuer ? updatePharmacyWorker('issuer') : addPharmacyWorker('issuer')"
                            :actions="false" #default="{ value }">
                            <div class="grid grid-cols-4 gap-5">
                                <FormKit type="text" label="Name" v-model="issuerName" validation="required" />
                                <FormKit type="text" label="Designation" v-model="issuerDesignation"
                                    validation="required" />
                                <FormKit type="text" label="Phone Number" v-model="issuerPhone" />
                                <FormKit type="text" label="Signature" v-model="issuerSignature" validation="required" />
                            </div>
                            <CoreActionButton type="submit" class="mt-3"
                                :text="hasIssuer ? 'Update changes' : 'Save changes'" :icon="saveIcon" :loading="adding"
                                :click="(() => { })" :color="hasIssuer ? 'primary' : 'success'" />
                        </FormKit>
                    </div>
                </div>

                <div class="border rounded mt-3">
                    <div class="px-4 py-2 bg-gray-50 font-medium rounded-t border-b">
                        Approver
                    </div>
                    <div class="px-5 py-5">
                        <FormKit type="form" submit-label="Update"
                            @submit="hasApprover ? updatePharmacyWorker('approver') : addPharmacyWorker('approver')"
                            :actions="false" #default="{ value }">
                            <div class="grid grid-cols-4 gap-5">
                                <FormKit type="text" label="Name" v-model="approverName" validation="required" />
                                <FormKit type="text" label="Designation" v-model="approverDesignation"
                                    validation="required" />
                                <FormKit type="text" label="Phone Number" v-model="approverPhone" />
                                <FormKit type="text" label="Signature" v-model="approverSignature" validation="required" />
                            </div>
                            <CoreActionButton type="submit" class="mt-3"
                                :text="hasApprover ? 'Update changes' : 'Save changes'" :icon="saveIcon" :loading="adding"
                                :click="(() => { })" :color="hasApprover ? 'primary' : 'success'" />
                        </FormKit>
                    </div>
                </div>

            </div>

            <div v-show="!loading" class="rounded border">
                <div class="px-4 py-2 bg-gray-50 font-medium rounded-t border-b">
                    Stock Items
                </div>
                <div class="flex flex-col space-y-3 px-5 py-5" v-for="(requisition, index) in requisitions" :key="index">
                    <div class="grid grid-cols-4 gap-4 mt-5">
                        <div class="flex flex-col space-y-2">
                            <FormKit label="Stock item" :disabled="true" v-model="requisition.item.name" />
                        </div>
                        <FormKit label="Quantity requested" type="number" validation="required"
                            v-model="requisition.quantity_requested" disabled />

                        <div class="mt-8 flex space-x-2.5 items-center">
                            <StockOrdersReceiveDialog :data="requisition" @update="init"
                                v-if="requisition.requisition_status.toLowerCase() !== 'received'" :disabled="!(hasIssuer && hasApprover)"/>
                            <StockOrdersNotCollected @update="init" :data="requisition"
                                :orderId="`${$route.params.voucherId}`"
                                v-if="requisition.requisition_status.toLowerCase() !== 'received'" />
                        </div>
                    </div>
                </div>
            </div>
            <div v-show="loading">
                <div class="flex items-center space-x-3 mb-3" v-for="i in 5" :key="i">
                    <div class="w-1/4 bg-gray-100 rounded animate-pulse h-10 mt"></div>
                    <div class="w-1/4 bg-gray-100 rounded animate-pulse h-10"></div>
                    <div class="w-1/4 bg-gray-100 rounded animate-pulse h-10"></div>
                    <div class="w-1/4 bg-gray-100 rounded animate-pulse h-10"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">

import type { Page, Response } from "@/types"
import { DocumentCheckIcon, ArrowDownTrayIcon, NoSymbolIcon, TicketIcon, TrashIcon, ArchiveBoxArrowDownIcon } from "@heroicons/vue/24/solid/index.js";
import StockModule from "@/repository/modules/stock";
import Package from '@/package.json'

export default {
    setup() {
        definePageMeta({
            layout: 'dashboard'
        });
        useHead({
            title: `${Package.name.toUpperCase()} - Receive Stock Order`
        });
    },
    components: { TicketIcon },
    data() {
        return {
            header: 'Receive Stock Order' as string,
            checkIcon: ArchiveBoxArrowDownIcon,
            saveIcon: ArrowDownTrayIcon,
            allIcon: DocumentCheckIcon,
            rejectIcon: NoSymbolIcon,
            pages: [
                {
                    name: 'Home',
                    link: '/home',
                },
                {
                    name: 'Stock Orders',
                    link: '/stock-management/orders',
                }
            ] as Page,
            cookie: useCookie('token'),
            deleteicon: TrashIcon,
            loading: false as boolean,
            adding: false as boolean,
            receiving: false as boolean,
            verifying: false as boolean,
            requisitions: new Array<any>(),
            voidReason: '' as string,
            issuerName: '' as string,
            issuerDesignation: '' as string,
            issuerPhone: '' as string,
            issuerSignature: '' as string,
            approverName: '' as string,
            approverDesignation: '' as string,
            approverPhone: '' as string,
            approverSignature: '' as string,
            pharmacyWorkers: new Array<any>(),
        };
    },
    created() {
        this.init();
    },
    computed: {
        hasApprover() {
            return this.pharmacyWorkers.length == 0 ? false : this.pharmacyWorkers.some(item => item.record_type === 'approver');
        },
        hasIssuer() {
            return this.pharmacyWorkers.length == 0 ? false : this.pharmacyWorkers.some(item => item.record_type === 'issuer');
        }
    }
    ,
    methods: {
        async addPharmacyWorker(type: string): Promise<void> {
            this.adding = true;
            const stockModule = new StockModule();
            const params = {
                stock_order_id: `${this.$route.query.order_id}`,
                record_type: type,
                name: type == 'issuer' ? this.issuerName : this.approverName,
                designation: type == 'issuer' ? this.issuerDesignation : this.approverDesignation,
                phone_number: type == 'issuer' ? this.issuerPhone : this.approverPhone,
                signature: type == 'issuer' ? this.issuerSignature : this.approverSignature
            }
            const { data, error, pending }: Response = await stockModule.createStockOrderPharmacy(`${this.cookie}`, params);
            this.adding = pending;
            if (data.value) {
                this.init();
                useNuxtApp().$toast.success('Stock order pharmacy details saved successfully')
                this.adding = false;
            }
            if (error.value) {
                console.error(error.value)
                useNuxtApp().$toast.error(ERROR_MESSAGE)
                this.adding = false;
            }
        },
        async updatePharmacyWorker(type: string): Promise<void> {
            this.adding = true;
            const stockModule = new StockModule();
            const params = {
                pharmacy_id: this.pharmacyWorkers.filter((item) => item.record_type == type)[0].id,
                record_type: type,
                name: type == 'issuer' ? this.issuerName : this.approverName,
                designation: type == 'issuer' ? this.issuerDesignation : this.approverDesignation,
                phone_number: type == 'issuer' ? this.issuerPhone : this.approverPhone,
                signature: type == 'issuer' ? this.issuerSignature : this.approverSignature
            }
            const { data, error, pending }: Response = await stockModule.updateStockOrderPharmacy(`${this.cookie}`, params);
            this.adding = pending;
            if (data.value) {
                this.init();
                useNuxtApp().$toast.success('Stock order pharmacy details updated successfully')
                this.adding = false;
            }
            if (error.value) {
                console.error(error.value)
                useNuxtApp().$toast.error(ERROR_MESSAGE)
                this.adding = false;
            }
        },
        async init(): Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const { data, error, pending }: Response = await stockModule.getStockOrder(`${this.cookie}`, `${this.$route.query.order_id}`);
            this.loading = pending;
            if (data.value) {
                this.requisitions = data.value.stock_requisitions
                data.value.stock_pharmacy_approver_and_issuers.map((item: any) => {
                    if (item.record_type == 'issuer') {
                        this.issuerName = item.name;
                        this.issuerPhone = item.phone_number;
                        this.issuerDesignation = item.designation;
                        this.issuerSignature = item.signature;
                    } else {
                        this.approverName = item.name;
                        this.approverPhone = item.phone_number;
                        this.approverDesignation = item.designation;
                        this.approverSignature = item.signature;
                    }
                })
                this.pharmacyWorkers = data.value.stock_pharmacy_approver_and_issuers
                this.loading = false;
            }
            if (error.value) {
                console.error(error.value)
                this.loading = false;
            }
        },
        deleteStockItem(index: number): void {
            if (index >= 0 && index < this.requisitions.length) {
                this.requisitions.splice(index, 1);
            }
        },
        isReceivedOrNotCollected(array: Array<any>): boolean {
            for (let i = 0; i < array.length; i++) {
                if (array[i].requisition_status.toLowerCase() === "received" || array[i].requisition_status.toLowerCase() === "not collected") {
                    return true;
                }
            }
            return false;
        },
        async receiveOrder(): Promise<void> {
            if (this.isReceivedOrNotCollected(this.requisitions)) {
                if (this.hasApprover && this.hasIssuer) {
                    this.receiving = true;
                    const stockModule = new StockModule();
                    const params = {
                        stock_order_id: `${this.$route.query.order_id}`,
                    }
                    const { data, error, pending }: Response = await stockModule.receiveStockOrder(`${this.cookie}`, params);
                    this.receiving = pending;
                    if (data.value) {
                        useNuxtApp().$toast.success('Stock order received successfully!');
                        this.$router.push('/stock-management/orders')
                        this.receiving = false;
                    }
                    if (error.value) {
                        console.error(error.value)
                        this.receiving = false;
                    }
                } else {
                    useNuxtApp().$toast.warn('Order should be issued and approved by pharmacy!')
                }

            }
            else {
                useNuxtApp().$toast.warn('Please enter the amount recieved or mark as not collected!')
            }
        }
    }

}
</script>
<style>
</style>
