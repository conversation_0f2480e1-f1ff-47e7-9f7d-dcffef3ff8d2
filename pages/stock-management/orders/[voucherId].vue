<template>
    <div class="px-5 py-5">
        <CoreBreadcrumb :pages="pages" />
        <div class="py-5 flex items-center justify-between">
            <h3 class="text-2xl font-semibold">{{ header }}</h3>
        </div>
        <div class="rounded border">
            <div class="flex rounded-t justify-between bg-gray-50 border-b px-2 py-2 cursor-text">
                <div class="flex items-center space-x-2">
                    <TicketIcon class="h-5 w-5" />
                    <p>Voucher Number: <strong>{{ $route.params.voucherId }}</strong></p>
                </div>
                <StockOrdersCheckout v-if="isValidRequisitions(requisitions)" @update="navigateOrders"
                    :voucher-id="`${$route.params.voucherId}`" :requisitions="requisitions" />
            </div>

            <div class="px-5 py-5">
                <div class="flex px-2 py-2 mb-4 rounded-r border-l-4 border-sky-100 bg-sky-50">
                    Click on the <strong class="mx-2">"Add Stock Item"</strong> button to start creating your order
                    requisition items
                </div>

                <CoreActionButton :icon="addIcon" text="Add Stock Item" color="success" :click="addStockItem" />

                <div class="flex flex-col space-y-3" v-for="(requisition, index) in requisitions" :key="index">
                    <div class="grid grid-cols-4 gap-4 mt-5">
                        <CoreMultiselect label="Stock item" :items="stockItemOptions"
                        v-model:items-selected="requisition.stock_item.name" @update:items-selected="updateStockItem($event, index)" />
                        <FormKit label="Quantity requested" type="number" validation="required"
                            v-model="requisition.quantity_requested" />
                        <div class="mt-8">
                            <CoreActionButton :icon="deleteIcon" text="Delete" color="error"
                                :click="() => deleteStockItem(index)" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { PlusIcon, TicketIcon, TrashIcon } from '@heroicons/vue/24/solid/index.js'
import StockModule from '@/repository/modules/stock'
import type { Page, RequisitionItem, StockItem } from '@/types'
import Package from '@/package.json'

definePageMeta({
    layout: 'dashboard'
})

useHead({
    title: `${Package.name.toUpperCase()} - Create Stock Order`
});

const router = useRouter()
const header = ref<string>('Create Stock Order')
const pages = ref<Page>([
    {
        name: 'Home',
        link: '/home',
    },
    {
        name: 'Stock Orders',
        link: '/stock-management/orders',
    }
])

const loading = ref<boolean>(false)
const requisitions = reactive<RequisitionItem[]>([])
const stockItems = ref<StockItem[]>([])
const addIcon = PlusIcon
const deleteIcon = TrashIcon

const stockItemOptions = computed(() =>
    stockItems.value.map((item) => item.name)
)

const init = async (): Promise<void> => {
    loading.value = true;
    const stockModule = new StockModule()
    const { data, error } = await stockModule.getStockItem(``);

    if (data.value) {
        stockItems.value = data.value
    }
    if (error.value) {
        console.error(error.value)
    }
    loading.value = false;
}

const addStockItem = (): void => {
    requisitions.push({
        stock_item: { name: '', id: 0 },
        quantity_requested: 0,
        id: 0,
        batch_number: '',
        lot_number: ''
    })
}

const deleteStockItem = (index: number): void => {
    if (index >= 0 && index < requisitions.length) {
        requisitions.splice(index, 1)
    }
}

const updateStockItem = (selectedName: string, index: number): void => {
    const selectedItem = stockItems.value.find(item => item.name === selectedName);
    if (selectedItem && requisitions[index]) {
        Object.assign(requisitions[index].stock_item, {
            name: selectedItem.name,
            id: selectedItem.id
        });
    }
};

const isValidRequisitions = (requisitions: RequisitionItem[]): boolean => {
    if (requisitions.length === 0) {
        return false
    }

    return requisitions.every(requisition =>
        requisition.quantity_requested > 0
    )
}

const navigateOrders = (value: boolean): void => {
    if (value) {
        router.push('/stock-management/orders');
    }
}

onMounted(() => {
    init()
})
</script>

<style scoped></style>