<template>
    <div class="px-5 py-5">

        <CoreBreadcrumb :pages="pages" />

        <div class="py-5 flex items-center justify-between">
            <h3 class="text-2xl font-semibold">{{ header }}</h3>
        </div>

        <div class="px-4 py-2 bg-gray-50 flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <TicketIcon class="h-5 w-5" />
                <p>Voucher Number: <strong>{{ $route.params.voucherId }}</strong></p>
            </div>
            <div class="flex items-center space-x-3 justify-end">
                <CoreActionButton :loading="approving" :icon="allIcon" text="Approve Order" color="success"
                    :click="approveStockOrder" />
                    <StockOrdersRejectDialog :stockId="`${$route.query.order_id}`" :orderId="`${$route.params.voucherId}`"/>
            </div>
        </div>

        <div class="rounded border mt-5">
            <div class="flex items-center space-x-3 bg-gray-50 py-2 rounded-t px-2 border-b">
                <img src="~assets/icons/pharmacy_alt.svg" class="w-6 h-6" />
                <h3 class="text-lg font-semibold">Pharmacy</h3>
            </div>
            <div class="w-full grid grid-cols-2 gap-5 py-5 px-5">
                <div class="col-span-1 flex flex-col space-y-2">
                    <div class="w-full flex items-center space-x-2">
                        <p class="w-72 font-medium">Issued by: </p>
                        <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ issuerName }}</span>
                    </div>
                    <div class="w-full flex items-center space-x-2">
                        <p class="w-72 font-medium">Designation: </p>
                        <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{  issuerDesignation }}</span>
                    </div>
                    <div class="w-full flex items-center space-x-2">
                        <p class="w-72 font-medium">Signature: </p>
                        <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{  issuerSignature }}</span>
                    </div>
                    <div class="w-full flex items-center space-x-2">
                        <p class="w-72 font-medium">Date:</p>
                        <span
                            class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ issuedDate }}</span>
                    </div>
                </div>
                <div class="col-span-1 flex flex-col space-y-2">
                    <div class="w-full flex items-center space-x-2">
                        <p class="w-72 font-medium">Approved by: </p>
                        <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ approverName }}</span>
                    </div>
                    <div class="w-full flex items-center space-x-2">
                        <p class="w-72 font-medium">Designation: </p>
                        <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ approverDesignation }}</span>
                    </div>
                    <div class="w-full flex items-center space-x-2">
                        <p class="w-72 font-medium">Signature: </p>
                        <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{  issuerSignature }}</span>
                    </div>
                    <div class="w-full flex items-center space-x-2">
                        <p class="w-72 font-medium">Date:</p>
                        <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ approvedDate }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div v-show="!loading" class="flex flex-col space-y-3" v-for="(requisition, index) in requisitions" :key="index">
            <div class="grid grid-cols-5 gap-4 mt-5" v-if="requisition.requisition_status.toLowerCase() !== 'rejected'">
                <div class="flex flex-col space-y-2">
                    <FormKit label="Stock item" :disabled="true" v-model="requisition.item.name" />
                </div>
                <FormKit label="Quantity requested" type="number" :disabled="true"
                    v-model="requisition.quantity_requested" />

                <FormKit label="Quantity issued" type="number" :disabled="true" v-model="requisition.quantity_issued" />

                <FormKit label="Quantity collected" type="number" :disabled="true"
                    v-model="requisition.quantity_collected" />
                <div class="flex items-center space-x-2 pt-8">
                    <CoreActionButton text="Approve" :icon="checkIcon" color="success" :click="(() => {approveRequisition(requisition.id)})"/>
                    <StockOrdersRejectRequisition @update="init" :data="requisition" :orderId="`${$route.params.voucherId}`"/>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">

import type { Page, Response } from "@/types"
import { DocumentCheckIcon, NoSymbolIcon, TicketIcon, TrashIcon, CheckIcon } from "@heroicons/vue/24/solid/index.js";
import StockModule from "@/repository/modules/stock";
import moment from "moment";
import Package from '@/package.json'

export default {
    setup() {
        definePageMeta({
            layout: 'dashboard'
        });
        useHead({
            title: `${Package.name.toUpperCase()} - Approve Stock Order`
        });
    },
    components: {
        TicketIcon
    },
    data() {
        return {
            header: 'Approve Stock Order' as string,
            checkIcon: CheckIcon,
            allIcon: DocumentCheckIcon,
            rejectIcon: NoSymbolIcon,
            pages: [
                {
                    name: 'Home',
                    link: '/home',
                },
                {
                    name: 'Stock Orders',
                    link: '/stock-management/orders',
                }
            ] as Page,
            cookie: useCookie('token'),
            deleteicon: TrashIcon,
            loading: false as boolean,
            approving: false as boolean,
            verifying: false as boolean,
            requisitions: new Array<any>(),
            voidReason: '' as string,
            issuerName: '' as string,
            issuerDesignation: '' as string,
            issuerPhone: '' as string,
            issuerSignature: '' as string,
            approverName: '' as string,
            approverDesignation: '' as string,
            approverPhone: '' as string,
            approverSignature: '' as string,
            issuedDate: '' as string,
            approvedDate: '' as string,
        };
    },
    created() {
        this.init();
    },
    methods: {
        async init(): Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const { data, error, pending }: Response = await stockModule.getStockOrder(`${this.cookie}`, `${this.$route.query.order_id}`);
            this.loading = pending;
            if (data.value) {
                this.requisitions = data.value.stock_requisitions
                this.loading = false;
                data.value.stock_pharmacy_approver_and_issuers.map((item: any) => {
                    if (item.record_type == 'issuer') {
                        this.issuerName = item.name;
                        this.issuerPhone = item.phone_number;
                        this.issuerDesignation = item.designation;
                        this.issuerSignature = item.signature;
                        this.issuedDate = moment(item.created_date).format(DATE_FORMAT)
                    } else {
                        this.approverName = item.name;
                        this.approverPhone = item.phone_number;
                        this.approverDesignation = item.designation;
                        this.approverSignature = item.signature;
                        this.approvedDate = moment(item.created_date).format(DATE_FORMAT)
                    }
                })
            }
            if (error.value) {
                console.error(error.value)
                this.loading = false;
            }
        },
        async approveStockOrder(): Promise<void> {
            this.approving = true;
            const stockModule = new StockModule();
            let requisitions = this.requisitions.map((requisition) => requisition.id );
            const { data, error, pending }: Response = await stockModule.approveStockOrder(`${this.cookie}`, { stock_order_id: `${this.$route.query.order_id}`, stock_requisition_ids: requisitions });
            this.approving = pending;
            if(data.value){
                this.approving = false;
                this.$router.push('/stock-management/orders');
                useNuxtApp().$toast.success(`Stock order request #${this.$route.params.voucherId} approved successfully`);
            }
            if(error.value){
                console.error(error.value);
                useNuxtApp().$toast.error(ERROR_MESSAGE);
                this.approving = false;
            }

        },
        async approveRequisition (id: number) : Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const { data, error, pending }: Response = await stockModule.approveStockOrderRequisition(`${this.cookie}`, { stock_requisition_id: id });
            this.loading = pending;
            if(data.value){
                this.loading = false;
                useNuxtApp().$toast.success(`Stock order requisition approved successfully`)
            }
            if(error.value){
                console.error(error.value);
                useNuxtApp().$toast.error(ERROR_MESSAGE);
                this.loading = false;
            }

        }
    }
}
</script>
<style>
</style>
e
