<template>
    <div class="px-5 py-5">
        <CoreBreadcrumb :pages="pages" />
        <div class="flex items-center justify-between py-5">

            <h3 class="text-2xl font-semibold">{{ header }}</h3>

            <div class="flex items-center space-x-3">
                <StockOrdersAddDialog />
            </div>

        </div>

        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <CoreDropdown :items="statuses" v-model="statusSelected" />
                <CoreActionButton v-if="statusSelected.id != 0" text="Clear filter" color="warning" :icon="clearIcon"
                    :click="(() => { clearFilter() })" :loading="loading" />
            </div>
            <CoreSearchBar v-model:search="search" />
        </div>

        <div class="mt-10">
            <CoreDatatable :data="orders" :headers="headers" :loading="loading" search-field="voucher_number"
                :search-value="search" :serverItemsLength="serverItemsLength" :serverOptions="serverOptions" @update="init">
                <template v-slot:actions="{ item }">
                    <div class="py-2 flex items-center space-x-2">
                        <StockOrdersViewDialog :data="item" />
                        <CoreActionButton v-for="(status, index) in orderStatuses" :key="index"
                            v-show="checkStatus(status, item)" :text="status.name"
                            :color="status.name == 'Reject' ? 'error' : 'success'" :icon="status.icon"
                            :click="() => { processStockOrder(status.name, item) }" />
                    </div>
                </template>
            </CoreDatatable>
        </div>
    </div>
</template>

<script lang="ts">

import { DocumentCheckIcon, CheckBadgeIcon, PencilSquareIcon, SquaresPlusIcon, PrinterIcon, XMarkIcon } from '@heroicons/vue/24/solid/index.js';
import moment from 'moment';
import StockModule from '@/repository/modules/stock';
import type { Header, Page, Response } from '@/types';
import type { ServerOptions } from 'vue3-easy-data-table';
import Package from '@/package.json'

export default {
    setup() {
        definePageMeta({
            layout: 'dashboard',
            middleware: ['stock-management']
        });
        useHead({
            title: `${Package.name.toUpperCase()} - Stock Orders`
        })
    },
    data() {
        return {
            header: 'Stock Orders' as string,
            pages: [
                {
                    name: 'Home',
                    link: '/home',
                },
                {
                    name: 'Stock Management',
                    link: '#',
                }
            ] as Page,
            search: '' as string,
            headers: [
                { text: "Voucher Number", value: "voucher_number", sortable: true },
                { text: "Requisitions", value: "requisitions" },
                { text: "Status", value: "stock_order_status" },
                { text: "Date Created", value: "created_date" },
                { text: "actions", value: "actions" }
            ] as Header,
            statuses: new Array<{ name: string, id: number }>(),
            statusSelected: { name: '-- select status -- ', id: 0 },
            orders: new Array<any>(),
            printIcon: PrinterIcon,
            editIcon: PencilSquareIcon,
            clearIcon: XMarkIcon,
            loading: false as boolean,
            statusLoading: false as boolean,
            cookie: useCookie('token'),
            serverItemsLength: 0 as number,
            serverOptions: <ServerOptions>{
                page: 1,
                rowsPerPage: 25,
                sortBy: "voucher_number",
            },
        }
    },
    created() {
        this.init();
        this.getOrderStatuses();
    },
    computed: {
        orderStatuses() {
            const statuses: Array<{ name: string, show: string, icon: any }> = [
                {
                    name: 'Edit',
                    show: 'pending',
                    icon: PencilSquareIcon
                },
                {
                    name: 'Verify',
                    show: 'draft',
                    icon: CheckBadgeIcon
                },
                {
                    name: 'Receive',
                    show: 'requested',
                    icon: SquaresPlusIcon
                },
                {
                    name: 'Approve',
                    show: 'received',
                    icon: DocumentCheckIcon
                }
            ];
            return statuses;
        }
    },
    methods: {
        async getOrderStatuses(): Promise<void> {
            const stockModule = new StockModule();
            const { data, error } = await stockModule.getStockOrderStatus(`${this.cookie}`)
            if (data.value) {
                data.value.map((value: { name: string; id: number; }) => {
                    this.statuses.push({ name: value.name, id: value.id })
                })
            }
            if (error.value) {
                console.error(error.value)
            }
        },
        async init(): Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            let stock_status_id = this.statusSelected.id == 0 ? '' : this.statusSelected.id
            const { page, rowsPerPage } = this.serverOptions;
            let pagination = `page=${page}&per_page=${rowsPerPage}`
            const { data, error, pending } = await stockModule.getStockOrder(`${this.cookie}`, '', this.search, `${stock_status_id}`, pagination)
            this.loading = pending;
            if (data.value) {
                this.orders = data.value.data.map((order: {
                    stock_requisitions: Array<any>;
                    created_date: moment.MomentInput;
                }) => ({
                    ...order,
                    requisitions: order.stock_requisitions.length,
                    created_date: moment(order.created_date).format(DATE_FORMAT)
                }))
                this.loading = false;
            }
            if (error.value) {
                console.error(error.value)
                this.loading = false;
            }
        },
        clearFilter(): void {
            this.statuses.push({ name: '-- select status -- ', id: 0 })
            this.statusSelected = this.statuses[this.statuses.length - 1];
            this.init();
        },
        checkStatus(status: { name: string, show: string }, orderStatuses: { stock_order_status: string }): boolean {
            if (status.show.toLowerCase() == orderStatuses.stock_order_status.toLowerCase()) {
                return true;
            }
            return false;
        },
        verifyStockOrder(orderId: string, voucher_number: string): void {
            this.$router.push(`/stock-management/orders/request/${voucher_number}?order_id=${orderId}`)
        },
        receiveStockOrder(orderId: string, voucher_number: string): void {
            this.$router.push(`/stock-management/orders/receive/${voucher_number}?order_id=${orderId}`)
        },
        approveStockOrder(orderId: string, voucher_number: string): void {
            this.$router.push(`/stock-management/orders/approve/${voucher_number}?order_id=${orderId}`)
        },
        rejectStockOrder(orderId: string, voucher_number: string): void {
            this.$router.push(`/stock-management/orders/reject/${voucher_number}?order_id=${orderId}`)
        },
        async changeStatus(status: string): Promise<void> {
            this.statusLoading = true;
            const stockModule = new StockModule();
            const params = {
                route: '',
                status: status
            }
            const { data, error, pending }: Response = await stockModule.updateStockOrderStatus(`${this.cookie}`, params);
            this.statusLoading = pending;
            if (data.value) {
                this.init();
                this.statusLoading = false;
            }
            if (error.value) {
                console.error(error.value);
                this.statusLoading = false;
            }
        },
        processStockOrder(status: string, item: { status: { name: string; }; id: any; voucher_number: any; }): void {
            if (status === 'Verify') {
                this.verifyStockOrder(item.id, item.voucher_number);
            } else if (status === 'Receive') {
                this.receiveStockOrder(item.id, item.voucher_number);
            } else if (status === 'Approve') {
                this.approveStockOrder(item.id, item.voucher_number);
            } else if (status === 'Reject') {
                this.rejectStockOrder(item.id, item.voucher_number);
            }
        }
    },
    watch: {
        search() {
            this.init();
        },
        statusSelected: {
            handler() {
                this.init();
            },
            deep: true
        }
    }
}
</script>

<style>
</style>
