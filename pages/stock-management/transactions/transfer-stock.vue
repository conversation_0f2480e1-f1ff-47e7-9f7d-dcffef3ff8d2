<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />
    <div class="flex items-center justify-between py-5">
      <h3 class="text-2xl font-semibold capitalize">Transfer Stock</h3>
      <div class="flex items-center space-x-3">
        <CoreOutlinedButton
          text="Clear Form"
          :click="clearForm"
          v-if="requisitions.length > 0 || reason.trim() !== ''"
        />
        <StockTransactionsCheckout
          :disable-checkout="!canSubmit"
          :data="{
            reason: reason,
            sendingTo: `${
              selectedItem.name.includes('select') ? '' : selectedItem.name
            } ${
              !selectedDestination.name.includes('select')
                ? selectedDestination.name
                : ''
            }`,
            requisitions: requisitions,
          }"
        />
      </div>
    </div>

    <div>
      <div class="w-full mt-2 grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Transfer Details Section -->
        <div class="lg:col-span-1 order-2 lg:order-2">
          <div class="bg-gray-50 border border-gray-200 rounded-lg">
            <div
              class="bg-gray-100 px-4 py-3 border-b border-gray-200 rounded-t-lg"
            >
              <h3 class="font-semibold text-lg text-gray-800 flex items-center">
                <InformationCircleIcon class="w-5 h-5 mr-2 text-sky-600" />
                Transfer Details
              </h3>
            </div>
            <div class="flex flex-col space-y-4 px-5 py-5">
              <div class="w-full flex flex-col space-y-2">
                <label class="font-medium text-gray-700"
                  >Destination Type</label
                >
                <CoreDropdown
                  :items="destinations"
                  v-model="selectedDestination"
                />
              </div>

              <div
                v-if="selectedDestination && selectedItems.length > 0"
                class="w-full flex flex-col space-y-2"
              >
                <label class="font-medium text-gray-700">
                  Select {{ selectedDestination.name.toLowerCase() }}
                  <span class="text-red-600 font-medium">*</span>
                </label>
                <CoreDropdown
                  :is-searchable="true"
                  :items="selectedItems"
                  v-model="selectedItem"
                />
              </div>

              <div class="w-full flex flex-col space-y-2">
                <FormKit
                  label="Reason for transfer"
                  type="textarea"
                  v-model="reason"
                  validation="required"
                  placeholder="Enter the reason for this stock transfer..."
                />
              </div>
            </div>
          </div>

          <!-- Transfer Summary -->
          <div
            v-if="selectedItemsCount > 0"
            class="mt-4 bg-gray-50 border border-gray-200 rounded-lg"
          >
            <div
              class="bg-gray-100 px-4 py-3 border-b border-gray-200 rounded-t-lg"
            >
              <h4 class="font-semibold text-lg text-gray-800 flex items-center">
                <ClipboardDocumentListIcon class="w-5 h-5 mr-2" />
                Transfer Summary
              </h4>
            </div>
            <div class="px-4 py-4 space-y-2 text-sm text-gray-700">
              <div class="flex justify-between">
                <span class="font-medium">Items selected:</span>
                <span class="font-semibold">{{ selectedItemsCount }}</span>
              </div>
              <div
                v-if="
                  selectedDestination &&
                  !selectedDestination.name.includes('select')
                "
                class="flex justify-between"
              >
                <span class="font-medium">Destination:</span>
                <span class="font-semibold">{{
                  selectedDestination.name
                }}</span>
              </div>
              <div
                v-if="selectedItem && !selectedItem.name.includes('select')"
                class="flex justify-between"
              >
                <span class="font-medium">To:</span>
                <span class="font-semibold">{{ selectedItem.name }}</span>
              </div>
              <div v-if="reason" class="flex justify-between">
                <span class="font-medium line-clamp-1">Reason:</span>
                <span class="font-semibold"
                  >{{ reason.substring(0, 50)
                  }}{{ reason.length > 50 ? "..." : "" }}</span
                >
              </div>
            </div>
          </div>
        </div>

        <!-- Stock Items Section -->
        <div class="lg:col-span-3 order-1 lg:order-1">
          <div class="bg-gray-50 border border-gray-200 rounded-lg">
            <div
              class="bg-gray-100 px-4 py-3 border-b border-gray-200 rounded-t-lg flex items-center justify-between"
            >
              <h3 class="font-semibold text-lg text-gray-800 flex items-center">
                <CubeIcon class="w-5 h-5 mr-2" />
                Items to Transfer
              </h3>
              <CoreActionButton
                color="primary"
                :click="addStockItem"
                text="Add Item"
                :icon="addIcon"
                size="sm"
              />
            </div>
            <div class="px-5 py-5">
              <!-- Stock Items List -->
              <div v-if="requisitions.length > 0" class="space-y-4">
                <div
                  v-for="(requisition, index) in requisitions"
                  :key="index"
                  class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"
                >
                  <!-- Item Header -->
                  <div
                    class="flex items-center justify-between mb-4 pb-2 border-b border-gray-200"
                  >
                    <h4
                      class="text-sm font-semibold text-gray-900 flex items-center"
                    >
                      <span
                        class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs mr-2"
                      >
                        Item {{ index + 1 }}
                      </span>
                    </h4>
                    <CoreActionButton
                      :icon="deleteIcon"
                      text="Remove"
                      color="error"
                      :click="() => deleteStockItem(index)"
                      size="sm"
                    />
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Stock Item Selection with Search -->
                    <div class="flex flex-col space-y-2">
                      <label class="font-medium text-gray-700"
                        >Stock Item</label
                      >

                      <!-- Search Input -->
                      <div class="relative">
                        <input
                          type="text"
                          v-model="(requisition as any).searchQuery"
                          @focus="(requisition as any).showDropdown = true"
                          @blur="hideDropdownDelayed(requisition)"
                          :placeholder="requisition.stock_item.id ? (requisition.stock_item as any).displayName || requisition.stock_item.name : 'Search for stock items...'"
                          class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-0"
                        />
                        <div
                          class="absolute inset-y-0 right-0 flex items-center pr-3"
                        >
                          <svg
                            class="w-4 h-4 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                            ></path>
                          </svg>
                        </div>
                      </div>

                      <!-- Searchable List -->
                      <div
                        v-if="(requisition as any).showDropdown"
                        class="relative"
                      >
                        <div
                          class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-64 overflow-y-auto"
                        >
                          <div
                            v-if="getFilteredItems(requisition).length === 0"
                            class="px-4 py-3 text-gray-500 text-center"
                          >
                            No items found
                          </div>
                          <div v-else>
                            <div
                              v-for="item in getFilteredItems(requisition)"
                              :key="item.uniqueKey"
                              @mousedown="selectStockItem(requisition, item)"
                              class="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                            >
                              <div class="flex flex-col">
                                <div class="font-medium text-gray-900 text-sm">
                                  {{ item.displayName }}
                                </div>
                                <div
                                  class="text-xs text-gray-600 mt-1 flex items-center space-x-4"
                                >
                                  <span class="flex items-center">
                                    <span class="font-medium">Batch:</span>
                                    <span class="ml-1">{{
                                      item.batch_number || "N/A"
                                    }}</span>
                                  </span>
                                  <span class="flex items-center">
                                    <span class="font-medium">Lot:</span>
                                    <span class="ml-1">{{
                                      item.lot_number || "N/A"
                                    }}</span>
                                  </span>
                                  <span
                                    class="flex items-center text-green-500"
                                  >
                                    <span class="font-medium">Available:</span>
                                    <span class="ml-1">{{
                                      item.available_quantity || 0
                                    }}</span>
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Selected Item Info -->
                      <div
                        v-if="
                          requisition.stock_item &&
                          requisition.stock_item.id !== 0
                        "
                        class="text-xs text-gray-600 bg-gray-100 p-3 rounded"
                      >
                        <div class="grid grid-cols-2 gap-2">
                          <div class="flex justify-between">
                            <span><strong>Batch:</strong></span>
                            <span class="font-medium">{{
                              requisition.batch_number || "N/A"
                            }}</span>
                          </div>
                          <div class="flex justify-between">
                            <span><strong>Lot:</strong></span>
                            <span class="font-medium">{{
                              requisition.lot_number || "N/A"
                            }}</span>
                          </div>
                          <div class="flex justify-between">
                            <span><strong>Available:</strong></span>
                            <span class="font-medium text-green-500">{{
                              requisition.stock_item.quantity || "N/A"
                            }}</span>
                          </div>
                          <div
                            v-if="requisition.expiry_date"
                            class="flex justify-between"
                          >
                            <span><strong>Expires:</strong></span>
                            <span class="font-medium">{{
                              formatDate(requisition.expiry_date)
                            }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Quantity Input -->
                    <div class="flex flex-col space-y-2">
                      <FormKit
                        label="Quantity to Transfer"
                        type="number"
                        validation="required|min:1"
                        v-model="requisition.quantity_requested"
                        :max="requisition.stock_item.quantity"
                        placeholder="Enter quantity"
                      />
                      <div
                        v-if="
                          requisition.stock_item &&
                          requisition.stock_item.quantity
                        "
                        class="text-xs text-gray-500"
                      >
                        Max available: {{ requisition.stock_item.quantity }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Empty State -->
              <div
                v-else
                class="text-center py-16 bg-white rounded-lg border-2 border-dashed border-gray-300"
              >
                <CubeIcon class="w-20 h-20 text-gray-300 mx-auto mb-4" />
                <h3 class="text-xl font-semibold text-gray-900 mb-2">
                  No items selected for transfer
                </h3>
                <p class="text-gray-600 mb-6 max-w-md mx-auto">
                  Start by adding stock items with their batch and lot
                  information to create your transfer request. Use the "Add
                  Item" button above to get started.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  TrashIcon,
  PlusIcon,
  InformationCircleIcon,
  ClipboardDocumentListIcon,
  CubeIcon,
} from "@heroicons/vue/24/solid/index.js";
import type {
  Department,
  Request,
  RequisitionItem,
  Response,
  Ward,
  Page,
  DropdownItem,
  Facility,
  StockItem,
} from "@/types";
import StockModule from "@/repository/modules/stock";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import Package from "@/package.json";

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: `${Package.name.toUpperCase()} - Transfer Stock`,
});

const addIcon = PlusIcon;
const deleteIcon = TrashIcon;
const { $toast, $metadata } = useNuxtApp();
const destinations = ref<DropdownItem[]>([]);
const selectedDestination = ref<DropdownItem>({ name: "select destination" });
const reason = ref<string>("");
const requisitions = ref<RequisitionItem[]>([]);
const stockItems = ref<StockItem[]>([]);
const cookie = useCookie("token");
const departments = ref<Department[]>([]);
const facilities = ref<Facility[]>([]);
const wards = ref<Ward[]>([]);
const selectedItem = ref<DropdownItem>({ name: "select item" });
const pages = ref<Page>([
  { name: "Home", link: "/home" },
  { name: "Stock Management", link: "#" },
  { name: "Transactions", link: "/stock-management/transactions" },
]);
const requisitionLoading = ref<{ id: number; value: boolean }>({
  id: 0,
  value: false,
});
const selectedItems: ComputedRef<any[]> = computed(() => {
  const optionsMap = {
    Department: { items: departments, placeholder: "select department" },
    Facility: { items: facilities, placeholder: "select facility" },
    Ward: { items: wards, placeholder: "select ward" },
  } as const;
  const destination =
    optionsMap[selectedDestination.value.name as keyof typeof optionsMap];
  if (destination) {
    selectedItem.value = { name: destination.placeholder };
    return destination.items.value;
  }
  return [];
});

// Consolidated stock items computed property
const consolidatedStockItems = computed(() => {
  const consolidated: any[] = [];

  stockItems.value.forEach((item: any) => {
    // Parse batches and lots from comma-separated strings
    const batches = item.batches
      ? item.batches
          .split(",")
          .map((b: string) => b.trim())
          .filter((b: string) => b)
      : [];
    const lots = item.lots
      ? item.lots
          .split(",")
          .map((l: string) => l.trim())
          .filter((l: string) => l)
      : [];

    if (batches.length > 0 && lots.length > 0) {
      // Create combinations of batches and lots
      batches.forEach((batch: string) => {
        lots.forEach((lot: string) => {
          const itemName =
            item.name.length > 30
              ? `${item.name.substring(0, 30)}...`
              : item.name;
          consolidated.push({
            ...item,
            name: `${itemName}
Batch: ${batch || "N/A"} | Lot: ${lot || "N/A"}`,
            displayName: item.name,
            batch_number: batch,
            lot_number: lot,
            available_quantity: item.quantity || 0,
            uniqueKey: `${item.id}-${batch}-${lot}`,
          });
        });
      });
    } else if (batches.length > 0) {
      // Only batches available
      batches.forEach((batch: string) => {
        const itemName =
          item.name.length > 30
            ? `${item.name.substring(0, 30)}...`
            : item.name;
        consolidated.push({
          ...item,
          name: `${itemName}
Batch: ${batch || "N/A"} | Lot: N/A`,
          displayName: item.name,
          batch_number: batch,
          lot_number: "",
          available_quantity: item.quantity || 0,
          uniqueKey: `${item.id}-${batch}-`,
        });
      });
    } else if (lots.length > 0) {
      // Only lots available
      lots.forEach((lot: string) => {
        const itemName =
          item.name.length > 30
            ? `${item.name.substring(0, 30)}...`
            : item.name;
        consolidated.push({
          ...item,
          name: `${itemName}
Batch: N/A | Lot: ${lot || "N/A"}`,
          displayName: item.name,
          batch_number: "",
          lot_number: lot,
          available_quantity: item.quantity || 0,
          uniqueKey: `${item.id}--${lot}`,
        });
      });
    } else {
      // No batch or lot information
      const itemName =
        item.name.length > 30 ? `${item.name.substring(0, 30)}...` : item.name;
      consolidated.push({
        ...item,
        name: `${itemName}
Batch: N/A | Lot: N/A`,
        displayName: item.name,
        batch_number: "",
        lot_number: "",
        available_quantity: item.quantity || 0,
        uniqueKey: `${item.id}--`,
      });
    }
  });

  return consolidated;
});

// Count of actually selected items (not just added requisitions)
const selectedItemsCount = computed(() => {
  return requisitions.value.filter((req) => req.stock_item.id !== 0).length;
});

// Enhanced form validation
const canSubmit = computed(() => {
  return (
    selectedItemsCount.value > 0 &&
    selectedDestination.value.name !== "-- select destination --" &&
    selectedItem.value.name !== "-- select item --" &&
    reason.value.trim() !== "" &&
    requisitions.value.every(
      (req) =>
        req.stock_item.id !== 0 &&
        req.quantity_requested > 0 &&
        req.quantity_requested <= (req.stock_item.quantity || 0)
    )
  );
});

async function init(): Promise<void> {
  loadStockItems();
  addDestinations();
}

async function loadStockItems(): Promise<void> {
  const stockModule = new StockModule();
  const { data, error } = await stockModule.getStockItem(
    String(cookie.value),
    "metadata=true"
  );
  if (data.value) {
    stockItems.value = data.value;
  }
  if (error.value) {
    console.error(error.value);
    $toast.error("Failed to load stock items");
  }
}

async function loadFacilities(): Promise<void> {
  const request: Request = {
    route: endpoints.facility,
    method: "GET",
    token: cookie.value as string,
  };
  const { data, error }: Response = await fetchRequest(request);
  if (data.value) {
    facilities.value = data.value.data;
  }
  if (error.value) {
    console.error(error.value);
  }
}

async function loadWards(): Promise<void> {
  const request: Request = {
    route:
      "encounter_type_facility_section_mappings/facility_sections?encounter_type_id=2",
    method: "GET",
    token: cookie.value as string,
  };
  const { data, error }: Response = await fetchRequest(request);
  if (data.value) {
    wards.value = data.value;
  }
  if (error.value) {
    console.error(error.value);
  }
}

function addDestinations(): void {
  let destinationList: string[] = ["Facility", "Ward", "Department"];
  destinations.value = destinationList.map((destination) => ({
    name: destination,
  }));
}

async function checkStockQuantity(requisition: RequisitionItem): Promise<void> {
  requisitionLoading.value = { id: requisition.id, value: true };
  const stockModule = new StockModule();
  const params = {
    stock_item_id:
      stockItems.value.find((item) => item.name == requisition.stock_item.name)
        ?.id || 0,
    quantity: requisition.quantity_requested,
    batch: requisition.batch_number,
    lot: requisition.lot_number,
  };
  const { data, error, pending }: Response =
    await stockModule.checkStockQuantity(cookie.value as string, params);
  requisitionLoading.value = { id: requisition.id, value: pending };
  if (data.value) {
    if (!data.value.deduction_allowed) {
      useNuxtApp().$toast.warning(data.value.message);
      requisition.quantity_requested = 0;
    }
    data.value.available_quantity !== null
      ? (requisition.stock_item.quantity = data.value.available_quantity)
      : (requisition.stock_item.quantity = 0);
    requisitionLoading.value = { id: requisition.id, value: false };
  }
  if (error.value) {
    console.error(error.value);
    requisitionLoading.value = { id: requisition.id, value: false };
    $toast.error(
      "Could not verify stock quantity for given lot and batch, please try again"
    );
  }
}

function addStockItem(): void {
  requisitions.value.push({
    id: Date.now(),
    stock_item: { name: "select item", id: 0, quantity: 0 },
    quantity_requested: 1,
    batch_number: "",
    lot_number: "",
    expiry_date: "",
    searchQuery: "",
    showDropdown: false,
  } as any);
}

function deleteStockItem(index: number): void {
  if (index >= 0 && index < requisitions.value.length) {
    requisitions.value.splice(index, 1);
  }
}

// New functions for searchable stock item selection
function getFilteredItems(requisition: any): any[] {
  const query = (requisition.searchQuery || "").toLowerCase();

  // Get already selected items (excluding current requisition)
  const selectedItems = requisitions.value
    .filter((req) => req.id !== requisition.id && req.stock_item.id !== 0)
    .map((req) => ({
      id: req.stock_item.id,
      batch: req.batch_number,
      lot: req.lot_number,
    }));

  // Filter out already selected combinations
  let availableItems = consolidatedStockItems.value.filter((item) => {
    return !selectedItems.some(
      (selected) =>
        selected.id === item.id &&
        selected.batch === item.batch_number &&
        selected.lot === item.lot_number
    );
  });

  // Apply search query filter
  if (query) {
    availableItems = availableItems.filter(
      (item) =>
        item.displayName.toLowerCase().includes(query) ||
        (item.batch_number &&
          item.batch_number.toLowerCase().includes(query)) ||
        (item.lot_number && item.lot_number.toLowerCase().includes(query))
    );
  }

  return availableItems;
}

function selectStockItem(requisition: any, selectedItem: any): void {
  if (selectedItem && selectedItem.id) {
    requisition.stock_item = {
      name: selectedItem.displayName,
      id: selectedItem.id,
      quantity: selectedItem.available_quantity,
      displayName: selectedItem.displayName,
    };
    requisition.batch_number = selectedItem.batch_number || "";
    requisition.lot_number = selectedItem.lot_number || "";
    requisition.expiry_date = selectedItem.expiry_date || "";
    requisition.searchQuery = selectedItem.displayName;
    requisition.showDropdown = false;

    // Check stock quantity for the selected item
    if (requisition.stock_item.id) {
      checkStockQuantity(requisition);
    }
  }
}

function hideDropdownDelayed(requisition: any): void {
  setTimeout(() => {
    requisition.showDropdown = false;
  }, 200);
}

function formatDate(dateString: string): string {
  if (!dateString) return "N/A";
  try {
    return new Date(dateString).toLocaleDateString();
  } catch {
    return "N/A";
  }
}

// Clear form function
function clearForm(): void {
  selectedDestination.value = { name: "-- select destination --" };
  selectedItem.value = { name: "-- select item --" };
  reason.value = "";
  requisitions.value = [];
  // Close any open dropdowns
  requisitions.value.forEach((req: any) => {
    req.showDropdown = false;
    req.searchQuery = "";
  });
}

onMounted(() => {
  init();
});

watch(
  selectedDestination,
  (value: { name: string }) => {
    const destinations: {
      [key: string]: () => Promise<void>;
    } = {
      Department: async () => {
        departments.value = $metadata.departments;
      },
      Facility: loadFacilities,
      Ward: loadWards,
    };

    const loadFunction = destinations[value.name];
    if (loadFunction) {
      loadFunction();
    }
  },
  { deep: true }
);

watch(
  requisitions,
  (oldRequisitions: RequisitionItem[], newRequisitions: RequisitionItem[]) => {
    const oldStockItems = oldRequisitions.map(
      (requisition: RequisitionItem) => requisition.stock_item.name
    );
    const newStockItems = newRequisitions.map(
      (requisition: RequisitionItem) => requisition.stock_item.name
    );
    if (oldStockItems !== newStockItems) {
      newRequisitions.forEach((requisition) => {
        requisition.stock_item.id =
          stockItems.value.find(
            (item) => item.name === requisition.stock_item.name
          )?.id || 0;
        requisition.stock_item.id && checkStockQuantity(requisition);
      });
    }
  },
  { deep: true }
);
</script>
<style scoped></style>
