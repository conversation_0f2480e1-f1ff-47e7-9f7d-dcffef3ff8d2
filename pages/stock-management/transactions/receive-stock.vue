<template>
    <div class="px-5 py-5">
        <CoreBreadcrumb :pages="pages" />
        <div class="flex items-center justify-between py-5">
            <h3 class="text-2xl font-semibold capitalize">Receive External Stock</h3>
            <StockTransactionsExternalStock
                :data="{ reason: reason, receiving_from: `${selectedItem.name.includes('select') ? '' : selectedItem.name} ${!selectedDestination.name.includes('select') ? selectedDestination.name : ''}`, requisitions: requisitions }" />
        </div>
        <div>

            <div class="w-full mt-2 grid grid-cols-5 gap-2">
                <div class="col-span-1 order-2 rounded border">
                    <div class="bg-gray-50 px-2 py-2 border-b rounded-t">
                        <h3 class="font-semibold text-lg">Details</h3>
                    </div>
                    <div class="flex flex-col space-y-2 px-5 py-5">
                        <div class="w-full flex flex-col space-y-2">
                            <label class="font-medium">Receive From</label>
                            <CoreDropdown :items="destinations" v-model="selectedDestination" />
                        </div>
                        <div v-if="selectedDestination && selectedItems.length > 0"
                            class="w-full flex flex-col space-y-2">
                            <label class="font-medium">Select {{ selectedDestination.name.toLowerCase() }}<span
                                    class="text-red-600 font-medium">*</span></label>
                            <CoreDropdown :is-searchable="true" :items="selectedItems" v-model="selectedItem" />
                        </div>
                        <FormKit label="Reason for receiving" type="textarea" v-model="reason" validation="required" />
                    </div>
                </div>

                <div class="col-span-4 order-1 rounded border">
                    <div class="bg-gray-50 px-2 py-2 border-b rounded-t">
                        <h3 class="font-semibold text-lg">Stock Items</h3>
                    </div>
                    <div class="px-5 py-5">
                        <CoreActionButton v-show="requisitions.length > 0" color="primary" :click="addStockItem"
                            text="Add items" :icon="addIcon" />

                        <div class="flex flex-col space-y-3 mt-5" v-for="(requisition, index) in requisitions"
                            :key="index">
                            <div class="grid grid-cols-6 gap-5 mb-5">
                                <CoreMultiselect label="Stock item" mode="single" :items="stockItems.map((s) => s.name)"
                                    v-model:items-selected="requisition.stock_item.name" />
                                <FormKit label="Quantity requested" type="number" validation="required"
                                    v-model.lazy="requisition.quantity_requested" :delay="1000" />

                                <FormKit label="Batch" type="text" validation="required"
                                    v-model.lazy="requisition.batch_number" :delay="2000" />

                                <FormKit label="Lot" type="text" validation="required" v-model.lazy="requisition.lot_number"
                                    :delay="2000" />
                                
                                <div class="items-center space-y-2">
                                    <label class="font-medium">Expiry Date</label>
                                    <datepicker  required position="left" placeholder="select start & end date" :range="false" input-class-name="datepicker" format="dd/MM/yyyy" v-model="requisition.expiry_date" />
                                </div>
                                
                                <div class="mt-8 flex items-center space-x-2">
                                    <CoreLoader class="w-5 h-5"
                                        v-if="requisitionLoading.id == requisition.id && requisitionLoading.value" />
                                    <CoreActionButton v-if="!requisitionLoading.value" :icon="deleteIcon" text="Delete"
                                        color="error" :click="(() => { deleteStockItem(index) })" />
                                </div>
                            </div>
                        </div>
                        <div v-show="requisitions.length == 0"
                            class="flex flex-col items-center justify-center space-y-2">
                            <img src="@/assets/icons/stock_out.svg" class="w-28 h-28 text-red-500"
                                alt="stock-out-icon" />
                            <p class="flex items-center">No stock items entered for receive</p>
                            <button @click="addStockItem" type="button"
                                class="flex items-center text-sky-500 font-medium">
                                <SquaresPlusIcon class="w-5 h-5 mr-2" />
                                Add stock item
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

import type { Page, RequisitionItem, Ward, Request, Response, StockSupplier, DropdownItem, StockItem } from '@/types';
import { SquaresPlusIcon, PlusIcon, TrashIcon } from '@heroicons/vue/24/solid/index.js';
import fetchRequest from '@/services/fetch';
import { endpoints } from '@/services/endpoints';
import StockModule from '@/repository/modules/stock';
import Package from '@/package.json';

definePageMeta({
    layout: 'dashboard',
});
useHead({
    title: `${Package.name.toUpperCase()} - Receive Stock`
});

const addIcon = PlusIcon;
const deleteIcon = TrashIcon;
const destinations = ref<DropdownItem[]>([]);
const selectedDestination = ref<DropdownItem>({ name: '-- select sender --' });
const reason = ref<string>("");
const requisitions = ref<RequisitionItem[]>([]);
const stockItems = ref<StockItem[]>([]);
const cookie = useCookie('token');
const stockModule = new StockModule()
const suppliers = ref<StockSupplier[]>([]);
const facilities = ref<any[]>([]);
const wards = ref<Ward[]>([]);
const selectedItem = ref<DropdownItem>({ name: '-- select item --' });
const pages = ref<Page>([
    {
        name: 'Home',
        link: '/home',
    },
    {
        name: 'Stock Management',
        link: '#',
    },
    {
        name: 'Transactions',
        link: '/stock-management/transactions',
    }
])
const requisitionLoading = ref<Record<string, number | boolean>>({ id: 0, value: false });
const selectedItems = computed(() => {
    const itemMap: Record<string, any[]> = {
        'Supplier': suppliers.value,
        'Facility': facilities.value,
        'Ward': wards.value
    }

    const defaultSelections: Record<string, DropdownItem> = {
        'Supplier': { name: '-- select supplier --' },
        'Facility': { name: '-- select facility --' },
        'Ward': { name: '-- select ward --' }
    }

    selectedItem.value = defaultSelections[selectedDestination.value.name] || { name: '-- select item --' }
    return itemMap[selectedDestination.value.name] || []
});

const loadStockItems = async (): Promise<void> => {
    const { data, error } = await stockModule.getStockItem(cookie.value as string)
    if (data.value) stockItems.value = data.value
    if (error.value) console.error(error.value)
}

const loadFacilities = async (): Promise<void> => {
    const request: Request = {
        route: endpoints.facility,
        method: "GET",
        token: cookie.value as string
    }
    const { data, error }: Response = await fetchRequest(request)
    if (data.value) facilities.value = data.value.data
    if (error.value) console.error(error.value)
}

const loadSuppliers = async (): Promise<void> => {
    const { data, error }: Response = await stockModule.getStockSupplier(cookie.value as string)
    if (data.value) suppliers.value = data.value
    if (error.value) console.error(error.value)
}

const loadWards = async (): Promise<void> => {
    const request: Request = {
        route: 'encounter_type_facility_section_mappings/facility_sections?encounter_type_id=2',
        method: "GET",
        token: cookie.value as string
    }
    const { data, error }: Response = await fetchRequest(request)
    if (data.value) wards.value = data.value
    if (error.value) console.error(error.value)
}

const addStockItem = (): void => {
    requisitions.value.push({
        id: 0,
        stock_item: { name: '-- select item --', id: 0 },
        quantity_requested: 0,
        batch_number: '',
        lot_number: '',
        expiry_date: ''
    })
}

const deleteStockItem = (index: number): void => {
    if (index >= 0 && index < requisitions.value.length) {
        requisitions.value.splice(index, 1)
    }
}

const addDestinations = (): void => {
    let d = ['Supplier', 'Ward', 'Facility'];
    d.forEach((destination: string) => destinations.value.push({ name: destination }));
}

watch(() => selectedDestination.value, (newValue: DropdownItem) => {
    const destinationHandlers: Record<string, () => Promise<void>> = {
        'Supplier': loadSuppliers,
        'Facility': loadFacilities,
        'Ward': loadWards
    }
    const handler = destinationHandlers[newValue.name]
    if (handler) handler()
}, { deep: true });

watchEffect(() => {
  const itemMap = Object.fromEntries(stockItems.value.map(item => [item.name, item.id]))
  requisitions.value.forEach((requisition: RequisitionItem, index: number) => {
    const newId = itemMap[requisition.stock_item.name] || 0
    if (newId !== requisition.stock_item.id) {
      requisitions.value[index] = {
        ...requisition,
        stock_item: {
          ...requisition.stock_item,
          id: newId
        }
      }
    }
  })
})

onMounted(() => {
    addDestinations()
    loadStockItems()
});
</script>

<style></style>
