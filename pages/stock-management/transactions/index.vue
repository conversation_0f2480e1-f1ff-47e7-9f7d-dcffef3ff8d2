<template>
    <div class="px-5 py-5">

        <CoreBreadcrumb :pages="pages" />

        <div class="flex items-center justify-between py-5">

            <h3 class="text-2xl font-semibold">{{ header }}</h3>

            <div class="flex items-center space-x-3">
                <CoreActionButton text="Receive stock" type="button" color="warning" :icon="transferIcon"
                    :click="(() => $router.push('/stock-management/transactions/receive-stock'))" />
                <CoreActionButton text="Transfer stock" type="button" color="success" :icon="transferIcon"
                    :click="(() => $router.push('/stock-management/transactions/transfer-stock'))" />
            </div>

        </div>

        <div class="flex items-center justify-end py-5">
            <CoreSearchBar v-model:search="search" @update="init" />
        </div>

        <div>
            <CoreDatatable :headers="headers" :data="filteredTransactions" :loading="loading" :searchField="'name'"
                :searchValue="search" :serverItemsLength="serverItemsLength" :serverOptions="serverOptions" @update="init">
                <template v-slot:actions="{ item }">
                    <div class="py-2 flex items-center space-x-2">
                        <StockTransactionsViewDialog :data="item" />
                    </div>
                </template>
            </CoreDatatable>
        </div>

    </div>
</template>

<script lang="ts">

import type { Header, Page, Response } from '@/types';
import { ArrowTopRightOnSquareIcon } from '@heroicons/vue/24/solid/index.js';
import StockModule from '@/repository/modules/stock';
import moment from 'moment';
import type { ServerOptions } from 'vue3-easy-data-table';
import Package from '@/package.json'

export default {
    setup() {
        definePageMeta({
            layout: 'dashboard'
        });
        useHead({
            title: `${Package.name.toUpperCase()} - Stock Transactions`
        })
    },
    data() {

        return {
            header: 'Stock Transactions',
            search: '' as string,
            loading: false as boolean,
            transferIcon: ArrowTopRightOnSquareIcon,
            viewIcon: ArrowTopRightOnSquareIcon,
            pages: [
                {
                    name: 'Home',
                    link: '/home',
                },
                {
                    name: 'Stock Management',
                    link: '#',
                }
            ] as Page,
            headers: [
                { text: "item", value: "name", sortable: true },
                { text: "type", value: "transaction_type" },
                { text: "sender", value: "receiving_from" },
                { text: "receiver", value: "sending_to" },
                { text: "batch", value: "batch" },
                { text: "lot", value: "lot" },
                { text: "expiry date", value: "expiry_date", sortable: true },
                { text: "quantity", value: "transacted_quantity", sortable: true },
                { text: "transaction date", value: "transaction_date", sortable: true },
                { text: "actions", value: "actions" }
            ] as Header,
            transactions: new Array<any>(),
            cookie: useCookie('token'),
            serverItemsLength: 0 as number,
            serverOptions: <ServerOptions>{
                page: 1,
                rowsPerPage: 25,
                sortBy: "name",
            },
        }
    },
    created() {
        this.init(this.search);
    },
    computed: {
        filteredTransactions() {
            return this.transactions.map((transaction) => ({
                ...transaction,
                expiry_date: moment(transaction.expiry_date).format(DATE_FORMAT),
                transaction_date: moment(transaction.transaction_date).format(DATE_FORMAT),
                sending_to: transaction.sending_to == null ? 'Laboratory Store' : transaction.sending_to,
                receiving_from: transaction.receiving_from == null ? 'Laboratory Store' : transaction.receiving_from
            }))
        }
    },
    methods: {
        async init(search: string): Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const { page, rowsPerPage } = this.serverOptions;
            const params = `search=${search}&page=${page}&per_page=${rowsPerPage}`
            const { data, pending, error }: Response = await stockModule.getStockTransactions(`${this.cookie}`, params);
            this.loading = pending;
            if (data.value) {
                this.transactions = data.value.data
                this.loading = false;
                this.serverItemsLength = data.value.meta.total_count;
            }
            if (error.value) {
                console.error(error.value)
                this.loading = false;
            }
        }
    }
}
</script>
<style>
</style>
