<template>
    <div class="px-5 py-5">

        <CoreBreadcrumb :pages="pages" />

        <div class="flex items-center justify-between py-5">

            <h3 class="text-2xl font-semibold">{{ header }}</h3>

            <StockMetricsAddDialog @update="init" />

        </div>

        <div class="flex items-center justify-end py-5">
            <CoreSearchBar v-bind:search="search" />
        </div>

        <div>
            <CoreDatatable :headers="headers" :data="metrics" :loading="loading">
                <template v-slot:actions="{ item }">
                    <div class="py-2 flex items-center space-x-2">
                        <StockMetricsViewDialog :data="item" @update="init"/>
                        <StockMetricsEditDialog :data="item" @update="init"/>
                        <StockMetricsDeleteDialog :data="item" @update="init"/>
                    </div>
                </template>
            </CoreDatatable>
        </div>
    </div>
</template>

<script lang="ts">

import type { Page, Response, Header } from '@/types';
import StockModule from '@/repository/modules/stock';
import moment from 'moment';
import Package from '@/package.json';

export default {
    setup(){
        definePageMeta({
            layout: 'dashboard'
        });
        useHead({
            title: `${Package.name.toUpperCase()} - Stock Metrics`
        })
    },
    data() {
        return {
            header: 'Stock Metrics' as string,
            cookie: useCookie('token'),
            search: '' as string,
            loading: false as boolean,
            headers: [
                { text: "name", value: "name", sortable: true },
                { text: "date created", value: "created_date" },
                { text: "actions", value: "actions" }
            ] as Header,
            metrics: new Array<any>(),
            pages: [
                {
                    name: 'Home',
                    link: '/home',
                },
                {
                    name: 'Stock Management',
                    link: '#',
                }
            ] as Page,
        }
    },
    created() {
        this.init();
    },
    methods: {
        async init(): Promise<void> {
            this.loading = true;
            const stockModule = new StockModule();
            const { data, pending, error }: Response = await stockModule.getStockUnit(`${this.cookie}`);
            this.loading = pending;
            if (data.value) {
                this.metrics = data.value.map((metric: { created_date: string }) => ({
                    ...metric,
                    created_date: moment(metric.created_date).format(DATE_FORMAT)
                }))
                this.loading = false;
            }
            if (error.value) {
                console.error(error.value)
                this.loading = false;
            }
        }
    }
}
</script>
<style lang="">

</style>
