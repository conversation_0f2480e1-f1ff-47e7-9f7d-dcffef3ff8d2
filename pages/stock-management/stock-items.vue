<template>
    <div class="px-5 py-5">
        <CoreBreadcrumb :pages="pages" />
        <div class="flex items-center justify-between py-5">
            <h3 class="text-2xl font-semibold uppercase">{{ header }}</h3>
            <div class="flex items-center space-x-3" v-if="can.manage('stock_management')">
                <StockItemsAddDialog @update="init" />
                <StockItemsImportDialog @update="init" />
            </div>
        </div>
        <div class="flex items-center justify-end mt-3">
            <CoreSearchBar v-model:search="search" />
        </div>
        <div class="mt-10">
            <CoreDatatable search-field="name" :search-value="search" :headers="headers" :data="stockItems" :loading="loading">
                <template v-slot:actions="{ item }">
                    <div class="py-2 flex items-center space-x-2">
                        <StockItemsViewDialog :data="item" @update="init" />
                        <StockItemsEditDialog v-if="can.manage('stock_management')" :data="item" @update="init" />
                        <StockItemsDeleteDialog v-if="can.manage('stock_management')" :data="item" @update="init" />
                    </div>
                </template>
            </CoreDatatable>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Header, Page } from '@/types';
import StockModule from '@/repository/modules/stock';
import moment from 'moment';
import Package from '@/package.json';

definePageMeta({
    layout: 'dashboard',
    middleware: ['stock-management']
});
useHead({
    title: `${Package.name.toUpperCase()} - Stock Items`
});

const { can } = usePermissions();
const header = ref<string>('Stock Items');
const loading = ref<boolean>(false);
const search = ref<string>("");
const cookie = useCookie("token");
const stockItems = ref([] as any);

const pages = ref<Page>([
    {
        name: 'Home',
        link: '/home',
    },
    {
        name: 'Stock Management',
        link: '#',
    }
]);
const headers = <Header>[
    { text: "name", value: "name", sortable: true },
    { text: "description", value: "description" },
    { text: "date modified", value: "updated_date" },
    { text: "actions", value: "actions" }
];

async function init(): Promise<void> {
    loading.value = true;
    const stockModule = new StockModule();
    const { data, error, pending } = await stockModule.getStockItem(`${cookie.value}`)
    loading.value = pending;
    if (data.value) {
        stockItems.value = data.value.map((item: { updated_date: string }) => ({
            ...item,
            updated_date: moment(item.updated_date).format(DATE_FORMAT)
        }))
        loading.value = false;
    }
    if (error.value) {
        console.error(error.value)
        loading.value = false;
    }
}

onMounted(() => {
    init();
});
</script>
<style></style>
