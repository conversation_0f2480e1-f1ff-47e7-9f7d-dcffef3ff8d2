<template>
  <div class="px-5 py-5">
    <div class="mb-4">
      <div class="flex items-center">
        <img src="@/assets/icons/desktop_app.svg" class="w-16 h-16 mr-3" alt="desktop-app-icon"/>
        <div>
          <h2 class="text-2xl font-semibold text-gray-900">IBLIS Configuration</h2>
          <p class="text-sm text-gray-500 mt-1">Manage facility details, printers, and system preferences</p>
        </div>
      </div>
    </div>

    <div class="font-medium text-center text-gray-500 bg-gray-50">
      <ul class="flex flex-wrap -mb-px">
        <li
          @click="tab = index"
          class="mr-2"
          v-for="(item, index) in filteredTabs"
          :key="index"
        >
          <a
            href="#"
            :class="
              tab == index
                ? 'inline-block py-2 px-4 text-white bg-sky-500 active dark:text-sky-500 dark:border-sky-500'
                : 'inline-block p-2 border-b-2 border-transparent rounded-t-lg hover:text-sky-500 hover:border-sky-500'
            "
          >
            {{ item }}
          </a>
        </li>
      </ul>
    </div>

    <div class="py-5">
      <component
        :is="tabComponents[tab]"
        v-if="tabComponents[tab]"
        @update="handleTabEvents"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import Package from "@/package.json";
import FacilityTab from "@/components/configuration/FacilityTab.vue";
import PrintersTab from "@/components/configuration/PrintersTab.vue";
import PreferencesTab from "@/components/configuration/PreferencesTab.vue";
import TestCatalogManagement from "@/components/test-catalog/Management.vue";

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: `${Package.name.toUpperCase()} - Configuration`,
});

const route = useRoute();
const { can } = usePermissions();

const config = ref<string[]>(["Facility", "Printers", "Preferences", "Test Catalog"]);
const filteredTabs = computed(() => {
  return can.manage("lab_configurations") ? config.value : [config.value[2]];
});

const tabComponents: Record<number, any> = {
  0: FacilityTab,
  1: PrintersTab,
  2: PreferencesTab,
  3: TestCatalogManagement
};

const tab = ref<number>(can.manage("lab_configurations") ? 0 : 2);

const handleTabEvents = (updated: boolean): void => {
  if (tab.value === 0 && updated) {
    console.log("Facility updated successfully");
  }
};

const tabRouteMap: Record<string, number> = {
  'printers': 1,
  'preferences': 2,
  'test-catalog': 3
};

onMounted(() => {
  const queryTab = route.query.tab as string;
  if (queryTab && tabRouteMap[queryTab] !== undefined) {
    tab.value = tabRouteMap[queryTab];
  }
});
</script>
