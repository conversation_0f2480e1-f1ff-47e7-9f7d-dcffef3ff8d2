<template>
    <div class="w-full">
        <iframe :src="`http://${ip}:5181`" style="width: 100%; height: 100vh; border: none;"></iframe>
    </div>
</template>

<script setup lang="ts">

import Package from '@/package.json';
import { useNetworkStore } from '@/store/network';

useHead({
    title: `${Package.name.toUpperCase()} - Machine Integration Manual`
});

definePageMeta({
    layout: 'dashboard'
});

const { ip } = useNetworkStore();

</script>

<style scoped>
</style>
