<template>
  <div class="w-full">
    <div v-if="isLoading" class="loader-container">
      <CoreLoader :width="80" :height="80" />
    </div>
    <iframe
      ref="iframeRef"
      :src="iframeSrc"
      class="w-full h-screen border-none"
      @load="isLoading = false"
    ></iframe>
  </div>
</template>

<script setup lang="ts">
import Package from "@/package.json";
import { useNetworkStore } from "@/store/network";

useHead({
  title: `${Package.name.toUpperCase()} - Usage Manual`,
});

definePageMeta({
  layout: "dashboard",
});

const { ip } = useNetworkStore();
const iframeSrc = `http://${ip}:5173`;
const isLoading = ref(true);
</script>

<style scoped>
.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background-color: rgba(73, 73, 73, 0.2);
}

.error {
  color: red;
  text-align: center;
  margin-top: 20px;
}
</style>
