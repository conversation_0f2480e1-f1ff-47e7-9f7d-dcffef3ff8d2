<template>
  <div class="py-20">
    <div
      class="px-10 py-5 flex flex-col items-center rounded shadow max-w-sm mx-auto bg-white"
    >
      <img
        src="@/assets/images/logo.png"
        alt="app-logo"
        class="w-28 h-28 object-cover"
      />

      <div
        class="mt-5 w-full text-3xl font-bold text-sky-500 text-center uppercase"
      >
        {{ appName }}
      </div>

      <h3 class="mt-3 text-2xl font-bold text-black text-center">
        {{ facility.details.name }}
      </h3>
      <div class="w-full flex flex-col space-y-2.5">
        <label class="text-lg font-semibold text-gray-500 text-center"
          >Select Location</label
        >
        <div class="w-full grid grid-cols-2 gap-5" v-show="!loading">
          <div
            @click="handleLocationClick(location)"
            v-for="location in locations"
            v-bind:key="location.id"
            class="relative py-10 px-10 flex flex-col items-center bg-gray-50 text-zinc-500 border rounded hover:bg-gray-100 transition duration-150 hover:cursor-pointer"
          >
            <img
              v-if="location.name !== 'Ward'"
              src="@/assets/icons/lab.png"
              class="w-12 h-12 object-cover"
              alt="lab-icon"
            />
            <img
              v-else
              src="@/assets/icons/hospital-bed.png"
              class="w-12 h-12 object-cover"
              alt="ward-icon"
            />
            <p
              class="w-full font-light text-black flex justify-center uppercase text-lg text-center mt-1"
            >
              {{ location.name }}
            </p>
            <div
              v-if="selectedLocation.name == location.name"
              class="absolute top-1.5 left-1.5"
            >
              <CheckCircleIcon class="w-6 h-6 text-green-500" />
            </div>
          </div>
        </div>
      </div>

      <TransitionRoot as="template" :show="showWardDialog">
        <Dialog
          as="div"
          class="relative z-10"
          @close="closeWardDialog"
          :initialFocus="confirmButtonRef"
        >
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0"
            enter-to="opacity-100"
            leave="ease-in duration-200"
            leave-from="opacity-100"
            leave-to="opacity-0"
          >
            <div
              class="fixed inset-0 bg-gray-900 bg-opacity-25 transition-opacity"
            />
          </TransitionChild>

          <div class="fixed inset-0 z-10 overflow-y-auto">
            <div
              class="flex min-h-full items-center justify-center p-4 text-center sm:p-0"
            >
              <TransitionChild
                as="template"
                enter="ease-out duration-300"
                enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enter-to="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leave-from="opacity-100 translate-y-0 sm:scale-100"
                leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <DialogPanel
                  class="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-md sm:p-6"
                >
                  <div>
                    <DialogTitle
                      as="h3"
                      class="text-xl font-semibold text-black mb-4"
                    >
                      Select Ward
                    </DialogTitle>

                    <div class="mb-4">
                      <CoreSearchBar
                        v-model:search="wardSearchQuery"
                        @update:search="wardSearchQuery = $event"
                        placeholder="Search wards..."
                      />
                    </div>

                    <div
                      v-if="wardLoading"
                      class="flex justify-center py-8 px-8"
                    >
                      <div
                        class="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-500"
                      ></div>
                    </div>

                    <div
                      v-else-if="filteredWards.length === 0"
                      class="py-8 text-center text-gray-500"
                    >
                      No wards found matching your search.
                    </div>

                    <div v-else class="max-h-60 overflow-y-auto mt-2">
                      <RadioGroup v-model="selectedWard">
                        <RadioGroupOption
                          v-for="ward in filteredWards"
                          :key="ward.id"
                          :value="{ name: ward.name }"
                          v-slot="{ checked, active }"
                          as="template"
                        >
                          <div
                            :class="[
                              'relative flex cursor-pointer p-3 border-b hover:bg-gray-100 transition-colors duration-150',
                              active ? 'bg-sky-100 border-sky-200' : '',
                            ]"
                            tabindex="0"
                            role="button"
                            aria-label="Select ward"
                            :aria-selected="checked"
                          >
                            <div class="flex items-center w-full">
                              <div class="flex-shrink-0 mr-3">
                                <div
                                  class="w-8 h-8 flex items-center justify-center"
                                  :class="{ 'text-sky-500': checked || active }"
                                >
                                  <img
                                    src="@/assets/icons/hospital-bed.png"
                                    class="w-8 h-8 object-cover"
                                    alt="ward-icon"
                                  />
                                </div>
                              </div>
                              <div class="flex-grow font-medium text-black">
                                {{ ward.name }}
                              </div>
                              <div v-if="checked" class="flex-shrink-0">
                                <CheckCircleIcon class="w-6 h-6 text-sky-500" />
                              </div>
                            </div>
                          </div>
                        </RadioGroupOption>
                      </RadioGroup>
                    </div>
                  </div>

                  <div
                    class="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3"
                  >
                    <CoreButton
                      ref="confirmButtonRef"
                      text="Confirm"
                      color="primary"
                      type="button"
                      @click="confirmWardSelection"
                      :disabled="!selectedWard.name"
                      class="sm:col-start-2"
                    />
                    <CoreButton
                      text="Cancel"
                      color="default"
                      type="button"
                      @click="closeWardDialog"
                      class="mt-3 sm:mt-0 sm:col-start-1"
                    />
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </TransitionRoot>

      <div
        v-if="selectedLocation.name === 'Ward' && selectedWard.name !== ''"
        class="w-full mt-4"
      >
        <div class="flex flex-col p-4 bg-white border rounded-md shadow-sm">
          <div class="text-sm text-gray-500 mb-1">Selected Ward</div>
          <div class="flex items-center">
            <img
              src="@/assets/icons/hospital-bed.png"
              class="w-8 h-8 mr-3"
              alt="ward-icon"
            />
            <div class="font-medium text-lg">{{ selectedWard.name }}</div>
            <button
              @click="openWardDialog"
              class="ml-auto text-sky-600 hover:text-sky-800 text-sm px-3 py-1 rounded-md border border-sky-200 hover:bg-sky-50 transition-colors"
            >
              Change
            </button>
          </div>
        </div>
      </div>

      <div class="w-full mt-3" v-if="canContinue">
        <CoreButton
          text="Continue &rarr;"
          color="primary"
          type="submit"
          @click="navigateHome"
          :loading="loading"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/store/auth";
import { useFacilityStore } from "@/store/facility";
import { useWardStore } from "@/store/ward";
import Package from "@/package.json";
import type { DropdownItem, Ward } from "@/types";
import { CheckCircleIcon } from "@heroicons/vue/24/solid/index.js";
import { useRouteStore } from "@/store/route";
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionRoot,
  TransitionChild,
  RadioGroup,
  RadioGroupOption,
} from "@headlessui/vue";

const appName = Package.name;
const facility = useFacilityStore();
const router = useRouter();
const authStore = useAuthStore();
const wardStore = useWardStore();
const { route } = useRouteStore();
const locations = ref<DropdownItem[]>(authStore.locations);
const loading = ref<boolean>(false);
const selectedLocation = ref<DropdownItem>({ name: "" });
const wardLoading = ref<boolean>(false);
const wards = ref<Ward[]>([]);
const selectedWard = ref<DropdownItem>({ name: "" });
const showWardDialog = ref<boolean>(false);
const selectedWardConfirmed = ref<boolean>(false);
const wardSearchQuery = ref<string>("");

const confirmButtonRef = ref<HTMLButtonElement | null>(null);

const filteredWards = computed(() => {
  if (!wardSearchQuery.value) return wards.value;

  const query = wardSearchQuery.value.toLowerCase().trim();
  return wards.value.filter((ward) => ward.name.toLowerCase().includes(query));
});

const canContinue = computed(() => {
  if (selectedLocation.value.name === "Ward") {
    return selectedWardConfirmed.value && selectedWard.value.name !== "";
  }
  return selectedLocation.value.name !== "";
});

const handleLocationClick = (location: DropdownItem): void => {
  if (location.name === "Ward") {
    selectedLocation.value = location;
    openWardDialog();
  } else {
    getLocation(location);
  }
};

const getLocation = (location: DropdownItem): void => {
  loading.value = true;
  authStore.selectedLocation = location.name;
  selectedLocation.value = location;
  loading.value = false;
};

const openWardDialog = async () => {
  wardSearchQuery.value = "";

  if (wards.value.length === 0) {
    await fetchWards();
  }
  showWardDialog.value = true;
};

const closeWardDialog = () => {
  showWardDialog.value = false;
};

const confirmWardSelection = () => {
  if (selectedWard.value.name) {
    wardStore.setSelectedWard(selectedWard.value.name);
    selectedWardConfirmed.value = true;
    showWardDialog.value = false;
    selectedLocation.value = { id: 0, name: "Ward" };
    authStore.selectedLocation = "Ward";
  }
};

const fetchWards = async () => {
  wardLoading.value = true;
  await wardStore.fetchWards();
  wards.value = wardStore.wards;
  wardLoading.value = false;
};

const navigateHome = () => {
  if (canContinue.value) {
    if (
      selectedLocation.value.name === "Ward" &&
      selectedWard.value.name !== ""
    ) {
      authStore.selectedLocation = "Ward";
      wardStore.setSelectedWard(selectedWard.value.name);
      router.push("/tests");
    } else {
      authStore.selectedLocation = selectedLocation.value.name;
      route === "" ? router.push("/home") : router.push(route);
    }
  }
};

onMounted(() => {
  if (locations.value && !locations.value.some((loc) => loc.name === "Ward")) {
    locations.value.push({
      id: 0,
      name: "Ward",
    });
  }
});
</script>
