<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <h3 class="text-2xl font-semibold">{{ header }}</h3>

      <FacilitiesAddDialog
        @update="updateFacilites"
        v-if="usePermissions().can.manage('lab_configurations')"
      />
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar :search="search" @update="updateSearch" />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="facilities"
      :loading="loading"
      :search-value="searchValue"
      search-field="name"
      :serverItemsLength="serverItemsLength"
      :serverOptions="serverOptions"
      @update="updateFacilites"
      v-if="usePermissions().can.manage('lab_configurations')"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <FacilitiesViewDialog :data="item" />
          <FacilitiesEditDialog :data="item" @update="updateFacilites" />
          <FacilitiesDeleteDialog :data="item" @update="updateFacilites" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script lang="ts">
import {
  PlusIcon,
  MagnifyingGlassIcon,
  TrashIcon,
  ArrowUpOnSquareIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import type { ServerOptions } from "vue3-easy-data-table";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request } from "@/types";
import Package from "@/package.json";

export default {
  components: {
    MagnifyingGlassIcon,
  },
  setup() {
    definePageMeta({
      layout: "dashboard",
      middleware: ["lab-configurations"]
    });
    useHead({
      title: `${Package.name.toUpperCase()} - Facilities`,
    });
  },
  data() {
    return {
      header: "List of facilities",
      pages: [
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Lab Configuration",
          link: "#",
        },
      ],
      addIcon: PlusIcon,
      deleteIcon: TrashIcon,
      viewIcon: ArrowUpOnSquareIcon,
      headers: [
        { text: "ID", value: "id", sortable: true },
        { text: "Name", value: "name", sortable: true },
        { text: "Date Created", value: "created_date" },
        { text: "Actions", value: "actions" },
      ],
      facilities: new Array<{ name: string; created_date: string }>(),
      cookie: useCookie("token"),
      loading: false as boolean,
      searchValue: "" as string,
      search: "" as string,
      serverItemsLength: 0,
      serverOptions: <ServerOptions>{
        page: 1,
        rowsPerPage: 25,
        sortBy: "name",
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    /**
     * @method init laods facilities
     * @param null
     * @returns promise @type void
     */
    async init(): Promise<void> {
      this.loading = true;

      const { page, rowsPerPage } = this.serverOptions;

      const request: Request = {
        route: `${endpoints.facility}?page=${page}&per_page=${rowsPerPage}&search=${this.search}`,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { data, error, pending } = await fetchRequest(request);

      this.loading = pending;

      if (data.value) {
        this.facilities = data.value.data.map(
          (facility: { created_date: string }) => ({
            ...facility,
            created_date: moment(facility.created_date).format(DATE_FORMAT),
          })
        );

        this.serverItemsLength = data.value.meta.total_count;
        this.loading = false;
      }

      if (error.value) {
        console.error(error.value);
        this.loading = false;
      }
    },
    /**
     * @method updateFacilities reloads facilites if changes occur
     * @param value boolean
     * @returns void
     */
    updateFacilites(value: any): void {
      if (typeof value === "object") {
        this.serverOptions = value;
      }
      this.init();
    },
    /**
     * @method updateSearch update facilities ~ client side search
     * @param value string
     * @returns void
     */
    updateSearch(value: string): void {
      if (typeof value === "string") {
        this.search = value;
        this.searchValue = value;

        this.updateFacilites(true);
      }
    },
  },
};
</script>

<style></style>
