<script setup lang="ts">
	import type { Header, Item, ServerOptions } from 'vue3-easy-data-table';
	import { endpoints } from '@/services/endpoints';
	import fetchRequest from '@/services/fetch';
	import type { Page, Request, Response } from '@/types';
	import Package from '@/package.json'

	definePageMeta({
		layout: 'dashboard',
		middleware: ["lab-configurations"]
	});

	useSeoMeta({
		title: `${Package.name.toUpperCase()} - Instruments`
	});

	const serverItemsLength: Ref<number> = ref<number>(0);
	const loading: Ref<boolean> = ref<boolean>(false);
	const instruments: Ref<Item[]> = ref<Item[]>([]);

	const cookie: Ref<string | null> = useCookie('token');
	const header: Ref<string> = ref<string>('List of Instruments');
	const search: Ref<string> = ref<string>('');

	const pages: Ref<Page> = ref<Page>([
		{
			name: 'Home',
			link: '/home',
		},
		{
			name: 'Lab Configuration',
			link: '#',
		},
	]);

	const headers: Ref<Header[]> = ref<Header[]>([
		{ text: 'Name', value: 'name', sortable: true },
		{ text: 'IP Address', value: 'ip_address' },
		{ text: 'Hostname', value: 'hostname' },
		{ text: 'Actions', value: 'actions', width: 18 },
	]);

	const serverOptions: Ref<ServerOptions> = ref<ServerOptions>({
		page: 1,
		rowsPerPage: 25,
		sortBy: 'name',
	});

	const reloadInstrument = async (): Promise<void> => loadInstruments();

	const loadInstruments = async (): Promise<void> => {
		loading.value = true;
		const { page, rowsPerPage } = serverOptions.value;

		const request: Request = {
			route: getParameterizedUrl(endpoints.instrument.index, {
				page: page,
				page_size: rowsPerPage,
				search: search.value,
			}),
			method: 'GET',
			token: `${cookie.value}`,
		};

		const v: Response = await fetchRequest(request);

		if (v.data.value) {
			instruments.value = v.data.value.data;
			serverItemsLength.value = v.data.value.total;
		}

		loading.value = false;

		if (v.error.value) {
			loading.value = false;
			useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
		}
	};

	const updateSearch = (value: string): void => {
		search.value = value;

		loadInstruments();
	};

	const updateServerOptions = (options: ServerOptions): ServerOptions =>
		(serverOptions.value = options);

	loadInstruments();

	watch(serverOptions, () => loadInstruments());
</script>

<template>
	<div class="py-5 px-5">
		<CoreBreadcrumb :pages="pages" />

		<div class="flex items-center justify-between py-5">
			<h3 class="text-2xl font-semibold">{{ header }}</h3>
			<div class="flex items-center space-x-3">
				<InstrumentsAddDialog @action-completed="reloadInstrument" v-if="usePermissions().can.manage('lab_configurations')"/>
				<InstrumentsAddDriver v-if="usePermissions().can.manage('lab_configurations')"/>
			</div>
		</div>

		<div class="flex justify-end w-full px-2 py-2 mb-2">
			<CoreSearchBar v-model:search="search" @update="updateSearch" v-if="usePermissions().can.manage('lab_configurations')"/>
		</div>

		<CoreDatatable
			:headers="headers"
			:data="instruments"
			:serverOptions="serverOptions"
			:loading="loading"
			:serverItemsLength="serverItemsLength"
			@update="updateServerOptions"
			v-if="usePermissions().can.manage('lab_configurations')"
		>
			<template v-slot:actions="{ item }">
				<div class="py-2 flex items-center space-x-2">
					<InstrumentsViewDialog :id="item.id" />

					<InstrumentsEditDialog
						:id="item.id"
						@action-completed="reloadInstrument"
					/>

					<InstrumentsDeleteDialog
						:id="item.id"
						:name="item.name"
						@action-completed="reloadInstrument"
					/>
				</div>
			</template>
		</CoreDatatable>
	</div>
</template>
