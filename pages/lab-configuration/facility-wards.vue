<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <h3 class="text-2xl font-semibold">{{ header }}</h3>
      <wards-add-dialog
        @update="updateWards"
        v-if="usePermissions().can.manage('lab_configurations')"
      />
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar
        v-model:search="search"
        @update="updateSearch"
        v-if="usePermissions().can.manage('lab_configurations')"
      />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="wards"
      :loading="loading"
      :search-value="searchValue"
      search-field="name"
      :serverItemsLength="serverItemsLength"
      :serverOptions="serverOptions"
      @update="updateWards"
      v-if="usePermissions().can.manage('lab_configurations')"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <WardsViewDialog :data="item" />
          <WardsEditDialog :data="item" @update="updateWards" />
          <WardsDeleteDialog :data="item" @update="updateWards" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script lang="ts">
import {
  PlusIcon,
  MagnifyingGlassIcon,
  TrashIcon,
  ArrowUpOnSquareIcon,
} from "@heroicons/vue/24/solid/index.js";
import type { ServerOptions } from "vue3-easy-data-table";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Header, Page, Request, Ward } from "@/types";
import Package from "@/package.json";

export default {
  components: {
    MagnifyingGlassIcon,
  },
  setup() {
    definePageMeta({
      layout: "dashboard",
      middleware: ["lab-configurations"],
    });
    useHead({
      title: `${Package.name.toUpperCase()} - Wards`,
    });
  },
  data() {
    return {
      header: "List of wards" as string,
      pages: [
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Lab Configuration",
          link: "#",
        },
      ] as Page,
      addIcon: PlusIcon as Object,
      deleteIcon: TrashIcon as Object,
      viewIcon: ArrowUpOnSquareIcon,
      headers: [
        { text: "ID", value: "id", sortable: true },
        { text: "Name", value: "name", sortable: true },
        { text: "Actions", value: "actions" },
      ] as Header,
      wards: new Array<Ward>(),
      cookie: useCookie("token"),
      loading: false as boolean,
      search: "" as string,
      searchValue: "" as string,
      searchField: "name" as string,
      serverItemsLength: 0,
      serverOptions: <ServerOptions>{
        page: 1,
        rowsPerPage: 25,
        sortBy: "name",
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    /**
     * @method init loads wards data
     * @returns promise @type void
     */
    async init(): Promise<void> {
      this.loading = true;

      const { page, rowsPerPage } = this.serverOptions;

      const request: Request = {
        route: `${endpoints.sections}?page=${page}&per_page=${rowsPerPage}&search=${this.search}`,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { data, error, pending } = await fetchRequest(request);

      this.loading = pending;

      if (data.value) {
        this.wards = data.value.data;

        this.loading = false;

        this.serverItemsLength = data.value.meta.total_count;
      }

      if (error.value) {
        console.log(error.value);
        this.loading = false;
      }
    },
    /**
     * @method updateWards reload wards
     * @param value boolean
     * @returns void
     */
    updateWards(value: any): void {
      if (typeof value === "object") {
        this.serverOptions = value;
      }
      this.init();
    },
    /**
     * @method updateSearch updates search params
     * @param value string
     * @returns void
     */
    updateSearch(value: string): void {
      if (typeof value === "string") {
        this.search = value;
        this.searchValue = value;

        this.updateWards(true);
      }
    },
  },
};
</script>

<style></style>
