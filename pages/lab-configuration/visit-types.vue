<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <h3 class="text-2xl font-semibold">{{ header }}</h3>
      <VisitTypesAddDialog
        @update="updateVisitTypes"
        v-if="usePermissions().can.manage('lab_configurations')"
      />
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar
        v-model:search="search"
        @update="updateSearch"
        v-if="usePermissions().can.manage('lab_configurations')"
      />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="visitTypes"
      :loading="loading"
      :search-field="searchField"
      :search-value="searchValue"
      v-if="usePermissions().can.manage('lab_configurations')"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <VisitTypesViewDialog :data="item" />
          <VisitTypesEditDialog :data="item" @update="updateVisitTypes" />
          <VisitTypesDeleteDialog :data="item" @update="updateVisitTypes" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script lang="ts">
import {
  PlusIcon,
  MagnifyingGlassIcon,
  TrashIcon,
  ArrowUpOnSquareIcon,
} from "@heroicons/vue/24/solid/index.js";
import type { ServerOptions } from "vue3-easy-data-table";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Header, Page, Request } from "@/types";
import Package from "@/package.json";

export default {
  components: {
    MagnifyingGlassIcon,
  },
  setup() {
    useHead({
      title: `${Package.name.toUpperCase()} - Visit Types`,
    });
    definePageMeta({
      layout: "dashboard",
      middleware: ["lab-configurations"]
    });
  },
  data() {
    return {
      header: "Visit Types" as String,
      pages: [
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Lab Configuration",
          link: "#",
        },
      ] as Page,
      addIcon: PlusIcon as Object,
      deleteIcon: TrashIcon as Object,
      viewIcon: ArrowUpOnSquareIcon as Object,
      headers: [
        { text: "ID", value: "id", sortable: true },
        { text: "NAME", value: "name", sortable: true },
        { text: "DESCRIPTION", value: "description", sortable: true },
        { text: "ACTIONS", value: "actions" },
      ] as Header,
      visitTypes: new Array<Object>(),
      loading: false as boolean,
      cookie: useCookie("token"),
      serverItemsLength: 0,
      serverOptions: <ServerOptions>{
        page: 1,
        rowsPerPage: 10,
        sortBy: "name",
      },
      searchField: "name" as string,
      search: "" as string,
      searchValue: "" as string,
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init(): Promise<void> {
      this.loading = true;

      const { page, rowsPerPage } = this.serverOptions;

      const request: Request = {
        route: `${endpoints.visitTypes}?page=${page}&per_page=${rowsPerPage}&search=${this.search}`,
        method: "GET",
        token: `${this.cookie}`,
      };

      let { data, error, pending } = await fetchRequest(request);

      this.loading = pending;

      if (data.value) {
        this.visitTypes = data.value.data;
        this.serverItemsLength = data.value.meta.total_count;
      }

      if (error.value) {
        console.log(error.value.data);
        useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
      }
    },
    updateSearch(value: string): void {
      this.searchValue = value;
      this.search = value;
    },
    updateVisitTypes(value: boolean): void {
      if (value) {
        this.init();
      }
    },
  },
};
</script>

<style></style>
