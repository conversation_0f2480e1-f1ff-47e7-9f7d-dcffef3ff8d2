<script lang="ts">
import { endpoints } from "@/services/endpoints";
import type { Request, Response, Page } from "@/types";
import fetchRequest from "@/services/fetch";
import type { Item, ServerOptions } from "vue3-easy-data-table";
import Package from "@/package.json";

definePageMeta({
  layout: "dashboard",
  middleware: ['lab-configurations']
});

export default {
  setup() {
    useHead({
      title: `${Package.name.toUpperCase()} - Surveillance`,
    });
  },
  data() {
    return {
      pages: new Array(
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Test Catalog",
          link: "#",
        }
      ) as Page,
      serverItemsLength: 0 as number,
      loading: false as boolean,
      items: new Array<Item>(),
      cookie: useCookie("token"),
      header: "Surveillance" as string,
      search: "" as string,
      searchValue: "" as string,
      headers: [
        { text: "Test Type", value: "test_type", sortable: true },
        { text: "Disease", value: "disease" },
        { text: "Actions", value: "actions", width: 18 },
      ],
      serverOptions: <ServerOptions>{
        page: 1,
        rowsPerPage: 25,
        sortBy: "test_type",
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    updateSearch(value: any): void {
      this.searchValue = value;
      this.search = value;

      this.init();
    },
    async init(): Promise<void> {
      this.loading = true;

      const { page, rowsPerPage } = this.serverOptions;

      const request: Request = {
        route: getParameterizedUrl(endpoints.surveillance.index, {
          page: page,
          page_size: rowsPerPage,
          search: this.search,
        }),
        method: "GET",
        token: `${this.cookie}`,
      };

      const { data, error, pending }: Response = await fetchRequest(request);

      if (data.value) {
        this.items = [];

        data.value.data.map(
          (
            item: {
              id: number;
              test_type: any;
              disease: any;
              action?: any;
            },
            key: any
          ) => {
            this.items.push({
              id: item.id,
              test_type: item.test_type.name,
              disease: item.disease.name,
            });
          }
        );
        this.serverItemsLength = data.value.total;
      }

      this.loading = false;

      if (error.value) {
        this.loading = false;
        useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
      }
    },
    updateSurveillance(value: any): void {
      if (typeof value === "object") {
        this.serverOptions = value;
      }
      this.init();
    },
  },
};
</script>

<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <h3 class="text-2xl font-semibold">{{ header }}</h3>
      <div class="flex items-center space-x-3">
        <SurveillanceAddDialog
          @update="updateSurveillance"
          v-if="usePermissions().can.manage('lab_configurations')"
        />
        <SurveillanceAddDisease />
      </div>
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar
        v-model:search="search"
        @update="updateSearch"
        v-if="usePermissions().can.manage('lab_configurations')"
      />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="items"
      :serverOptions="serverOptions"
      :loading="loading"
      :serverItemsLength="serverItemsLength"
      :searchField="'test_type'"
      :searchValue="searchValue"
      @update="updateSurveillance"
      v-if="usePermissions().can.manage('lab_configurations')"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <SurveillanceEditDialog :id="item.id" @update="updateSurveillance" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>
