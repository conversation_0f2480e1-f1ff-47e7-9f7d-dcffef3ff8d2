<template>
  <div>
    <div class="w-full flex items-center justify-end p-3">
      <Network :isConnected="isConnected"/>
    </div>
    <LandingLogin @connection="getConnectionStatus"/>
  </div>
</template>

<script setup lang="ts">

import Package from '@/package.json';

useHead({
  title: `${Package.name.toUpperCase()} - ${Package.version}`,
});
definePageMeta({
  layout: "default"
});
const isConnected = ref<boolean>(false);
const getConnectionStatus = (value: boolean) => {
  isConnected.value = value;
}
</script>

<style>
</style>
