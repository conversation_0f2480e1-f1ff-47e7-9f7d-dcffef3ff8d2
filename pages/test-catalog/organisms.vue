<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <div class="text-2xl font-semibold flex items-center uppercase">
        <img
          src="@/assets/icons/bacteria.svg"
          alt="report-icon"
          class="w-8 h-8 mr-2"
        />
        {{ header }}
      </div>
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar :search="search" @update="updateSearch" />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="organisms"
      :loading="loading"
      :search-value="searchValue"
      v-if="usePermissions().can.manage('test_catalog')"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <OrganismsViewDialog :data="item" />
          <OrganismsEditDialog :data="item" @update="updateOrganisms" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response, Header, Page } from "@/types";
import Package from "@/package.json";

definePageMeta({
  layout: "dashboard",
  middleware: ["test-catalog"],
});

useHead({
  title: `${Package.name.toUpperCase()} - Organisms`,
});

const header = ref<string>("Organisms");
const loading = ref<boolean>(false);
const search = ref<string>("");
const searchValue = ref<string>("");
const headers = ref<Header>([
  { text: "id", value: "id", sortable: true },
  { text: "name", value: "name", sortable: true },
  { text: "preferred name", value: "preferred_name", sortable: true },
  { text: "nlims code", value: "nlims_code", sortable: true },
  { text: "description", value: "description", sortable: false, html: true },
  { text: "actions", value: "actions", sortable: false },
]);
const organisms = ref([]);
const pages = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Test Catalog",
    link: "#",
  },
]);

const updateSearch = (value: string): void => {
  searchValue.value = value;
  search.value = value;
};

const init = async (): Promise<void> => {
  loading.value = true;

  const request: Request = {
    route: endpoints.organisms,
    method: "GET",
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;

  if (data.value) {
    organisms.value = data.value;
    loading.value = false;
  }

  if (error.value) {
    console.error(error.value);
    loading.value = false;
  }
};

const updateOrganisms = (value: boolean): void => {
  if (value) {
    init();
  }
};

onMounted(() => {
  init();
});

defineExpose({
  init,
  updateOrganisms,
});
</script>
<style></style>
