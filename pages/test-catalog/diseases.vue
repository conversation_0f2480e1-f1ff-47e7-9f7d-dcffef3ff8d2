<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <div class="text-2xl font-semibold flex items-center uppercase">
        <img
          src="@/assets/icons/virus.svg"
          alt="report-icon"
          class="w-8 h-8 mr-2"
        />
        Diseases
      </div>

      <div class="flex items-center space-x-3">
        <SurveillanceAddDisease
          @action-completed="updateDiseases"
          v-if="usePermissions().can.manage('test_catalog')"
        />
      </div>
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar
        :search="search"
        @update="updateSearch"
        v-if="usePermissions().can.manage('test_catalog')"
      />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="filteredDiseases"
      :loading="loading"
      :search-value="searchValue"
      :search-field="'name'"
      v-if="usePermissions().can.manage('test_catalog')"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <DiseasesEditDialog :data="item" @update="updateDiseases" />
          <DiseasesDeleteDialog :data="item" @update="updateDiseases" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script lang="ts">
import { search } from "@formkit/inputs";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Header, Page, Request, Response } from "@/types";
import moment from "moment";
import Package from "@/package.json";

export default {
  setup() {
    definePageMeta({
      layout: "dashboard",
      middleware: ["test-catalog"],
    });
    useHead({
      title: `${Package.name.toUpperCase()} - Diseases`,
    });
  },
  data() {
    return {
      header: "Diseases" as string,
      pages: [
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Test Catalog",
          link: "#",
        },
      ] as Page,
      headers: [
        { text: "ID", value: "id", sortable: true },
        { text: "NAME", value: "name", sortable: true },
        { text: "DATE CREATED", value: "created_date" },
        { text: "Actions", value: "actions" },
      ] as Header,
      diseases: new Array<any>(),
      cookie: useCookie("token"),
      search: "" as string,
      searchValue: "" as string,
      loading: false as boolean,
    };
  },
  created() {
    this.init();
  },
  computed: {
    /***
     * @method filteredDiseases
     * @param void
     * @returns diseases @type Object
     */
    filteredDiseases(): Object {
      /**
       * filtering array
       */
      return this.diseases.map((disease) => ({
        ...disease,
        created_date: moment(disease.created_date).format(DATE_FORMAT),
      }));
    },
  },
  methods: {
    async init(): Promise<void> {
      this.loading = true;

      const request: Request = {
        route: endpoints.disease.index,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { data, error, pending }: Response = await fetchRequest(request);

      this.loading = pending;

      if (data.value) {
        this.diseases = data.value;

        this.loading = false;
      }

      if (error.value) {
        console.log(error.value);
        this.loading = false;
      }
    },
    updateSearch(value: string): void {
      if (search) {
        this.searchValue = value;
        this.search = value;
      }
    },
    updateDiseases(value: Array<any>) {
      this.init();
    },
  },
};
</script>
<style lang=""></style>
