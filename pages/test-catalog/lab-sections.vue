<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <div class="text-2xl font-semibold flex items-center uppercase">
        <img src="@/assets/icons/hospital.svg" alt="report-icon" class="w-8 h-8 mr-2" />
        {{ header }}
      </div>
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar v-model:search="search" @update="updateSearch" />
    </div>

    <CoreDatatable :headers="headers" :data="departments" :loading="loading" :search-value="searchValue"
      v-if="canManageTestCatalog">
      <template #actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <LabSectionsViewDialog :data="item" />
          <LabSectionsEditDialog :data="item" @update="onLaboratorySectionUpdate" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script setup lang="ts">
import type { Header, Request, Response, Department } from "@/types";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import Package from "@/package.json";

definePageMeta({
  layout: "dashboard",
  middleware: ["test-catalog"],
});

useHead({
  title: `${Package.name.toUpperCase()} - Laboratory Sections`,
});

const header: string = "Laboratory Sections";
const departments = ref<Department[]>([]);
const pages = [
  { name: "Home", link: "/home" },
  { name: "Test Catalog", link: "#" },
];
const loading = ref<boolean>(false);
const search = ref<string>("");
const searchValue = ref<string>("");
const headers: Header = [
  { text: "id", value: "id", sortable: true },
  { text: "name", value: "name", sortable: true },
  { text: "preferred name", value: "preferred_name", sortable: true },
  { text: "nlims code", value: "nlims_code", sortable: false, html: true },
  { text: "actions", value: "actions", sortable: false },
];

const { can } = usePermissions();
const canManageTestCatalog = computed(() => can.manage("test_catalog"));

const updateSearch = (value: string): void => {
  searchValue.value = value;
  search.value = value;
};

const fetchDepartments = async (): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: endpoints.departments,
    method: "GET",
  };
  const { pending, error, data }: Response = await fetchRequest(request);
  loading.value = pending;
  if (data.value) {
    departments.value = data.value;
    loading.value = false;
  }
  if (error.value) {
    console.error(error.value);
    loading.value = false;
  }
};

const onLaboratorySectionUpdate = async (value: boolean): Promise<void> => {
  if (value) {
    await fetchDepartments();
  }
};

onMounted(fetchDepartments);
</script>
