<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <div class="text-2xl font-semibold flex items-center uppercase">
        <svg class="w-12 h-12 mr-2" xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48">
          <path fill="currentColor" fill-rule="evenodd"
            d="M31 10a1 1 0 1 0 0 2v7h-3v-7a1 1 0 1 0 0-2h-8a1 1 0 1 0 0 2v7h-3v-7a1 1 0 1 0 0-2H9a1 1 0 1 0 0 2v7H7v-3H5v3H4v2h1v13H4v2h1v2h2v-2h34v2h2v-2h1v-2h-1V21h1v-2h-1v-3h-2v3h-2v-7a1 1 0 1 0 0-2zm6 9v-7h-4v7zm-4 2h4v5h-4zm-2 0v8a4 4 0 0 0 8 0v-8h2v13H7V21h2v8a4 4 0 0 0 8 0v-8h3v8a4 4 0 0 0 8 0v-8zm-5-2v-7h-4v7zm-11 0v-7h-4v7zm-4 2h4v2h-4z"
            clip-rule="evenodd" />
        </svg>
        {{ header }}
      </div>
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar :search="search" @update="updateSearch" v-if="permissions.can.manage('test_catalog')" />
    </div>

    <CoreDatatable :headers="headers" :data="testTypes" :loading="loading" :searchField="searchField"
      :searchValue="searchValue" :serverItemsLength="serverItemsLength" :serverOptions="serverOptions"
      @update="updateTestTypes" v-if="permissions.can.manage('test_catalog')">
      <template v-slot:can_be_ordered="{ item }">
        <div class="py-2 flex items-center justify-center">
          <span class="text-sm">{{ item.can_be_ordered ? 'Yes' : 'No' }}</span>
        </div>
      </template>
      <template v-slot:actions="{ item }" :loading="loading">
        <div class="py-2 flex items-center space-x-2">
          <TestTypesViewDialog @edit="edit" :open="showEdit" :data="item" />
          <CoreActionButton :click="() => navigateToEdit(item)" color="success" text="Edit" :icon="editIcon" />
          <CoreActionButton :click="() => handleCanBeOrderedClick(item)"
            :color="item.can_be_ordered ? 'error' : 'primary'"
            :text="item.can_be_ordered ? 'Disable Ordering' : 'Enable Ordering'" />
        </div>
      </template>
    </CoreDatatable>

    <!-- Confirmation Dialog -->
    <CoreConfirmationDialog :show="showConfirmDialog" :title="confirmDialogTitle" :message="confirmDialogMessage"
      @confirm="confirmCanBeOrderedChange" @cancel="cancelCanBeOrderedChange" />
  </div>
</template>

<script setup lang="ts">
import {
  PencilSquareIcon,
} from "@heroicons/vue/24/solid/index.js";
import type { ServerOptions } from "vue3-easy-data-table";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Header, Request, Page } from "@/types";
import Package from "@/package.json";
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useNuxtApp } from '#app';
import { useSecureRouting } from '@/composables/useSecureRouting';

definePageMeta({
  layout: "dashboard",
  middleware: ["test-catalog"],
});

useHead({
  title: `${Package.name.toUpperCase()} - Test Types`,
});

const route = useRoute();
const permissions = usePermissions();
const nuxtApp = useNuxtApp();
const header = ref<string>("Test Types");
const loading = ref<boolean>(true);
const editIcon = PencilSquareIcon;
const showEdit = ref<boolean>(false);
const serverItemsLength = ref<number>(0);
const serverOptions = ref<ServerOptions>({
  page: 1,
  rowsPerPage: 25,
  sortBy: "name",
});
const search = ref<string>("");
const testTypes = ref<any[]>([]);
const showConfirmDialog = ref<boolean>(false);
const confirmDialogTitle = ref<string>("");
const confirmDialogMessage = ref<string>("");
const selectedTestType = ref<any>(null);
const headers = ref<Header>([
  { text: "name", value: "name", sortable: true },
  { text: "short name", value: "short_name", sortable: false },
  { text: "Preferred name", value: "preferred_name", sortable: false },
  { text: "nlims code", value: "nlims_code", sortable: false },
  {
    text: "expected TAT",
    value: "expected_turn_around_time",
    sortable: false,
  },
  { text: "actions", value: "actions", sortable: false },
]);
const searchField = ref<string>("name");
const searchValue = ref<string>("");

const pages = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Test Catalog",
    link: "#",
  },
]);

const init = async (): Promise<void> => {
  const { page, rowsPerPage } = serverOptions.value;
  loading.value = true;
  const request: Request = {
    route: `${endpoints.testTypes}?page=${page}&per_page=${rowsPerPage}&search=${search.value}`,
    method: "GET"
  };
  let { data, error, pending } = await fetchRequest(request);
  loading.value = pending;
  if (data.value) {
    testTypes.value = data.value.test_types.map(
      (testType: {
        expected_turn_around_time: { value: any; unit: string };
      }) => ({
        ...testType,
        expected_turn_around_time: `${testType.expected_turn_around_time !== null
          ? testType.expected_turn_around_time?.value +
          " " +
          (testType.expected_turn_around_time.unit == null
            ? ""
            : testType.expected_turn_around_time.unit?.toLowerCase())
          : ""
          }`,
      })
    );
    serverItemsLength.value = data.value.meta.total_count;
    loading.value = false;
  }
  if (error.value) {
    console.error(error.value);
    nuxtApp.$toast.error("An error occurred while loading test types, please try again!");
    loading.value = false;
  }
};

const updateTestTypes = (value: any): void => {
  if (typeof value === "object") {
    serverOptions.value = value;
  }
  init();
};

const updateSearch = (value: string): void => {
  searchValue.value = value;
  search.value = value;
  updateTestTypes(true);
};

const edit = (value: boolean): void => {
  showEdit.value = true;
};

const { navigateSecure } = useSecureRouting();

const navigateToEdit = (item: { id: number; name: string }): void => {
  navigateSecure('/test-catalog/test-types/edit', item.id);
};

const handleCanBeOrderedClick = (item: any): void => {
  selectedTestType.value = item;
  const newStatus = !item.can_be_ordered;
  confirmDialogTitle.value = "Confirm Change";
  confirmDialogMessage.value = `Are you sure you want to ${newStatus ? 'enable' : 'disable'} ordering for "${item.name}"?`;
  showConfirmDialog.value = true;
};

const confirmCanBeOrderedChange = async (): Promise<void> => {
  if (!selectedTestType.value) return;

  const newStatus = !selectedTestType.value.can_be_ordered;
  loading.value = true;

  const request: Request = {
    route: `${endpoints.testTypes}/${selectedTestType.value.id}/can_be_ordered`,
    method: "POST",
    body: { can_be_ordered: newStatus }
  };

  const { data, error } = await fetchRequest(request);

  if (data.value) {
    // Update the local data
    const index = testTypes.value.findIndex(t => t.id === selectedTestType.value.id);
    if (index !== -1) {
      testTypes.value[index].can_be_ordered = newStatus;
    }
    nuxtApp.$toast.success(`Test type ordering ${newStatus ? 'enabled' : 'disabled'} successfully!`);
  }

  if (error.value) {
    console.error(error.value);
    nuxtApp.$toast.error("An error occurred while updating the test type, please try again!");
  }

  loading.value = false;
  showConfirmDialog.value = false;
  selectedTestType.value = null;
};

const cancelCanBeOrderedChange = (): void => {
  showConfirmDialog.value = false;
  selectedTestType.value = null;
};

onMounted(() => {
  init();
  if (Boolean(route.query.create)) {
    nuxtApp.$toast.success("Test type created successfully!");
  }
});
</script>

<style></style>
