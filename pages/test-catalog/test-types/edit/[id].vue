<template>
  <div class="px-5 py-5">
    <div class="relative min-h-screen">
      <div
        v-if="loading"
        class="absolute inset-0 z-20 bg-white bg-opacity-70 flex items-center justify-center"
      >
        <div class="text-center">
          <CoreLoader size="lg" />
          <div class="mt-4">
            <label class="font-medium text-gray-700"
              >Loading test type data, please wait...</label
            >
          </div>
        </div>
      </div>

      <CoreBreadcrumb :pages="pages" />

      <div class="flex items-center justify-between py-5">
        <div v-if="loading" class="flex items-center">
          <div
            class="h-12 w-12 bg-gray-200 rounded-full animate-pulse mr-4"
          ></div>
          <div class="h-8 w-56 bg-gray-200 rounded animate-pulse"></div>
        </div>

        <div v-else-if="!details.name" class="flex items-center">
          <div class="text-red-500">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-10 h-10"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3 class="text-2xl font-semibold ml-3 text-red-500">
            Test Type Not Found
          </h3>
        </div>

        <div v-else class="flex items-center">
          <div class="text-sky-500 flex-shrink-0">
            <svg
              class="w-16 h-16"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 48 48"
            >
              <path
                fill="currentColor"
                fill-rule="evenodd"
                d="M31 10a1 1 0 1 0 0 2v7h-3v-7a1 1 0 1 0 0-2h-8a1 1 0 1 0 0 2v7h-3v-7a1 1 0 1 0 0-2H9a1 1 0 1 0 0 2v7H7v-3H5v3H4v2h1v13H4v2h1v2h2v-2h34v2h2v-2h1v-2h-1V21h1v-2h-1v-3h-2v3h-2v-7a1 1 0 1 0 0-2zm6 9v-7h-4v7zm-4 2h4v5h-4zm-2 0v8a4 4 0 0 0 8 0v-8h2v13H7V21h2v8a4 4 0 0 0 8 0v-8h3v8a4 4 0 0 0 8 0v-8zm-5-2v-7h-4v7zm-11 0v-7h-4v7zm-4 2h4v2h-4z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="text-2xl font-semibold">{{ details.name }}</h3>
            <p class="text-gray-500 text-sm">Edit Test Type Details</p>
          </div>
        </div>

        <div v-if="details.name" class="flex space-x-2">
          <div class="pt-5">
              <div class="flex justify-end space-x-3">
                <button
                  type="button"
                  class="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50 transition"
                  @click="clearAndNavigate('/test-catalog/test-types')"
                  :disabled="updating"
                >
                  Cancel
                </button>
                <CoreActionButton
                  type="submit"
                  :click="() => { submitForm(); }"
                  :loading="updating"
                  color="success"
                  :icon="saveIcon"
                  text="Save Changes"
                />
              </div>
            </div>
        </div>
      </div>

      <div v-if="loading" class="animate-pulse">
        <div class="mt-4 space-y-6">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <div class="h-5 w-28 bg-gray-200 rounded mb-2"></div>
              <div class="h-10 bg-gray-200 rounded w-full"></div>
            </div>
          </div>

          <div class="w-full flex">
            <div class="flex-1">
              <div class="h-5 w-48 bg-gray-200 rounded mb-2"></div>
              <div class="h-10 bg-gray-200 rounded w-full"></div>
            </div>
            <div class="ml-2 w-48">
              <div class="h-5 w-20 bg-gray-200 rounded mb-2"></div>
              <div class="h-10 bg-gray-200 rounded w-full"></div>
            </div>
          </div>

          <div class="mt-10 flex justify-end">
            <div class="h-10 w-36 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>

      <div v-else-if="!details.name" class="mt-10">
        <div class="bg-white border border-red-200 rounded-lg p-6 shadow-sm">
          <div class="flex flex-col items-center justify-center py-10">
            <div class="text-red-500 mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="w-20 h-20"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"
                />
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 mb-2">
              Test Type Not Found
            </h2>
            <p class="text-gray-600 mb-8 text-center max-w-lg">
              The test type you are looking for could not be found or has been
              removed from the system.
            </p>
            <button
              @click="clearAndNavigate('/test-catalog/test-types')"
              class="px-4 py-2 bg-sky-500 flex items-center text-white rounded-md hover:bg-sky-600 transition shadow-sm"
            >
              <ArrowLeftIcon class="w-5 h-5 mr-2" />
              Return to Test Types List
            </button>
          </div>
        </div>
      </div>

      <div v-else>
        <div class="bg-white border border-gray-200 rounded p-6">
          <FormKit
            type="form"
            submit-label="Update"
            @submit="submitForm"
            :actions="false"
            #default=""
          >
            <div class="mt-2 space-y-5">
              <div class="grid grid-cols-2 gap-6">
                <FormKit
                  type="text"
                  label="Preferred name"
                  validation="required"
                  validation-visibility="live"
                  help="The display name for this test type"
                  v-model="details.preferred_name"
                />
              </div>

              <div class="w-full flex space-x-4">
                <FormKit
                  type="number"
                  label="Expected Turn Around Time"
                  validation="number"
                  :validation-messages="{
                    number: 'Turn around time must be a valid number',
                  }"
                  validation-visibility="live"
                  placeholder="Enter turn around time (default: 0)"
                  help="How long this test typically takes to complete"
                  v-model="expected_turn_around_time"
                />
                <div class="ml-2 flex flex-col space-y-2">
                  <label class="font-medium text-base"
                    >Duration <span class="text-red-600">*</span>
                  </label>
                  <div class="relative">
                    <select
                      v-model="duration.name"
                      class="w-full appearance-none border border-gray-300 rounded py-1.5 pl-3 pr-10 text-gray-700 cursor-pointer focus:outline-none focus:ring-0 focus:border-transparent"
                    >
                      <option value="-- select value --" disabled>
                        -- select value --
                      </option>
                      <option
                        v-for="(item, index) in timeTypes"
                        :key="index"
                        :value="item.name"
                      >
                        {{ item.name }}
                      </option>
                    </select>
                    <div
                      class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500"
                    >
                      <svg
                        class="w-5 h-5"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                      >
                        <path
                          fill="none"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="1.5"
                          d="m8 16l4 4l4-4M8 8l4-4l4 4"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="details.indicators && details.indicators.length > 0" class="mt-6">
                <h3 class="text-lg font-medium text-gray-800 mb-3">Measures (Indicators)</h3>
                <div class="bg-gray-50 p-4 rounded border border-gray-100">
                  <div v-for="(indicator, index) in details.indicators" :key="index" class="mb-4 pb-4 border-b border-gray-200 last:border-0 last:mb-0 last:pb-0">
                    <div class="flex flex-col">
                      <div class="flex items-center mb-2">
                        <span class="pl-2 py-0 bg-gray-200 flex items-center text-gray-700 text-xs rounded-full">
                          System name <span class="bg-sky-500 text-sm text-white px-2 py-1 rounded-r-full ml-2 font-medium">{{ indicator.name }}</span>
                        </span>
                      </div>
                      <FormKit
                        type="text"
                        label="Preferred name"
                        validation="required"
                        validation-visibility="live"
                        :help="`Set display name for '${indicator.name}'`"
                        v-model="indicator.preferred_name"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </FormKit>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowDownTrayIcon } from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { TestType, Page, Request } from "@/types";

import Package from "@/package.json";
import { ArrowLeftIcon } from "@heroicons/vue/24/outline";
import { useSecureRouting } from "@/composables/useSecureRouting";

definePageMeta({
  layout: "dashboard",
  middleware: ["test-catalog"],
});

useHead({
  title: `${Package.name.toUpperCase()} - Test Types`,
});

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const loading = ref<boolean>(false);
const updating = ref<boolean>(false);
const saveIcon = ArrowDownTrayIcon;
const { getSecureId, clearAndNavigate } = useSecureRouting();

const details = ref<TestType>({
  preferred_name: "",
  expected_turn_around_time: {
    value: "",
    unit: "",
  },
  name: "",
  indicators: [],
});

const expected_turn_around_time = ref<string>("");
const duration = ref({ name: "-- select value --" });

const timeTypes = [
  { name: "Month" },
  { name: "Weeks" },
  { name: "Days" },
  { name: "Hours" },
  { name: "Minutes" },
];

const pages: Page = [
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Test Catalog",
    link: "#",
  },
  {
    name: "Test Types",
    link: "/test-catalog/test-types",
  },
];

const init = async (): Promise<void> => {
  loading.value = true;

  const testTypeId = getSecureId();

  if (!testTypeId) {
    const routeId = route.params.id as string;
    if (routeId && !isNaN(parseInt(routeId))) {
      const directId = parseInt(routeId);
      const request: Request = {
        route: `${endpoints.viewTestType}/${directId}`,
        method: "GET",
      };
      try {
        const { data, error } = await fetchRequest(request);
        if (data.value && Object.keys(data.value).length > 0) {
          details.value = data.value;

          // Initialize indicators with preferred_name if missing
          if (details.value.indicators && details.value.indicators.length > 0) {
            details.value.indicators = details.value.indicators.map(indicator => {
              return {
                ...indicator,
                preferred_name: indicator.preferred_name || indicator.name
              };
            });
          }

          const turnAroundTimeData = data.value.expected_turn_around_time || {
            unit: null,
            value: "",
          };
          duration.value = {
            name: turnAroundTimeData.unit || "-- select value --",
          };
          expected_turn_around_time.value = turnAroundTimeData.value || "";
          loading.value = false;
          return;
        }
      } catch (err) {
        console.error("Error trying direct ID:", err);
      }
    }

    loading.value = false;
    nuxtApp.$toast.error("Invalid or missing test type ID");
    router.push("/test-catalog/test-types");
    return;
  }

  const request: Request = {
    route: `${endpoints.viewTestType}/${testTypeId}`,
    method: "GET",
  };

  try {
    const { data, error } = await fetchRequest(request);

    if (data.value && Object.keys(data.value).length > 0) {
      details.value = data.value;

      // Initialize indicators with preferred_name if missing
      if (details.value.indicators && details.value.indicators.length > 0) {
        details.value.indicators = details.value.indicators.map(indicator => {
          return {
            ...indicator,
            preferred_name: indicator.preferred_name || indicator.name
          };
        });
      }

      const turnAroundTimeData = data.value.expected_turn_around_time || {
        unit: null,
        value: "",
      };

      duration.value = {
        name: turnAroundTimeData.unit || "-- select value --",
      };

      expected_turn_around_time.value = turnAroundTimeData.value || "";
    } else {
      console.error("Test type data is empty or not found");
      nuxtApp.$toast.error("Test type not found or could not be loaded");
    }
  } catch (err) {
    console.error("Error fetching test type:", err);
    nuxtApp.$toast.error("Failed to load test type data");
  } finally {
    loading.value = false;
  }
};

const submitForm = async (): Promise<void> => {
  updating.value = true;

  const turnAroundValue =
    expected_turn_around_time.value === ""
      ? "0"
      : expected_turn_around_time.value;
  const durationUnit =
    duration.value.name === "-- select value --" ? "Days" : duration.value.name;

  details.value.expected_turn_around_time = {
    value: turnAroundValue,
    unit: durationUnit,
  };

  // Ensure indicators have preferred_name (fallback to name if not set)
  if (details.value.indicators && details.value.indicators.length > 0) {
    details.value.indicators = details.value.indicators.map(indicator => {
      return {
        ...indicator,
        preferred_name: indicator.preferred_name || indicator.name
      };
    });
  }

  let testTypeId = getSecureId();

  // If secure ID is not found, try to use the route param directly
  if (!testTypeId) {
    const routeId = route.params.id as string;
    if (routeId && !isNaN(parseInt(routeId))) {
      testTypeId = parseInt(routeId);
    } else {
      nuxtApp.$toast.error("Invalid test type ID");
      updating.value = false;
      return;
    }
  }

  const request: Request = {
    route: `${endpoints.testTypes}/${testTypeId}`,
    method: "PUT",
    body: details.value,
  };

  const { data, error } = await fetchRequest(request);

  if (data.value) {
    nuxtApp.$toast.success(`Test type updated successfully!`);
    updating.value = false;
    // Clear the current route mapping and navigate back to the list
    clearAndNavigate("/test-catalog/test-types");
  }

  if (error.value) {
    nuxtApp.$toast.error(`An error occurred, please try again!`);
    console.error("error:", error.value);
    updating.value = false;
  }
};

onMounted(() => {
  init();
});
</script>

<style></style>
