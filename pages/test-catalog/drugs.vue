<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
        <div class="text-2xl font-semibold flex items-center uppercase">
          <img
            src="@/assets/icons/medicines.svg"
            alt="report-icon"
            class="w-8 h-8 mr-2"
          />
          Drugs
        </div>
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar
        v-model:search="search"
        @update="updateSearch"
        v-model="search"
        v-if="usePermissions().can.manage('test_catalog')"
      />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="drugs"
      :loading="loading"
      :search-value="searchValue"
      v-if="usePermissions().can.manage('test_catalog')"
    >
    <template #item-description="{ item }">
        <div
          class="line-clamp-1"
          :class="item.description ? '' : 'italic text-sm text-zinc-600'"
          v-html="item.description || 'No description available'"
        ></div>
      </template>
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <DrugsViewDialog :data="item" />
          <DrugsEditDialog :data="item" @update="updateDrugs" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script lang="ts">
import { MagnifyingGlassIcon } from "@heroicons/vue/24/solid/index.js";
import fetchRequest from "@/services/fetch";
import { endpoints } from "@/services/endpoints";
import type { Drug, Page, Header, Request, Response } from "@/types";
import moment from "moment";
import Package from "@/package.json";

export default {
  setup() {
    definePageMeta({
      layout: "dashboard",
      middleware: ["test-catalog"],
    });
    useHead({
      title: `${Package.name.toUpperCase()} - Drugs`,
    });
  },
  data() {
    return {
      header: "List Of Drugs" as string,
      drugs: new Array<Drug>(),
      pages: [
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Test Catalog",
          link: "#",
        },
      ] as Page,
      loading: false as boolean,
      search: "" as string,
      searchValue: "" as string,
      cookie: useCookie("token"),
      headers: [
        { text: "id", value: "id", sortable: true },
        { text: "name", value: "name", sortable: true },
        { text: "preferred name", value: "preferred_name", sortable: true },
        { text: "nlims code", value: "nlims_code", sortable: false },
        { text: "description", value: "description", sortable: true, html: true },
        { text: "date created", value: "created_date" },
        { text: "actions", value: "actions" },
      ] as Header,
    };
  },
  components: { MagnifyingGlassIcon },
  created() {
    this.init();
  },
  methods: {
    updateSearch(value: string): void {
      this.searchValue = value;
      this.search = value;
    },
    async init(): Promise<void> {
      this.loading = true;

      const request: Request = {
        route: endpoints.drugs,
        method: "GET",
        token: `${this.cookie}`,
      };
      const { pending, error, data }: Response = await fetchRequest(request);
      this.loading = pending;

      if (data.value) {
        this.drugs = data.value.map((item: { created_date: string }) => ({
          ...item,
          created_date: moment(item.created_date).format(DATE_FORMAT),
        }));
        this.loading = false;
      }

      if (error.value) {
        console.log(error.value);
        this.loading = false;
      }
    },
    updateDrugs(value: boolean): void {
      if (value) {
        this.search = "";
        this.searchValue = "";
        this.init();
      }
    },
  },
};
</script>

<style></style>
