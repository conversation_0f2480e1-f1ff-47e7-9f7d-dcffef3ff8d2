<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <div class="text-2xl font-semibold flex items-center uppercase">
        <img
          src="@/assets/icons/tests.png"
          alt="report-icon"
          class="w-8 h-8 mr-2"
        />
        {{ header }}
      </div>
      <div class="flex items-center space-x-3">
        <SpecimenRejectionAddDialog
          @update="updateSpecimenRejection"
          v-if="usePermissions().can.manage('test_catalog')"
        />
      </div>
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar
        v-model:search="search"
        @update="updateSearch"
        v-if="usePermissions().can.manage('test_catalog')"
      />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="reasons"
      :loading="loading"
      :search-value="searchValue"
      :search-field="'description'"
      v-if="usePermissions().can.manage('test_catalog')"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <SpecimenRejectionViewDialog :data="item" />

          <SpecimenRejectionEditDialog
            :data="item"
            @update="updateSpecimenRejection"
          />

          <SpecimenRejectionDeleteDialog
            :data="item"
            @update="updateSpecimenRejection"
          />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script lang="ts">
import { MagnifyingGlassIcon } from "@heroicons/vue/24/solid/index.js";
import fetchRequest from "@/services/fetch";
import { endpoints } from "@/services/endpoints";
import type { Header, Page, Request, Response } from "@/types";
import Package from "@/package.json";

export default {
  setup() {
    definePageMeta({
      layout: "dashboard",
      middleware: ["test-catalog"],
    });
    useHead({
      title: `${Package.name.toUpperCase()} - Specimen Rejection Reasons`,
    });
  },
  components: { MagnifyingGlassIcon },
  data() {
    return {
      header: "Specimen Rejection Reasons" as string,
      reasons: new Array<Object>(),
      pages: [
        {
          name: "Home",
          link: "/home",
        },
        {
          name: "Test Catalog",
          link: "#",
        },
      ] as Page,
      search: "" as string,
      searchValue: "" as string,
      loading: false as boolean,
      cookie: useCookie("token"),
      headers: [
        { text: "id", value: "id", sortable: true },
        { text: "reason", value: "description", sortable: true },
        { text: "actions", value: "actions" },
      ] as Header,
    };
  },
  created() {
    this.init();
  },
  methods: {
    updateSearch(value: string): void {
      this.searchValue = value;
      this.search = value;
    },
    async init(): Promise<void> {
      this.loading = true;

      const request: Request = {
        route: endpoints.rejectionReasons,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { pending, error, data }: Response = await fetchRequest(request);
      this.loading = pending;

      if (data.value) {
        this.loading = false;
        this.reasons = data.value;
      }

      if (error.value) {
        this.loading = false;
        console.log(error.value);
      }
    },
    updateSpecimenRejection(value: boolean): void {
      if (value) {
        this.init();
      }
    },
  },
};
</script>

<style></style>
