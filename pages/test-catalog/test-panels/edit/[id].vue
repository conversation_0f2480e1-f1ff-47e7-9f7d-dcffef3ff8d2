<template>
  <div class="px-5 py-5">
    <div class="relative min-h-screen">
      <div
        v-if="loading"
        class="absolute inset-0 z-20 bg-white bg-opacity-70 flex items-center justify-center"
      >
        <div class="text-center">
          <CoreLoader size="lg" />
          <div class="mt-4">
            <label class="font-medium text-gray-700"
              >Loading test panel data, please wait...</label
            >
          </div>
        </div>
      </div>

      <CoreBreadcrumb :pages="pages" />

      <div class="flex items-center justify-between py-5">
        <div v-if="loading" class="flex items-center">
          <div
            class="h-12 w-12 bg-gray-200 rounded-full animate-pulse mr-4"
          ></div>
          <div class="h-8 w-56 bg-gray-200 rounded animate-pulse"></div>
        </div>

        <div v-else-if="!data.name" class="flex items-center">
          <div class="text-red-500">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-10 h-10"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3 class="text-2xl font-semibold ml-3 text-red-500">
            Test Panel Not Found
          </h3>
        </div>

        <div v-else class="flex items-center">
          <div class="text-sky-500 flex-shrink-0">
            <svg
              class="w-12 h-12"
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
            >
              <path
                fill="currentColor"
                d="m22.7 2.14l-11.98.03v20H1.37v.76H22.7Zm-7.604 2.158h3.065l-.006 4.234h-3.072zm.125.163v3.908h2.789V4.461zm.119.129h2.55v3.6h-2.55Zm-.285 5.25l3.127.007l.007 4.309h-3.147zm.166.21v3.91h2.789v-3.91zm.119.14h2.55v3.6h-2.55zm-.305 5.563l3.188.007l-.04 4.325l-3.169-.014zm.186.218v3.91h2.789v-3.91zm.119.138h2.55v3.602h-2.55z"
              />
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="text-2xl font-semibold">{{ data.name }}</h3>
            <p class="text-gray-500 text-sm">Edit Test Panel Details</p>
          </div>
        </div>

        <div v-if="data.name" class="flex space-x-2">
          <button
            @click="clearAndNavigate('/test-catalog/test-panels')"
            class="px-3 py-1 text-gray-600 border border-gray-300 rounded hover:bg-gray-100 flex items-center text-sm"
          >
            <ArrowLeftIcon class="w-4 h-4 mr-1" />
            Back
          </button>
        </div>
      </div>

      <div v-if="loading" class="animate-pulse">
        <div class="mt-4 space-y-6">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <div class="h-5 w-28 bg-gray-200 rounded mb-2"></div>
              <div class="h-10 bg-gray-200 rounded w-full"></div>
            </div>
          </div>

          <div class="w-full flex">
            <div class="flex-1">
              <div class="h-5 w-48 bg-gray-200 rounded mb-2"></div>
              <div class="h-10 bg-gray-200 rounded w-full"></div>
            </div>
          </div>

          <div class="mt-10 flex justify-end">
            <div class="h-10 w-36 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>

      <div v-else-if="!data.name" class="mt-10">
        <div class="bg-white border border-red-200 rounded-lg p-6 shadow-sm">
          <div class="flex flex-col items-center justify-center py-10">
            <div class="text-red-500 mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="w-20 h-20"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"
                />
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 mb-2">
              Test Panel Not Found
            </h2>
            <p class="text-gray-600 mb-8 text-center max-w-lg">
              The test panel you are looking for could not be found or has been
              removed from the system.
            </p>
            <button
              @click="clearAndNavigate('/test-catalog/test-panels')"
              class="px-4 py-2 bg-sky-500 flex items-center text-white rounded-md hover:bg-sky-600 transition shadow-sm"
            >
              <ArrowLeftIcon class="w-5 h-5 mr-2" />
              Return to Test Panels List
            </button>
          </div>
        </div>
      </div>

      <div v-else>
        <div class="bg-white border border-gray-200 rounded p-6">
          <FormKit
            type="form"
            submit-label="Update"
            @submit="submitForm"
            :actions="false"
            #default="{ value }"
            id="submitForm"
          >
            <div class="mt-2 space-y-3">
              <FormKit
                type="text"
                label="Preferred Name"
                validation="required"
                v-model="data.preferred_name"
              />
            </div>

            <div class="border-t border-gray-100 pt-5 mt-6">
              <div class="flex justify-end space-x-3">
                <button
                  type="button"
                  class="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50 transition"
                  @click="clearAndNavigate('/test-catalog/test-panels')"
                  :disabled="loading"
                >
                  Cancel
                </button>
                <CoreActionButton
                  type="submit"
                  :click="() => {}"
                  :loading="loading"
                  color="success"
                  :icon="saveIcon"
                  text="Save Changes"
                />
              </div>
            </div>
          </FormKit>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowDownTrayIcon } from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Page, Request, Response } from "@/types";
import Package from "@/package.json";
import { ArrowLeftIcon } from "@heroicons/vue/24/outline";
import { ERROR_MESSAGE } from "@/utils/constants";
import { useSecureRouting } from "@/composables/useSecureRouting";

definePageMeta({
  layout: "dashboard",
  middleware: ["test-catalog"],
});

useHead({
  title: `${Package.name.toUpperCase()} - Test Panels`,
});

const router = useRouter();
const nuxtApp = useNuxtApp();
const loading = ref<boolean>(false);
const saveIcon = ArrowDownTrayIcon;
const { getSecureId, clearAndNavigate } = useSecureRouting();

const data = ref<{
  id: string;
  name: string;
  short_name: string;
  preferred_name: string;
  description: string;
  test_types: number[];
}>({
  id: "",
  name: "",
  short_name: "",
  preferred_name: "",
  description: "",
  test_types: [],
});

const pages: Page = [
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Test Catalog",
    link: "#",
  },
  {
    name: "Test Panels",
    link: "/test-catalog/test-panels",
  },
];

const init = async (): Promise<void> => {
  loading.value = true;

  const panelId = getSecureId();

  if (!panelId) {
    loading.value = false;
    nuxtApp.$toast.error("No test panel specified");
    router.push("/test-catalog/test-panels");
    return;
  }

  const request: Request = {
    route: `${endpoints.testPanels}/${panelId}`,
    method: "GET",
  };

  try {
    const { data: panelData, error }: Response = await fetchRequest(request);

    if (panelData.value && Object.keys(panelData.value).length > 0) {
      data.value = panelData.value;
    }
  } catch (err) {
    console.error("Error fetching test panel:", err);
    nuxtApp.$toast.error("Failed to load test panel data");
  } finally {
    loading.value = false;
  }
};

const submitForm = async (): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: `${endpoints.testPanels}/${data.value.id}`,
    method: "PUT",
    body: data.value,
  };

  try {
    const { data: responseData, error } = await fetchRequest(request);

    if (responseData.value) {
      nuxtApp.$toast.success("Test panel updated successfully!");
      // Clear the current route mapping and navigate back to the list
      clearAndNavigate("/test-catalog/test-panels");
    }

    if (error.value) {
      console.error(error.value);
      nuxtApp.$toast.error(ERROR_MESSAGE);
    }
  } catch (err) {
    console.error("Error updating test panel:", err);
    nuxtApp.$toast.error(ERROR_MESSAGE);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  init();
});
</script>

<style>
.multiselect-green {
  --ms-tag-bg: rgb(22 163 74);
  --ms-tag-color: #ffffff;
}
</style>
