<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <div class="text-2xl font-semibold flex items-center uppercase">
        <svg
          class="w-10 h-10 mr-2"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="m22.7 2.14l-11.98.03v20H1.37v.76H22.7Zm-7.604 2.158h3.065l-.006 4.234h-3.072zm.125.163v3.908h2.789V4.461zm.119.129h2.55v3.6h-2.55Zm-.285 5.25l3.127.007l.007 4.309h-3.147zm.166.21v3.91h2.789v-3.91zm.119.14h2.55v3.6h-2.55zm-.305 5.563l3.188.007l-.04 4.325l-3.169-.014zm.186.218v3.91h2.789v-3.91zm.119.138h2.55v3.602h-2.55z"
          />
        </svg>

        {{ header }}
      </div>
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar v-model:search="search" @update="updateSearch" />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="testPanels"
      :loading="loading"
      :search-value="searchValue"
      v-if="usePermissions().can.manage('test_catalog')"
    >
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <TestPanelsViewDialog :data="item" />
          <CoreActionButton
            :click="
              () => navigateSecure('/test-catalog/test-panels/edit', item.id)
            "
            text="Edit"
            color="success"
            :icon="PencilSquareIcon"
          />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script setup lang="ts">
import { PencilSquareIcon } from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Page, Header, Request, Response } from "@/types";
import Package from "@/package.json";
import { useSecureRouting } from "@/composables/useSecureRouting";

definePageMeta({
  layout: "dashboard",
  middleware: ["test-catalog"],
});

useHead({
  title: `${Package.name.toUpperCase()} - Test Panels`,
});

const header = ref("Test Panels");
const testPanels = ref<any[]>([]);
const loading = ref(false);
const { navigateSecure } = useSecureRouting();
const pages = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Test Catalog",
    link: "#",
  },
]);
const search = ref<string>("");
const searchValue = ref<string>("");
const headers = ref<Header>([
  { text: "id", value: "id", sortable: true },
  { text: "name", value: "name", sortable: true },
  { text: "short name", value: "short_name", sortable: false },
  { text: "preferred name", value: "preferred_name", sortable: false },
  { text: "nlims code", value: "nlims_code", sortable: false },
  { text: "description", value: "description", sortable: true, html: true },
  { text: "actions", value: "actions", sortable: false },
]);

const updateSearch = (value: string): void => {
  searchValue.value = value;
  search.value = value;
};

const init = async (): Promise<void> => {
  loading.value = true;

  const request: Request = {
    route: endpoints.testPanels,
    method: "GET",
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending.value;

  if (data.value) {
    testPanels.value = data.value;
    loading.value = false;
  }

  if (error.value) {
    console.error(error.value);
    loading.value = false;
  }
};

onMounted(() => {
  init();
});
</script>

<style></style>
