
# Plugins

This folder contains Nuxt plugins written in TypeScript using the defineNuxtPlugin helper function.

Plugins allow you to add reusable functionality to your Nuxt application. They are executed before instantiating the root Vue app.

Some things you may want to put in a plugin:

- Utility libraries
- Vue plugins
- Third party modules
- Persistent state handling like Pinia
- Custom directives
