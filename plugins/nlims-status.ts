import fetchRequest from "@/services/fetch";
import type { Request, Response } from "@/types";
import { endpoints } from "@/services/endpoints";

export default defineNuxtPlugin(async (nuxtApp) => {
    const request: Request = {
        route: `${endpoints.global}/nlims_status`,
        method: "GET",
    };
    const { data, error }: Response = await fetchRequest(request);
    if (data.value) {
        nuxtApp.provide('nlims', data.value);
    }
    if (error.value) {
        console.error('error fetching nlims status', error.value);
    }
})